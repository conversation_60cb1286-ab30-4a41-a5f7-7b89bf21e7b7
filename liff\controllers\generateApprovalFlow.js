const sql = require("mssql");

async function generateApprovalFlow(formId, formType, userId, transaction) {
  const flowResult = await transaction
    .request()
    .input("form_type", sql.VarChar, formType)
    .query(
      "SELECT step_number, remark AS step_name, approver_role, approver_id, Notify FROM Forms_flows WHERE form_type = @form_type ORDER BY step_number"
    );

  const approvals = [];
  const notifications = [];
  let nextStepNumber = 0; // `Step_number` 遞增

  for (const flow of flowResult.recordset) {
    let approverIds = [];

    if (flow.approver_role.trim() === "manager") {
      // 🔹 查詢部門主管
      const depResult = await transaction
        .request()
        .input("user_id", sql.VarChar, userId)
        .query(
          "SELECT Manager FROM Dep WHERE Id = (SELECT Dep FROM Users WHERE id = @user_id)"
        );

      if (depResult.recordset.length > 0 && depResult.recordset[0].Manager) {
        // 檢查主管是否為填單人，若是則略過此步驟
        if (depResult.recordset[0].Manager !== userId) {
          approverIds.push(depResult.recordset[0].Manager);
        }
      }
    } else if (flow.approver_role.trim() === "Umanager") {
      // 🔹 一層一層往上查主管，直到 `Ulevel = NULL`
      let depIdResult = await transaction
        .request()
        .input("user_id", sql.VarChar, userId)
        .query("SELECT Dep FROM Users WHERE id = @user_id");

      if (depIdResult.recordset.length > 0) {
        let depId = depIdResult.recordset[0].Dep;
        let firstApprover = true;

        while (depId) {
          const ulevelResult = await transaction
            .request()
            .input("dep_id", sql.VarChar, depId)
            .query("SELECT Ulevel, Manager FROM Dep WHERE Id = @dep_id");

          if (ulevelResult.recordset.length > 0) {
            const { Ulevel, Manager } = ulevelResult.recordset[0];

            if (Manager && Manager !== userId) {
              // 檢查主管是否為填單人，若是則略過
              approverIds.push({
                id: Manager,
                step: nextStepNumber,
                status: firstApprover ? "waiting" : "-",
                step_name: flow.step_name,
              });

              firstApprover = false;
              nextStepNumber++;
            }

            depId = Ulevel;
          } else {
            break;
          }
        }
      }
    } else if (flow.approver_role.trim() === "personal") {
      // 🔹 若為個人簽核，直接使用 `approver_id`
      if (flow.approver_id && flow.approver_id !== userId) {
        // 檢查簽核人是否為填單人，若是則略過
        approverIds.push(flow.approver_id);
      }
    }

    for (const approver of approverIds) {
      const step =
        typeof approver === "object" ? approver.step : nextStepNumber++;
      const status =
        typeof approver === "object"
          ? approver.status
          : step === 0
          ? "waiting"
          : "-";
      const stepName =
        typeof approver === "object" ? approver.step_name : flow.step_name;
      const notify = flow.Notify ? `'${flow.Notify}'` : "NULL";

      approvals.push(
        `('${formId}', '${step}', '${stepName}', '${
          typeof approver === "object" ? approver.id : approver
        }', '${status}', ${notify})`
      );

      if (status === "waiting" && flow.Notify === "1") {
        notifications.push(
          `('${formId}', '${step}', '${
            typeof approver === "object" ? approver.id : approver
          }', 'pending', 'pending', GETUTCDATE(), NULL)`
        );
      }
    }
  }

  // 若沒有產生任何簽核步驟，則由填單人自行簽核
  if (approvals.length === 0) {
    approvals.push(
      `('${formId}', '0', '自行簽核', '${userId}', 'waiting', NULL)`
    );
  }

  if (approvals.length > 0) {
    await transaction.request().query(`
      INSERT INTO Forms_approver (Form_id, Step_number, Step_name, Approver_id, Status, Notify)
      VALUES ${approvals.join(",")}
    `);
  }

  if (notifications.length > 0) {
    await transaction.request().query(`
      INSERT INTO Forms_notify (Form_id, Step_number, Approver_id, Status, Form_status, Created, Sent)
      VALUES ${notifications.join(",")}
    `);
  }

  return { approvals, notifications };
}

module.exports = { generateApprovalFlow };
