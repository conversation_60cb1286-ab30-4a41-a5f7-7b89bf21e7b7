const express = require("express");
const {
  getBulletins,
  getBulletinById,
  searchBulletins,
  createBulletin,
  updateBulletin,
  deleteBulletin,
  uploadAttachment,
  deleteAttachment,
  getBulletinStats,
  markBulletinAsRead,
  confirmBulletinRead,
  deleteImage,
  getUserBulletins,
  searchUserBulletins,
  getUserReadStatus,
  getUnreadCount,
  getRequiredUnreadCount,
  getUnreadBulletins,
  sendNotifications,
  getNotifications,
  resendNotification,
  deleteNotificationsHistory,
} = require("../controllers/btinController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// 定義檔案限制
const fileFilter = (req, file, cb) => {
  // 允許的檔案類型
  const allowedMimeTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "image/jpeg",
    "image/png",
    "image/gif",
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true); // 接受檔案
  } else {
    cb(new Error(`不支援的檔案類型: ${file.mimetype}`), false); // 拒絕檔案
  }
};

// 設定 multer 上傳限制
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制檔案大小為 5MB
  },
  fileFilter: fileFilter,
});

// 設定圖片上傳的 multer 配置
const imageStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 創建年月資料夾路徑
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, 0); // 確保月份是兩位數
    const yearMonth = `${year}${month}`;

    // 構建完整的資料夾路徑
    const uploadDir = path.join(__dirname, "../uploads/btin", yearMonth);

    // 確保目錄存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now();
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  },
});

const imageUpload = multer({
  storage: imageStorage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB 限制
  },
  fileFilter: function (req, file, cb) {
    // 只接受圖片
    if (!file.mimetype.startsWith("image/")) {
      return cb(new Error("只允許上傳圖片檔案"), false);
    }
    cb(null, true);
  },
});

// 圖片上傳路由
router.post("/upload-image", imageUpload.single("file"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: "沒有上傳檔案" });
    }

    // 創建年月資料夾路徑用於回應
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, 0); // 確保月份是兩位數
    const yearMonth = `${year}${month}`;

    // 返回檔案名稱和包含年月資料夾的路徑
    res.json({
      success: true,
      fileName: req.file.filename,
      filePath: `/uploads/btin/${yearMonth}/${req.file.filename}`,
    });
  } catch (error) {
    console.error("圖片上傳錯誤:", error);
    res.status(500).json({ success: false, message: "圖片上傳失敗" });
  }
});

// 刪除公告中的圖片 - 移到前面避免被其他路由攔截
router.delete("/images/*", deleteImage);

// 獲取所有公告
router.get("/get_bulletins", authMiddleware, getBulletins);

// 搜尋公告
router.get("/bulletins/search", authMiddleware, searchBulletins);

// 獲取公告詳情
router.get("/bulletins/:id", authMiddleware, getBulletinById);

// 新增公告
router.post("/bulletins", authMiddleware, createBulletin);

// 更新公告
router.put("/bulletins/:id", authMiddleware, updateBulletin);

// 刪除公告
router.delete("/bulletins/:id", authMiddleware, deleteBulletin);

// 上傳附件
router.post(
  "/bulletins/:id/attachments",
  authMiddleware,
  (req, res, next) => {
    console.log("處理附件上傳請求:", req.params);
    console.log("請求內容類型:", req.headers["content-type"]);
    upload.single("file")(req, res, (err) => {
      if (err) {
        console.error("Multer 錯誤:", err);
        return res
          .status(400)
          .json({ success: false, message: "文件上傳錯誤: " + err.message });
      }
      next();
    });
  },
  uploadAttachment
);

// 刪除附件
router.delete(
  "/bulletins/:id/attachments/:sno",
  authMiddleware,
  deleteAttachment
);

// 獲取閱讀統計
router.get("/bulletins/:id/stats", authMiddleware, getBulletinStats);

// 標記公告為已讀
router.post("/bulletins/:id/read", authMiddleware, markBulletinAsRead);

// 確認閱讀公告
router.post("/bulletins/:id/confirm", authMiddleware, confirmBulletinRead);

// 獲取用戶公告列表
router.get("/user-bulletins/:userId", authMiddleware, getUserBulletins);

// 搜尋用戶公告
router.get(
  "/user-bulletins/:userId/search",
  authMiddleware,
  searchUserBulletins
);

// 獲取用戶閱讀狀態
router.get("/read-status/:userId", authMiddleware, getUserReadStatus);

// 獲取未讀公告數量
router.get("/unread-count/:userId", authMiddleware, getUnreadCount);

// 獲取必讀未讀公告數量
router.get(
  "/required-unread-count/:userId",
  authMiddleware,
  getRequiredUnreadCount
);

// 獲取未讀公告列表
router.get("/unread-bulletins/:userId", authMiddleware, getUnreadBulletins);

// 手動觸發發送公告通知
router.post("/send-notifications", authMiddleware, sendNotifications);

// 獲取所有公告通知
router.get("/get-notifications", authMiddleware, getNotifications);

// 重新發送特定通知
router.post("/resend-notification", authMiddleware, resendNotification);

// 刪除歷史通知記錄
router.post(
  "/delete-notifications-history",
  authMiddleware,
  deleteNotificationsHistory
);

module.exports = router;
