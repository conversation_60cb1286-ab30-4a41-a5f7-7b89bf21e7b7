const sql = require("mssql");
const { getPoolByBranch } = require("../services/dbPoolManager");

// 獲取所有套餐群組
exports.getSetGroups = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ error: "缺少分店資訊" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 先獲取所有套餐群組資訊
    const groupsResult = await pool.request().query(`
      SELECT 
        sets_code, sets_title, c_min, c_max, sort, type, mcq
      FROM setsgroup
      ORDER BY sort ASC
    `);

    const groups = groupsResult.recordset;

    // 獲取每個群組的項目資訊
    for (const group of groups) {
      const itemsResult = await pool
        .request()
        .input("sets_code", sql.NVarChar, group.sets_code).query(`
          SELECT 
            si.sets, si.sno, si.code, si.name, si.sname, si.price, ISNULL(si.unit, '份') as unit
          FROM SetsItems si
          WHERE si.sets = @sets_code
          ORDER BY si.sno ASC
        `);

      group.items = itemsResult.recordset;
    }

    return res.json({
      success: true,
      data: groups,
    });
  } catch (error) {
    console.error("獲取套餐群組時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取套餐群組資料時發生錯誤",
      error: error.message,
    });
  }
};

// 生成下一個套餐群組編號
const generateNextSetsCode = async (pool) => {
  try {
    // 查詢當前最大的編號
    const result = await pool.request().query(`
      SELECT TOP 1 sets_code FROM setsgroup 
      WHERE sets_code LIKE 'S%' 
      ORDER BY sets_code DESC
    `);

    if (result.recordset.length === 0) {
      // 如果沒有記錄，則從S00001開始
      return "S00001";
    }

    // 從現有最大編號中提取數字部分並加1
    const currentCode = result.recordset[0].sets_code;
    const currentNumber = parseInt(currentCode.substring(1), 10);
    const nextNumber = currentNumber + 1;

    // 格式化為5位數，不足前面補0
    return `S${nextNumber.toString().padStart(5, "0")}`;
  } catch (error) {
    console.error("生成套餐群組編號時發生錯誤:", error);
    throw error;
  }
};

// 儲存套餐群組資訊
exports.saveSetGroup = async (req, res) => {
  const { branch, group } = req.body;

  if (!branch || !group) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查是否有提供sets_code (前端現在不會提供)
    let sets_code = group.sets_code;
    let isNewGroup = !sets_code;

    if (isNewGroup) {
      // 如果是新群組，生成新的編號
      sets_code = await generateNextSetsCode(pool);
    } else {
      // 如果有提供sets_code，檢查是否存在
      const checkResult = await pool
        .request()
        .input("sets_code", sql.NVarChar, sets_code)
        .query("SELECT sets_code FROM setsgroup WHERE sets_code = @sets_code");

      isNewGroup = checkResult.recordset.length === 0;
    }

    if (!isNewGroup) {
      // 更新現有群組
      await pool
        .request()
        .input("sets_code", sql.NVarChar, sets_code)
        .input("sets_title", sql.NVarChar, group.sets_title)
        .input("c_min", sql.Int, group.c_min)
        .input("c_max", sql.Int, group.c_max)
        .input("sort", sql.Int, group.sort)
        .input("type", sql.NVarChar, group.type)
        .input("mcq", sql.NVarChar, group.mcq).query(`
          UPDATE setsgroup
          SET sets_title = @sets_title,
              c_min = @c_min,
              c_max = @c_max,
              sort = @sort,
              type = @type,
              mcq = @mcq
          WHERE sets_code = @sets_code
        `);
    } else {
      // 新增群組
      await pool
        .request()
        .input("sets_code", sql.NVarChar, sets_code)
        .input("sets_title", sql.NVarChar, group.sets_title)
        .input("c_min", sql.Int, group.c_min)
        .input("c_max", sql.Int, group.c_max)
        .input("sort", sql.Int, group.sort)
        .input("type", sql.NVarChar, group.type)
        .input("mcq", sql.NVarChar, group.mcq).query(`
          INSERT INTO setsgroup (sets_code, sets_title, c_min, c_max, sort, type, mcq)
          VALUES (@sets_code, @sets_title, @c_min, @c_max, @sort, @type, @mcq)
        `);
    }

    // 返回生成的編號給前端
    return res.json({
      success: true,
      message: "套餐群組已儲存",
      sets_code: sets_code,
    });
  } catch (error) {
    console.error("儲存套餐群組時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "儲存套餐群組時發生錯誤",
      error: error.message,
    });
  }
};

// 刪除套餐群組
exports.deleteSetGroup = async (req, res) => {
  const { branch, sets_code } = req.body;

  if (!branch || !sets_code) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 先刪除關聯的項目
    await pool
      .request()
      .input("sets_code", sql.NVarChar, sets_code)
      .query("DELETE FROM SetsItems WHERE sets = @sets_code");

    // 刪除套餐映射關係
    await pool
      .request()
      .input("sets_code", sql.NVarChar, sets_code)
      .query("DELETE FROM SetsMap WHERE sets_code = @sets_code");

    // 刪除套餐群組
    await pool
      .request()
      .input("sets_code", sql.NVarChar, sets_code)
      .query("DELETE FROM setsgroup WHERE sets_code = @sets_code");

    return res.json({
      success: true,
      message: "套餐群組已刪除",
    });
  } catch (error) {
    console.error("刪除套餐群組時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "刪除套餐群組時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取可新增的菜單項目 (type=B1 套餐用食材)
exports.getMenuItems = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ error: "缺少分店資訊" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 獲取套餐用食材
    const result = await pool.request().query(`
      SELECT 
        o.code, o.name, os.name as sname, os.oprice as price, ISNULL(o.unit, '份') as unit
      FROM omenum o
      LEFT JOIN omenus os ON o.code = os.code
      WHERE o.B1 = '3'
      ORDER BY o.name
    `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取菜單項目時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取菜單項目時發生錯誤",
      error: error.message,
    });
  }
};

// 添加項目到套餐群組
exports.addSetItems = async (req, res) => {
  const { branch, sets_code, items } = req.body;

  if (!branch || !sets_code || !items || !items.length) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 獲取當前最大的序號
    const maxSnoResult = await pool
      .request()
      .input("sets_code", sql.NVarChar, sets_code)
      .query(
        "SELECT MAX(sno) as maxSno FROM SetsItems WHERE sets = @sets_code"
      );

    let maxSno = maxSnoResult.recordset[0].maxSno || 0;

    // 添加新項目
    for (const item of items) {
      maxSno++;
      await pool
        .request()
        .input("sets", sql.NVarChar, sets_code)
        .input("sno", sql.Int, maxSno)
        .input("code", sql.NVarChar, item.code)
        .input("name", sql.NVarChar, item.name)
        .input("sname", sql.NVarChar, item.sname || "")
        .input("price", sql.Float, item.price || 0)
        .input("unit", sql.NVarChar, item.unit || "份").query(`
          INSERT INTO SetsItems (sets, sno, code, name, sname, price, unit)
          VALUES (@sets, @sno, @code, @name, @sname, @price, @unit)
        `);
    }

    return res.json({
      success: true,
      message: "項目已添加到套餐群組",
    });
  } catch (error) {
    console.error("添加項目到套餐群組時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "添加項目到套餐群組時發生錯誤",
      error: error.message,
    });
  }
};

// 刪除套餐群組中的項目
exports.deleteSetItem = async (req, res) => {
  const { branch, sets_code, code, sno } = req.body;

  if (!branch || !sets_code || !code) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 刪除項目
    await pool
      .request()
      .input("sets", sql.NVarChar, sets_code)
      .input("code", sql.NVarChar, code)
      .input("sno", sql.Int, sno)
      .query(
        "DELETE FROM SetsItems WHERE sets = @sets AND code = @code AND sno = @sno"
      );

    return res.json({
      success: true,
      message: "項目已從套餐群組中刪除",
    });
  } catch (error) {
    console.error("刪除套餐群組項目時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "刪除套餐群組項目時發生錯誤",
      error: error.message,
    });
  }
};

// 保存群組排序
exports.saveGroupSort = async (req, res) => {
  const { branch, groups } = req.body;

  if (!branch || !groups || !groups.length) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 逐一更新排序
    for (const group of groups) {
      await pool
        .request()
        .input("sets_code", sql.NVarChar, group.sets_code)
        .input("sort", sql.Int, group.sort)
        .query(
          "UPDATE setsgroup SET sort = @sort WHERE sets_code = @sets_code"
        );
    }

    return res.json({
      success: true,
      message: "群組排序已儲存",
    });
  } catch (error) {
    console.error("儲存群組排序時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "儲存群組排序時發生錯誤",
      error: error.message,
    });
  }
};

// 保存項目排序
exports.saveSetItemSort = async (req, res) => {
  const { branch, sets_code, items } = req.body;

  if (!branch || !sets_code || !items) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 使用交易確保更新的一致性
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // 先獲取現有的項目資料
      const existingItemsResult = await transaction
        .request()
        .input("sets", sql.NVarChar, sets_code).query(`
          SELECT code, name, sname, price, unit
          FROM SetsItems 
          WHERE sets = @sets
        `);

      const existingItems = existingItemsResult.recordset;
      const existingItemsMap = new Map();
      existingItems.forEach((item) => {
        existingItemsMap.set(item.code, item);
      });

      // 刪除所有現有項目
      await transaction
        .request()
        .input("sets", sql.NVarChar, sets_code)
        .query("DELETE FROM SetsItems WHERE sets = @sets");

      // 重新插入所有項目，使用新的排序，並保留原有資料
      for (const item of items) {
        const existingItem = existingItemsMap.get(item.code);
        if (existingItem) {
          await transaction
            .request()
            .input("sets", sql.NVarChar, sets_code)
            .input("sno", sql.Int, item.sno)
            .input("code", sql.NVarChar, item.code)
            .input("name", sql.NVarChar, existingItem.name)
            .input("sname", sql.NVarChar, existingItem.sname || "")
            .input("price", sql.Float, existingItem.price || 0)
            .input("unit", sql.NVarChar, existingItem.unit || "份").query(`
              INSERT INTO SetsItems (sets, sno, code, name, sname, price, unit)
              VALUES (@sets, @sno, @code, @name, @sname, @price, @unit)
            `);
        }
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return res.json({
      success: true,
      message: "項目排序已儲存",
    });
  } catch (error) {
    console.error("儲存項目排序時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "儲存項目排序時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取主要商品列表 (type=1)
exports.getMainProducts = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ error: "缺少分店資訊" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 獲取主要商品
    const result = await pool.request().query(`
      SELECT 
        code, name
      FROM omenum
      WHERE type = '1'
      ORDER BY name
    `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取主要商品時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取主要商品時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取套餐與商品的對應關係
exports.getSetsMap = async (req, res) => {
  const { branch, sets_code } = req.query;

  if (!branch || !sets_code) {
    return res.status(400).json({ error: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 獲取對應關係
    const result = await pool
      .request()
      .input("sets_code", sql.NVarChar, sets_code).query(`
        SELECT 
          code, sets_code
        FROM SetsMap
        WHERE sets_code = @sets_code
      `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取套餐對應關係時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取套餐對應關係時發生錯誤",
      error: error.message,
    });
  }
};

// 更新套餐與商品的對應關係
exports.updateSetsMap = async (req, res) => {
  const { branch, sets_code, code, isMap } = req.body;

  if (!branch || !sets_code || !code) {
    return res.status(400).json({ error: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    if (isMap) {
      // 添加對應關係
      await pool
        .request()
        .input("sets_code", sql.NVarChar, sets_code)
        .input("code", sql.NVarChar, code).query(`
          IF NOT EXISTS (SELECT 1 FROM SetsMap WHERE sets_code = @sets_code AND code = @code)
          BEGIN
            INSERT INTO SetsMap (sets_code, code)
            VALUES (@sets_code, @code)
          END
        `);
    } else {
      // 移除對應關係
      await pool
        .request()
        .input("sets_code", sql.NVarChar, sets_code)
        .input("code", sql.NVarChar, code).query(`
          DELETE FROM SetsMap
          WHERE sets_code = @sets_code AND code = @code
        `);
    }

    return res.json({
      success: true,
      message: isMap ? "已新增對應關係" : "已移除對應關係",
    });
  } catch (error) {
    console.error("更新套餐對應關係時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "更新套餐對應關係時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取套餐群組列表（用於商品規格對映）
exports.getSetsGroupsForMapping = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ error: "缺少分店資訊" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 獲取所有套餐群組資訊
    const result = await pool.request().query(`
      SELECT 
        sets_code, sets_title
      FROM setsgroup
      ORDER BY sort ASC
    `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取套餐群組列表時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取套餐群組列表時發生錯誤",
      error: error.message,
    });
  }
};

// 新增商品規格對映套餐
exports.addSetsMapping = async (req, res) => {
  const { branch, code, sname, sets_code } = req.body;

  if (!branch || !code || !sets_code) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查是否已存在相同的對映
    const checkResult = await pool
      .request()
      .input("code", sql.NVarChar, code)
      .input("sname", sql.NVarChar, sname || "")
      .input("sets_code", sql.NVarChar, sets_code).query(`
        SELECT * FROM SetsMap 
        WHERE code = @code AND sname = @sname AND sets_code = @sets_code
      `);

    if (checkResult.recordset.length > 0) {
      return res.json({
        success: false,
        message: "此規格已對映到此套餐",
      });
    }

    // 新增對映關係
    await pool
      .request()
      .input("code", sql.NVarChar, code)
      .input("sname", sql.NVarChar, sname || "")
      .input("sets_code", sql.NVarChar, sets_code).query(`
        INSERT INTO SetsMap (code, sname, sets_code)
        VALUES (@code, @sname, @sets_code)
      `);

    return res.json({
      success: true,
      message: "套餐對映已新增",
    });
  } catch (error) {
    console.error("新增套餐對映時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "新增套餐對映時發生錯誤",
      error: error.message,
    });
  }
};

// 刪除商品規格對映套餐
exports.deleteSetsMapping = async (req, res) => {
  const { branch, code, sname, sets_code } = req.body;

  if (!branch || !code || !sets_code) {
    return res.status(400).json({
      success: false,
      message: "缺少必要參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 刪除對映關係
    await pool
      .request()
      .input("code", sql.NVarChar, code)
      .input("sname", sql.NVarChar, sname || "")
      .input("sets_code", sql.NVarChar, sets_code).query(`
        DELETE FROM SetsMap 
        WHERE code = @code AND sname = @sname AND sets_code = @sets_code
      `);

    return res.json({
      success: true,
      message: "套餐對映已刪除",
    });
  } catch (error) {
    console.error("刪除套餐對映時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "刪除套餐對映時發生錯誤",
      error: error.message,
    });
  }
};

// 獲取商品的套餐對映列表
exports.getMenuSetsMappings = async (req, res) => {
  const { branch, code } = req.query;

  if (!branch || !code) {
    return res.status(400).json({ error: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 獲取商品的套餐對映，不考慮 sname
    const result = await pool.request().input("code", sql.NVarChar, code)
      .query(`
        SELECT 
          sm.code, sm.sname, sm.sets_code,
          sg.sets_title
        FROM SetsMap sm
        LEFT JOIN setsgroup sg ON sm.sets_code = sg.sets_code
        WHERE sm.code = @code
        ORDER BY sm.sets_code, sm.sname
      `);

    return res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("獲取商品套餐對映時發生錯誤:", error);
    return res.status(500).json({
      success: false,
      message: "獲取商品套餐對映時發生錯誤",
      error: error.message,
    });
  }
};
