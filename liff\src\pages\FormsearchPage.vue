<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">單據查詢</div>
            <q-btn
              flat
              round
              size="sm"
              color="red"
              class="search-button"
              @click="openAdvancedSearch"
            >
              <Search />
              <q-tooltip>進階搜尋</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>

        <!-- 搜尋輸入框 -->
        <q-card-section class="q-pt-none">
          <q-input
            v-model="queryKeyword"
            placeholder="輸入單號或關鍵字搜尋"
            outlined
            dense
            clearable
            class="search-input"
            @update:model-value="handleQueryChange"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 統計卡片區域 -->
        <q-card-section class="q-py-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部單據統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': activeTab === 'all' }"
                  @click="activeTab = 'all'"
                >
                  <div class="stats-icon">
                    <Files :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ totalRequests }}</div>
                    <div class="stats-label">全部單據</div>
                  </div>
                </div>
              </div>

              <!-- 已簽核統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card approved-card"
                  :class="{ 'active-filter': activeTab === 'approved' }"
                  @click="activeTab = 'approved'"
                >
                  <div class="stats-icon">
                    <CheckCircle :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ approvedRequests.length }}</div>
                    <div class="stats-label">已簽核</div>
                  </div>
                </div>
              </div>

              <!-- 待處理統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card pending-card"
                  :class="{ 'active-filter': activeTab === 'pending' }"
                  @click="activeTab = 'pending'"
                >
                  <div class="stats-icon">
                    <Clock :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ pendingRequests.length }}</div>
                    <div class="stats-label">待處理</div>
                  </div>
                </div>
              </div>

              <!-- 已取消統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card cancelled-card"
                  :class="{ 'active-filter': activeTab === 'cancel' }"
                  @click="activeTab = 'cancel'"
                >
                  <div class="stats-icon">
                    <X :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ cancelledRequests.length }}
                    </div>
                    <div class="stats-label">已取消</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 單據列表卡片 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <q-card-section class="q-pa-xs">
        <!-- 加載中狀態 -->
        <div v-if="loading" class="loader-container">
          <q-spinner size="40px" color="grey-5" />
          <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
            載入單據中...
          </div>
        </div>

        <!-- 無資料狀態 -->
        <div
          v-else-if="filteredRequests.length === 0"
          class="empty-state-container"
        >
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <message-square-warning
              :size="50"
              :stroke-width="1"
              style="width: 60px; height: 50px; margin-bottom: 12px"
            />
            <div class="empty-text text-grey-5">
              {{ searchForm.title ? "沒有找到符合的單據" : "目前沒有單據" }}
            </div>
          </div>
        </div>

        <!-- 單據列表 -->
        <div v-else class="request-list-container">
          <!-- 添加分頁控制器到頂部 -->
          <div class="row justify-end q-mb-sm">
            <q-pagination
              v-model="currentPage"
              :max="totalPages"
              :max-pages="5"
              boundary-numbers
              direction-links
              color="primary"
              active-color="blue"
              active-text-color="white"
              class="pagination-controls"
              size="sm"
            />
          </div>

          <q-list separator class="request-list">
            <q-item
              v-for="request in paginatedRequests"
              :key="request.Form_id"
              class="bg-white q-mb-xs request-item"
              :class="{ 'pending-request': request.fstatus === 'pending' }"
              clickable
              @click="viewRequest(request.Form_id)"
            >
              <q-item-section>
                <!-- 標題 -->
                <div
                  class="text-indigo-10 text-weight-medium request-title q-mb-xs"
                >
                  {{ request.doc_name }} - {{ request.submitter }}
                </div>

                <!-- 單號和時間 -->
                <div class="text-caption text-grey-8 request-meta">
                  <div class="flex items-center">
                    <hash :stroke-width="1" :size="16" class="q-mr-xs" />
                    單號: {{ request.Form_id }}
                  </div>
                  <div class="flex items-center q-mt-xs">
                    <calendar-clock
                      :stroke-width="1"
                      :size="16"
                      class="q-mr-xs"
                    />
                    送單時間: {{ request.submit_date }}
                  </div>
                </div>
              </q-item-section>

              <!-- 狀態顯示 -->
              <q-item-section side>
                <q-badge
                  outline
                  :color="getStatusColor(request.fstatus)"
                  :label="getStatusLabel(request.fstatus)"
                  class="q-px-sm q-py-xs"
                />
              </q-item-section>
            </q-item>
          </q-list>

          <!-- 移除原有的分頁控制器 -->
        </div>
      </q-card-section>
    </q-card>

    <!-- 進階搜尋對話框 -->
    <q-dialog v-model="advancedSearchDialog">
      <q-card style="width: 500px; max-width: 90vw">
        <q-card-section class="row items-center bg-primary text-white q-pb-xs">
          <div class="text-subtitle1">
            <q-icon name="search" class="q-mr-xs" size="sm" />
            進階搜尋
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-sm">
          <q-form>
            <!-- 日期區間 -->
            <q-input
              v-model="formattedDate"
              label="選擇日期區間"
              outlined
              readonly
              dense
              class="full-width"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="dateRange" range mask="YYYY-MM-DD">
                      <div
                        class="row items-center justify-end q-gutter-sm q-pa-sm"
                      >
                        <q-btn
                          label="清除"
                          color="grey"
                          flat
                          @click="clearDateRange"
                        />
                        <q-btn
                          label="確定"
                          color="primary"
                          flat
                          v-close-popup
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>

            <!-- 送單人員 -->
            <q-select
              v-model="searchForm.submitter"
              :options="submitterOptions"
              label="送單人員"
              outlined
              dense
              class="q-mt-md"
              use-input
              hide-selected
              fill-input
              input-debounce="300"
              @filter="filterSubmitters"
            />

            <!-- 表單類型 -->
            <q-select
              v-model="searchForm.formType"
              :options="formTypeOptions"
              label="表單類型"
              outlined
              dense
              class="q-mt-md"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            label="清除條件"
            color="red"
            flat
            @click="clearSearchFilters"
          />
          <q-btn
            label="取消"
            color="grey"
            flat
            @click="advancedSearchDialog = false"
          />
          <q-btn label="查詢" color="primary" @click="fetchRequests" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 詳細資訊對話框 -->
    <q-dialog v-model="detailsDialog">
      <q-card style="max-width: 420px; width: 100%">
        <q-card-section>
          <div class="text-h6 text-primary text-center">單據明細</div>
        </q-card-section>

        <!-- 明細加載中狀態 -->
        <div
          v-if="detailLoading"
          class="q-pa-md flex flex-center column text-primary"
        >
          <q-spinner size="40px" color="primary" />
          <div class="text-subtitle2 q-mt-sm">載入明細中...</div>
        </div>

        <template v-else>
          <q-card-section>
            <div class="row">
              <div class="col-6"><strong>送出人員：</strong></div>
              <div class="col-6 text-right">
                {{ selectedRequest?.Submitter }}
                <span class="text-grey">
                  ({{ selectedRequest?.Submitter_id }})
                </span>
              </div>
            </div>
            <div class="row">
              <div class="col-6"><strong>表單類型：</strong></div>
              <div class="col-6 text-right">
                {{ selectedRequest?.Doc_name }}
              </div>
            </div>
            <div class="row">
              <div class="col-6"><strong>單號：</strong></div>
              <div class="col-6 text-right">{{ selectedRequest?.Form_id }}</div>
            </div>
            <div class="row">
              <div class="col-6"><strong>簽呈狀態：</strong></div>
              <div class="col-6 text-right">
                <q-badge
                  :color="getStatusColor(selectedRequest?.Status)"
                  class="text-xs q-pa-xs"
                >
                  {{ getStatusLabel(selectedRequest?.Status) }}
                </q-badge>
              </div>
            </div>
          </q-card-section>

          <q-separator inset />

          <!-- 動態明細內容 -->
          <q-card-section v-if="Object.keys(detailData).length">
            <div
              v-for="(value, key) in formattedDetails"
              :key="key"
              class="row"
            >
              <div class="col-6 text-brown-9">
                <strong>{{ key }}：</strong>
              </div>
              <div class="col-6 text-right text-blue-grey-8">{{ value }}</div>
            </div>
          </q-card-section>

          <q-separator inset />

          <!-- 附件列表 -->
          <q-card-section v-if="attachments.length" class="q-pt-md">
            <div class="text-subtitle2 text-primary">附件：</div>
            <q-list
              bordered
              separator
              style="max-height: 150px; overflow-y: auto"
            >
              <q-item v-for="file in attachments" :key="file.sno">
                <q-item-section>
                  <q-item-label>
                    <a :href="file.file_url" target="_blank" class="text-blue">
                      {{
                        file.file_url
                          ? file.file_url.split("/").pop()
                          : "無效連結"
                      }}
                    </a>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>

          <!-- 簽核歷程 -->
          <q-card-section>
            <q-expansion-item
              expand-separator
              icon="history"
              label="簽核歷程"
              class="text-primary"
            >
              <q-list bordered separator>
                <q-item
                  v-for="(history, index) in approvalHistory"
                  :key="index"
                >
                  <q-item-section avatar>
                    <q-spinner-hourglass
                      v-if="history.Status.trim() === 'waiting'"
                      :color="getStatusColor(history.Status.trim())"
                      size="24px"
                      class="animate-spin"
                    />
                    <q-icon
                      v-else
                      :name="getStatusIcon(history.Status)"
                      :color="getStatusColor(history.Status)"
                      size="24px"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>
                      <strong>{{ history.Remark }}</strong> -
                      {{ history.Approver }}
                      <span class="q-ml-xs text-grey-7 text-caption">
                        ({{ getStatusLabel(history.Status) }})
                      </span>
                    </q-item-label>
                    <q-item-label
                      v-if="
                        history.Status.trim() !== 'waiting' &&
                        history.Status.trim() !== '-' &&
                        history.Status.trim() !== 'up-reject' &&
                        history.Status.trim() !== 'cancel'
                      "
                      caption
                      class="text-grey-7"
                    >
                      {{ history.Approval_time }} -
                      {{
                        history.Comment?.trim()
                          ? history.Comment.trim()
                          : "無意見"
                      }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-expansion-item>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn
              v-if="canWithdraw"
              label="撤回表單"
              color="red"
              flat
              @click="deleteDialog = true"
            />
            <q-btn
              label="關閉"
              color="grey"
              flat
              @click="detailsDialog = false"
            />
          </q-card-actions>
        </template>
      </q-card>
    </q-dialog>

    <!-- 確認刪除對話框 -->
    <q-dialog v-model="deleteDialog">
      <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
        <q-card-section class="text-center">
          <q-icon name="close" color="negative" size="xl" class="q-mb-md" />
          <div class="text-h6 text-negative">確定撤銷？</div>
          <div class="text-body2 text-grey-8 q-mt-sm">
            表單撤銷無法復原，是否繼續。
          </div>
        </q-card-section>
        <q-separator class="q-mt-md" />
        <q-card-actions class="q-mt-sm row justify-between">
          <q-btn
            label="取消"
            color="grey"
            flat
            class="full-width q-mr-xs"
            v-close-popup
          />
          <q-btn
            label="刪除"
            color="negative"
            unelevated
            class="full-width q-ml-xs"
            @click="deleteFormConfirmed"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from "vue";
import { useQuasar } from "quasar";
import { QSpinner } from "quasar";
import apiClient from "../api";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 添加 props 定義
const props = defineProps({
  Uid: String,
  Userid: String,
});

// 基本狀態
const queryKeyword = ref("");
const activeTab = ref("all");
const requests = ref([]);
const loading = ref(false);
const detailLoading = ref(false); // 新增明細加載狀態

// 新增的狀態
const advancedSearchDialog = ref(false);
const detailsDialog = ref(false);
const deleteDialog = ref(false);
const selectedRequest = ref(null);
const detailData = ref({});
const attachments = ref([]);
const approvalHistory = ref([]);
const currentPage = ref(1);
const itemsPerPage = ref(5);
const dateRange = ref({ from: "", to: "" });
const searchForm = reactive({
  title: "",
  submitter: null,
  formType: null,
});

// 新增狀態
const visibleFields = ref({});
const detailFieldMapping = ref({});

// 添加進階搜尋相關的狀態
const submitterOptions = ref([]);
const formTypeOptions = ref([]);

// 載入表單欄位設定
const loadFormFields = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/forms/get_form_fields`);
    detailFieldMapping.value = response.data.detailFieldMapping;
    visibleFields.value = response.data.visibleFields;
  } catch (error) {
    console.error("獲取欄位失敗:", error);
  }
};

// 計算屬性
const totalRequests = computed(
  () =>
    requests.value.filter((r) =>
      ["pending", "reject", "approve", "cancel"].includes(r.fstatus.trim())
    ).length
);

const cancelledRequests = computed(() =>
  requests.value.filter((r) => r.fstatus.trim() === "cancel")
);

const approvedRequests = computed(() =>
  requests.value.filter((r) => ["reject", "approve"].includes(r.fstatus.trim()))
);

const pendingRequests = computed(() =>
  requests.value.filter((r) => r.fstatus.trim() === "pending")
);

// 修改過濾邏輯
const filteredRequests = computed(() => {
  let result = [...requests.value];

  switch (activeTab.value) {
    case "cancel":
      result = result.filter((r) => r.fstatus.trim() === "cancel");
      break;
    case "approved":
      result = result.filter((r) =>
        ["reject", "approve"].includes(r.fstatus.trim())
      );
      break;
    case "pending":
      result = result.filter((r) => r.fstatus.trim() === "pending");
      break;
  }

  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredRequests.value.length / itemsPerPage.value);
});

const paginatedRequests = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  return filteredRequests.value.slice(start, start + itemsPerPage.value);
});

const formattedDate = computed(() => {
  if (!dateRange.value) return "請選擇日期";
  if (typeof dateRange.value === "string") return dateRange.value;
  if (!dateRange.value.from) return "請選擇日期";
  return dateRange.value.from === dateRange.value.to
    ? dateRange.value.from
    : `${dateRange.value.from} ~ ${dateRange.value.to}`;
});

const canWithdraw = computed(() => {
  return (
    selectedRequest.value?.Submitter_id === props.Userid &&
    selectedRequest.value?.Status.trim() !== "cancel" &&
    approvalHistory.value.every(
      (history) =>
        !history.Approval_time || history.Approval_time.trim() === "無日期"
    )
  );
});

// 添加 formattedDetails 計算屬性
const formattedDetails = computed(() => {
  if (!detailData.value || !selectedRequest.value) return {};

  const allowedFields =
    visibleFields.value[selectedRequest.value.Type?.trim()] || [];

  return Object.fromEntries(
    Object.entries(detailData.value)
      .filter(([key]) => allowedFields.includes(key)) // 過濾不需要的欄位
      .map(([key, value]) => [
        detailFieldMapping.value[key] ?? key, // 轉換名稱
        ["Stime", "Etime"].includes(key)
          ? formatDateTime(value)
          : value?.toString().trim() ?? "無資料", // 格式化時間
      ])
  );
});

// 方法
const handleQueryChange = () => {
  if (
    !queryKeyword.value ||
    queryKeyword.value.length === 0 ||
    queryKeyword.value.length === 12
  ) {
    fetchRequests();
  }
};

// 添加進階搜尋相關的方法
const openAdvancedSearch = async () => {
  await fetchSubmitters(); // 載入送單人員選項
  await fetchFormTypes(); // 載入表單類型選項
  advancedSearchDialog.value = true;
};

// 載入送單人員選項
const fetchSubmitters = async () => {
  try {
    const response = await apiClient.post(
      `${apiBaseUrl}/users/get_enable_users`
    );
    submitterOptions.value = response.data.map((user) => ({
      label: `${user.Name} (${user.ID})`,
      value: user.ID,
    }));
  } catch (error) {
    console.error("載入送單人員失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入送單人員失敗",
      icon: "error",
    });
  }
};

// 載入表單類型選項
const fetchFormTypes = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/forms/get_form_types`);
    formTypeOptions.value = response.data.map((form) => ({
      label: form.Name,
      value: form.Type,
    }));
  } catch (error) {
    console.error("載入表單類型失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入表單類型失敗",
      icon: "error",
    });
  }
};

// 送單人員過濾方法
const filterSubmitters = (val, update) => {
  if (val === "") {
    update(() => {
      submitterOptions.value = submitterOptions.value;
    });
    return;
  }

  const needle = val.toLowerCase();
  update(() => {
    submitterOptions.value = submitterOptions.value.filter(
      (user) =>
        user.label.toLowerCase().includes(needle) ||
        user.value.toLowerCase().includes(needle)
    );
  });
};

// 移除這裡的 fetchRequests 宣告
// const fetchRequests = async () => {
//   // 將在下一部分實現
// };

onMounted(async () => {
  await loadFormFields();
  await fetchRequests();
});

// 新增的方法
const getStatusLabel = (status) => {
  switch ((status || "").trim()) {
    case "pending":
      return "進行中";
    case "reject":
      return "已駁回";
    case "approve":
      return "已核准";
    case "cancel":
      return "已撤回";
    case "-":
      return "待上層處理";
    case "waiting":
      return "待處理";
    case "up-reject":
      return "上層已駁回";
    default:
      return "未知狀態";
  }
};

const getStatusColor = (status) => {
  switch ((status || "").trim()) {
    case "pending":
      return "blue";
    case "reject":
      return "red";
    case "approve":
      return "green";
    case "cancel":
      return "grey";
    case "waiting":
      return "orange";
    case "-":
      return "grey";
    case "up-reject":
      return "grey";
    default:
      return "black";
  }
};

const getStatusIcon = (status) => {
  switch (status.trim()) {
    case "approve":
      return "check_circle";
    case "reject":
      return "cancel";
    case "waiting":
      return "hourglass_empty";
    case "-":
      return "more_horiz";
    case "up-reject":
      return "remove_circle_outline";
    default:
      return "help_outline";
  }
};

const clearDateRange = () => {
  dateRange.value = { from: "", to: "" };
};

// 清除搜尋條件
const clearSearchFilters = () => {
  dateRange.value = { from: "", to: "" };
  searchForm.submitter = null;
  searchForm.formType = null;
  queryKeyword.value = "";
  fetchRequests();
  advancedSearchDialog.value = false;
};

const deleteFormConfirmed = async () => {
  try {
    await apiClient.post(`${apiBaseUrl}/forms/cancel_form`, {
      form_id: selectedRequest.value.Form_id,
      status: "cancel",
    });
    deleteDialog.value = false;
    detailsDialog.value = false;
    await fetchRequests();
  } catch (error) {
    console.error("撤回失敗", error);
    $q.notify({
      type: "negative",
      message: "撤回表單失敗",
      icon: "error",
    });
  }
};

const viewRequest = async (formId) => {
  try {
    detailLoading.value = true; // 設置明細加載狀態
    selectedRequest.value = null;
    detailData.value = {};
    approvalHistory.value = [];
    attachments.value = [];

    const token = localStorage.getItem("jwt_token");
    const res = await apiClient.post(
      `${apiBaseUrl}/forms/view_full_form`,
      { form_id: formId },
      { headers: { Authorization: `Bearer ${token}` } }
    );

    const data = res.data;
    selectedRequest.value = data.header || {};
    detailData.value = data.detail || {};
    approvalHistory.value = (data.history || []).map((h) => ({
      ...h,
      Approval_time: formatDateTime(h.Approval_time),
    }));
    attachments.value = data.attachments || [];

    detailsDialog.value = true;
  } catch (error) {
    console.error("取得表單資料失敗:", error);
    $q.notify({
      type: "negative",
      message: "讀取表單失敗，請稍後再試",
    });
  } finally {
    detailLoading.value = false; // 重置明細加載狀態
  }
};

const formatDateTime = (dateString) => {
  if (!dateString) return "無日期";
  return new Date(dateString).toLocaleString("zh-TW", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 更新 fetchRequests 方法
const fetchRequests = async () => {
  try {
    loading.value = true; // 設置列表加載狀態
    const params = {
      userid: props.Userid,
      uid: props.Userid,
      queryKeyword: queryKeyword.value || "",
      date_from: dateRange.value.from || null,
      date_to: dateRange.value.to || null,
      submitter_id: searchForm.submitter?.value || null,
      form_type: searchForm.formType?.value || null,
    };

    const response = await apiClient.get(
      `${apiBaseUrl}/formsearch/form_search`,
      {
        params,
      }
    );

    if (Array.isArray(response.data)) {
      requests.value = response.data.map((item) => ({
        ...item,
        submit_date: formatDateTime(item.submit_date),
      }));
      requests.value = cleanStringsDeep(requests.value);
      //console.log(requests.value);
    } else {
      requests.value = [];
      console.error("查詢結果不是陣列，已設定為空陣列");
      $q.notify({
        type: "negative",
        message: "查詢結果格式錯誤",
        icon: "error",
      });
    }
  } catch (error) {
    console.error("查詢失敗:", error);
    $q.notify({
      type: "negative",
      message: "查詢失敗",
      icon: "error",
      caption: error.response?.data?.error || error.message,
    });
    requests.value = [];
  } finally {
    loading.value = false; // 重置列表加載狀態
    advancedSearchDialog.value = false;
  }
};

function cleanStringsDeep(obj) {
  if (Array.isArray(obj)) {
    return obj.map(cleanStringsDeep);
  } else if (obj && typeof obj === "object") {
    const cleaned = {};
    for (const key in obj) {
      const val = obj[key];
      cleaned[key] =
        typeof val === "string" ? val.trim() : cleanStringsDeep(val); // 遞迴處理巢狀物件
    }
    return cleaned;
  } else {
    return obj;
  }
}

// 移除重複的 onMounted
onMounted(async () => {
  await loadFormFields();
  await fetchRequests();
});
</script>

<style scoped>
/* 基本樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.search-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  transition: all 0.3s ease;
}

.search-input:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.all-card::before {
  background-color: #4caf50;
}

.all-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.submitted-card::before {
  background-color: #1976d2;
}

.submitted-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.approved-card::before {
  background-color: #ff9800;
}

.approved-card .stats-icon {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.pending-card::before {
  background-color: #9c27b0;
}

.pending-card .stats-icon {
  color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.1);
}

.cancelled-card::before {
  background-color: #9e9e9e;
}

.cancelled-card .stats-icon {
  color: #9e9e9e;
  background-color: rgba(158, 158, 158, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 響應式調整 */
@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .stats-card {
    padding: 8px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 16px;
  }

  .stats-label {
    font-size: 10px;
  }
}

/* 超小螢幕響應式調整 */
@media (max-width: 340px) {
  .stats-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }

  .stats-value {
    font-size: 14px;
  }

  .stats-label {
    font-size: 9px;
  }
}
/* 空狀態 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  font-weight: 500;
}
/* 新增的樣式 */
.request-list-container {
  position: relative;
}

.request-list {
  margin: 0;
  padding: 0;
}

.request-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.request-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pending-request {
  border-left: 4px solid #1976d2;
}

.request-header {
  min-height: auto;
  padding: 0;
}

.request-badge {
  font-size: 10px;
  padding: 0 4px;
  height: 18px;
}

.request-title {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  padding: 0 2px;
}

.request-meta {
  font-size: 11px;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: loader-rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 150, 200;
  stroke-dashoffset: -10;
  stroke-linecap: round;
  animation: loader-dash 1.5s ease-in-out infinite;
}

.loader-message {
  margin-top: 12px;
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -125;
  }
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }
}
</style>
