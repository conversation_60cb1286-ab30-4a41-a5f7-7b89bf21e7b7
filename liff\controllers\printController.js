const escpos = require("escpos");
escpos.Network = require("escpos-network");
const iconv = require("iconv-lite");

const { DateTime } = require("luxon");

const sendToPrinter = async (ip, groupList) => {
  for (const group of groupList) {
    const device = new escpos.Network(ip.trim(), 9100);
    const printer = new escpos.Printer(device, {
      encoding: "binary", // ✅ 重要：讓你自己控制文字轉碼
    });

    const now = DateTime.now().toFormat("yyyy-MM-dd HH:mm:ss");

    device.open(() => {
      // 設定為 Big5 語系：請確認印表機是否支援此指令
      printer.raw(Buffer.from([0x1b, 0x74, 0x02])); // Big5 模式（部分機器有效）

      // ✅ 文字改為 iconv-lite 編碼 Big5
      const write = (text = "") =>
        printer.raw(iconv.encode(text + "\n", "big5"));

      printer.align("CT").style("NORMAL").size(2, 2);
      write(`【${group.kname}】`);
      write(""); // 空行
      printer.align("LT").style("A").size(1, 1);
      write(` 桌號: ${group.roomname}   人數: ${group.person}`);
      printer.font("B");
      printer.size(0, 0).style("NORMAL").align("LT");
      write(``);
      write(`  單號: ${group.scode}                      時間: ${now}`);
      write("----------------------------------------------------------------");
      printer.font("B");
      printer.style("NORMAL").size(1, 1).align("CT");
      write(`第  ${group.oround}  輪`);
      printer.size(0, 0).style("NORMAL").align("LT");
      write("---------------------------------------------------------");
      printer.style("NORMAL").size(1, 1).align("LT");
      for (const item of group.items) {
        const specText =
          item.spec && item.spec !== "標準" ? `(${item.spec})` : "";
        const name = `${item.name.trim()}${specText}`;
        const qty = `${item.qty}`;

        // ✅ 數量放前面，不用對齊
        write(` ${qty}  ${name}`);

        // ✅ 口味處理
        if (item.tastes && item.tastes.trim()) {
          printer.style("NORMAL").size(0, 1).align("LT");
          write(`         └${item.tastes}`);
          printer.style("NORMAL").size(1, 1).align("LT");
        }
        printer.size(0, 0).style("NORMAL").align("LT");
        // ✅ 空行間隔
        write("---------------------------------------------------------");
        printer.style("NORMAL").size(1, 1).align("LT");
      }

      write(""); // 空行
      write(""); // 空行
      write(""); // 空行
      write(""); // 空行
      write(""); // 空行
      printer.cut().close();
    });
  }
};

module.exports = { sendToPrinter };
