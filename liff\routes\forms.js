const express = require("express");
const router = express.Router();
const formsController = require("../controllers/formsController");
const authMiddleware = require("../middleware/authMiddleware");

// 更新簽核狀態
router.post(
  "/update_approval_status",
  authMiddleware,
  formsController.updateApprovalStatus
);

// 新增一個與前端匹配的路由 (使用連字符)
router.post(
  "/update-approval-status",
  authMiddleware,
  formsController.updateApprovalStatus
);

// 獲取簽核歷程
router.post(
  "/get_approval_history",
  authMiddleware,
  formsController.getApprovalHistory
);

// 獲取表單主檔
router.post("/get_main_data", authMiddleware, formsController.getSMainData);

// 獲取表單明細
router.post("/get_details", authMiddleware, formsController.getSDetails);

// 新增一個與前端匹配的路由
router.post("/get_SDetails", authMiddleware, formsController.getSDetails);

// 獲取表單類型
router.get("/get_form_types", authMiddleware, formsController.getFormTypes);

// 獲取表單欄位
router.get("/get_form_fields", authMiddleware, formsController.getFormFields);

// 撤銷表單
router.post("/cancel_form", authMiddleware, formsController.cancelform);

// 獲取簽核流程
router.get("/get_form_flows", authMiddleware, formsController.getFormFlows);

// 保存簽核流程
router.post("/save_form_flows", authMiddleware, formsController.saveFormFlows);

// 獲取附件
router.post("/get_attachments", authMiddleware, formsController.getAttachments);

// 查看完整表單
router.post("/view_full_form", authMiddleware, formsController.viewFullForm);

// 獲取表單可檢視人員
router.get("/get_form_viewers", authMiddleware, formsController.getFormViewers);

// 保存表單可檢視人員
router.post(
  "/save_form_viewers",
  authMiddleware,
  formsController.saveFormViewers
);

// 獲取通知記錄
router.get(
  "/get_notifications",
  authMiddleware,
  formsController.getNotifications
);

// 重新發送通知
router.post(
  "/resend_notification",
  authMiddleware,
  formsController.resendNotification
);

// 刪除歷史通知記錄
router.post(
  "/delete_notifications_history",
  authMiddleware,
  formsController.deleteNotificationsHistory
);

module.exports = router;
