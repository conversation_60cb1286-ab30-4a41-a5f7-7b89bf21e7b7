<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">通知記錄</div>
            <div class="row">
              <q-btn
                flat
                round
                size="sm"
                color="grey-7"
                class="search-button q-mr-xs"
                @click="openDeleteHistoryDialog"
              >
                <trash :size="18" :stroke-width="1.5" />
                <q-tooltip>刪除歷史記錄</q-tooltip>
              </q-btn>
              <q-btn
                flat
                round
                size="sm"
                color="red"
                class="search-button"
                @click="openAdvancedSearch"
              >
                <Search :size="18" :stroke-width="1.5" />
                <q-tooltip>進階搜尋</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>

        <!-- 搜尋輸入框 -->
        <q-card-section class="q-pt-none">
          <q-input
            v-model="queryKeyword"
            placeholder="輸入單號或通知人員搜尋"
            outlined
            dense
            clearable
            class="search-input"
            @update:model-value="handleQueryChange"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 統計卡片區域 -->
        <q-card-section class="q-py-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部通知統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': activeTab === 'all' }"
                  @click="activeTab = 'all'"
                >
                  <div class="stats-icon">
                    <Files :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ totalNotifications }}</div>
                    <div class="stats-label">全部通知</div>
                  </div>
                </div>
              </div>

              <!-- 已發送統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card sent-card"
                  :class="{ 'active-filter': activeTab === 'sent' }"
                  @click="activeTab = 'sent'"
                >
                  <div class="stats-icon">
                    <CheckCircle :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ sentNotifications.length }}
                    </div>
                    <div class="stats-label">已發送</div>
                  </div>
                </div>
              </div>

              <!-- 待發送統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card pending-card"
                  :class="{ 'active-filter': activeTab === 'pending' }"
                  @click="activeTab = 'pending'"
                >
                  <div class="stats-icon">
                    <Clock :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ pendingNotifications.length }}
                    </div>
                    <div class="stats-label">待發送</div>
                  </div>
                </div>
              </div>

              <!-- 發送失敗統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card failed-card"
                  :class="{ 'active-filter': activeTab === 'failed' }"
                  @click="activeTab = 'failed'"
                >
                  <div class="stats-icon">
                    <AlertCircle :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ failedNotifications.length }}
                    </div>
                    <div class="stats-label">發送失敗</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 通知列表卡片 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <q-card-section class="q-pa-xs">
        <!-- 加載中狀態 -->
        <div v-if="loading" class="loader-container">
          <q-spinner size="40px" color="grey-5" />
          <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
            載入通知中...
          </div>
        </div>

        <!-- 無資料狀態 -->
        <div
          v-else-if="filteredNotifications.length === 0"
          class="empty-state-container"
        >
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <message-square-warning
              :size="50"
              :stroke-width="1"
              style="width: 60px; height: 50px; margin-bottom: 12px"
            />
            <div class="empty-text text-grey-5">
              {{ searchForm.title ? "沒有找到符合的通知" : "目前沒有通知記錄" }}
            </div>
          </div>
        </div>

        <!-- 通知列表 -->
        <div v-else class="notification-list-container">
          <!-- 添加分頁控制器到頂部 -->
          <div class="row justify-end q-mb-sm">
            <q-pagination
              v-model="currentPage"
              :max="totalPages"
              :max-pages="5"
              boundary-numbers
              direction-links
              color="primary"
              active-color="blue"
              active-text-color="white"
              class="pagination-controls"
              size="sm"
            />
          </div>

          <q-list separator class="notification-list">
            <q-item
              v-for="notification in paginatedNotifications"
              :key="`${notification.Form_id}-${notification.Step_number}-${notification.Approver_id}`"
              class="bg-white q-mb-xs notification-item"
              :class="{
                'pending-notification': notification.Status === 'pending',
              }"
            >
              <q-item-section>
                <!-- 表單ID和類型 -->
                <div
                  class="text-indigo-10 text-weight-medium notification-title q-mb-xs"
                >
                  {{ notification.Form_id }} -
                  <span
                    v-if="
                      notification.Step_number &&
                      notification.Step_number.trim() === '-1'
                    "
                  >
                    結果通知
                  </span>
                  <span v-else>
                    {{ getStepDisplay(notification.Step_number) }}
                  </span>
                </div>

                <!-- 通知人員和時間 -->
                <div class="text-caption text-grey-8 notification-meta">
                  <div class="flex items-center">
                    <user :stroke-width="1" :size="16" class="q-mr-xs" />
                    通知人員: {{ notification.Approver_id }}
                  </div>
                  <div class="flex items-center q-mt-xs">
                    <calendar-clock
                      :stroke-width="1"
                      :size="16"
                      class="q-mr-xs"
                    />
                    建立: {{ formatDateTime(notification.Created) }}
                  </div>
                  <div
                    v-if="notification.Sent"
                    class="flex items-center q-mt-xs"
                  >
                    <send :stroke-width="1" :size="16" class="q-mr-xs" />
                    發送: {{ formatDateTime(notification.Sent) }}
                  </div>
                </div>
              </q-item-section>

              <!-- 狀態顯示和操作按鈕 -->
              <q-item-section side>
                <div class="column items-end">
                  <div class="row items-center">
                    <q-btn
                      flat
                      round
                      dense
                      :color="getResendButtonColor(notification.Status)"
                      @click="resendNotification(notification)"
                      class="q-mr-xs"
                    >
                      <send :size="20" :stroke-width="1.5" class="q-mr-md" />
                      <q-tooltip>{{
                        getResendButtonLabel(notification.Status)
                      }}</q-tooltip>
                    </q-btn>
                    <q-badge
                      outline
                      :color="getStatusColor(notification.Status)"
                      :label="getStatusLabel(notification.Status)"
                      class="q-px-sm q-py-xs"
                    />
                  </div>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-card-section>
    </q-card>

    <!-- 進階搜尋對話框 -->
    <q-dialog v-model="advancedSearchDialog">
      <q-card style="width: 500px; max-width: 90vw">
        <q-card-section class="row items-center bg-primary text-white q-pb-xs">
          <div class="text-subtitle1">
            <q-icon name="search" class="q-mr-xs" size="sm" />
            進階搜尋
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-sm">
          <q-form>
            <!-- 日期區間 -->
            <q-input
              v-model="formattedDate"
              label="選擇日期區間"
              outlined
              readonly
              dense
              class="full-width"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="dateRange" range mask="YYYY-MM-DD">
                      <div
                        class="row items-center justify-end q-gutter-sm q-pa-sm"
                      >
                        <q-btn
                          label="清除"
                          color="grey"
                          flat
                          @click="clearDateRange"
                        />
                        <q-btn
                          label="確定"
                          color="primary"
                          flat
                          v-close-popup
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>

            <!-- 通知狀態 -->
            <q-select
              v-model="searchForm.status"
              :options="statusOptions"
              label="通知狀態"
              outlined
              dense
              class="q-mt-md"
            />

            <!-- 表單狀態 -->
            <q-select
              v-model="searchForm.formStatus"
              :options="formStatusOptions"
              label="表單狀態"
              outlined
              dense
              class="q-mt-md"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            label="清除條件"
            color="red"
            flat
            @click="clearSearchFilters"
          />
          <q-btn
            label="取消"
            color="grey"
            flat
            @click="advancedSearchDialog = false"
          />
          <q-btn label="查詢" color="primary" @click="fetchNotifications" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 重新發送確認對話框 -->
    <q-dialog v-model="resendDialog">
      <q-card style="width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ getResendDialogTitle() }}</div>
        </q-card-section>

        <q-card-section> {{ getResendDialogMessage() }} </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            flat
            :color="getResendButtonColor(selectedNotification?.Status)"
            @click="confirmResend"
          >
            <send :size="16" :stroke-width="1.5" class="q-mr-xs" />
            {{ getResendButtonLabel(selectedNotification?.Status) }}
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 刪除歷史記錄對話框 -->
    <q-dialog v-model="deleteHistoryDialog">
      <q-card style="width: 400px">
        <q-card-section class="row items-center bg-negative text-white">
          <div class="text-h6">
            <trash :size="20" :stroke-width="1.5" class="q-mr-xs" />
            刪除歷史通知記錄
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-md">
          <p>
            請選擇要刪除的日期範圍，系統將刪除此日期<strong>之前</strong>的所有通知記錄。
          </p>
          <q-input
            v-model="deleteBeforeDate"
            label="選擇日期"
            outlined
            dense
            readonly
          >
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <q-date v-model="deleteBeforeDate" mask="YYYY-MM-DD">
                    <div
                      class="row items-center justify-end q-gutter-sm q-pa-sm"
                    >
                      <q-btn label="確定" color="primary" flat v-close-popup />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </q-card-section>

        <q-card-section
          class="text-negative q-pt-none"
          v-if="deleteHistoryError"
        >
          {{ deleteHistoryError }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            flat
            label="刪除"
            color="negative"
            :loading="deleteHistoryLoading"
            :disable="!deleteBeforeDate"
            @click="confirmDeleteHistory"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 添加 props 定義
const props = defineProps({
  Uid: String,
  Userid: String,
});

// 基本狀態
const queryKeyword = ref("");
const activeTab = ref("all");
const notifications = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const dateRange = ref({ from: "", to: "" });
const advancedSearchDialog = ref(false);
const resendDialog = ref(false);
const selectedNotification = ref(null);

// 刪除歷史記錄相關狀態
const deleteHistoryDialog = ref(false);
const deleteBeforeDate = ref("");
const deleteHistoryLoading = ref(false);
const deleteHistoryError = ref("");

const searchForm = reactive({
  title: "",
  status: null,
  formStatus: null,
});

// 通知狀態選項
const statusOptions = [
  { label: "全部", value: null },
  { label: "待發送", value: "pending" },
  { label: "已發送", value: "sent" },
  { label: "發送失敗", value: "failed" },
];

// 表單狀態選項
const formStatusOptions = [
  { label: "全部", value: null },
  { label: "待處理", value: "waiting" },
  { label: "已核准", value: "approved" },
  { label: "已駁回", value: "rejected" },
  { label: "已取消", value: "canceled" },
];

// 計算屬性
const totalNotifications = computed(() => notifications.value.length);

const sentNotifications = computed(() =>
  notifications.value.filter(
    (n) => n.Status && n.Status.toString().trim().toLowerCase() === "sent"
  )
);

const pendingNotifications = computed(() =>
  notifications.value.filter(
    (n) => n.Status && n.Status.toString().trim().toLowerCase() === "pending"
  )
);

const failedNotifications = computed(() =>
  notifications.value.filter(
    (n) => n.Status && n.Status.toString().trim().toLowerCase() === "failed"
  )
);

// 過濾通知
const filteredNotifications = computed(() => {
  let result = [...notifications.value];

  // 根據標籤過濾
  switch (activeTab.value) {
    case "sent":
      result = result.filter(
        (n) => n.Status && n.Status.toString().trim().toLowerCase() === "sent"
      );
      break;
    case "pending":
      result = result.filter(
        (n) =>
          n.Status && n.Status.toString().trim().toLowerCase() === "pending"
      );
      break;
    case "failed":
      result = result.filter(
        (n) => n.Status && n.Status.toString().trim().toLowerCase() === "failed"
      );
      break;
  }

  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredNotifications.value.length / itemsPerPage.value);
});

const paginatedNotifications = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  return filteredNotifications.value.slice(start, start + itemsPerPage.value);
});

const formattedDate = computed(() => {
  if (!dateRange.value) return "請選擇日期";
  if (typeof dateRange.value === "string") return dateRange.value;
  if (!dateRange.value.from) return "請選擇日期";
  return dateRange.value.from === dateRange.value.to
    ? dateRange.value.from
    : `${dateRange.value.from} ~ ${dateRange.value.to}`;
});

// 方法
const handleQueryChange = () => {
  if (!queryKeyword.value || queryKeyword.value.length === 0) {
    fetchNotifications();
  }
};

// 進階搜尋相關方法
const openAdvancedSearch = () => {
  advancedSearchDialog.value = true;
};

// 清除日期範圍
const clearDateRange = () => {
  dateRange.value = { from: "", to: "" };
};

// 清除搜尋條件
const clearSearchFilters = () => {
  dateRange.value = { from: "", to: "" };
  searchForm.status = null;
  searchForm.formStatus = null;
  queryKeyword.value = "";
  fetchNotifications();
  advancedSearchDialog.value = false;
};

// 獲取通知狀態標籤
const getStatusLabel = (status) => {
  if (!status) return "未知狀態";

  // 去除空格並轉為小寫
  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "pending":
      return "待發送";
    case "sent":
      return "已發送";
    case "failed":
      return "發送失敗";
    default:
      console.log("未識別的狀態值:", status);
      return "未知狀態";
  }
};

// 獲取通知狀態顏色
const getStatusColor = (status) => {
  if (!status) return "grey";

  // 去除空格並轉為小寫
  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "pending":
      return "orange";
    case "sent":
      return "green";
    case "failed":
      return "red";
    default:
      return "grey";
  }
};

// 格式化日期時間
const formatDateTime = (dateString) => {
  if (!dateString) return "無日期";
  return new Date(dateString).toLocaleString("zh-TW", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 獲取步驟顯示文字
const getStepDisplay = (stepNumber) => {
  if (!stepNumber) return "未知步驟";

  // 去除可能的空格
  const trimmedStep = stepNumber.toString().trim();

  // 檢查是否為 -1（表單結果通知）
  if (trimmedStep === "-1") {
    return "表單結果通知";
  }

  // 嘗試轉換為數字
  try {
    const stepNum = parseInt(trimmedStep);
    return `第${stepNum + 1}步簽核`;
  } catch (error) {
    console.error("步驟號格式錯誤:", stepNumber);
    return `步驟: ${trimmedStep}`;
  }
};

// 獲取重新發送按鈕顏色
const getResendButtonColor = (status) => {
  if (!status) return "primary";

  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "negative";
    case "pending":
      return "warning";
    case "sent":
      return "primary";
    default:
      return "primary";
  }
};

// 獲取重新發送按鈕文字
const getResendButtonLabel = (status) => {
  if (!status) return "重新發送";

  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "重新發送";
    case "pending":
      return "立即發送";
    case "sent":
      return "再次通知";
    default:
      return "重新發送";
  }
};

// 獲取對話框標題
const getResendDialogTitle = () => {
  if (!selectedNotification.value || !selectedNotification.value.Status) {
    return "發送通知";
  }

  const normalizedStatus = selectedNotification.value.Status.toString()
    .trim()
    .toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "重新發送失敗通知";
    case "pending":
      return "立即發送待處理通知";
    case "sent":
      return "再次發送通知";
    default:
      return "發送通知";
  }
};

// 獲取對話框信息
const getResendDialogMessage = () => {
  if (!selectedNotification.value || !selectedNotification.value.Status) {
    return "確定要發送此通知嗎？";
  }

  const normalizedStatus = selectedNotification.value.Status.toString()
    .trim()
    .toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "此通知先前發送失敗，確定要重新嘗試發送嗎？";
    case "pending":
      return "此通知尚未發送，確定要立即發送嗎？";
    case "sent":
      return "此通知已發送過，確定要再次發送給簽核人嗎？";
    default:
      return "確定要發送此通知嗎？";
  }
};

// 重新發送通知
const resendNotification = (notification) => {
  selectedNotification.value = notification;
  resendDialog.value = true;
};

// 確認重新發送
const confirmResend = async () => {
  if (!selectedNotification.value) return;

  try {
    loading.value = true;
    const response = await apiClient.post(
      `${apiBaseUrl}/forms/resend_notification`,
      {
        form_id: selectedNotification.value.Form_id,
        step_number: selectedNotification.value.Step_number,
        approver_id: selectedNotification.value.Approver_id,
      }
    );

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "通知已重新發送",
        icon: "check_circle",
      });
      await fetchNotifications();
    } else {
      throw new Error(response.data.error || "發送失敗");
    }
  } catch (error) {
    console.error("重新發送通知失敗:", error);
    $q.notify({
      type: "negative",
      message: `重新發送通知失敗: ${error.message}`,
      icon: "error",
    });
  } finally {
    loading.value = false;
    resendDialog.value = false;
  }
};

// 獲取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true;
    const params = {
      keyword: queryKeyword.value || "",
      date_from: dateRange.value.from || null,
      date_to: dateRange.value.to || null,
      status: searchForm.status?.value || null,
      form_status: searchForm.formStatus?.value || null,
    };

    const response = await apiClient.get(
      `${apiBaseUrl}/forms/get_notifications`,
      {
        params,
      }
    );

    if (Array.isArray(response.data)) {
      notifications.value = response.data.map((item) => ({
        ...item,
        Created: item.Created,
        Sent: item.Sent,
      }));
    } else {
      notifications.value = [];
      console.error("查詢結果不是陣列，已設定為空陣列");
      $q.notify({
        type: "negative",
        message: "查詢結果格式錯誤",
        icon: "error",
      });
    }
  } catch (error) {
    console.error("查詢通知失敗:", error);
    $q.notify({
      type: "negative",
      message: "查詢通知失敗",
      icon: "error",
      caption: error.response?.data?.error || error.message,
    });
    notifications.value = [];
  } finally {
    loading.value = false;
    advancedSearchDialog.value = false;
  }
};

// 打開刪除歷史記錄對話框
const openDeleteHistoryDialog = () => {
  deleteHistoryDialog.value = true;
  deleteHistoryError.value = "";
  deleteBeforeDate.value = "";
};

// 確認刪除歷史記錄
const confirmDeleteHistory = async () => {
  if (!deleteBeforeDate.value) {
    deleteHistoryError.value = "請選擇日期";
    return;
  }

  try {
    deleteHistoryLoading.value = true;
    deleteHistoryError.value = "";

    const response = await apiClient.post(
      `${apiBaseUrl}/forms/delete_notifications_history`,
      {
        date_before: deleteBeforeDate.value,
      }
    );

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: `成功刪除 ${response.data.deletedCount || 0} 筆歷史通知記錄`,
        icon: "check_circle",
      });
      deleteHistoryDialog.value = false;
      await fetchNotifications();
    } else {
      throw new Error(response.data.error || "刪除失敗");
    }
  } catch (error) {
    console.error("刪除歷史記錄失敗:", error);
    deleteHistoryError.value = `刪除失敗: ${error.message}`;
    $q.notify({
      type: "negative",
      message: `刪除歷史記錄失敗: ${error.message}`,
      icon: "error",
    });
  } finally {
    deleteHistoryLoading.value = false;
  }
};

// 組件載入時獲取通知列表
onMounted(async () => {
  await fetchNotifications();
});
</script>

<style scoped>
/* 基本樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.search-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  transition: all 0.3s ease;
}

.search-input:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.all-card::before {
  background-color: #4caf50;
}

.all-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.sent-card::before {
  background-color: #1976d2;
}

.sent-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.pending-card::before {
  background-color: #ff9800;
}

.pending-card .stats-icon {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.failed-card::before {
  background-color: #f44336;
}

.failed-card .stats-icon {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 空狀態 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  font-weight: 500;
}

/* 通知列表樣式 */
.notification-list-container {
  position: relative;
}

.notification-list {
  margin: 0;
  padding: 0;
}

.notification-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pending-notification {
  border-left: 4px solid #ff9800;
}

.notification-title {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  padding: 0 2px;
}

.notification-meta {
  font-size: 11px;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }
}

/* 響應式調整 */
@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .stats-card {
    padding: 8px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 16px;
  }

  .stats-label {
    font-size: 10px;
  }
}

/* 超小螢幕響應式調整 */
@media (max-width: 340px) {
  .stats-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }

  .stats-value {
    font-size: 14px;
  }

  .stats-label {
    font-size: 9px;
  }
}
</style>
