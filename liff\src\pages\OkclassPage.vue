<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="width: 100%; max-width: 500px; border-radius: 12px"
    >
      <!-- 🔷 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        廚房部門設定
      </q-card-section>

      <!-- 🍳 廚房部門清單 -->
      <q-list separator>
        <div
          v-for="kitchen in kclassList"
          :key="kitchen.code"
          class="bg-white q-mb-sm"
          style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
        >
          <q-expansion-item
            expand-separator
            class="q-pa-none"
            header-class="text-indigo"
          >
            <!-- 🔹 廚房部門主列 -->
            <template #header>
              <q-item
                dense
                class="q-pa-none q-gutter-none items-center"
                style="width: 100%; position: relative"
              >
                <q-item-section>
                  <div class="text-indigo-10">{{ kitchen.name }}</div>
                  <div class="text-caption text-grey-8">
                    <span class="text-brown-10">{{ kitchen.code }}</span> |
                    主機：{{ kitchen.vcode1_name || "未設定" }} | 備用：{{
                      kitchen.vcode2_name || "未設定"
                    }}
                  </div>
                </q-item-section>

                <!-- 最右側展開箭頭 -->
                <q-item-section side />

                <!-- 編輯按鈕 -->
                <q-btn
                  flat
                  dense
                  size="sm"
                  icon="edit"
                  color="primary"
                  @click.stop="openEditDialog(kitchen)"
                  style="
                    position: absolute;
                    right: 14px;
                    top: 50%;
                    transform: translateY(-50%);
                    z-index: 1;
                  "
                />
              </q-item>
            </template>

            <!-- 🔽 詳細內容 -->
            <q-card-section class="q-pa-sm q-pt-none bg-grey-1 text-caption">
              <div class="row q-col-gutter-sm q-mb-xs q-pl-md">
                <!-- ✅ 出據主機 -->
                <div class="col-12">
                  <q-icon
                    name="print"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  出據主機：
                  <span class="text-weight-medium">
                    {{ kitchen.vcode1_name || "未設定" }}
                    <span class="text-grey-6">({{ kitchen.vcode1 }})</span>
                  </span>
                </div>

                <!-- ✅ 備用出據主機 -->
                <div class="col-12">
                  <q-icon
                    name="backup"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  備用出據主機：
                  <span class="text-weight-medium">
                    {{ kitchen.vcode2_name || "未設定" }}
                    <span class="text-grey-6">({{ kitchen.vcode2 }})</span>
                  </span>
                </div>

                <!-- ✅ 部門編號 -->
                <div class="col-6">
                  <q-icon name="tag" size="16px" class="q-mr-xs text-primary" />
                  部門編號：
                  <span class="text-weight-medium">{{ kitchen.code }}</span>
                </div>

                <!-- ✅ 部門名稱 -->
                <div class="col-6">
                  <q-icon
                    name="restaurant"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  部門名稱：
                  <span class="text-weight-medium">{{ kitchen.name }}</span>
                </div>
              </div>

              <!-- 🔻 刪除 -->
              <div class="row justify-end q-mt-sm q-pr-sm">
                <q-btn
                  flat
                  dense
                  icon="delete"
                  label="刪除"
                  color="negative"
                  @click.stop="deleteKitchen(kitchen)"
                  class="q-px-sm"
                />
              </div>
            </q-card-section>
          </q-expansion-item>
        </div>
      </q-list>

      <!-- ➕ 新增廚房部門按鈕 -->
      <q-btn
        color="green"
        icon="add"
        label="新增廚房部門"
        class="full-width q-mt-md"
        @click="editKitchen(null)"
      />
    </q-card>
  </q-page>

  <!-- 編輯對話框 -->
  <q-dialog v-model="editDialog.visible" persistent>
    <q-card
      class="dialog-card"
      :class="$q.screen.lt.md ? 'mobile-dialog' : 'desktop-dialog'"
    >
      <!-- 標題列 -->
      <q-card-section class="dialog-header">
        <div class="text-h6 text-weight-medium text-primary">
          <q-icon name="restaurant" class="q-mr-sm" />
          {{ editDialog.isEdit ? "編輯廚房部門" : "新增廚房部門" }}
        </div>
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          class="text-grey-6"
        />
      </q-card-section>

      <q-separator />

      <!-- 表單內容 -->
      <q-card-section class="dialog-body">
        <div class="form-grid">
          <!-- 基本資訊 -->
          <q-input
            v-model="editDialog.form.code"
            label="廚房編號"
            dense
            outlined
            :disable="editDialog.isEdit"
          >
            <template v-slot:prepend>
              <q-icon name="tag" color="primary" />
            </template>
          </q-input>

          <q-input
            v-model="editDialog.form.name"
            label="廚房名稱"
            dense
            outlined
          >
            <template v-slot:prepend>
              <q-icon name="restaurant" color="primary" />
            </template>
          </q-input>

          <!-- 出據主機設定 -->
          <q-select
            v-model="editDialog.form.vcode1"
            label="出據主機"
            :options="vclassOptions"
            dense
            outlined
            emit-value
            map-options
            :rules="[(val) => !!val || '出據主機為必填項目']"
            :error="!editDialog.form.vcode1"
            error-message="請選擇出據主機"
          >
            <template v-slot:prepend>
              <q-icon name="print" color="indigo" />
            </template>
          </q-select>

          <q-select
            v-model="editDialog.form.vcode2"
            label="備用出據主機"
            :options="vclassOptions"
            dense
            outlined
            emit-value
            map-options
            clearable
          >
            <template v-slot:prepend>
              <q-icon name="backup" color="indigo" />
            </template>
          </q-select>
        </div>
      </q-card-section>

      <!-- 操作按鈕 -->
      <q-card-actions class="dialog-actions">
        <q-btn
          color="primary"
          :label="editDialog.isEdit ? '更新' : '儲存'"
          @click="saveKitchen"
          unelevated
          class="action-btn"
          :loading="false"
        >
          <template v-slot:loading>
            <q-spinner-facebook />
          </template>
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 刪除確認對話框 -->
  <q-dialog v-model="deleteDialog" persistent>
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          廚房部門「{{ kitchenToDelete?.name }}」刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteKitchenConfirmed"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";

const $q = useQuasar();
const props = defineProps({ Branch: String });

const editDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    code: "",
    name: "",
    vcode1: "",
    vcode2: "",
  },
});

const openEditDialog = (kitchen = null) => {
  if (kitchen) {
    editDialog.value.form = { ...kitchen };
    editDialog.value.isEdit = true;
  } else {
    editDialog.value.form = {
      code: "",
      name: "",
      vcode1: "",
      vcode2: "",
    };
    editDialog.value.isEdit = false;
  }
  editDialog.value.visible = true;
};

const kclassList = ref([]);
const vclassOptions = ref([]);

const fetchKclass = async () => {
  try {
    const res = await apiClient.get(`/okclass/get_Kclass`, {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      kclassList.value = res.data.data;
    }
    $q.loading.hide();
  } catch (err) {
    console.error("❌ 載入廚房部門失敗", err);
    $q.notify({ type: "negative", message: "廚房部門載入失敗" });
    $q.loading.hide();
  }
};

const fetchVclassOptions = async () => {
  try {
    const res = await apiClient.get(`/okclass/get_AvailableVclass`, {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      vclassOptions.value = res.data.data.map((item) => ({
        label: `${item.name} (${item.code})`,
        value: item.code,
      }));
    }
  } catch (err) {
    console.error("❌ 載入出據主機選項失敗", err);
  }
};

const editKitchen = (kitchen) => {
  openEditDialog(kitchen);
};

const saveKitchen = async () => {
  // 檢查必填欄位
  if (!editDialog.value.form.vcode1) {
    $q.notify({
      type: "negative",
      message: "請選擇出據主機",
      icon: "error",
      position: "top",
    });
    return;
  }

  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "儲存中...",
    });

    const formData = {
      ...editDialog.value.form,
      branch: props.Branch,
    };

    let res;
    if (editDialog.value.isEdit) {
      // 編輯現有廚房部門
      res = await apiClient.put(`/okclass/update_Kclass`, formData, {
        params: { branch: props.Branch },
      });
    } else {
      // 新增廚房部門
      res = await apiClient.post(`/okclass/create_Kclass`, formData, {
        params: { branch: props.Branch },
      });
    }

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: editDialog.value.isEdit
          ? "廚房部門更新成功"
          : "廚房部門新增成功",
      });
      editDialog.value.visible = false;
      await fetchKclass(); // 重新載入資料
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "操作失敗",
      });
    }
  } catch (err) {
    console.error("❌ 儲存廚房部門失敗", err);
    $q.notify({
      type: "negative",
      message: "儲存失敗：" + (err.response?.data?.message || err.message),
    });
  } finally {
    $q.loading.hide();
  }
};

const deleteDialog = ref(false);
const kitchenToDelete = ref(null);

const deleteKitchen = async (kitchen) => {
  kitchenToDelete.value = kitchen;
  deleteDialog.value = true;
};

const deleteKitchenConfirmed = async () => {
  if (!kitchenToDelete.value) return;

  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "刪除中...",
    });

    const res = await apiClient.delete(
      `/okclass/delete_Kclass/${kitchenToDelete.value.code}`,
      {
        params: {
          branch: props.Branch,
        },
      }
    );

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: "廚房部門刪除成功",
        icon: "check_circle",
        position: "top",
      });
      await fetchKclass(); // 重新載入資料
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "刪除失敗",
        icon: "error",
        position: "top",
      });
    }
  } catch (err) {
    console.error("❌ 刪除廚房部門失敗", err);
    $q.notify({
      type: "negative",
      message: "刪除失敗：" + (err.response?.data?.message || err.message),
      icon: "error",
      position: "top",
    });
  } finally {
    $q.loading.hide();
    deleteDialog.value = false;
    kitchenToDelete.value = null;
  }
};

onMounted(() => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  fetchKclass();
  fetchVclassOptions();
});
</script>

<style scoped>
/* 對話框樣式 */
.dialog-card {
  border-radius: 12px;
  overflow: hidden;
}

.desktop-dialog {
  width: 100%;
  max-width: 600px;
  margin: 20px auto;
}

.mobile-dialog {
  width: 95%;
  max-width: 95%;
  margin: 10px auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.dialog-body {
  padding: 20px;
}

.dialog-actions {
  padding: 16px 20px;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

/* 表單網格佈局 */
.form-grid {
  display: grid;
  gap: 16px;
}

/* 桌面版：2列佈局 */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}
</style>
