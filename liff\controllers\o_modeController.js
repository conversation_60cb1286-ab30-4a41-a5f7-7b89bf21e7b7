const { getPoolByBranch, sql } = require("../services/dbPoolManager");

// 取得消費方式清單（按分類分組）
const getConsumptionModes = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ success: false, message: "缺少分店參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().input("branch", sql.VarChar, branch)
      .query(`
        SELECT 
          mm.no,
          mm.code,
          mm.name
        FROM menumod mm
        ORDER BY mm.no
      `);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ 取得消費方式失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 儲存消費方式
const saveConsumptionMode = async (req, res) => {
  const { branch, mode } = req.body;

  if (!branch || !mode) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  if (mode.no) {
    // 不允許修改
    return res
      .status(400)
      .json({ success: false, message: "不允許修改消費方式" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    // 查詢該 code 的所有規格
    const specRes = await pool
      .request()
      .input("code", sql.VarChar, mode.code)
      .query("SELECT name FROM omenus WHERE code = @code ORDER BY sno");
    const specsArr = specRes.recordset
      .map((row) => (row.name ? row.name.trim() : ""))
      .filter(Boolean);
    const specsJson = JSON.stringify(specsArr);
    // 新增消費方式
    const maxNo = await pool
      .request()
      .query("SELECT ISNULL(MAX(no), 0) + 1 as nextNo FROM menumod");
    const nextNo = maxNo.recordset[0].nextNo;
    await pool
      .request()
      .input("no", sql.Int, nextNo)
      .input("code", sql.VarChar, mode.code)
      .input("name", sql.NVarChar, mode.name)
      .input("specs", sql.NVarChar, specsJson).query(`
        INSERT INTO menumod (no, code, name, specs)
        VALUES (@no, @code, @name, @specs)
      `);
    res.json({ success: true, message: "儲存成功" });
  } catch (err) {
    console.error("❌ 儲存消費方式失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 刪除消費方式
const deleteConsumptionMode = async (req, res) => {
  const { branch, code } = req.body;

  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 先刪除相關的隱藏商品設定
    await pool
      .request()
      .input("menumodcode", sql.VarChar, code)
      .query("DELETE FROM menumodoin WHERE menumodcode = @menumodcode");

    // 刪除消費方式
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM menumod WHERE code = @code");

    res.json({ success: true, message: "刪除成功" });
  } catch (err) {
    console.error("❌ 刪除消費方式失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 儲存消費方式排序
const saveModeSort = async (req, res) => {
  const { branch, modes } = req.body;

  if (!branch || !modes) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    for (const mode of modes) {
      await pool
        .request()
        .input("code", sql.VarChar, mode.code)
        .input("no", sql.Int, mode.no).query(`
          UPDATE menumod 
          SET no = @no
          WHERE code = @code
        `);
    }

    res.json({ success: true, message: "排序儲存成功" });
  } catch (err) {
    console.error("❌ 儲存消費方式排序失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得可用商品清單（按分類分組）
const getAvailableItems = async (req, res) => {
  const { branch } = req.query;

  try {
    const pool = await getPoolByBranch(branch); // 仍用預設分店連線池
    // 查詢所有商品及其規格（每一種規格一筆資料）
    const result = await pool.request().query(`
        SELECT 
          o.code,
          o.name,
          o.cclass,
          c.name as cclass_name,
          c.sno as category_sort,
          os.name as spec
        FROM omenum o
        LEFT JOIN cclass c ON o.cclass = c.code
        LEFT JOIN omenus os ON o.code = os.code
        WHERE c.showorder = 'T' 
          AND o.omenu = '1'
        ORDER BY c.sno, o.name, os.sno
      `);

    // 依分類分組
    const groupedData = {};
    result.recordset.forEach((row) => {
      const categoryCode = row.cclass || "DEFAULT";
      const categoryName = row.cclass_name || "未分類";
      const categorySort = row.category_sort || 999;
      if (!groupedData[categoryCode]) {
        groupedData[categoryCode] = {
          categoryCode,
          categoryName,
          sort: categorySort,
          items: [],
        };
      }
      groupedData[categoryCode].items.push({
        code: row.code,
        name: row.name,
        spec: row.spec ? row.spec.trim() : "",
      });
    });
    const availableItems = Object.values(groupedData).sort(
      (a, b) => a.sort - b.sort
    );
    res.json({ success: true, data: availableItems });
  } catch (err) {
    console.error("❌ 取得可用商品失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得已隱藏的商品
const getHiddenItems = async (req, res) => {
  const { branch, menumodcode } = req.query;

  if (!branch || !menumodcode) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const result = await pool
      .request()
      .input("menumodcode", sql.VarChar, menumodcode).query(`
        SELECT 
          mmi.menucode,
          o.name,
          o.cclass,
          c.name as cclass_name,
          mmi.spec
        FROM menumodoin mmi
        JOIN omenum o ON mmi.menucode = o.code
        LEFT JOIN cclass c ON o.cclass = c.code
        WHERE mmi.menumodcode = @menumodcode
        ORDER BY c.sno, o.name
      `);

    const hiddenItems = result.recordset.map((row) => {
      return {
        code: row.menucode,
        name: row.name,
        spec: row.spec ? row.spec.trim() : "",
      };
    });

    res.json({ success: true, data: hiddenItems });
  } catch (err) {
    console.error("❌ 取得隱藏商品失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 儲存隱藏商品設定
const saveHiddenItems = async (req, res) => {
  const { branch, menumodcode, items } = req.body;

  if (!branch || !menumodcode || !Array.isArray(items)) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 先刪除現有的隱藏商品設定
    await pool
      .request()
      .input("menumodcode", sql.VarChar, menumodcode)
      .query("DELETE FROM menumodoin WHERE menumodcode = @menumodcode");

    // 直接依序寫入 items
    let insertNo = 1;
    for (let i = 0; i < items.length; i++) {
      const { code: menucode, spec } = items[i];
      await pool
        .request()
        .input("no", sql.Int, insertNo++)
        .input("menucode", sql.VarChar, menucode)
        .input("menumodcode", sql.VarChar, menumodcode)
        .input("spec", sql.NVarChar, spec)
        .query(
          `INSERT INTO menumodoin (no, menucode, menumodcode, spec) VALUES (@no, @menucode, @menumodcode, @spec)`
        );
    }

    res.json({ success: true, message: "隱藏商品設定儲存成功" });
  } catch (err) {
    console.error("❌ 儲存隱藏商品設定失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得可新增的消費方式清單
const getAvailableModes = async (req, res) => {
  const { branch } = req.query;
  try {
    const pool = branch
      ? await getPoolByBranch(branch)
      : await getPoolByBranch();
    const result = await pool.request().query(`
      SELECT o.code, o.name
      FROM omenum o
      LEFT JOIN omenus os ON os.code = o.code
      WHERE os.name = '模式'
        AND o.code NOT IN (SELECT code FROM menumod)
      ORDER BY o.name
    `);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ 取得可新增消費方式清單失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getConsumptionModes,
  saveConsumptionMode,
  deleteConsumptionMode,
  saveModeSort,
  getAvailableItems,
  getHiddenItems,
  saveHiddenItems,
  getAvailableModes,
};
