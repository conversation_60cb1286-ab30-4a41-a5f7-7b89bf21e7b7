const express = require("express");
const gcountsController = require("../controllers/gcountsController");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 類別管理
router.get("/get_categories", authMiddleware, gcountsController.getCategories);
router.post("/save_category", authMiddleware, gcountsController.saveCategory);
router.post(
  "/delete_category",
  authMiddleware,
  gcountsController.deleteCategory
);

// 群組管理
router.get("/get_groups", authMiddleware, gcountsController.getGroups);
router.post("/save_group", authMiddleware, gcountsController.saveGroup);
router.post("/delete_group", authMiddleware, gcountsController.deleteGroup);
router.post(
  "/save_category_sort",
  authMiddleware,
  gcountsController.saveCategorySort
);
router.post(
  "/save_group_sort",
  authMiddleware,
  gcountsController.saveGroupSort
);

// 商品項目管理
router.get("/get_items", authMiddleware, gcountsController.getGroupItems);
router.get(
  "/get_branch_items",
  authMiddleware,
  gcountsController.getBranchItems
);
router.post("/add_items", authMiddleware, gcountsController.addItems);
router.post("/delete_item", authMiddleware, gcountsController.deleteItem);

// 統計功能
router.post("/get_stats", authMiddleware, gcountsController.getProductStats);

// 門市管理
router.get("/get_branches", authMiddleware, gcountsController.getBranches);

module.exports = router;
