//單據查詢主功能
const sql = require("mssql");
const dbConfig = require("../config/db");

const getBranch = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);
    // console.log("✅ 已連接到資料庫，準備獲取門市資料");

    const result = await pool.request().query(`
        SELECT Cod_cust, Cod_name, Cod_group, Invest, District, Sts, Ip, Rtime, AttendanceIP, Comment_Url, Comment_Rating, Comment_Count, Comment_At
        FROM Branch Where Cod_group<>'A'
      `);

    // console.log(`✅ 獲取門市資料成功，共 ${result.recordset.length} 筆記錄`);
    // 檢查第一筆資料是否包含評論欄位
    if (result.recordset.length > 0) {
      const firstRecord = result.recordset[0];
      // console.log("✅ 資料範例:", {
      //   Cod_cust: firstRecord.Cod_cust,
      //   Cod_name: firstRecord.Cod_name,
      //   Comment_Rating: firstRecord.Comment_Rating,
      //   Comment_Count: firstRecord.Comment_Count,
      //   Comment_At: firstRecord.Comment_At,
      // });
    }

    res.json(result.recordset);
  } catch (error) {
    console.error("獲取門市資料失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法獲取門市資料" });
  }
};

const saveBranch = async (req, res) => {
  const branch = req.body;

  try {
    const pool = await sql.connect(dbConfig);

    // 先獲取現有的評論統計資料，確保不會被覆蓋
    const existingData = await pool
      .request()
      .input("cod_cust", sql.VarChar, branch.Cod_cust).query(`
        SELECT Comment_Rating, Comment_Count, Comment_At
        FROM Branch
        WHERE Cod_cust = @cod_cust
      `);

    // 保留現有的評論統計資料
    let commentRating = null;
    let commentCount = null;
    let commentAt = null;

    if (existingData.recordset.length > 0) {
      commentRating = existingData.recordset[0].Comment_Rating;
      commentCount = existingData.recordset[0].Comment_Count;
      commentAt = existingData.recordset[0].Comment_At;
    }

    // ✅ 主表：更新或新增本地 Branch 資料
    await pool
      .request()
      .input("cod_cust", sql.VarChar, branch.Cod_cust)
      .input("cod_name", sql.NVarChar, branch.Cod_name)
      .input("cod_group", sql.VarChar, branch.Cod_group)
      .input("invest", sql.VarChar, branch.Invest)
      .input("district", sql.VarChar, branch.District)
      .input("AttendanceIP", sql.VarChar, branch.AttendanceIP)
      .input("Comment_Url", sql.VarChar, branch.Comment_Url)
      .input("Comment_Rating", sql.Decimal(3, 1), commentRating)
      .input("Comment_Count", sql.Int, commentCount)
      .input("Comment_At", sql.DateTime, commentAt)
      .input("sts", sql.VarChar, branch.Sts).query(`
        IF EXISTS (SELECT 1 FROM Branch WHERE Cod_cust = @cod_cust)
          UPDATE Branch
          SET Cod_name = @cod_name,
              Cod_group = @cod_group,
              Invest = @invest,
              District = @district,
              Sts = @sts,
              AttendanceIP = @AttendanceIP,
              Comment_Url = @Comment_Url
          WHERE Cod_cust = @cod_cust
        ELSE
          INSERT INTO Branch (Cod_cust, Cod_name, Cod_group, Invest, District, Sts, Comment_Rating, Comment_Count, Comment_At)
          VALUES (@cod_cust, @cod_name, @cod_group, @invest, @district, @sts, @Comment_Rating, @Comment_Count, @Comment_At)
      `);

    // ✅ 根據 Sts 執行副表 Restq.dbo.Branch 操作
    if (branch.Sts === "1") {
      await pool
        .request()
        .input("cod_cust", sql.VarChar, branch.Cod_cust)
        .input("cod_name", sql.NVarChar, branch.Cod_name)
        .input("b1", sql.VarChar, branch.Invest === "0" ? "MO" : null).query(`
          IF NOT EXISTS (
            SELECT 1 FROM [Restq].[dbo].[Branch] WHERE CUST_CODE = @cod_cust
          )
          BEGIN
            INSERT INTO [Restq].[dbo].[Branch]
              (CUST_CODE, CUST_NAME, CUST_SIM, MINVO, MCUST, MSAVE, DBTYPE, B1)
            VALUES
              (@cod_cust, @cod_name, @cod_name, 1, 1, 1, 1, @b1)
          END
        `);
    } else if (branch.Sts === "0") {
      await pool.request().input("cod_cust", sql.VarChar, branch.Cod_cust)
        .query(`
          DELETE FROM [Restq].[dbo].[Branch] WHERE CUST_CODE = @cod_cust
        `);
    }

    res.json({ status: "success" });
  } catch (err) {
    console.error("儲存錯誤：", err);
    res.status(500).json({ status: "error", message: err.message });
  }
};

module.exports = { getBranch, saveBranch };
