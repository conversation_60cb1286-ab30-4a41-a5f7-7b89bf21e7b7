const sql = require("mssql");
const dbConfig = require("../config/db");
const mysqlDb = require("../config/mysqldb");

exports.getPayslip = async (req, res) => {
  const { user_id, year_month } = req.query;
  //console.log(user_id, year_month);
  if (!user_id || !year_month) {
    return res.status(400).json({ error: "缺少 user_id 或 year_month" });
  }
  // console.log(user_id, year_month);
  try {
    // 查詢員工基本資料 (使用 MySQL)
    const [userRows] = await mysqlDb.query(
      "SELECT id, sn, name FROM users WHERE sn = ?",
      [user_id]
    );
    if (userRows.length === 0) {
      return res.status(404).json({ error: "找不到該員工" });
    }
    const user = userRows[0];

    // 查詢薪資明細 (使用 MySQL)
    const [details] = await mysqlDb.query(
      `SELECT d.salary_title_code AS code, t.name, d.v01, t.plus, t.rank
       FROM user_salary_record_details d
       JOIN salary_titles t ON d.salary_title_code = t.code
       JOIN users u on d.user_id = u.id
       WHERE u.sn = ? AND d.year_month = ?
       ORDER BY t.rank`,
      [user_id, year_month]
    );

    res.json({
      user,
      year_month,
      details,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

exports.getLastSettlementMonth = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    // 查詢最後結算月份
    const result = await pool
      .request()
      .input("class", sql.VarChar, "04")
      .input("code", sql.VarChar, "Payslip_ym")
      .query(
        "SELECT detail FROM setting WHERE class = @class AND code = @code"
      );

    if (result.recordset.length === 0) {
      return res.status(404).json({ error: "找不到最後結算月份設定" });
    }

    const lastSettlementMonth = result.recordset[0].detail;

    res.json({
      lastSettlementMonth,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};
