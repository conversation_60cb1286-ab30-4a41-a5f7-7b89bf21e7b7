import{d as W,s as D,i as T,t as F,c as d,g as G,w as A,m as R,o as q,n as V,h as Z,p as J,q as Q,u as X,b as ee,v as te,j as ne,x as se,y as ie,z as ae,A as oe,B as le,C as ce,D as re,E as ue}from"./index.036468ba.js";const fe=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function de(e){function a(t){return(...o)=>{if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[t].apply(e.value,o)}}function n(){const t=Object.create(null);return fe.forEach(o=>{t[o]=a(o)}),t}return n()}function pe(e,a,n){A([n,e,a],([t,o,l],M,C)=>{let c=null;if(t&&o&&l){const{offsetWidth:x,offsetHeight:z}=t,I=l===!0?{}:l,{throttle:m=100,onResize:f}=I;let p=!1;const h=()=>{o.resize(),f==null||f()},P=m?X(h,m):h;c=new ResizeObserver(()=>{!p&&(p=!0,t.offsetWidth===x&&t.offsetHeight===z)||P()}),c.observe(t)}C(()=>{c&&(c.disconnect(),c=null)})})}const he={autoresize:[Boolean,Object]},ve=/^on[^a-z]/,H=e=>ve.test(e);function ge(e){const a={};for(const n in e)H(n)||(a[n]=e[n]);return a}function L(e,a){const n=ee(e)?te(e):e;return n&&typeof n=="object"&&"value"in n?n.value||a:n||a}const Oe="ecLoadingOptions";function me(e,a,n){const t=T(Oe,{}),o=d(()=>({...L(t,{}),...n==null?void 0:n.value}));R(()=>{const l=e.value;!l||(a.value?l.showLoading(o.value):l.hideLoading())})}const Ee={loading:Boolean,loadingOptions:Object};let O=null;const S="x-vue-echarts";function _e(){if(O!=null)return O;if(typeof HTMLElement=="undefined"||typeof customElements=="undefined")return O=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(S)}catch{return O=!1}return O=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const be=_e(),we="ecTheme",Te="ecInitOptions",Ae="ecUpdateOptions",N=/(^&?~?!?)native:/;var Le=W({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...he,...Ee},emits:{},inheritAttrs:!1,setup(e,{attrs:a}){const n=D(),t=D(),o=D(),l=T(we,null),M=T(Te,null),C=T(Ae,null),{autoresize:c,manualUpdate:x,loading:z,loadingOptions:I}=F(e),m=d(()=>o.value||e.option||null),f=d(()=>e.theme||L(l,{})),p=d(()=>e.initOptions||L(M,{})),h=d(()=>e.updateOptions||L(C,{})),P=d(()=>ge(a)),j={},E=G().proxy.$listeners,_={};E?Object.keys(E).forEach(i=>{N.test(i)?j[i.replace(N,"$1")]=E[i]:_[i]=E[i]}):Object.keys(a).filter(i=>H(i)).forEach(i=>{let s=i.charAt(2).toLowerCase()+i.slice(3);if(s.indexOf("native:")===0){const v=`on${s.charAt(7).toUpperCase()}${s.slice(8)}`;j[v]=a[i];return}s.substring(s.length-4)==="Once"&&(s=`~${s.substring(0,s.length-4)}`),_[s]=a[i]});function b(i){if(!n.value)return;const s=t.value=J(n.value,f.value,p.value);e.group&&(s.group=e.group),Object.keys(_).forEach(g=>{let r=_[g];if(!r)return;let u=g.toLowerCase();u.charAt(0)==="~"&&(u=u.substring(1),r.__once__=!0);let U=s;if(u.indexOf("zr:")===0&&(U=s.getZr(),u=u.substring(3)),r.__once__){delete r.__once__;const Y=r;r=(...k)=>{Y(...k),U.off(u,r)}}U.on(u,r)});function v(){s&&!s.isDisposed()&&s.resize()}function y(){const g=i||m.value;g&&s.setOption(g,h.value)}c.value?Q(()=>{v(),y()}):y()}function B(i,s){e.manualUpdate&&(o.value=i),t.value?t.value.setOption(i,s||{}):b(i)}function $(){t.value&&(t.value.dispose(),t.value=void 0)}let w=null;A(x,i=>{typeof w=="function"&&(w(),w=null),i||(w=A(()=>e.option,(s,v)=>{!s||(t.value?t.value.setOption(s,{notMerge:s!==v,...h.value}):b())},{deep:!0}))},{immediate:!0}),A([f,p],()=>{$(),b()},{deep:!0}),R(()=>{e.group&&t.value&&(t.value.group=e.group)});const K=de(t);return me(t,z,I),pe(t,c,n),q(()=>{b()}),V(()=>{be&&n.value?n.value.__dispose=$:$()}),{chart:t,root:n,setOption:B,nonEventAttrs:P,nativeListeners:j,...K}},render(){const e={...this.nonEventAttrs,...this.nativeListeners};return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",Z(S,e)}});se([ie,ae,oe,le,ce,re,ue]);var xe=ne(({app:e})=>{e.component("v-chart",Le)});export{xe as default};
