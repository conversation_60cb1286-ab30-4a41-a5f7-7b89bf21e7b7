<template>
  <q-page class="q-pa-sm" style="width: 100%; max-width: 800px; margin: 0 auto">
    <!-- 標題和搜尋區域 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="text-subtitle1 text-primary">彙總報表</div>
      </q-card-section>

      <!-- 日期選擇 -->
      <q-card-section class="row items-center q-gutter-sm q-pt-none q-pb-md">
        <q-btn
          flat
          round
          icon="chevron_left"
          @click="adjustDate(-1)"
          color="primary"
          :disable="!props.Branch"
        />
        <q-input
          v-model="selectedDate"
          dense
          outlined
          square
          mask="####-##-##"
          readonly
          class="col"
        >
          <template v-slot:prepend>
            <q-icon name="calendar_month" size="sm" />
          </template>
          <template v-slot:append>
            <q-icon
              name="edit_calendar"
              color="primary"
              class="cursor-pointer"
              size="18px"
            >
              <q-popup-proxy
                ref="datePopup"
                cover
                transition-show="scale"
                transition-hide="scale"
              >
                <q-date
                  v-model="selectedDate"
                  mask="YYYY-MM-DD"
                  @update:model-value="closeDatePopup"
                >
                </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
        <q-btn
          flat
          round
          icon="chevron_right"
          @click="adjustDate(1)"
          color="primary"
          :disable="!props.Branch"
        />
      </q-card-section>
    </q-card>

    <!-- 主內容區域 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <!-- 載入中狀態 -->
      <div v-if="isLoading" class="q-pa-xl">
        <div class="fancy-loader">
          <svg
            class="loader-svg"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">資料整理中...</div>
        </div>
      </div>

      <!-- 無資料顯示 -->
      <q-card-section
        v-else-if="!report.summary && !isLoading"
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無彙總報表資料</div>
          <div class="empty-subtext">請選擇其他日期或確認分店設定</div>
        </div>
      </q-card-section>

      <!-- 報表內容 -->
      <div v-if="report.summary && !isLoading">
        <q-card-section
          v-if="
            report.invoiceValid > 0 && report.invoiceValid !== report.summary
          "
          class="q-pt-xs q-pb-sm"
        >
          <q-banner class="bg-red-1 text-negative" dense rounded>
            <template v-slot:avatar>
              <q-icon name="warning" color="negative" />
            </template>
            有效發票金額（{{ format(report.invoiceValid) }}）與實收總計（{{
              format(report.summary)
            }}）不一致，請檢查。
          </q-banner>
        </q-card-section>

        <!-- 業績區塊 -->
        <q-card-section class="q-mt-sm q-pt-sm q-pb-none">
          <div
            class="text-subtitle2 text-weight-medium text-grey-8 q-mb-xs row items-center"
          >
            <q-icon name="bar_chart" size="18px" class="q-mr-xs" />
            實際業績
          </div>
          <q-list dense bordered separator>
            <q-item>
              <q-item-section>含稅</q-item-section>
              <q-item-section side>{{
                format(report.realTotalTax)
              }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>未稅</q-item-section>
              <q-item-section side class="text-negative">{{
                format(report.realTotal)
              }}</q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 支付方式 -->
        <q-card-section class="q-pt-sm q-pb-none">
          <div
            class="text-subtitle2 text-weight-medium text-grey-8 q-mb-xs row items-center"
          >
            <q-icon name="credit_card" size="18px" class="q-mr-xs" />
            支付方式
          </div>
          <q-list dense bordered separator>
            <q-item
              ><q-item-section>現金</q-item-section
              ><q-item-section side>{{
                format(report.cash)
              }}</q-item-section></q-item
            >
            <q-item
              ><q-item-section>信用卡</q-item-section
              ><q-item-section side>{{
                format(report.credit)
              }}</q-item-section></q-item
            >
            <q-item
              clickable
              @click="report.otherPayments.length && (showOtherDialog = true)"
            >
              <q-item-section>其他支付</q-item-section>
              <q-item-section side class="text-primary">
                {{
                  format(
                    report.otherPayments.reduce(
                      (sum, item) => sum + Number(item.total),
                      0
                    )
                  )
                }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>實收總計</q-item-section>
              <q-item-section side>{{ format(report.summary) }}</q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 票券與發票 -->
        <q-card-section class="q-pt-sm q-pb-none">
          <div
            class="text-subtitle2 text-weight-medium text-grey-8 q-mb-xs row items-center"
          >
            <q-icon name="sell" size="18px" class="q-mr-xs" />
            票券與發票
          </div>
          <q-list dense bordered separator>
            <q-item v-if="report.couponSold > 0">
              <q-item-section>禮券銷售(已開票)</q-item-section>
              <q-item-section side>
                {{ format(report.couponSold) }}
              </q-item-section>
            </q-item>

            <q-item
              clickable
              @click="
                report.couponUsedDetails.length && (showCouponDialog = true)
              "
            >
              <q-item-section>票券回收</q-item-section>
              <q-item-section side class="text-primary">
                {{
                  format(
                    report.couponUsedDetails.reduce(
                      (sum, item) => sum + Number(item.amount),
                      0
                    )
                  )
                }}
              </q-item-section>
            </q-item>

            <!-- 🔸未開票票券與貴賓券 -->
            <q-item
              clickable
              v-if="report.unbilledCoupons.length"
              @click="showUnbilledCouponDialog = true"
            >
              <q-item-section>禮券銷售(未開) / 貴賓券 / 統計</q-item-section>
              <q-item-section side class="text-primary">
                {{
                  report.unbilledCoupons.reduce(
                    (sum, item) => sum + Number(item.qty),
                    0
                  )
                }}
              </q-item-section>
            </q-item>

            <q-item v-if="report.invoiceValid > 0">
              <q-item-section>有效發票金額</q-item-section>
              <q-item-section side>
                {{ format(report.invoiceValid) }}
              </q-item-section>
            </q-item>

            <q-item
              clickable
              v-if="report.invoiceVoided > 0"
              @click="showVoidInvoiceDialog = true"
            >
              <q-item-section>作廢發票金額</q-item-section>
              <q-item-section side class="text-primary">
                {{ format(report.invoiceVoided) }}
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 來客數 -->
        <q-card-section class="q-pt-sm">
          <div
            class="text-subtitle2 text-weight-medium text-grey-8 q-mb-xs row items-center"
          >
            <q-icon name="people" size="18px" class="q-mr-xs" />
            來客數
          </div>

          <q-list dense bordered separator>
            <!-- 總來客數 -->
            <q-item>
              <q-item-section>總來客數</q-item-section>
              <q-item-section side>
                {{ report.totalGuests }}
              </q-item-section>
            </q-item>

            <!-- 各時段來客數 -->
            <q-item v-for="item in report.guestByTime" :key="item.time">
              <q-item-section>
                {{ item.time }}（{{ item.stime }}-{{ item.etime }}）
              </q-item-section>
              <q-item-section side>
                {{ item.count }}
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </div>

      <!-- Dialogs -->
      <q-dialog v-model="showOtherDialog">
        <q-card style="min-width: 300px; max-width: 90vw; border-radius: 10px">
          <!-- 標題列，無 padding -->
          <div class="row items-center justify-between q-pa-sm">
            <div class="text-primary q-pa-xs">
              <q-icon
                name="account_balance_wallet"
                class="q-mr-sm"
                size="25px"
              />
              其他支付明細
            </div>
          </div>
          <!-- 內容區，取消多餘 padding -->
          <q-list bordered separator>
            <q-item
              v-for="item in report.otherPayments"
              :key="item.name"
              class="q-px-md"
              dense
            >
              <q-item-section>{{ item.name }}</q-item-section>
              <q-item-section side class="text-right">
                {{ format(item.total) }}
              </q-item-section>
            </q-item>
          </q-list>
          <div style="height: 40px"></div>
        </q-card>
      </q-dialog>

      <q-dialog v-model="showCouponDialog">
        <q-card style="min-width: 300px; max-width: 90vw; border-radius: 10px">
          <!-- 標題列，無 padding -->
          <div class="row items-center justify-between q-pa-sm">
            <div class="text-primary q-pa-xs">
              <q-icon name="redeem" class="q-mr-sm" size="25px" />
              票券回收明細
            </div>
          </div>

          <!-- 內容區：列表方式顯示 -->
          <q-list bordered separator>
            <q-item
              v-for="item in report.couponUsedDetails"
              :key="item.name"
              class="q-px-md"
              dense
            >
              <q-item-section>{{ item.name }}</q-item-section>
              <q-item-section side class="text-right">
                {{ format(item.amount) }}
              </q-item-section>
            </q-item>
          </q-list>
          <div style="height: 40px"></div>
        </q-card>
      </q-dialog>

      <q-dialog v-model="showVoidInvoiceDialog">
        <q-card style="min-width: 300px; max-width: 90vw; border-radius: 10px">
          <div class="row items-center justify-between q-pa-sm">
            <div class="text-negative q-pa-xs">
              <q-icon name="receipt_long" class="q-mr-sm" size="25px" />
              作廢發票明細
            </div>
          </div>

          <q-list bordered separator>
            <q-item
              v-for="item in report.voidInvoiceList"
              :key="item.ino"
              class="q-px-md"
              dense
            >
              <q-item-section>{{ item.ino }}</q-item-section>
              <q-item-section side class="text-right">
                {{ format(item.amount) }}
              </q-item-section>
            </q-item>
          </q-list>
          <div style="height: 40px"></div>
        </q-card>
      </q-dialog>

      <q-dialog v-model="showUnbilledCouponDialog">
        <q-card style="min-width: 300px; max-width: 90vw; border-radius: 10px">
          <div class="row items-center justify-between q-pa-sm">
            <div class="text-secondary q-pa-xs">
              <q-icon name="local_offer" class="q-mr-sm" size="25px" />
              禮券銷售(未開) / 貴賓券 / 統計
            </div>
          </div>
          <q-list bordered separator>
            <q-item
              v-for="item in report.unbilledCoupons"
              :key="item.name"
              class="q-px-md"
              dense
            >
              <q-item-section>{{ item.name }}</q-item-section>
              <q-item-section side class="text-right">
                {{ item.qty }}
              </q-item-section>
            </q-item>
          </q-list>
          <div style="height: 40px"></div>
        </q-card>
      </q-dialog>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, watch } from "vue";
import { DateTime } from "luxon";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
import { useQuasar } from "quasar";
const $q = useQuasar();

const props = defineProps({
  Branch: String,
});
const showVoidInvoiceDialog = ref(false);
const datePopup = ref(null);
const closeDatePopup = () => {
  datePopup.value?.hide(); // ✅ 手動關閉 popup
};
const showOtherDialog = ref(false);
const showCouponDialog = ref(false);
const showUnbilledCouponDialog = ref(false);
const selectedDate = ref(DateTime.now().toISODate());
const isLoading = ref(false);

const report = ref({
  cash: 0,
  credit: 0,
  otherpay: 0,
  summary: 0,
  otherPayments: [],
  couponSold: 0,
  couponUsed: 0,
  realTotal: 0,
  realTotalTax: 0,
  invoiceValid: 0,
  invoiceVoided: 0,
  totalGuests: 0,
  guestByTime: [],
  couponUsedDetails: [],
  voidInvoiceList: [],
  unbilledCoupons: [],
});

// ✅ 新增票券回收明細
const couponUsedDetails = ref([]);

const fetchReport = async () => {
  if (!props.Branch || !selectedDate.value) return;
  isLoading.value = true;
  try {
    const response = await apiClient.get(
      `${apiBaseUrl}/bsummary/get_DailyReport`,
      {
        params: {
          cod_cust: props.Branch,
          date: selectedDate.value,
        },
      }
    );
    report.value = response.data;
    couponUsedDetails.value = response.data.couponUsedDetails || []; // ✅ 記得接這欄
    isLoading.value = false;
  } catch (err) {
    console.error("❌ 報表資料載入失敗:", err);
    isLoading.value = false;
    $q.notify({ type: "negative", message: "資料載入失敗" });
  }
};

const adjustDate = (offset) => {
  selectedDate.value = DateTime.fromISO(selectedDate.value)
    .plus({ days: offset })
    .toISODate();
};

const format = (val) => "$" + Number(val).toLocaleString();

watch([selectedDate, () => props.Branch], fetchReport, { immediate: true });
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #ff9800;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}

.loader-message {
  margin-top: 1rem;
  color: #ff9800;
  font-size: 0.9rem;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #757575;
  margin-bottom: 0.5rem;
}

.empty-subtext {
  font-size: 0.9rem;
  color: #9e9e9e;
}
</style>
