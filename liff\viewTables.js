const mysql = require("mysql2/promise");

const dbName = "femas-new";

async function listTables() {
  try {
    // 直接建立連線池（不透過設定檔）
    const pool = await mysql.createPool({
      host: "************", // 你的資料庫位址
      user: "mongo", // 資料庫帳號
      password: "!lcc86740736", // 資料庫密碼
      database: dbName, // 資料庫名稱
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      timezone: "+08:00",
    });

    console.log("📂 資料庫名稱:", dbName);

    const [tables] = await pool.query("SHOW TABLES");
    console.log("✅ 表格列表載入成功");
    console.log("tables:", tables);

    for (const row of tables) {
      const tableName = row[`Tables_in_${dbName}`];
      console.log(`\n📦 表名: ${tableName}`);

      const [columns] = await pool.query(`DESCRIBE \`${tableName}\``);
      console.log("欄位結構:");
      console.table(columns);

      const [tableInfo] = await pool.query(
        `SELECT TABLE_COMMENT 
         FROM information_schema.TABLES 
         WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?`,
        [dbName, tableName]
      );

      if (tableInfo[0]?.TABLE_COMMENT) {
        console.log(`📄 表描述: ${tableInfo[0].TABLE_COMMENT}`);
      }

      const [columnComments] = await pool.query(
        `SELECT COLUMN_NAME, COLUMN_COMMENT 
         FROM information_schema.COLUMNS 
         WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?`,
        [dbName, tableName]
      );

      console.log("📝 欄位注釋:");
      for (const column of columnComments) {
        if (column.COLUMN_COMMENT) {
          console.log(`- ${column.COLUMN_NAME}: ${column.COLUMN_COMMENT}`);
        }
      }

      console.log("-------------------------------------------------");
    }

    await pool.end();
  } catch (error) {
    console.error("❌ 查詢資料庫時出錯:", error.message);
    console.error("🔍 詳細錯誤:", error);
  }
}

listTables();
