const { getPoolByBranch, sql } = require("../services/dbPoolManager");
const mssql = require("mssql");
const headDbConfig = require("../config/db");

// 取得門市 otherpay 清單
const getOtherpayList = async (req, res) => {
  const { branch } = req.query;
  if (!branch)
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool
      .request()
      .query(`SELECT code, name, paykind, sn FROM otherpay ORDER BY sn`);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 新增/更新 otherpay
const saveOtherpay = async (req, res) => {
  const { branch } = req.query;
  const { code, name, paykind, sn } = req.body;
  if (!branch || !code || !name)
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  try {
    const pool = await getPoolByBranch(branch);
    // 檢查是否存在
    const check = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("SELECT COUNT(*) as cnt FROM otherpay WHERE code=@code");
    const exists = check.recordset[0].cnt > 0;
    const snVal = sn || 1;
    if (exists) {
      await pool
        .request()
        .input("code", sql.VarChar, code)
        .input("name", sql.NVarChar, name)
        .input("paykind", sql.VarChar, paykind)
        .input("sn", sql.Int, snVal)
        .query(
          `UPDATE otherpay SET name=@name, paykind=@paykind, sn=@sn WHERE code=@code`
        );
    } else {
      await pool
        .request()
        .input("code", sql.VarChar, code)
        .input("name", sql.NVarChar, name)
        .input("paykind", sql.VarChar, paykind)
        .input("sn", sql.Int, snVal)
        .query(
          `INSERT INTO otherpay (code, name, paykind, sn) VALUES (@code, @name, @paykind, @sn)`
        );
    }
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 刪除 otherpay
const deleteOtherpay = async (req, res) => {
  const { branch } = req.query;
  const { code } = req.body;
  if (!branch || !code)
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  try {
    const pool = await getPoolByBranch(branch);
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM otherpay WHERE code=@code");
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 儲存排序
const saveOtherpaySort = async (req, res) => {
  const { branch, list } = req.body;
  if (!branch || !Array.isArray(list))
    return res.status(400).json({ success: false, message: "參數錯誤" });
  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();
    for (const item of list) {
      if (!item.code || typeof item.sn !== "number") continue;
      await new sql.Request(transaction)
        .input("code", sql.VarChar, item.code)
        .input("sn", sql.Int, item.sn)
        .query(`UPDATE otherpay SET sn=@sn WHERE code=@code`);
    }
    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得總公司 otherpay 清單
const getHeadOtherpayList = async (req, res) => {
  try {
    const pool = await mssql.connect(headDbConfig);
    const result = await pool
      .request()
      .query(`SELECT name, chtot FROM otherpay WHERE chtot <>'M'`);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  } finally {
  }
};

module.exports = {
  getOtherpayList,
  saveOtherpay,
  deleteOtherpay,
  saveOtherpaySort,
  getHeadOtherpayList,
};
