const sql = require("mssql");
const dbConfig = require("../config/db");

// 新增 API 方法：獲取門市評論統計資料
const getBranchCommentStats = async (req, res) => {
  try {
    const { cod_cust } = req.query;

    if (!cod_cust) {
      return res.status(400).json({ error: "缺少 cod_cust 參數" });
    }

    const pool = await sql.connect(dbConfig);
    const result = await pool
      .request()
      .input("Cod_Cust", sql.NChar(6), cod_cust).query(`
        SELECT Comment_Rating, Comment_Count, Comment_At
        FROM Branch
        WHERE Cod_cust = @Cod_Cust
      `);

    if (result.recordset.length > 0) {
      res.json(result.recordset[0]);
    } else {
      res.json({ Comment_Rating: null, Comment_Count: 0, Comment_At: null });
    }
  } catch (error) {
    console.error("獲取門市評論統計資料失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法獲取門市評論統計資料" });
  }
};

const getComments = async (req, res) => {
  try {
    const { cod_cust, month } = req.query;

    if (!cod_cust || !month) {
      return res.status(400).json({ error: "缺少 cod_cust 或 month 參數" });
    }

    const pool = await sql.connect(dbConfig);
    const result = await pool
      .request()
      .input("Cod_Cust", sql.NChar(6), cod_cust)
      .input("Month", sql.VarChar(7), month) // 格式 yyyy-MM
      .query(`
        SELECT Cod_Cust, Comment_Date, Reviewer, Rating, Comment_Text
        FROM comments
        WHERE Cod_Cust = @Cod_Cust
          AND FORMAT(Comment_Date, 'yyyy-MM') = @Month
        ORDER BY Comment_Date DESC
      `);

    res.json(result.recordset);
  } catch (error) {
    console.error("獲取評論資料失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法獲取評論資料" });
  }
};

module.exports = {
  getComments,
  getBranchCommentStats,
};
