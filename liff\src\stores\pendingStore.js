import { defineStore } from "pinia";
import { ref } from "vue";
import axios from "axios";

export const usePendingStore = defineStore("pendingStore", () => {
  const pendingTasks = ref(0);
  const userId = ref(null); // 🔹 儲存 `userId`

  // ✅ 取得待簽核數量
  const fetchPendingTasks = async (uid = null) => {
    const currentUserId = uid || userId.value; // ✅ 如果有傳入 `uid`，則使用它

    if (!currentUserId) {
      //console.warn("⚠️ `user_id` 尚未設定，等待更新...");
      return;
    }

    try {
      //console.log("📌 查詢待簽核數量，使用者:", currentUserId);

      const response = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/get_pending_tasks`,
        { userid: currentUserId, status: "waiting" }
      );

      pendingTasks.value = response.data.pendingTasks || 0;
      //console.log("✅ 取得待簽核數量:", pendingTasks.value);
    } catch (error) {
      console.error("❌ 查詢待簽核數量失敗:", error);
    }
  };

  return { pendingTasks, fetchPendingTasks, userId };
});
