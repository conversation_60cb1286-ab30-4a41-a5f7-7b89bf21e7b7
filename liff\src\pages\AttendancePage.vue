<template>
  <q-page
    class="q-pa-sm flex justify-center"
    style="width: 100%; max-width: 1000px; margin: 0 auto; position: relative"
  >
    <!-- Main Card -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- Header Section -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">出勤紀錄管理</div>
          <div class="row q-gutter-sm">
            <q-btn flat round size="sm" color="green" @click="openbranch">
              <store :stroke-width="1" :size="26" />
              <q-tooltip>選擇門市</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              @click="showExportDialog = true"
            >
              <settings :stroke-width="1" :size="26" />
              <q-tooltip>輸出設定</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              color="orange"
              @click="handleBatchExecute()"
            >
              <list-video :stroke-width="1" :size="26" />
              <q-tooltip>批次執行</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              color="negative"
              @click="showDeleteDialog = true"
            >
              <trash-2 :stroke-width="1" :size="26" />
              <q-tooltip>刪除歷史紀錄</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- No Branch Selected Message -->
      <q-card-section v-if="!selectedBranch" class="q-pt-none">
        <q-banner class="bg-blue-1 text-grey-8" rounded>
          <template v-slot:avatar>
            <Info class="text-primary" :stroke-width="1" :size="30" />
          </template>
          請點擊右上角按鈕選擇要管理的門市
        </q-banner>
      </q-card-section>

      <!-- Branch Info & Controls -->
      <q-card-section v-if="selectedBranch" class="q-pt-none">
        <!-- 基本資訊卡片 -->
        <q-card-section class="q-pt-md q-pb-sm">
          <div class="row q-col-gutter-sm">
            <div class="col-12 col-md-4">
              <div class="info-card info-card-person info-card-person-purple">
                <div class="info-icon info-icon-person info-icon-person-purple">
                  <store :stroke-width="1" :size="22" />
                </div>
                <div class="info-content">
                  <div class="info-label">門市名稱</div>
                  <div class="info-value person-value person-value-purple">
                    {{ selectedBranch.Cod_Name }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-4">
              <div
                class="info-card info-card-calendar info-card-calendar-green"
              >
                <div
                  class="info-icon info-icon-calendar info-icon-calendar-green"
                >
                  <calendar-days :stroke-width="1" :size="22" />
                </div>
                <div class="info-content">
                  <div class="info-label">最後更新</div>
                  <div class="info-value calendar-value calendar-value-green">
                    {{ selectedBranch.AttendanceSync || "無" }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-4">
              <div class="info-card info-card-type info-card-type-indigo">
                <div class="info-icon info-icon-type info-icon-type-indigo">
                  <list :stroke-width="1" :size="22" />
                </div>
                <div class="info-content">
                  <div class="info-label">記錄數量</div>
                  <div class="info-value type-value type-value-indigo">
                    {{ attendanceData.length || 0 }} 筆
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- Action Buttons -->
        <div class="row q-col-gutter-sm q-mb-md q-pt-xl">
          <div class="col-12 col-sm-6">
            <q-btn
              unelevated
              outline
              color="teal"
              @click="downloadRecords"
              class="full-width"
              :loading="isLoading"
            >
              <template v-slot:default>
                <arrow-down-to-line
                  :stroke-width="1"
                  :size="22"
                  class="q-mr-xs"
                />
                下載紀錄
              </template>
            </q-btn>
          </div>
          <div class="col-12 col-sm-6">
            <q-btn
              unelevated
              outline
              color="orange"
              @click="syncDeviceTime"
              class="full-width"
              :loading="isLoading"
            >
              <template v-slot:default>
                <refresh-cw :stroke-width="1" :size="22" class="q-mr-xs" />
                同步時間
              </template>
            </q-btn>
          </div>
        </div>
        <q-btn
          unelevated
          outline
          color="negative"
          @click="ConfirmDeleteDeviceAtt = true"
          class="full-width"
          :loading="isLoading"
        >
          <template v-slot:default>
            <triangle-alert :stroke-width="1" :size="22" class="q-mr-xs" />
            刪除設備紀錄
          </template>
        </q-btn>

        <!-- 原出勤紀錄表格已註解 -->
        <!-- 
        <q-separator class="q-my-md" />

        <q-input
          filled
          dense
          debounce="300"
          v-model="searchId"
          label="篩選工號"
          class="q-mb-md"
          square
        >
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
          <template v-slot:append v-if="searchId">
            <q-icon
              name="close"
              @click="searchId = ''"
              class="cursor-pointer"
            />
          </template>
        </q-input>

        <q-card flat bordered class="no-shadow">
          <q-table
            :rows="filteredData"
            :columns="columns"
            dense
            flat
            bordered
            wrap-cells
            row-key="id"
            :pagination="{ rowsPerPage: 10 }"
            :loading="isLoading"
          >
            <template v-slot:top>
              <div
                class="row items-center justify-between full-width q-py-sm q-px-md"
              >
                <div class="text-subtitle2 text-primary">出勤紀錄</div>
                <div class="text-caption text-grey-7">
                  共 {{ filteredData.length }} 筆資料
                </div>
              </div>
            </template>

            <template v-slot:loading>
              <q-inner-loading showing color="primary">
                <q-spinner-dots size="40px" color="primary" />
              </q-inner-loading>
            </template>

            <template v-slot:no-data>
              <div class="full-width row flex-center q-pa-md text-grey-7">
                <q-icon
                  name="sentiment_dissatisfied"
                  size="24px"
                  class="q-mr-sm"
                />
                無符合條件的資料
              </div>
            </template>
          </q-table>
        </q-card>
        -->
      </q-card-section>
    </q-card>

    <!--🟢 匯出設定 🟢 -->
    <q-dialog v-model="showExportDialog">
      <q-card
        style="width: 100%; max-width: 400px"
        class="column q-pa-sm bg-white rounded-borders"
      >
        <div class="q-pa-xs row items-center justify-between text-primary">
          <!-- 左側圖示 + 標題 -->
          <div class="row items-center q-gutter-sm text-subtitle1">
            <cloud-download :stroke-width="1" :size="30" class="q-mr-xs" />
            <span>匯出資料</span>
          </div>

          <!-- 右側關閉按鈕 -->
          <q-btn dense flat round class="text-primary" v-close-popup>
            <x :stroke-width="1" :size="26" />
          </q-btn>
        </div>
        <!-- 🔹 日期區段 -->
        <q-card-section>
          <q-input
            v-model="exportStart"
            type="date"
            label="起始日期"
            outlined
            dense
          >
            <template v-slot:prepend>
              <calendar-days :stroke-width="1" :size="22" />
            </template>
          </q-input>
          <q-input
            v-model="exportEnd"
            type="date"
            label="結束日期"
            outlined
            dense
            class="q-mt-sm"
          >
            <template v-slot:prepend>
              <calendar-days :stroke-width="1" :size="22" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 🔹 狀態對照 -->
        <q-card-section>
          <div class="text-subtitle2 text-primary q-mb-sm">狀態對照</div>
          <div class="row q-col-gutter-sm q-mb-sm" v-for="row in 3" :key="row">
            <q-input
              class="col-6"
              outlined
              dense
              :label="`狀態 ${2 * (row - 1)}`"
              v-model="stateMapManual[2 * (row - 1)]"
            />
            <q-input
              class="col-6"
              outlined
              dense
              :label="`狀態 ${2 * (row - 1) + 1}`"
              v-model="stateMapManual[2 * (row - 1) + 1]"
            />
          </div>
        </q-card-section>

        <!-- 🔹 輸出選項 -->

        <!-- 🔽 匯出操作按鈕區 -->
        <q-card-section class="q-pt-none">
          <!-- FTP 設定齒輪按鈕 -->
          <q-btn
            flat
            dense
            color="grey-8"
            outline
            @click="showFtpSetting = true"
          >
            <settings
              :stroke-width="1"
              :size="26"
              class="q-mr-xs"
            />匯出連線設定
          </q-btn>
        </q-card-section>
        <q-separator />
        <!-- 🔹 底部取消 -->
        <q-card-actions align="right" class="q-gutter-sm q-pa-sm">
          <!-- 下載 TXT 按鈕 -->
          <q-btn
            unelevated
            outline
            color="primary"
            @click="exportToFtp"
            :disable="!exportStart || !exportEnd"
            :loading="isExporting"
          >
            <cloud-upload :stroke-width="1" :size="22" class="q-mr-md" />
            匯出
          </q-btn>
          <q-btn
            outline
            unelevated
            color="secondary"
            @click="downloadAttendanceTxt"
            :disable="!exportStart || !exportEnd"
            :loading="isDownloading"
          >
            <download :stroke-width="1" :size="22" class="q-mr-md" />
            下載
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!--🟢 刪除本地資料 🟢 -->
    <q-dialog v-model="showDeleteDialog">
      <q-card style="width: 100%; max-width: 400px" class="rounded-borders">
        <q-card-section class="text-h6 text-negative text-center">
          刪除歷史紀錄
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="deleteBeforeDate"
            type="date"
            label="保留起始日"
            outlined
            dense
          >
            <template v-slot:prepend>
              <q-icon name="event" />
            </template>
          </q-input>
        </q-card-section>

        <q-card-actions align="right" class="q-gutter-sm q-pa-sm">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            label="刪除"
            color="negative"
            :disable="!deleteBeforeDate"
            @click="confirmDelete"
            :loading="isDeleting"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!--🟢 門市資料 🟢 -->
    <q-dialog v-model="branchDialog">
      <q-card style="width: 100%; max-width: 420px" class="rounded-borders">
        <!-- 標題區 -->
        <q-card-section class="text-center page-dialog-title q-pb-none">
          <q-icon name="store" color="primary" size="24px" class="q-mr-sm" />
          選擇門市
        </q-card-section>

        <!-- 搜尋區 -->
        <q-card-section class="q-pt-none q-pb-xs">
          <q-input
            v-model="searchQuery"
            dense
            outlined
            placeholder="輸入名稱或代號"
            clearable
            class="q-mt-sm"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 門市清單 -->
        <q-card-section style="height: 350px" class="scroll">
          <q-list bordered separator>
            <q-item
              v-for="branch in filteredBranches"
              :key="branch.Cod_cust"
              clickable
              v-ripple
              @click="selectBranch(branch)"
            >
              <q-item-section>
                <q-item-label class="text-weight-medium">
                  {{ branch.Cod_Name }}
                </q-item-label>
                <q-item-label caption class="text-grey row items-center">
                  <q-icon name="schedule" size="16px" class="q-mr-xs" />
                  {{ branch.AttendanceSync || "無" }}
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-badge color="primary" outline>
                  {{ branch.Cod_Cust }}
                </q-badge>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 關閉按鈕 -->
        <q-card-actions align="right" class="q-pa-sm">
          <q-btn flat label="取消" color="grey" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!--🟢 批次執行 🟢 -->
    <q-dialog v-model="batchDialog">
      <q-card
        style="width: 100%; max-width: 420px; height: 80vh"
        class="column q-pa-sm bg-white rounded-borders"
      >
        <!-- 📌 標題 -->
        <div class="q-pa-xs row items-center justify-between text-primary">
          <!-- 左側圖示 + 標題 -->
          <div class="row items-center q-gutter-sm text-subtitle1">
            <q-icon name="cloud_download" size="30px" />
            <span>批次下載打卡紀錄</span>
          </div>

          <!-- 右側關閉按鈕 -->
          <q-btn
            dense
            flat
            round
            icon="close"
            class="text-primary"
            v-close-popup
            aria-label="關閉"
          />
        </div>

        <!-- 📋 滾動區 -->
        <q-scroll-area class="col q-pa-sm">
          <q-list bordered separator>
            <q-item
              v-for="log in batchLogs"
              :key="log.Cod_Cust"
              class="rounded-borders q-mb-xs q-pa-sm"
            >
              <q-item-section avatar>
                <q-avatar color="blue-1" text-color="blue" size="32px">
                  <q-icon name="store" />
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <div class="row items-center justify-between">
                  <!-- 左側名稱與狀態訊息 -->
                  <div class="column">
                    <q-item-label>
                      {{ log.Cod_Name.trim() }}
                    </q-item-label>
                    <q-item-label caption class="q-mt-xs text-left">
                      <div
                        :class="[
                          'q-px-none q-py-xs rounded-borders',
                          {
                            pending: 'text-grey-9',
                            processing: 'text-grey-9',
                            success: 'text-positive',
                            error: 'text-negative',
                          }[log.status],
                        ]"
                        style="display: inline-block"
                      >
                        共 {{ log.deviceCount || 0 }} 筆 {{ log.message }} -
                        {{ log.count || 0 }} 筆
                      </div>
                    </q-item-label>
                  </div>

                  <div class="q-ml-md">
                    <q-spinner-hourglass
                      v-if="log.status === 'pending'"
                      color="grey"
                      size="24px"
                      class="animate-spin"
                    />
                    <q-icon
                      v-if="log.status !== 'pending'"
                      :name="
                        {
                          processing: 'sync',
                          success: 'check_circle',
                          error: 'error',
                        }[log.status]
                      "
                      :color="
                        {
                          processing: 'grey',
                          success: 'secondary',
                          error: 'negative',
                        }[log.status]
                      "
                      size="24px"
                    />
                  </div>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>

        <!-- 🔘 底部按鈕 -->
        <q-separator />
        <q-card-actions align="right" class="q-pa-sm">
          <q-btn
            unelevated
            outline
            color="primary"
            label="執行"
            icon="play_arrow"
            :disable="isBatchRunning"
            @click="startBatchDownload()"
            class="q-px-md q-rounded"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!--🟢 FTP設定 🟢 -->
    <q-dialog v-model="showFtpSetting">
      <q-card style="width: 100%; max-width: 400px" class="rounded-borders">
        <q-card-section class="text-h6 text-primary text-center">
          FTP 匯出設定
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="ftpHost"
            label="主機位址"
            outlined
            dense
            class="q-mb-sm"
          >
            <template v-slot:prepend>
              <hard-drive :stroke-width="1" :size="22" />
            </template>
          </q-input>
          <q-input
            v-model="ftpUser"
            label="帳號"
            outlined
            dense
            class="q-mb-sm"
          >
            <template v-slot:prepend>
              <user-round :stroke-width="1" :size="22" />
            </template>
          </q-input>
          <q-input
            v-model="ftpPassword"
            label="密碼"
            type="password"
            outlined
            dense
            class="q-mb-sm"
          >
            <template v-slot:prepend>
              <lock-keyhole :stroke-width="1" :size="22" />
            </template>
          </q-input>
          <q-btn
            unelevated
            color="info"
            label="測試連線"
            @click="testFTP"
            class="full-width"
            :loading="isTestingFtp"
          />
        </q-card-section>

        <q-card-actions align="right" class="q-gutter-sm q-pa-sm">
          <q-btn flat label="關閉" color="grey" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!--🟢 刪除設備紀錄 🟢 -->
    <q-dialog v-model="ConfirmDeleteDeviceAtt">
      <q-card class="q-pa-lg rounded-borders" style="width: 320px">
        <q-card-section class="text-center">
          <q-icon
            name="delete_forever"
            color="negative"
            size="xl"
            class="q-mb-md"
          />
          <div class="text-h6 text-negative">確定刪除？</div>
          <div class="text-body2 text-grey-8 q-mt-sm">
            設備紀錄刪除後無法復原，請確認是否繼續。
          </div>
        </q-card-section>
        <q-separator class="q-mt-md" />
        <q-card-actions class="q-mt-sm row justify-between">
          <q-btn
            label="取消"
            color="grey"
            flat
            class="full-width q-mr-xs"
            v-close-popup
          />
          <q-btn
            label="刪除"
            color="negative"
            unelevated
            class="full-width q-ml-xs"
            @click="DeleteDeviceAtt()"
            :loading="isDeleting"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar, QSpinnerFacebook, QSpinnerGears } from "quasar";
import { DateTime } from "luxon";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// Loading states
const isLoading = ref(false);
const isSyncing = ref(false);
const isExporting = ref(false);
const isDownloading = ref(false);
const isDeleting = ref(false);
const isTestingFtp = ref(false);

// Dialog states
const showFtpSetting = ref(false);
const ConfirmDeleteDeviceAtt = ref(false);
const showExportDialog = ref(false);
const showDeleteDialog = ref(false);
const branchDialog = ref(false);
const batchDialog = ref(false);

// FTP settings
const ftpHost = ref("************");
const ftpUser = ref("mongobeefEIP");
const ftpPassword = ref("!mongo123456");

const batchLogs = ref([]);

const attendanceData = ref([]); // 全部紀錄
const searchId = ref("");
const searchQuery = ref(""); // 新增搜尋門市的變數
const branches = ref([]); // 所有門市資料

const UTCformatDateTime = (dateString) => {
  if (!dateString) return "無日期"; // 避免 `null` 或 `undefined`
  return DateTime.fromISO(dateString, { zone: "utc" }) // 解析為 UTC
    .setZone("Asia/Taipei") // 轉換為台北時間 (UTC+8)
    .toFormat("yyyy-MM-dd HH:mm"); // 格式化
};

const fetchAttendance = async () => {
  try {
    isLoading.value = true;
    const response = await apiClient.get(`${apiBaseUrl}/att/get_Attendance`, {
      params: { Cod_cust: selectedBranch.value.Cod_Cust },
    });
    attendanceData.value = response.data;
    //console.log("📥 載入紀錄完成", attendanceData.value);
  } catch (error) {
    console.error("❌ 無法載入該門市紀錄", error);
    attendanceData.value = [];
  } finally {
    isLoading.value = false;
  }
};

const exportToFtp = async () => {
  try {
    isExporting.value = true;
    $q.loading.show({
      message: "正在匯出並上傳至 FTP，請稍候...",
      spinnerColor: "blue",
    });

    const res = await apiClient.post(`${apiBaseUrl}/att/export_Ftp`, {
      host: ftpHost.value,
      user: ftpUser.value,
      password: ftpPassword.value,
      remoteDir: "/", // 可自定義
      filename: "",
      startDate: exportStart.value,
      endDate: exportEnd.value,
      stateMap: Object.fromEntries(stateMap.value.map((s) => s.split("="))),
    });

    if (res.data.status === "success") {
      $q.notify({ type: "positive", message: res.data.message });
    } else {
      $q.notify({ type: "warning", message: res.data.message });
    }
  } catch (err) {
    console.error("❌ FTP 匯出錯誤：", err);
    $q.notify({
      type: "negative",
      message: err.response?.data?.message || "FTP 匯出失敗",
    });
  } finally {
    isExporting.value = false;
    $q.loading.hide(); // 無論成功或失敗都關閉 loading
  }
};

const testFTP = async () => {
  try {
    isTestingFtp.value = true;
    const res = await apiClient.get(`${apiBaseUrl}/att/test_Ftp`, {
      params: {
        host: ftpHost.value,
        user: ftpUser.value,
        password: ftpPassword.value,
      },
    });

    if (res.data.status === "success") {
      $q.notify({ type: "positive", message: res.data.message });
    } else {
      $q.notify({ type: "warning", message: res.data.message });
    }
  } catch (err) {
    console.error("❌ 測試連線失敗:", err);
    const msg =
      err.response?.data?.message || err.message || "❌ 測試失敗，請稍後再試";
    $q.notify({ type: "negative", message: msg });
  } finally {
    isTestingFtp.value = false;
  }
};

const fetchBranches = async () => {
  try {
    isLoading.value = true;
    const response = await apiClient.get(`${apiBaseUrl}/att/get_branchs`);
    branches.value = response.data.map((user) => {
      const cleaned = Object.fromEntries(
        Object.entries(user).map(([key, value]) => [
          key,
          typeof value === "string" ? value.trim() : value,
        ])
      );

      // ✅ 格式化 AttendanceSync
      cleaned.AttendanceSync = UTCformatDateTime(cleaned.AttendanceSync);

      return cleaned;
    });
  } catch (error) {
    console.error("獲取門市資料失敗:", error);
  } finally {
    isLoading.value = false;
  }
};

const deleteBeforeDate = ref(
  DateTime.now().minus({ months: 1 }).toFormat("yyyy-LL-dd")
);

// 篩選後的資料
const filteredData = computed(() =>
  attendanceData.value.filter(
    (rec) => !searchId.value || rec.User_id.includes(searchId.value)
  )
);

// 篩選後的門市列表
const filteredBranches = computed(() => {
  if (!searchQuery.value) return branches.value;

  return branches.value.filter(
    (branch) =>
      branch.Cod_Name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      branch.Cod_Cust.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const cstateMap = {
  0: "上班",
  1: "下班",
  2: "空班",
  3: "返回",
  4: "未指定",
  5: "未指定",
  255: "未輸入",
};
// 表格欄位
const columns = [
  { name: "User_id", label: "工號", field: "User_id", align: "left" },
  {
    name: "Timestamp",
    label: "時間",
    field: "Timestamp",
    align: "left",
    format: (val) => DateTime.fromISO(val).toFormat("yyyy-MM-dd HH:mm"),
  },
  {
    name: "State",
    label: "狀態",
    field: "State",
    align: "left",
    format: (val) => {
      const label = cstateMap[Number(val)];
      return label ? `${label} (${Number(val)})` : val;
    },
  },
];

const $q = useQuasar();
const stateMapManual = ref(["A", "D", "B", "C", "D", "E"]); // 狀態碼 0~5 的對應值
// 🏬 門市資訊
const selectedBranch = ref(null);

// 🔍 查詢與記錄
const records = ref([]);

// 🗂️ 狀態對照設定
const stateMap = ref(["0=A", "1=D", "2=B", "3=C"]);

// 📅 區間與路徑
const exportStart = ref(DateTime.now().startOf("month").toFormat("yyyy-LL-dd"));
const exportEnd = ref(DateTime.now().toFormat("yyyy-LL-dd"));
const exportPath = ref("\\\\************\\attcard");

// ❌ 刪除舊紀錄
const confirmDelete = async () => {
  try {
    isDeleting.value = true;
    const res = await apiClient.post(`${apiBaseUrl}/att/delete_Attendance`, {
      beforeDate: deleteBeforeDate.value, // 這是你挑選的日期
    });

    if (res.data.message === "刪除成功") {
      $q.notify({
        type: "positive",
        message: `已刪除 ${res.data.rowsAffected || 0} 筆歷史紀錄`,
      });
    } else {
      $q.notify({
        type: "warning",
        message: res.data.error || " 刪除失敗，請稍後再試",
      });
    }
  } catch (err) {
    console.error("刪除歷史紀錄失敗:", err);
    $q.notify({
      type: "negative",
      message: "刪除失敗：" + err.message,
    });
  } finally {
    isDeleting.value = false;
  }
};

// 📥 下載出勤紀錄）
const downloadRecords = async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "yellow",
    message: "下載中...",
  });

  try {
    isLoading.value = true;
    const res = await apiClient.post("/att/download_Attendance", {
      Cod_cust: selectedBranch.value.Cod_Cust,
      AttendanceIP: selectedBranch.value.AttendanceIP,
    });

    if (res.data?.status === "success") {
      const logCount = res.data.deviceCount ?? 0; // 🧩 設備原始筆數
      const writeCount = res.data.count ?? 0; // 📝 寫入 DB 的筆數
      console.log(res.data);
      if (logCount === 0) {
        $q.notify({
          type: "warning",
          message: "設備連線成功，但目前無考勤紀錄",
        });
      } else if (writeCount === 0) {
        $q.notify({
          type: "positive",
          message: "下載成功，無新增寫入資料",
        });
      } else {
        $q.notify({
          type: "positive",
          message: `下載成功，共 ${writeCount} 筆資料`,
        });

        await fetchAttendance();

        // 更新最後同步時間
        selectedBranch.value.AttendanceSync = UTCformatDateTime(
          new Date().toISOString()
        );

        // 重新讀取門市資訊以更新記錄數量
        await fetchBranches();
      }
    } else {
      $q.notify({
        type: "warning",
        message: res.data?.error || "未知錯誤，請稍後再試",
      });
    }
  } catch (err) {
    console.error("❌ API 錯誤:", err);
    $q.notify({
      type: "negative",
      message: "下載失敗，請稍後再試",
    });
  } finally {
    isLoading.value = false;
    $q.loading.hide();
  }
};

// 🔄 同步設備時間（模擬）
const syncDeviceTime = async () => {
  try {
    isSyncing.value = true;
    const res = await apiClient.post(`${apiBaseUrl}/att/sync_DeviceTime`, {
      ip: selectedBranch.value.AttendanceIP,
    });
    if (res.data.status === "success") {
      $q.notify({ type: "positive", message: res.data.message });
    } else {
      $q.notify({ type: "warning", message: res.data.error || "未知錯誤" });
    }
  } catch (err) {
    console.error("❌ 同步設備時間失敗:", err);
    $q.notify({ type: "negative", message: "同步失敗：" + err.message });
  } finally {
    isSyncing.value = false;
  }
};

// 🧹 刪除設備所有紀錄
const DeleteDeviceAtt = async () => {
  try {
    isDeleting.value = true;
    const res = await apiClient.post(`${apiBaseUrl}/att/delete_DeviceAtt`, {
      ip: selectedBranch.value.AttendanceIP,
    });
    if (res.data.status === "success") {
      $q.notify({ type: "positive", message: res.data.message });
    } else {
      $q.notify({ type: "warning", message: res.data.error || "未知錯誤" });
    }
    ConfirmDeleteDeviceAtt.value = false;
  } catch (err) {
    console.error("❌ 刪除設備紀錄失敗:", err);
    $q.notify({ type: "negative", message: "刪除失敗：" + err.message });
  } finally {
    isDeleting.value = false;
  }
};

// 🔘 選擇門市（打開 dialog）
const selectBranch = async (branch) => {
  selectedBranch.value = branch;
  await fetchAttendance();
  branchDialog.value = false;
};

const openbranch = async () => {
  await fetchBranches();
  branchDialog.value = true;
};

const handleBatchExecute = async () => {
  batchLogs.value = [];
  batchDialog.value = true;
};
const isBatchRunning = ref(false);

const startBatchDownload = async () => {
  isBatchRunning.value = true;
  batchLogs.value = [];

  try {
    // ✅ 啟動批次下載流程
    const { data: startRes } = await apiClient.post(
      `${apiBaseUrl}/att/batch_Download`
    );
    $q.notify({ type: "info", message: startRes.message || "批次下載已啟動" });

    // ✅ 每 0.5 秒輪詢批次進度
    const interval = setInterval(async () => {
      const { data } = await apiClient.get(`${apiBaseUrl}/att/batch_Status`);
      batchLogs.value = data;

      const done = data.every(
        (log) => log.status === "success" || log.status === "error"
      );
      if (done) {
        clearInterval(interval);
        isBatchRunning.value = false;
        await fetchBranches();
      }
    }, 500);
  } catch (err) {
    $q.notify({ type: "negative", message: "批次執行失敗：" + err.message });
    isBatchRunning.value = false;
  }
};

const downloadAttendanceTxt = async () => {
  try {
    isDownloading.value = true;
    const payload = {
      startDate: exportStart.value,
      endDate: exportEnd.value,
      stateMap: Object.fromEntries(stateMap.value.map((s) => s.split("="))),
    };

    const res = await apiClient.post(
      `${apiBaseUrl}/att/download_AttendanceTxt`,
      payload,
      { responseType: "blob" }
    );

    const blob = new Blob([res.data], { type: "text/plain;charset=utf-8" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.setAttribute("download", `${DateTime.now().toFormat("yyyyLLdd")}.txt`);
    document.body.appendChild(link);
    link.click();

    link.remove();
    window.URL.revokeObjectURL(url);

    $q.notify({ type: "positive", message: "下載完成！" });
  } catch (err) {
    console.error(err);
    $q.notify({ type: "negative", message: "下載失敗" });
  } finally {
    isDownloading.value = false;
  }
};

// 初始化
onMounted(async () => {
  // 可以在這裡放置初始化邏輯
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.custom-separator {
  height: 0.5px;
  margin-bottom: 16px;
}

.page-dialog-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--q-primary);
  padding: 16px;
}

/* 基本資訊卡片樣式 */
.info-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.07);
  padding: 14px 10px;
  margin-bottom: 0;
  transition: box-shadow 0.2s;
  min-height: 60px;
  flex: 1 1 0;
  min-width: 0;
  position: relative;
}

.info-card:hover {
  box-shadow: 0 4px 16px 0 rgba(25, 118, 210, 0.13);
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 10px;
  font-size: 18px;
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 0.72rem;
  color: #888;
  margin-bottom: 1px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-value {
  color: #222;
  word-break: break-all;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 600;
}

/* 卡片樣式 - 門市名稱 */
.info-card-person-purple::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #ce93d8 0%, #8e24aa 100%);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.info-icon-person-purple {
  background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
  color: #8e24aa;
}

.person-value-purple {
  color: #8e24aa;
}

/* 卡片樣式 - 最後更新 */
.info-card-calendar-green::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #a5d6a7 0%, #388e3c 100%);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.info-icon-calendar-green {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  color: #388e3c;
}

.calendar-value-green {
  color: #388e3c;
}

/* 卡片樣式 - 記錄數量 */
.info-card-type-indigo::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #9fa8da 0%, #3949ab 100%);
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.info-icon-type-indigo {
  background: linear-gradient(135deg, #e8eaf6 0%, #c5cae9 100%);
  color: #3949ab;
}

.type-value-indigo {
  color: #3949ab;
}

/* 確保手機上圖標和文字不縮小 */
@media (max-width: 599px) {
  .info-card {
    padding: 14px 10px !important;
    min-height: 60px !important;
  }

  .info-icon {
    width: 32px !important;
    height: 32px !important;
    margin-right: 10px !important;
    font-size: 18px !important;
  }

  .info-label {
    font-size: 0.72rem !important;
  }

  .info-value {
    font-size: 1rem !important;
    line-height: 1.2 !important;
  }
}
</style>
