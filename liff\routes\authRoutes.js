const express = require("express");
const {
  verifyToken,
  lineLogin,
  login,
  changePassword,
  resetPassword,
  keepAlive,
  getOnlineUsers,
  getUserInfo,
} = require("../controllers/authController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.post("/verify-token", verifyToken); // ✅ JWT 驗證 API
router.post("/line-login", lineLogin); // ✅ LINE 驗證
router.post("/login", login); // ✅ 一般帳號密碼登入
router.post("/change-password", changePassword); // ✅ 修改密碼
router.post("/reset-password", resetPassword); // ✅ 恢復密碼
router.get("/get-OnlineUsers", getOnlineUsers); // ✅ 在線登記
router.get("/get-UserInfo", getUserInfo); // ✅ 反解登入訊息

router.post("/keep-Alive", authMiddleware, keepAlive); // ✅ 在線登記

module.exports = router;
