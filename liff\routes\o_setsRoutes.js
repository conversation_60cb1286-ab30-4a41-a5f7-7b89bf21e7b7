const express = require("express");
const router = express.Router();
const controller = require("../controllers/o_setsController");
const authMiddleware = require("../middleware/authMiddleware");

// 獲取所有套餐群組
router.get("/get_SetGroups", authMiddleware, controller.getSetGroups);

// 儲存套餐群組資訊
router.post("/save_SetGroup", authMiddleware, controller.saveSetGroup);

// 刪除套餐群組
router.post("/delete_SetGroup", authMiddleware, controller.deleteSetGroup);

// 獲取可新增的菜單項目
router.get("/get_MenuItems", authMiddleware, controller.getMenuItems);

// 添加項目到套餐群組
router.post("/add_SetItems", authMiddleware, controller.addSetItems);

// 刪除套餐群組中的項目
router.post("/delete_SetItem", authMiddleware, controller.deleteSetItem);

// 保存群組排序
router.post("/save_GroupSort", authMiddleware, controller.saveGroupSort);

// 保存項目排序
router.post("/save_SetItemSort", authMiddleware, controller.saveSetItemSort);

// 獲取主要商品列表
router.get("/get_MainProducts", authMiddleware, controller.getMainProducts);

// 獲取套餐與商品的對應關係
router.get("/get_SetsMap", authMiddleware, controller.getSetsMap);

// 更新套餐與商品的對應關係
router.post("/update_SetsMap", authMiddleware, controller.updateSetsMap);

// 獲取套餐群組列表（用於商品規格對映）
router.get(
  "/get_SetsGroupsForMapping",
  authMiddleware,
  controller.getSetsGroupsForMapping
);

// 新增商品規格對映套餐
router.post("/add_SetsMapping", authMiddleware, controller.addSetsMapping);

// 刪除商品規格對映套餐
router.post(
  "/delete_SetsMapping",
  authMiddleware,
  controller.deleteSetsMapping
);

// 獲取商品的套餐對映列表
router.get(
  "/get_MenuSetsMappings",
  authMiddleware,
  controller.getMenuSetsMappings
);

module.exports = router;
