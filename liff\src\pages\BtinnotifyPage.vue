<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">公告通知記錄</div>
            <div class="row">
              <q-btn
                flat
                round
                size="sm"
                color="grey-7"
                class="search-button q-mr-xs"
                @click="openDeleteHistoryDialog"
              >
                <trash :size="18" :stroke-width="1.5" />
                <q-tooltip>刪除歷史記錄</q-tooltip>
              </q-btn>
              <q-btn
                flat
                round
                size="sm"
                color="red"
                class="search-button"
                @click="openAdvancedSearch"
              >
                <Search :size="18" :stroke-width="1.5" />
                <q-tooltip>進階搜尋</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>

        <!-- 搜尋輸入框 -->
        <q-card-section class="q-pt-none">
          <q-input
            v-model="queryKeyword"
            placeholder="輸入公告編號或通知人員搜尋"
            outlined
            dense
            clearable
            class="search-input"
            @update:model-value="handleQueryChange"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 統計卡片區域 -->
        <q-card-section class="q-py-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部通知統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': activeTab === 'all' }"
                  @click="activeTab = 'all'"
                >
                  <div class="stats-icon">
                    <Files :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ totalNotifications }}</div>
                    <div class="stats-label">全部通知</div>
                  </div>
                </div>
              </div>

              <!-- 已發送統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card sent-card"
                  :class="{ 'active-filter': activeTab === 'sent' }"
                  @click="activeTab = 'sent'"
                >
                  <div class="stats-icon">
                    <CheckCircle :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ sentNotifications.length }}
                    </div>
                    <div class="stats-label">已發送</div>
                  </div>
                </div>
              </div>

              <!-- 待發送統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card pending-card"
                  :class="{ 'active-filter': activeTab === 'pending' }"
                  @click="activeTab = 'pending'"
                >
                  <div class="stats-icon">
                    <Clock :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ pendingNotifications.length }}
                    </div>
                    <div class="stats-label">待發送</div>
                  </div>
                </div>
              </div>

              <!-- 發送失敗統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card failed-card"
                  :class="{ 'active-filter': activeTab === 'failed' }"
                  @click="activeTab = 'failed'"
                >
                  <div class="stats-icon">
                    <AlertCircle :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{ failedNotifications.length }}
                    </div>
                    <div class="stats-label">發送失敗</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 通知列表卡片 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <q-card-section class="q-pa-xs">
        <!-- 加載中狀態 -->
        <div v-if="loading" class="loader-container">
          <q-spinner size="40px" color="grey-5" />
          <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
            載入通知中...
          </div>
        </div>

        <!-- 無資料狀態 -->
        <div
          v-else-if="groupedAndFilteredNotifications.length === 0"
          class="empty-state-container"
        >
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <message-square-warning
              :size="50"
              :stroke-width="1"
              style="width: 60px; height: 50px; margin-bottom: 12px"
            />
            <div class="empty-text text-grey-5">
              {{ searchForm.title ? "沒有找到符合的通知" : "目前沒有通知記錄" }}
            </div>
          </div>
        </div>

        <!-- 通知列表 -->
        <div v-else class="notification-list-container">
          <!-- 分頁控制器 -->
          <div class="row justify-end q-mb-sm">
            <q-pagination
              v-model="currentPage"
              :max="totalPages"
              :max-pages="5"
              boundary-numbers
              direction-links
              color="primary"
              active-color="blue"
              active-text-color="white"
              class="pagination-controls"
              size="sm"
            />
          </div>

          <q-list separator class="notification-list">
            <!-- 使用q-expansion-item將公告通知分組顯示 -->
            <q-expansion-item
              v-for="bulletin in paginatedGroupedNotifications"
              :key="bulletin.id"
              class="bg-white q-mb-xs notification-item"
              :class="{
                'pending-notification': hasPendingNotifications(
                  bulletin.notifications
                ),
              }"
              expand-separator
              header-class="text-indigo bulletin-header"
            >
              <!-- 公告主列 -->
              <template #header>
                <q-item
                  dense
                  class="q-pa-none q-gutter-none items-center"
                  style="width: 100%; position: relative; padding: 8px 10px"
                >
                  <q-item-section>
                    <!-- 公告ID和類型 -->
                    <div
                      class="text-indigo-10 text-weight-medium notification-title"
                    >
                      {{ bulletin.id }} - 公告通知
                    </div>
                    <!-- 通知摘要 -->
                    <div
                      class="text-caption text-grey-8 notification-meta q-mt-xs"
                    >
                      <div class="flex items-center">
                        <calendar-clock
                          :stroke-width="1"
                          :size="16"
                          class="q-mr-xs"
                        />
                        建立: {{ formatDateTime(bulletin.created) }}
                      </div>
                      <div class="flex items-center q-mt-xs">
                        <users :stroke-width="1" :size="16" class="q-mr-xs" />
                        通知人數: {{ bulletin.notifications.length }}
                      </div>
                    </div>
                  </q-item-section>
                </q-item>
              </template>

              <!-- 展開後顯示通知人員明細 -->
              <q-card-section class="q-pa-sm">
                <q-list dense class="recipient-list">
                  <q-item
                    v-for="notification in bulletin.notifications"
                    :key="`${notification.id}-${notification.target_id}`"
                    class="q-py-sm"
                  >
                    <q-item-section>
                      <div class="flex items-center">
                        <user :stroke-width="1" :size="16" class="q-mr-xs" />
                        <span class="text-weight-medium">{{
                          notification.target_name || notification.target_id
                        }}</span>
                      </div>
                      <div
                        class="flex items-center q-mt-xs text-caption text-grey-8 notification-meta"
                        v-if="notification.Sent"
                      >
                        <send :stroke-width="1" :size="16" class="q-mr-xs" />
                        發送: {{ formatDateTime(notification.Sent) }}
                      </div>
                    </q-item-section>

                    <!-- 狀態顯示和操作按鈕 -->
                    <q-item-section side>
                      <div class="row items-center">
                        <q-btn
                          flat
                          round
                          dense
                          :color="getResendButtonColor(notification.status)"
                          @click="resendNotification(notification)"
                          class="q-mr-xs"
                        >
                          <send :size="20" :stroke-width="1.5" />
                          <q-tooltip>{{
                            getResendButtonLabel(notification.status)
                          }}</q-tooltip>
                        </q-btn>
                        <q-badge
                          outline
                          :color="getStatusColor(notification.status)"
                          :label="getStatusLabel(notification.status)"
                          class="q-px-sm q-py-xs"
                        />
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card-section>
            </q-expansion-item>
          </q-list>
        </div>
      </q-card-section>
    </q-card>

    <!-- 進階搜尋對話框 -->
    <q-dialog v-model="advancedSearchDialog">
      <q-card style="width: 500px; max-width: 90vw">
        <q-card-section class="row items-center bg-primary text-white q-pb-xs">
          <div class="text-subtitle1">
            <q-icon name="search" class="q-mr-xs" size="sm" />
            進階搜尋
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-sm">
          <q-form>
            <!-- 日期區間 -->
            <q-input
              v-model="formattedDate"
              label="選擇日期區間"
              outlined
              readonly
              dense
              class="full-width"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="dateRange" range mask="YYYY-MM-DD">
                      <div
                        class="row items-center justify-end q-gutter-sm q-pa-sm"
                      >
                        <q-btn
                          label="清除"
                          color="grey"
                          flat
                          @click="clearDateRange"
                        />
                        <q-btn
                          label="確定"
                          color="primary"
                          flat
                          v-close-popup
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>

            <!-- 通知狀態 -->
            <q-select
              v-model="searchForm.status"
              :options="statusOptions"
              label="通知狀態"
              outlined
              dense
              class="q-mt-md"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            label="清除條件"
            color="red"
            flat
            @click="clearSearchFilters"
          />
          <q-btn
            label="取消"
            color="grey"
            flat
            @click="advancedSearchDialog = false"
          />
          <q-btn label="查詢" color="primary" @click="fetchNotifications" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 重新發送確認對話框 -->
    <q-dialog v-model="resendDialog">
      <q-card style="width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ getResendDialogTitle() }}</div>
        </q-card-section>

        <q-card-section>{{ getResendDialogMessage() }}</q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            flat
            :color="getResendButtonColor(selectedNotification?.status)"
            @click="confirmResend"
          >
            <send :size="16" :stroke-width="1.5" class="q-mr-xs" />
            {{ getResendButtonLabel(selectedNotification?.status) }}
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 刪除歷史記錄對話框 -->
    <q-dialog v-model="deleteHistoryDialog">
      <q-card style="width: 400px">
        <q-card-section class="row items-center bg-negative text-white">
          <div class="text-h6">
            <trash :size="20" :stroke-width="1.5" class="q-mr-xs" />
            刪除歷史通知記錄
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-md">
          <p>
            請選擇要刪除的日期範圍，系統將刪除此日期<strong>之前</strong>的所有通知記錄。
          </p>
          <q-input
            v-model="deleteBeforeDate"
            label="選擇日期"
            outlined
            dense
            readonly
          >
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <q-date v-model="deleteBeforeDate" mask="YYYY-MM-DD">
                    <div
                      class="row items-center justify-end q-gutter-sm q-pa-sm"
                    >
                      <q-btn label="確定" color="primary" flat v-close-popup />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </q-card-section>

        <q-card-section
          class="text-negative q-pt-none"
          v-if="deleteHistoryError"
        >
          {{ deleteHistoryError }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            flat
            label="刪除"
            color="negative"
            :loading="deleteHistoryLoading"
            :disable="!deleteBeforeDate"
            @click="confirmDeleteHistory"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";
import {
  Search,
  Files,
  CheckCircle,
  Clock,
  AlertCircle,
  User,
  Users,
  CalendarClock,
  Send,
  Trash,
  MessageSquareWarning,
} from "lucide-vue-next";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 基本狀態
const queryKeyword = ref("");
const activeTab = ref("all");
const notifications = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const dateRange = ref({ from: "", to: "" });
const advancedSearchDialog = ref(false);
const resendDialog = ref(false);
const selectedNotification = ref(null);

// 刪除歷史記錄相關狀態
const deleteHistoryDialog = ref(false);
const deleteBeforeDate = ref("");
const deleteHistoryLoading = ref(false);
const deleteHistoryError = ref("");

const searchForm = reactive({
  title: "",
  status: null,
});

// 通知狀態選項
const statusOptions = [
  { label: "全部", value: null },
  { label: "待發送", value: "pending" },
  { label: "已發送", value: "sent" },
  { label: "發送失敗", value: "failed" },
];

// 計算屬性
const totalNotifications = computed(() => notifications.value.length);

const sentNotifications = computed(() =>
  notifications.value.filter(
    (n) => n.status && n.status.toString().trim().toLowerCase() === "sent"
  )
);

const pendingNotifications = computed(() =>
  notifications.value.filter(
    (n) => n.status && n.status.toString().trim().toLowerCase() === "pending"
  )
);

const failedNotifications = computed(() =>
  notifications.value.filter(
    (n) => n.status && n.status.toString().trim().toLowerCase() === "failed"
  )
);

// 過濾通知
const filteredNotifications = computed(() => {
  let result = [...notifications.value];

  // 根據標籤過濾
  switch (activeTab.value) {
    case "sent":
      result = result.filter(
        (n) => n.status && n.status.toString().trim().toLowerCase() === "sent"
      );
      break;
    case "pending":
      result = result.filter(
        (n) =>
          n.status && n.status.toString().trim().toLowerCase() === "pending"
      );
      break;
    case "failed":
      result = result.filter(
        (n) => n.status && n.status.toString().trim().toLowerCase() === "failed"
      );
      break;
  }

  return result;
});

const totalPages = computed(() => {
  return Math.ceil(
    groupedAndFilteredNotifications.value.length / itemsPerPage.value
  );
});

const paginatedNotifications = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  return filteredNotifications.value.slice(start, start + itemsPerPage.value);
});

// 將通知按公告ID分組
const groupedNotifications = computed(() => {
  const grouped = {};
  filteredNotifications.value.forEach((notification) => {
    const bulletinId = notification.id;
    if (!grouped[bulletinId]) {
      grouped[bulletinId] = {
        id: bulletinId,
        created: notification.Created,
        title: notification.bulletin_title || "",
        notifications: [],
      };
    }
    grouped[bulletinId].notifications.push(notification);
  });
  return Object.values(grouped).sort(
    (a, b) => new Date(b.created) - new Date(a.created)
  );
});

// 分頁後的分組通知
const groupedAndFilteredNotifications = computed(() => {
  return groupedNotifications.value;
});

// 分頁後的分組通知
const paginatedGroupedNotifications = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  return groupedAndFilteredNotifications.value.slice(
    start,
    start + itemsPerPage.value
  );
});

const formattedDate = computed(() => {
  if (!dateRange.value) return "請選擇日期";
  if (typeof dateRange.value === "string") return dateRange.value;
  if (!dateRange.value.from) return "請選擇日期";
  return dateRange.value.from === dateRange.value.to
    ? dateRange.value.from
    : `${dateRange.value.from} ~ ${dateRange.value.to}`;
});

// 方法
const handleQueryChange = () => {
  fetchNotifications();
};

// 進階搜尋相關方法
const openAdvancedSearch = () => {
  advancedSearchDialog.value = true;
};

// 清除日期範圍
const clearDateRange = () => {
  dateRange.value = { from: "", to: "" };
};

// 清除搜尋條件
const clearSearchFilters = () => {
  dateRange.value = { from: "", to: "" };
  searchForm.status = null;
  queryKeyword.value = "";
  fetchNotifications();
  advancedSearchDialog.value = false;
};

// 獲取通知狀態標籤
const getStatusLabel = (status) => {
  if (!status) return "未知狀態";

  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "pending":
      return "待發送";
    case "sent":
      return "已發送";
    case "failed":
      return "發送失敗";
    default:
      console.log("未識別的狀態值:", status);
      return "未知狀態";
  }
};

// 獲取通知狀態顏色
const getStatusColor = (status) => {
  if (!status) return "grey";

  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "pending":
      return "orange";
    case "sent":
      return "green";
    case "failed":
      return "red";
    default:
      return "grey";
  }
};

// 格式化日期時間
const formatDateTime = (dateString) => {
  if (!dateString) return "無日期";

  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 獲取重新發送按鈕顏色
const getResendButtonColor = (status) => {
  if (!status) return "primary";

  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "negative";
    case "pending":
      return "warning";
    case "sent":
      return "primary";
    default:
      return "primary";
  }
};

// 獲取重新發送按鈕文字
const getResendButtonLabel = (status) => {
  if (!status) return "重新發送";

  const normalizedStatus = status.toString().trim().toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "重新發送";
    case "pending":
      return "立即發送";
    case "sent":
      return "再次通知";
    default:
      return "重新發送";
  }
};

// 獲取對話框標題
const getResendDialogTitle = () => {
  if (!selectedNotification.value || !selectedNotification.value.status) {
    return "發送通知";
  }

  const normalizedStatus = selectedNotification.value.status
    .toString()
    .trim()
    .toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "重新發送失敗通知";
    case "pending":
      return "立即發送待處理通知";
    case "sent":
      return "再次發送通知";
    default:
      return "發送通知";
  }
};

// 獲取對話框信息
const getResendDialogMessage = () => {
  if (!selectedNotification.value || !selectedNotification.value.status) {
    return "確定要發送此通知嗎？";
  }

  const normalizedStatus = selectedNotification.value.status
    .toString()
    .trim()
    .toLowerCase();

  switch (normalizedStatus) {
    case "failed":
      return "此通知先前發送失敗，確定要重新嘗試發送嗎？";
    case "pending":
      return "此通知尚未發送，確定要立即發送嗎？";
    case "sent":
      return "此通知已發送過，確定要再次發送給使用者嗎？";
    default:
      return "確定要發送此通知嗎？";
  }
};

// 重新發送通知
const resendNotification = (notification) => {
  selectedNotification.value = notification;
  resendDialog.value = true;
};

// 確認重新發送
const confirmResend = async () => {
  if (!selectedNotification.value) return;

  try {
    loading.value = true;
    const response = await apiClient.post(
      `${apiBaseUrl}/btin/resend-notification`,
      {
        id: selectedNotification.value.id,
        target_id: selectedNotification.value.target_id,
      }
    );

    // 檢查響應格式
    if (response.data && response.data.success) {
      $q.notify({
        type: "positive",
        message: "通知已重新發送",
        icon: "check_circle",
      });
      await fetchNotifications();
    } else {
      throw new Error((response.data && response.data.message) || "發送失敗");
    }
  } catch (error) {
    console.error("重新發送通知失敗:", error);
    $q.notify({
      type: "negative",
      message: `重新發送通知失敗: ${error.message}`,
      icon: "error",
    });
  } finally {
    loading.value = false;
    resendDialog.value = false;
  }
};

// 獲取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true;
    const params = {
      keyword: queryKeyword.value || "",
      date_from: dateRange.value.from || null,
      date_to: dateRange.value.to || null,
      status: searchForm.status?.value || null,
    };

    const response = await apiClient.get(
      `${apiBaseUrl}/btin/get-notifications`,
      {
        params,
      }
    );

    // 後端直接返回陣列，不是包裝在 {success: true, data: [...]} 中
    const data = response.data;
    if (Array.isArray(data)) {
      notifications.value = data.map((item) => ({
        ...item,
        Created: item.Created,
        Sent: item.Sent,
      }));
    } else {
      notifications.value = [];
      console.error("查詢結果不是陣列，已設定為空陣列", data);
      $q.notify({
        type: "negative",
        message: "查詢結果格式錯誤",
        icon: "error",
      });
    }
  } catch (error) {
    console.error("查詢通知失敗:", error);
    $q.notify({
      type: "negative",
      message: "查詢通知失敗",
      icon: "error",
      caption: error.response?.data?.error || error.message,
    });
    notifications.value = [];
  } finally {
    loading.value = false;
    advancedSearchDialog.value = false;
  }
};

// 打開刪除歷史記錄對話框
const openDeleteHistoryDialog = () => {
  deleteHistoryDialog.value = true;
  deleteHistoryError.value = "";
  deleteBeforeDate.value = "";
};

// 確認刪除歷史記錄
const confirmDeleteHistory = async () => {
  if (!deleteBeforeDate.value) {
    deleteHistoryError.value = "請選擇日期";
    return;
  }

  try {
    deleteHistoryLoading.value = true;
    deleteHistoryError.value = "";

    const response = await apiClient.post(
      `${apiBaseUrl}/btin/delete-notifications-history`,
      {
        date_before: deleteBeforeDate.value,
      }
    );

    // 檢查響應格式
    if (response.data && response.data.success) {
      $q.notify({
        type: "positive",
        message: `成功刪除 ${response.data.deletedCount || 0} 筆歷史通知記錄`,
        icon: "check_circle",
      });
      deleteHistoryDialog.value = false;
      await fetchNotifications();
    } else {
      throw new Error((response.data && response.data.message) || "刪除失敗");
    }
  } catch (error) {
    console.error("刪除歷史記錄失敗:", error);
    deleteHistoryError.value = `刪除失敗: ${error.message}`;
    $q.notify({
      type: "negative",
      message: `刪除歷史記錄失敗: ${error.message}`,
      icon: "error",
    });
  } finally {
    deleteHistoryLoading.value = false;
  }
};

// 組件載入時獲取通知列表
onMounted(async () => {
  await fetchNotifications();
});

// 判斷是否有待發送的通知
const hasPendingNotifications = (notifications) => {
  return notifications.some(
    (n) => n.status && n.status.toString().trim().toLowerCase() === "pending"
  );
};

// 獲取通知狀態數量
const getStatusCount = (notifications, status) => {
  return notifications.filter(
    (n) => n.status && n.status.toString().trim().toLowerCase() === status
  ).length;
};
</script>

<style scoped>
/* 基本樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.search-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  transition: all 0.3s ease;
}

.search-input:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.all-card::before {
  background-color: #4caf50;
}

.all-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.sent-card::before {
  background-color: #1976d2;
}

.sent-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.pending-card::before {
  background-color: #ff9800;
}

.pending-card .stats-icon {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.failed-card::before {
  background-color: #f44336;
}

.failed-card .stats-icon {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 空狀態 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  font-weight: 500;
}

/* 通知列表樣式 */
.notification-list-container {
  position: relative;
}

.notification-list {
  margin: 0;
  padding: 0;
}

.notification-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pending-notification {
  border-left: 4px solid #ff9800;
}

.bulletin-header {
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #eee;
}

.notification-title {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  padding: 0 2px;
}

.notification-meta {
  font-size: 11px;
}

.notification-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recipient-list {
  padding: 0;
  margin: 0;
}

.recipient-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.recipient-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.recipient-item:last-child {
  margin-bottom: 0;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }
}

/* 響應式調整 */
@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .stats-card {
    padding: 8px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 16px;
  }

  .stats-label {
    font-size: 10px;
  }
}

/* 超小螢幕響應式調整 */
@media (max-width: 340px) {
  .stats-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }

  .stats-value {
    font-size: 14px;
  }

  .stats-label {
    font-size: 9px;
  }
}
</style>
