const express = require("express");
const {
  getOmenum,
  createOmenum,
  updateOmenum,
  deleteOmenum,
  getOmenus,
  getCclasses,
  getKitchens,
  getUnits,
  getRmnames,
  getSpecs,
  getTasteGroups,
  getMenus,
  upsertTasteMap,
  getTasteMap,
  checkCodeUnique,
  saveOmenum,
  insertSpec,
  deleteSpec,
  getProductsWithRcode,
  searchProductsWithRcode,
  getProductsByRcode,
  insertRmname,
  deleteRmname,
} = require("../controllers/o_menumController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.use(authMiddleware);

router.get("/get_Cclasses", getCclasses);
router.get("/get_Kitchens", getKitchens);
router.get("/get_Units", getUnits);
router.get("/get_Rmnames", getRmnames);
router.get("/get_Specs", getSpecs);
router.post("/insert_Spec", insertSpec);
router.post("/delete_Spec", deleteSpec);
router.post("/insert_Rmname", insertRmname);
router.post("/delete_Rmname", deleteRmname);
router.get("/get_TasteGroups", getTasteGroups);
router.get("/get_Menus", getMenus);
router.get("/get_TasteMap", getTasteMap);
router.post("/upsert_TasteMap", upsertTasteMap);
router.get("/get_Omenus", getOmenus);
router.get("/get_Omenum", getOmenum);
router.post("/create_Omenum", createOmenum);
router.put("/update_Omenum", updateOmenum);
router.delete("/delete_Omenum/:code", deleteOmenum);
router.get("/check_code_unique", checkCodeUnique);
router.post("/save_Omenum", saveOmenum);
router.get("/get_products_with_rcode", getProductsWithRcode);
router.get("/search_products_with_rcode", searchProductsWithRcode);
router.get("/get_products_by_rcode", getProductsByRcode);

module.exports = router;
