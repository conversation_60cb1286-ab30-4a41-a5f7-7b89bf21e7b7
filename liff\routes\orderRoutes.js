const express = require("express");
const {
  getOrders,
  updateStatus,
  getOrderotherpay,
  getOrdermainn,
} = require("../controllers/orderController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_Orders", authMiddleware, getOrders); // ✅ 讀取 `/api/order`
router.post("/update_Status", authMiddleware, updateStatus); // ✅ 更新 `/api/order`
router.get("/get_Orderotherpay", authMiddleware, getOrderotherpay); // ✅ 讀取 `/api/order`
router.get("/get_Ordermainn", authMiddleware, getOrdermainn); // ✅ 讀取 `/api/order`
module.exports = router;
