const mssql = require("mssql");
const dbConfig = require("../config/db");
const mysqlPool = require("../config/mysqldb");
const phpUnserialize = require("php-unserialize");

// 清理假別名稱，移除 PHP 序列化的殘留部分
function cleanLeaveTypeName(name) {
  if (!name) return "";
  return name.replace(/;[is]:\d+;?.*$/, "").trim();
}

// 解析 PHP 序列化數據
function parsePhpData(str) {
  if (!str || typeof str !== "string") return null;

  str = str.trim();

  try {
    // 檢查是否是 PHP 序列化格式
    if (str.match(/^[adbis]:\d+:/)) {
      // 嘗試使用 php-unserialize 庫解析
      return phpUnserialize.unserialize(str);
    }
    // 檢查是否是 print_r 輸出格式
    else if (str.includes("Array") || str.includes("=>")) {
      // 將 print_r 輸出轉換為 JSON 格式
      return parsePrintRFormat(str);
    }
    return null;
  } catch (err) {
    console.error("解析 PHP 數據失敗:", err);
    return null;
  }
}

// 解析 PHP print_r 輸出格式
function parsePrintRFormat(str) {
  // 清理字符串，移除多餘的空格和換行
  str = str.trim();

  // 添加 Array( 和 ) 如果需要
  if (!str.startsWith("Array")) {
    str = `Array(\n${str}\n)`;
  }

  // 解析函數
  function parseArray(lines, startIndex) {
    const result = {};
    let i = startIndex;

    // 跳過 "Array (" 行
    if (lines[i].trim().startsWith("Array")) {
      i++;
    }

    while (i < lines.length) {
      const line = lines[i].trim();

      // 檢查是否到達數組結束
      if (line === ")") {
        return { result, endIndex: i };
      }

      // 解析鍵值對
      const match = line.match(/\[([^\]]+)\]\s*=>\s*(.*)/);
      if (match) {
        const keyStr = match[1].trim();
        const value = match[2].trim();

        // 處理鍵 - 可能帶引號或數字
        let key = keyStr.replace(/^['"]|['"]$/g, "");
        // 如果鍵看起來像數字，轉換它
        if (/^\d+$/.test(key)) {
          key = parseInt(key);
        }

        if (value === "Array") {
          // 遞歸解析子數組
          i++; // 跳過 "Array (" 行
          const { result: subArray, endIndex } = parseArray(lines, i);
          result[key] = subArray;
          i = endIndex;
        } else {
          // 解析基本類型
          if (value === "") {
            result[key] = "";
          } else if (value === "Array()") {
            result[key] = {};
          } else if (/^\d+$/.test(value)) {
            result[key] = parseInt(value);
          } else if (/^\d+\.\d+$/.test(value)) {
            result[key] = parseFloat(value);
          } else {
            // 字符串，移除引號
            result[key] = value.replace(/^['"]|['"]$/g, "");
          }
        }
      }

      i++;
    }

    return { result, endIndex: lines.length - 1 };
  }

  try {
    // 按行分割
    const lines = str.split("\n");
    // 解析根數組
    const { result } = parseArray(lines, 0);
    return result;
  } catch (err) {
    console.error("解析 print_r 格式失敗:", err);
    return null;
  }
}

// 獲取假別信息
exports.getLeaveInfo = async (req, res) => {
  const { userId, month } = req.query;

  if (!userId) {
    return res.status(400).json({ error: "缺少 userId" });
  }

  try {
    // 解析月份，默認為當前月份
    let targetYear, targetMonth;
    if (month) {
      [targetYear, targetMonth] = month.split("-");
      targetYear = parseInt(targetYear);
      targetMonth = parseInt(targetMonth);
    } else {
      const now = new Date();
      targetYear = now.getFullYear();
      targetMonth = now.getMonth() + 1; // JavaScript月份從0開始
    }

    // 查詢員工基本資料 (使用 MySQL)
    // console.log("開始查詢員工信息，userId:", userId);
    const [userRows] = await mysqlPool.query(
      `SELECT 
        u.id AS user_hr_id,
        u.sn AS employee_id,
        u.name AS employee_name,
        u.arrivedate AS arrive_date,
        u.user_type_id,
        ut.name AS user_type_name,
        ut.alv_count_type AS annual_leave_count_type,
        ut.id AS ut_id
      FROM 
        users u
      LEFT JOIN 
        user_types ut ON u.user_type_id = ut.id
      WHERE 
        u.sn = ?`,
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({ error: "找不到該員工" });
    }

    const user = userRows[0];
    // console.log("員工完整信息:", user);

    // 計算年資
    const arriveDate = new Date(user.arrive_date);
    const today = new Date();
    const yearsOfService = Math.floor(
      (today - arriveDate) / (365.25 * 24 * 60 * 60 * 1000)
    );

    // 獲取假別設置
    const [settingsRows] = await mysqlPool.query(
      "SELECT `key`, value FROM settings WHERE type = 'S' AND `key` IN ('alv_count_type', 'actual_type', 'actualTypeInfo', 'lvMaxDay', 'lvCondition')"
    );

    if (!settingsRows || settingsRows.length === 0) {
      return res.status(404).json({ error: "找不到假別設置" });
    }

    // 處理設置
    const settings = {};
    for (const row of settingsRows) {
      const parsedData = parsePhpData(row.value);
      if (parsedData) {
        settings[row.key] = parsedData;
      }
    }

    // 調試輸出 - 打印關鍵數據
    // console.log("===== 調試輸出開始 =====");
    // console.log(
    //   "alv_count_type:",
    //   JSON.stringify(settings.alv_count_type, null, 2)
    // );
    // console.log("actual_type:", JSON.stringify(settings.actual_type, null, 2));
    // console.log(
    //   "actualTypeInfo:",
    //   JSON.stringify(settings.actualTypeInfo, null, 2)
    // );
    // console.log("lvMaxDay:", JSON.stringify(settings.lvMaxDay, null, 2));
    // console.log("lvCondition:", JSON.stringify(settings.lvCondition, null, 2));
    // console.log("===== 調試輸出結束 =====");

    // 獲取員工類別名稱和年資類別
    const employeeTypeId = user.user_type_id;
    // console.log(
    //   "原始 user.annual_leave_count_type:",
    //   user.annual_leave_count_type
    // );
    // console.log(
    //   "annual_leave_count_type 的類型:",
    //   typeof user.annual_leave_count_type
    // );

    // 確保 employeeTypeKey 是字符串
    let employeeTypeKey = null;
    if (
      user.annual_leave_count_type !== null &&
      user.annual_leave_count_type !== undefined
    ) {
      employeeTypeKey = user.annual_leave_count_type.toString();
    } else {
      employeeTypeKey = employeeTypeId.toString();
    }

    const employeeType =
      user.user_type_name ||
      (settings.alv_count_type
        ? settings.alv_count_type[employeeTypeId] || "未知"
        : "未知");

    // console.log(
    //   `員工類型ID: ${employeeTypeId}, 員工類型名稱: ${employeeType}, 年資類別: ${employeeTypeKey}`
    // );

    // 檢查 lvMaxDay 中是否有此年資類別的數據
    // if (settings.lvMaxDay) {
    //   console.log("lvMaxDay 中的所有鍵:", Object.keys(settings.lvMaxDay));
    //   console.log(
    //     `lvMaxDay 中是否有 ${employeeTypeKey} 的數據:`,
    //     !!settings.lvMaxDay[employeeTypeKey]
    //   );
    // }

    // 計算特休假天數
    let annualLeaveDays = 0;
    if (
      settings.lvMaxDay &&
      settings.lvMaxDay[employeeTypeKey] &&
      settings.lvMaxDay[employeeTypeKey]["0"]
    ) {
      const annualLeaveRules = settings.lvMaxDay[employeeTypeKey]["0"];
      //console.log("特休假規則:", JSON.stringify(annualLeaveRules, null, 2));

      // 找出最接近但不超過年資的規則
      let closestYear = 0;
      for (const year in annualLeaveRules) {
        const yearNum = parseFloat(year);
        if (
          !isNaN(yearNum) &&
          yearNum <= yearsOfService &&
          yearNum > closestYear
        ) {
          closestYear = yearNum;
          // console.log(
          //   `找到符合年資的規則: ${yearNum} 年 -> ${annualLeaveRules[yearNum]} 天`
          // );
        }
      }

      if (closestYear > 0) {
        annualLeaveDays = parseInt(annualLeaveRules[closestYear]) || 0;
        // console.log(
        //   `最終特休假天數: ${annualLeaveDays} 天 (基於 ${closestYear} 年資)`
        // );
      } else {
        //console.log(`未找到符合年資 ${yearsOfService} 年的特休假規則`);
      }
    } else {
      // console.log("找不到特休假規則，可能原因:");
      // console.log(`- settings.lvMaxDay 存在: ${!!settings.lvMaxDay}`);
      // console.log(
      //   `- settings.lvMaxDay[${employeeTypeKey}] 存在: ${
      //     settings.lvMaxDay ? !!settings.lvMaxDay[employeeTypeKey] : false
      //   }`
      // );
      // console.log(
      //   `- settings.lvMaxDay[${employeeTypeKey}]["0"] 存在: ${
      //     settings.lvMaxDay && settings.lvMaxDay[employeeTypeKey]
      //       ? !!settings.lvMaxDay[employeeTypeKey]["0"]
      //       : false
      //   }`
      // );
    }

    // 查詢年度結算的特休時間 (從總部主機)
    let lastYearSpecialLeaveHours = 0;
    let lastYearOvertimeLeaveHours = 0;

    try {
      const pool = await mssql.connect(dbConfig);
      const result = await pool
        .request()
        .input("id", mssql.VarChar, userId)
        .input("year", mssql.Int, targetYear)
        .query(
          `SELECT sv, cl FROM Users_Vacation WHERE id = @id AND year = @year`
        );

      if (result.recordset && result.recordset.length > 0) {
        lastYearSpecialLeaveHours = result.recordset[0].sv || 0;
        lastYearOvertimeLeaveHours = result.recordset[0].cl || 0;
      }
    } catch (dbErr) {
      console.error("查詢年度結算特休時間失敗:", dbErr);
      // 繼續執行，使用默認值0
    }

    // 計算特休假天數 (基於到職日期)
    let annualSpecialLeaveHours = annualLeaveDays * 8; // 將天數轉換為小時

    // 檢查選擇的月份是否已經到達新年度的特休生效日期

    // 獲取到職日期的月和日
    const arriveMonth = arriveDate.getMonth();
    const arriveDay = arriveDate.getDate();

    // 創建選擇年份的周年日期
    const anniversaryDate = new Date(targetYear, arriveMonth, arriveDay);

    // 創建一個基於選擇月份的日期對象，設置為該月的1號
    const selectedDate = new Date(targetYear, targetMonth - 1, 1);

    // 判斷年度特休是否可用
    // 如果選擇的日期已經達到或超過周年日期，則年度特休可用
    const isAnnualLeaveAvailable = selectedDate >= anniversaryDate;

    // 不再將 annualSpecialLeaveHours 設為 0，而是保留實際值

    // 查詢本年度已休特休時數
    let usedSpecialLeaveHours = 0;
    let monthlySpecialLeaveHours = 0;
    // 初始化加班補休相關變數
    let currentYearOvertimeHours = 0;
    let usedOvertimeLeaveHours = 0;
    let monthlyOvertimeLeaveHours = 0;

    try {
      // 獲取人資系統用戶ID
      const [userIdMapping] = await mysqlPool.query(
        `SELECT id FROM users WHERE sn = ?`,
        [userId]
      );

      if (userIdMapping.length > 0) {
        const hrUserId = userIdMapping[0].id;

        // 查詢本年度已休特休時數 (改為查詢到選擇月份為止)
        const [totalUsedResults] = await mysqlPool.query(
          `SELECT SUM(eff_hours) as total_hours 
           FROM leave_records 
           WHERE user_id = ? 
           AND leave_type_id = 5
           AND YEAR(lv_date) = ?
           AND MONTH(lv_date) <= ?`,
          [hrUserId, targetYear, targetMonth]
        );

        if (
          totalUsedResults.length > 0 &&
          totalUsedResults[0].total_hours !== null
        ) {
          usedSpecialLeaveHours = parseFloat(totalUsedResults[0].total_hours);
        }

        // 查詢本月使用特休時數
        const [monthlyUsedResults] = await mysqlPool.query(
          `SELECT SUM(eff_hours) as monthly_hours 
           FROM leave_records 
           WHERE user_id = ? 
           AND leave_type_id = 5
           AND YEAR(lv_date) = ?
           AND MONTH(lv_date) = ?`,
          [hrUserId, targetYear, targetMonth]
        );

        if (
          monthlyUsedResults.length > 0 &&
          monthlyUsedResults[0].monthly_hours !== null
        ) {
          monthlySpecialLeaveHours = parseFloat(
            monthlyUsedResults[0].monthly_hours
          );
        }

        // 查詢本年度已使用補休時數 (改為查詢到選擇月份為止)
        const [usedOvertimeResults] = await mysqlPool.query(
          `SELECT SUM(eff_hours) as total_hours 
           FROM leave_records 
           WHERE user_id = ? 
           AND leave_type_id = 19
           AND YEAR(lv_date) = ?
           AND MONTH(lv_date) <= ?`,
          [hrUserId, targetYear, targetMonth]
        );

        if (
          usedOvertimeResults.length > 0 &&
          usedOvertimeResults[0].total_hours !== null
        ) {
          usedOvertimeLeaveHours = parseFloat(
            usedOvertimeResults[0].total_hours
          );
        }
      }
    } catch (leaveErr) {
      console.error("查詢已休特休時數失敗:", leaveErr);
      // 繼續執行，使用默認值0
    }

    // 查詢本年度加班補休時數
    try {
      // 獲取人資系統用戶ID
      const [userIdMapping] = await mysqlPool.query(
        `SELECT id FROM users WHERE sn = ?`,
        [userId]
      );

      if (userIdMapping.length > 0) {
        const hrUserId = userIdMapping[0].id;

        // 查詢本年度加班補休時數
        const [overtimeResults] = await mysqlPool.query(
          `SELECT 
             SUM(CASE 
                  WHEN use_type = 'leave' THEN hours 
                  WHEN use_type = 'paid' THEN leave_hours 
                  ELSE 0 
                END) as total_hours 
           FROM ot_records 
           WHERE user_id = ? 
           AND (use_type = 'leave' OR (use_type = 'paid' AND leave_hours > 0))
           AND YEAR(ot_date) = ?`,
          [hrUserId, targetYear]
        );

        if (
          overtimeResults.length > 0 &&
          overtimeResults[0].total_hours !== null
        ) {
          currentYearOvertimeHours = parseFloat(overtimeResults[0].total_hours);
        }

        // 查詢本月使用補休時數
        const [monthlyOvertimeResults] = await mysqlPool.query(
          `SELECT SUM(eff_hours) as monthly_hours 
           FROM leave_records 
           WHERE user_id = ? 
           AND leave_type_id = 19
           AND YEAR(lv_date) = ?
           AND MONTH(lv_date) = ?`,
          [hrUserId, targetYear, targetMonth]
        );

        if (
          monthlyOvertimeResults.length > 0 &&
          monthlyOvertimeResults[0].monthly_hours !== null
        ) {
          monthlyOvertimeLeaveHours = parseFloat(
            monthlyOvertimeResults[0].monthly_hours
          );
        }
      }
    } catch (overtimeErr) {
      console.error("查詢加班補休時數失敗:", overtimeErr);
      // 繼續執行，使用默認值0
    }

    // 構建響應數據
    const userInfo = {
      name: user.employee_name,
      employeeId: user.employee_id,
      arriveDate: user.arrive_date,
      yearsOfService: yearsOfService,
      employeeType: employeeType,
    };

    // 處理假別類型
    const leaveTypes = {};
    if (settings.actual_type) {
      // console.log("開始處理假別類型...");
      for (const typeId in settings.actual_type) {
        if (settings.actual_type.hasOwnProperty(typeId)) {
          const typeName = cleanLeaveTypeName(settings.actual_type[typeId]);
          //console.log(`處理假別: ${typeId} - ${typeName}`);
          let maxDays = 0;

          // 特殊處理特休假，使用年資計算
          if (typeId === "0") {
            // 假設 typeId = 0 是特休假
            maxDays = annualLeaveDays;
            //console.log(`特休假天數: ${maxDays} 天 (基於年資計算)`);
          } else {
            // 檢查 lvCondition 中是否有該假別的子項目定義
            const hasSubItemsInCondition =
              settings.lvCondition &&
              settings.lvCondition[employeeTypeKey] &&
              settings.lvCondition[employeeTypeKey][typeId];

            if (hasSubItemsInCondition) {
              // 有子項目的假別，maxDays設為null或0，前端顯示"詳情"
              maxDays = 0;
              // console.log(
              //   `假別 ${typeId} 在lvCondition中有子項目定義，前端將顯示詳情`
              // );
            } else {
              // 沒有子項目的假別，直接從 lvMaxDay 獲取
              if (
                settings.lvMaxDay &&
                settings.lvMaxDay[employeeTypeKey] &&
                settings.lvMaxDay[employeeTypeKey][typeId] !== undefined
              ) {
                const value = settings.lvMaxDay[employeeTypeKey][typeId];
                //console.log(`假別 ${typeId} 的設置值:`, JSON.stringify(value));

                maxDays =
                  typeof value === "object"
                    ? value[0]
                      ? parseFloat(value[0])
                      : 0
                    : parseFloat(value) || 0;
                //console.log(`假別 ${typeId} 的最終天數: ${maxDays} 天`);
              } else {
                //console.log(`找不到假別 ${typeId} 的天數設置`);
              }
            }
          }

          const typeInfo = {
            name: typeName,
            maxDays: maxDays,
            usedDays: 0, // 由於沒有 leave_usage 表，默認為0
            remainingDays: maxDays, // 由於沒有使用數據，剩餘天數等於最大天數
            hasSubItems: false, // 預設沒有子項目
            showDetails: false, // 預設不顯示詳情按鈕
          };

          // 檢查 lvCondition 中是否有該假別的子項目，如果有則建立子項目數據
          if (
            settings.lvCondition &&
            settings.lvCondition[employeeTypeKey] &&
            settings.lvCondition[employeeTypeKey][typeId]
          ) {
            const subConditions = settings.lvCondition[employeeTypeKey][typeId];
            // console.log(
            //   `找到假別 ${typeId} 在lvCondition中的子項目定義：`,
            //   JSON.stringify(subConditions)
            // );

            // 標記有子項目
            typeInfo.hasSubItems = true;
            typeInfo.subItems = {};
            typeInfo.showDetails = true; // 明確標記需要顯示詳情
            typeInfo.detailType = "subItems"; // 詳情類型為子項目
            // 修改假別名稱，添加詳情標記
            typeInfo.name = typeName;
            typeInfo.originalName = typeName; // 保留原始名稱
            typeInfo.hasDetailsMark = true; // 額外標記
            typeInfo.detailSymbol = "📋"; // 添加視覺標識符號
            typeInfo.displayName = `${typeInfo.detailSymbol} ${typeName}`; // 顯示名稱

            // 從 lvCondition 取得子項目定義，並對應 actualTypeInfo 中的名稱
            for (const subItemKey in subConditions) {
              // 查找子項目在 actualTypeInfo 中的名稱
              let subItemName = subItemKey; // 默認使用鍵名
              if (settings.actualTypeInfo && settings.actualTypeInfo[typeId]) {
                const typeInfoObj = settings.actualTypeInfo[typeId];
                // 遍歷所有lv*Type欄位
                for (const field in typeInfoObj) {
                  if (
                    /^lv.*Type$/.test(field) &&
                    typeInfoObj[field] &&
                    typeInfoObj[field][subItemKey]
                  ) {
                    subItemName = typeInfoObj[field][subItemKey];
                    if (typeof subItemName === "object" && subItemName.title) {
                      subItemName = subItemName.title;
                    }
                    break;
                  }
                }
              }
              // 從 lvCondition 中獲取對應的 lvMaxDay 索引
              const lvMaxDayIndex = subConditions[subItemKey];
              // console.log(
              //   `子項目 ${subItemKey} 的 lvMaxDay 索引: ${lvMaxDayIndex}`
              // );

              // 查詢 lvMaxDay 獲取天數
              let subItemDays = 0;
              if (
                settings.lvMaxDay &&
                settings.lvMaxDay[employeeTypeKey] &&
                settings.lvMaxDay[employeeTypeKey][typeId] &&
                settings.lvMaxDay[employeeTypeKey][typeId][lvMaxDayIndex] !==
                  undefined
              ) {
                subItemDays =
                  parseFloat(
                    settings.lvMaxDay[employeeTypeKey][typeId][lvMaxDayIndex]
                  ) || 0;
                // console.log(`子項目 ${subItemKey} 的天數: ${subItemDays} 天`);
              } else {
                //  console.log(`找不到子項目 ${subItemKey} 的天數設置`);
              }

              // 添加子項目信息
              typeInfo.subItems[subItemKey] = {
                name: subItemName,
                days: subItemDays,
                key: subItemKey,
                index: Object.keys(typeInfo.subItems).length + 1, // 為前端排序提供索引
                displayName: subItemName || subItemKey, // 確保有顯示名稱
              };
            }
          }

          leaveTypes[typeId] = typeInfo;
        }
      }
    }

    // 構建假期資訊
    const vacationInfo = {
      lastYearSpecialLeaveHours,
      lastYearOvertimeLeaveHours,
      annualSpecialLeaveHours,
      usedSpecialLeaveHours,
      monthlySpecialLeaveHours,
      currentYearOvertimeHours,
      usedOvertimeLeaveHours,
      monthlyOvertimeLeaveHours,
      isAnnualLeaveAvailable, // 添加年度特休可用標記
    };

    res.json({
      success: true,
      userInfo,
      vacationInfo,
      leaveInfo: {
        annualLeaveMaxDays: annualLeaveDays,
        leaveTypes: leaveTypes,
        detailsAvailable: true, // 告訴前端支持詳情功能
        detailsCount: Object.values(leaveTypes).filter(
          (type) => type.showDetails
        ).length, // 有詳情的假別數量
      },
      uiDisplay: {
        detailsAvailable: true,
        detailsSymbol: "📋", // 建議使用的詳情標識符號
        detailsLabel: "詳情", // 建議的按鈕文字
        detailsTypes: Object.keys(leaveTypes).filter(
          (id) => leaveTypes[id].hasSubItems
        ), // 有詳情的假別ID列表
      },
    });
  } catch (err) {
    console.error("獲取假別信息時出錯:", err);
    res.status(500).json({ error: "伺服器錯誤", message: err.message });
  }
};

// 獲取員工休假使用情況
exports.getLeaveUsage = async (req, res) => {
  const { userId, year, month } = req.query;

  if (!userId) {
    return res.status(400).json({ error: "缺少 userId" });
  }

  try {
    // 解析年月，默認為當前年月
    let targetYear = parseInt(year) || new Date().getFullYear();
    let targetMonth = parseInt(month) || new Date().getMonth() + 1;

    // 獲取人資系統用戶ID
    const [userIdMapping] = await mysqlPool.query(
      `SELECT id FROM users WHERE sn = ?`,
      [userId]
    );

    if (userIdMapping.length === 0) {
      return res.status(404).json({ error: "找不到該員工" });
    }

    const hrUserId = userIdMapping[0].id;

    // 查詢當月請假記錄
    const [leaveRecords] = await mysqlPool.query(
      `SELECT 
        lr.id,
        lr.user_id,
        lr.leave_type_id,
        lt.name as leave_type_name,
        lr.lv_date,
        lr.start_date,
        lr.end_date,
        lr.eff_hours,
        'approved' as status
      FROM 
        leave_records lr
      LEFT JOIN
        leave_types lt ON lr.leave_type_id = lt.id
      WHERE 
        lr.user_id = ? 
        AND YEAR(lr.lv_date) = ?
        AND MONTH(lr.lv_date) = ?
      ORDER BY 
        lr.lv_date DESC`,
      [hrUserId, targetYear, targetMonth]
    );

    // 請假記錄調試信息
    // console.log(
    //   `請假記錄調試 - 用戶ID: ${hrUserId}, 年份: ${targetYear}, 月份: ${targetMonth}`
    // );
    // console.log(`總請假記錄數: ${leaveRecords.length}`);
    // leaveRecords.forEach((record, index) => {
    //   console.log(`請假記錄 ${index + 1}:`, {
    //     id: record.id,
    //     date: record.lv_date,
    //     leave_type_id: record.leave_type_id,
    //     leave_type_name: record.leave_type_name,
    //     eff_hours: record.eff_hours,
    //     status: record.status,
    //   });
    // });

    // 查詢當月加班記錄
    const [overtimeRecords] = await mysqlPool.query(
      `SELECT 
        id,
        user_id,
        ot_date,
        start_date,
        end_date,
        hours,
        paid_hours,
        leave_hours,
        use_type,
        CASE 
          WHEN use_type = 'leave' THEN hours 
          WHEN use_type = 'paid' AND paid_hours IS NOT NULL THEN paid_hours
          WHEN use_type = 'paid' AND leave_hours IS NOT NULL THEN leave_hours
          WHEN use_type = 'paid' THEN hours
          ELSE 0 
        END as comp_hours
      FROM 
        ot_records
      WHERE 
        user_id = ? 
        AND YEAR(ot_date) = ?
        AND MONTH(ot_date) = ?
      ORDER BY 
        ot_date DESC`,
      [hrUserId, targetYear, targetMonth]
    );

    // // 調試信息
    // console.log(
    //   `加班記錄調試 - 用戶ID: ${hrUserId}, 年份: ${targetYear}, 月份: ${targetMonth}`
    // );
    // console.log(`總加班記錄數: ${overtimeRecords.length}`);
    // overtimeRecords.forEach((record, index) => {
    //   console.log(`記錄 ${index + 1}:`, {
    //     id: record.id,
    //     date: record.ot_date,
    //     hours: record.hours,
    //     paid_hours: record.paid_hours,
    //     leave_hours: record.leave_hours,
    //     use_type: record.use_type,
    //     comp_hours: record.comp_hours,
    //   });
    // });

    return res.status(200).json({
      success: true,
      year: targetYear,
      month: targetMonth,
      leaveUsage: leaveRecords,
      overtimeRecords: overtimeRecords,
    });
  } catch (err) {
    console.error("獲取休假使用情況時出錯:", err);
    return res.status(500).json({
      success: false,
      message: "獲取休假使用情況時出錯",
      error: err.message,
    });
  }
};

// 調試端點：獲取假別設置數據
exports.getLeaveSettings = async (req, res) => {
  try {
    // 獲取假別設置
    const [settingsRows] = await mysqlPool.query(
      "SELECT `key`, value FROM settings WHERE type = 'S' AND `key` IN ('alv_count_type', 'actual_type', 'actualTypeInfo', 'lvMaxDay', 'lvCondition')"
    );

    if (!settingsRows || settingsRows.length === 0) {
      return res.status(404).json({ error: "找不到假別設置" });
    }

    // 處理設置
    const settings = {};
    for (const row of settingsRows) {
      // 保存原始值
      settings[`${row.key}_raw`] = row.value.substring(0, 200) + "..."; // 只保存前200個字符

      // 解析值
      const parsedData = parsePhpData(row.value);
      if (parsedData) {
        settings[row.key] = parsedData;
      } else {
        settings[`${row.key}_parse_failed`] = true;
      }
    }

    res.json({
      success: true,
      settings: settings,
    });
  } catch (err) {
    console.error("獲取假別設置時出錯:", err);
    res.status(500).json({ error: "伺服器錯誤", message: err.message });
  }
};

// 獲取部門員工的休假統計
exports.getDepartmentLeaveStats = async (req, res) => {
  const { departmentId, year, month, userId } = req.query; // 直接從請求參數獲取 userId

  if (!departmentId) {
    return res.status(400).json({ error: "缺少部門ID" });
  }

  try {
    // 先檢查請求的用戶是否為該部門的主管
    const mssqlPool = await mssql.connect(dbConfig);
    const managerCheck = await mssqlPool
      .request()
      .input("userId", mssql.VarChar, userId)
      .input("depId", mssql.VarChar, departmentId)
      .query("SELECT Id FROM Dep WHERE Manager = @userId AND Id = @depId");

    // 如果不是主管，則拒絕訪問
    if (managerCheck.recordset.length === 0) {
      return res.status(403).json({ error: "您沒有權限查看此部門的休假統計" });
    }

    // 解析年月，默認為當前年月
    let targetYear = parseInt(year) || new Date().getFullYear();
    let targetMonth = parseInt(month) || new Date().getMonth() + 1;

    // 獲取部門內所有員工
    const departmentUsers = await mssqlPool
      .request()
      .input("depId", mssql.VarChar, departmentId)
      .query(
        "SELECT ID, Name FROM Users WHERE Dep = @depId AND Sts = '1' ORDER BY Name"
      );

    if (departmentUsers.recordset.length === 0) {
      return res.json({ users: [], message: "此部門沒有員工" });
    }

    // 準備返回數據結構
    let usersLeaveStats = [];

    // 對每個員工查詢休假統計
    for (const user of departmentUsers.recordset) {
      const employeeId = user.ID.trim();
      const employeeName = user.Name.trim();

      // 查詢MySQL資料庫獲取休假記錄
      const [userRows] = await mysqlPool.query(
        `SELECT id FROM users WHERE sn = ?`,
        [employeeId]
      );

      if (userRows.length > 0) {
        const hrUserId = userRows[0].id;

        // 查詢指定年月的休假記錄
        const [leaveRecords] = await mysqlPool.query(
          `SELECT 
            lr.leave_type_id, 
            lt.name as leave_type_name, 
            SUM(lr.eff_hours) as total_hours,
            COUNT(DISTINCT lr.lv_date) as days_count
          FROM leave_records lr
          JOIN leave_types lt ON lr.leave_type_id = lt.id
          WHERE lr.user_id = ? 
          AND YEAR(lr.lv_date) = ? 
          AND MONTH(lr.lv_date) = ?
          GROUP BY lr.leave_type_id, lt.name`,
          [hrUserId, targetYear, targetMonth]
        );

        // 整理該員工的休假記錄
        const leaveTypes = {};
        for (const record of leaveRecords) {
          leaveTypes[record.leave_type_id] = {
            name: cleanLeaveTypeName(record.leave_type_name),
            hours: parseFloat(record.total_hours) || 0,
            days: parseFloat(record.days_count) || 0,
          };
        }

        // 查詢MSSQL中的特休額度
        const specialLeaveResult = await mssqlPool
          .request()
          .input("id", mssql.VarChar, employeeId)
          .input("year", mssql.Int, targetYear)
          .query(
            `SELECT sv FROM Users_Vacation WHERE id = @id AND year = @year`
          );

        const specialLeaveHours =
          specialLeaveResult.recordset.length > 0
            ? parseFloat(specialLeaveResult.recordset[0].sv) || 0
            : 0;

        // 添加到結果集
        usersLeaveStats.push({
          employeeId,
          employeeName,
          specialLeaveHours, // 特休額度
          leaveTypes, // 各類休假使用情況
        });
      }
    }

    // 返回部門員工休假統計
    res.json({
      departmentId,
      year: targetYear,
      month: targetMonth,
      users: usersLeaveStats,
    });
  } catch (err) {
    console.error("獲取部門員工休假統計時出錯:", err);
    res.status(500).json({ error: "伺服器錯誤", message: err.message });
  }
};

// 調試端點：獲取 lvMaxDay 結構
exports.getLvMaxDayStructure = async (req, res) => {
  try {
    // 獲取 lvMaxDay 設置
    const [settingRow] = await mysqlPool.query(
      "SELECT value FROM settings WHERE type = 'S' AND `key` = 'lvMaxDay'"
    );

    if (!settingRow || settingRow.length === 0) {
      return res.status(404).json({ error: "找不到 lvMaxDay 設置" });
    }

    // 解析 lvMaxDay
    const lvMaxDayRaw = settingRow[0].value;
    const lvMaxDay = parsePhpData(lvMaxDayRaw);

    // 獲取所有 user_types
    const [userTypes] = await mysqlPool.query(
      "SELECT id, name, alv_count_type FROM user_types"
    );

    // 構建結果
    const result = {
      lvMaxDay: lvMaxDay,
      userTypes: userTypes,
      availableKeys: Object.keys(lvMaxDay || {}),
    };

    // 檢查每個用戶類型的 alv_count_type 是否在 lvMaxDay 中存在
    const typeChecks = [];
    for (const ut of userTypes) {
      const alvCountType = ut.alv_count_type;
      const alvCountTypeStr = alvCountType ? alvCountType.toString() : "";
      typeChecks.push({
        id: ut.id,
        name: ut.name,
        alv_count_type: alvCountType,
        alv_count_type_str: alvCountTypeStr,
        exists_in_lvMaxDay: lvMaxDay ? !!lvMaxDay[alvCountTypeStr] : false,
      });
    }
    result.typeChecks = typeChecks;

    res.json({
      success: true,
      result: result,
    });
  } catch (err) {
    console.error("獲取 lvMaxDay 結構時出錯:", err);
    res.status(500).json({ error: "伺服器錯誤", message: err.message });
  }
};
