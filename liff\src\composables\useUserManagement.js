import { ref, computed } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

export function useUserManagement() {
  const $q = useQuasar();

  // 響應式數據
  const users = ref([]);
  const selectedUser = ref({});
  const filteredUsers = ref([]);
  const currentPage = ref(1);
  const searchQuery = ref("");
  const isLoadingUsers = ref(false);
  const isSaving = ref(false);
  const isResettingPassword = ref(false);

  // 常數
  const ROWS_PER_PAGE = 5;
  const STATUS_OPTIONS = [
    { label: "審核", value: "0" },
    { label: "啟用", value: "1" },
    { label: "停用", value: "2" },
  ];

  // 計算屬性
  const totalPages = computed(() =>
    Math.ceil((filteredUsers.value || []).length / ROWS_PER_PAGE)
  );

  const paginatedUsers = computed(() => {
    const start = (currentPage.value - 1) * ROWS_PER_PAGE;
    return (filteredUsers.value || []).slice(start, start + ROWS_PER_PAGE);
  });

  // 工具函數
  const getStatusLabel = (sts) => {
    return (
      STATUS_OPTIONS.find((option) => option.value === sts)?.label || "未知"
    );
  };

  const getStatusColor = (sts) => {
    const colorMap = {
      0: "grey",
      1: "green",
      2: "red",
    };
    return colorMap[sts] || "blue";
  };

  // 防抖搜尋
  let searchTimeout = null;
  const debouncedSearch = () => {
    if (searchTimeout) clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      triggerSearch();
    }, 300);
  };

  // API 函數
  const fetchUsers = async () => {
    try {
      isLoadingUsers.value = true;
      const response = await apiClient.get("/users/get_all_users");

      users.value = response.data.map((user) => {
        return Object.fromEntries(
          Object.entries(user).map(([key, value]) => [
            key,
            typeof value === "string" ? value.trim() : value,
          ])
        );
      });
      filteredUsers.value = [...users.value];
    } catch (error) {
      console.error("載入使用者失敗:", error);
      $q.notify({ type: "negative", message: "載入使用者失敗" });
    } finally {
      isLoadingUsers.value = false;
    }
  };

  const selectUser = async (user) => {
    selectedUser.value = { ...user };
  };

  const resetSearch = () => {
    searchQuery.value = "";
    filteredUsers.value = [...users.value];
    currentPage.value = 1;
  };

  const triggerSearch = () => {
    if (!searchQuery.value.trim()) {
      resetSearch();
      return;
    }

    const keyword = searchQuery.value.toLowerCase();
    filteredUsers.value = users.value.filter(
      (user) =>
        (user.Name?.toLowerCase() || "").includes(keyword) ||
        (user.ID?.toLowerCase() || "").includes(keyword)
    );
    currentPage.value = 1;
  };

  // 表單驗證函數
  const validateForm = () => {
    if (!selectedUser.value.Uid) {
      $q.notify({ type: "negative", message: "請先選擇使用者" });
      return false;
    }

    if (!selectedUser.value.Name) {
      $q.notify({ type: "negative", message: "姓名為必填項目" });
      return false;
    }

    if (!selectedUser.value.ID) {
      $q.notify({ type: "negative", message: "工號為必填項目" });
      return false;
    }

    if (!selectedUser.value.Username) {
      $q.notify({ type: "negative", message: "使用者帳號為必填項目" });
      return false;
    }

    return true;
  };

  const submitForm = async (onSuccess) => {
    if (!validateForm()) {
      return;
    }

    try {
      isSaving.value = true;
      // 修改API路徑，使用PUT方法匹配後端路由
      const response = await apiClient.put(
        "/users/updateuser",
        selectedUser.value
      );

      if (response.data.success) {
        $q.notify({
          type: "positive",
          message: "使用者資料已儲存",
        });

        // 如果有回調函數，調用它並傳入用戶ID
        if (typeof onSuccess === "function") {
          await onSuccess(selectedUser.value.ID);
        }
      } else {
        $q.notify({
          type: "negative",
          message: response.data.message || "儲存使用者資料失敗",
        });
      }
    } catch (error) {
      console.error("儲存使用者資料錯誤:", error);
      $q.notify({
        type: "negative",
        message: "儲存錯誤",
      });
    } finally {
      isSaving.value = false;
    }
  };

  const resetPassword = async () => {
    if (!selectedUser.value.ID) {
      $q.notify({ type: "negative", message: "請先選擇使用者" });
      return;
    }

    try {
      isResettingPassword.value = true;
      const response = await apiClient.post("/auth/reset-password", {
        userId: selectedUser.value.ID.trim(),
      });

      if (response.data.success) {
        $q.notify({ type: "positive", message: response.data.message });
      } else {
        $q.notify({ type: "negative", message: response.data.message });
      }
    } catch (error) {
      console.error("重設密碼錯誤:", error);
      $q.notify({ type: "negative", message: "伺服器錯誤，請稍後再試" });
    } finally {
      isResettingPassword.value = false;
    }
  };

  const resetForm = () => {
    selectedUser.value = {};
  };

  return {
    // 響應式數據
    users,
    selectedUser,
    filteredUsers,
    currentPage,
    searchQuery,
    isLoadingUsers,
    isSaving,
    isResettingPassword,

    // 常數
    STATUS_OPTIONS,
    ROWS_PER_PAGE,

    // 計算屬性
    totalPages,
    paginatedUsers,

    // 工具函數
    getStatusLabel,
    getStatusColor,
    debouncedSearch,

    // API 函數
    fetchUsers,
    selectUser,
    resetSearch,
    triggerSearch,
    submitForm,
    resetPassword,
    resetForm,
  };
}
