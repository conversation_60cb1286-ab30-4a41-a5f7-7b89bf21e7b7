<template>
  <q-page class="q-pa-md" style="width: 100%; max-width: 100%">
    <div style="width: 100%; height: 100%">
      <!-- 📌 標題 -->
      <q-card-section class="text-center">
        <div class="text-h6 text-primary">開桌狀況</div>
      </q-card-section>

      <!-- 🔘 分頁選單 -->
      <q-card-section class="q-px-none q-pb-sm row items-center q-gutter-sm">
        <q-icon
          :name="connectionStatus === 'connected' ? 'wifi' : 'wifi_off'"
          :color="connectionStatus === 'connected' ? 'green' : 'red'"
          size="sm"
        >
          <q-tooltip>
            {{ connectionStatus === "connected" ? "已連線" : "斷線" }}
          </q-tooltip>
        </q-icon>

        <q-select
          v-model="currentPage"
          :options="pageOptions"
          emit-value
          map-options
          dense
          outlined
          class="col"
          :disable="pageOptions.length === 0"
          @update:model-value="fetchTables(sortBy)"
        />

        <q-btn
          flat
          round
          dense
          icon="settings"
          class="q-ml-sm"
          @click="dialogVisible = true"
        />
      </q-card-section>

      <!-- 📋 服務鈴提示 -->
      <q-card-section
        v-for="table in serviceCallTables"
        :key="table.code"
        class="q-pt-xs q-pb-sm"
      >
        <q-banner class="bg-red-1 text-negative" dense rounded>
          <template v-slot:avatar>
            <q-icon name="notification_important" color="negative" />
          </template>

          <div class="row items-center justify-between no-wrap">
            <div class="text-body2">
              {{ table.name }}桌 有服務鈴：{{ table.appmemo }}
            </div>

            <q-btn
              dense
              flat
              round
              icon="close"
              color="negative"
              size="sm"
              class="q-ml-sm"
              @click="cancelServiceCall(table.code)"
            />
          </div>
        </q-banner>
      </q-card-section>

      <!-- 📋 桌況清單（格狀排列） -->
      <q-card-section
        class="q-pa-sm scroll"
        style="flex: 1; overflow-y: auto"
        v-if="connectionStatus === 'connected'"
      >
        <div class="row q-col-gutter-sm">
          <div
            v-for="table in tables"
            :key="table.roomcode"
            class="col-xs-12 col-sm-6 col-md-4 col-lg-2-4"
          >
            <q-card
              class="q-pa-sm cursor-pointer flex column items-start justify-center"
              style="
                height: 65px;
                box-shadow: none;
                border: 1px solid #e0e0e0;
                position: relative;
              "
              @click="table.state !== '1' && onClickTable(table)"
            >
              <div class="row items-center q-gutter-sm no-wrap">
                <q-avatar
                  :icon="table.appmemo ? 'notifications_active' : 'restaurant'"
                  text-color="white"
                  size="40px"
                  :class="table.appmemo ? 'animated-bounce' : ''"
                  :style="{
                    backgroundColor:
                      table.state === '1' ? '#ff0000' : getTableColor(table),
                  }"
                />
                <div class="col">
                  <div class="text-subtitle2 text-weight-medium ellipsis">
                    {{ table.name }}
                  </div>
                  <div class="text-caption text-grey ellipsis">
                    <template v-if="table.state === '1'">停用</template>
                    <template v-else-if="table.code">
                      {{ table.rtime }}｜{{ table.person }}人｜${{
                        table.amount
                      }}
                    </template>
                    <template v-else>空桌</template>
                  </div>
                </div>
              </div>
              <q-icon
                :name="
                  table.appmemo
                    ? 'notifications_active'
                    : table.state === '1'
                    ? 'block'
                    : table.code
                    ? 'info'
                    : 'add'
                "
                :color="
                  table.appmemo
                    ? 'red'
                    : table.state === '1'
                    ? 'red'
                    : table.code
                    ? 'green'
                    : 'grey-7'
                "
                class="absolute q-mr-xs q-mt-xs"
                style="top: 4px; right: 4px"
                size="20px"
              />
            </q-card>
          </div>
        </div>
      </q-card-section>
    </div>

    <!-- 📦 Dialog 如有 -->
    <q-dialog v-model="showDialog">
      <q-card style="min-width: 320px; max-width: 90vw" class="q-pa-sm">
        <!-- 🔵 桌號標題 -->
        <q-card-section class="row items-center q-gutter-sm">
          <q-icon name="restaurant" color="deep-orange" size="30px" />
          <div class="text-h6">桌號：{{ selectedTable.name }}</div>
        </q-card-section>

        <!-- 📋 桌況明細 -->
        <q-separator />
        <q-card-section class="q-gutter-y-sm text-body2">
          <template v-if="selectedTable?.code">
            <div class="row justify-between">
              <span class="text-weight-medium">單號：</span>
              <span>{{ selectedTable.code }}</span>
            </div>
            <div class="row justify-between">
              <span class="text-weight-medium">人數：</span>
              <span>{{ selectedTable.person }}</span>
            </div>
            <div class="row justify-between">
              <span class="text-weight-medium">金額：</span>
              <span class="text-negative">
                ${{ Number(selectedTable.amount).toLocaleString() }}
              </span>
            </div>
            <div class="row justify-between">
              <span class="text-weight-medium">開桌時間：</span>
              <span>{{ selectedTable.rtime }}</span>
            </div>
            <div class="row justify-between">
              <span class="text-weight-medium">消費方式：</span>
              <span>{{ selectedTable.hidename }}</span>
            </div>
          </template>

          <template v-else>
            <div class="text-grey text-center q-my-md">尚未開桌</div>
          </template>
        </q-card-section>

        <!-- 🟦 操作按鈕 -->
        <q-separator spaced />
        <q-card-actions align="right" class="q-gutter-sm">
          <q-btn
            v-close-popup
            flat
            label="開鍋"
            color="primary"
            @click="
              openbase(
                selectedTable.code,
                selectedTable.roomcode,
                selectedTable.name,
                selectedTable.person
              )
            "
            :disable="!selectedTable?.code || !OpenTableOptions"
          />
          <q-btn
            label="點餐"
            color="primary"
            @click="emitOrder(selectedTable)"
          />
          <q-btn flat label="關閉" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="dialogVisible" persistent>
      <q-card style="width: 100%; max-width: 500px" class="q-pa-sm">
        <!-- 🔵 標題 -->
        <q-card-section class="bg-primary text-white text-center">
          <div class="text-h6">桌況設定</div>
        </q-card-section>

        <!-- 🕒 時間設定 -->
        <q-separator />
        <q-card-section class="bg-grey-1 rounded-borders q-mt-md q-pa-md">
          <div class="text-subtitle1 text-primary q-mb-sm">🕒 時間設定</div>

          <q-banner dense class="bg-grey-3 text-grey-9 q-mb-md">
            根據經過時間改變桌況顏色（分鐘 ≥ 顏色）
          </q-banner>

          <div class="q-gutter-y-sm">
            <div
              v-for="(rule, index) in timeColors"
              :key="index"
              class="row items-center q-col-gutter-sm q-mb-sm"
            >
              <div class="col-5 col-md-3">
                <q-input
                  v-model.number="rule.min"
                  type="number"
                  label="分鐘 ≥"
                  dense
                  outlined
                />
              </div>
              <div class="col-6 col-md-7">
                <q-input v-model="rule.color" label="顏色" dense outlined>
                  <template #append>
                    <q-icon name="colorize" :style="{ color: rule.color }">
                      <q-popup-proxy cover transition-show="scale">
                        <q-color
                          v-model="rule.color"
                          format="hex"
                          no-footer
                          default-view="palette"
                        />
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
              <div class="col-1">
                <q-btn
                  icon="delete"
                  flat
                  dense
                  size="sm"
                  color="negative"
                  @click="timeColors.splice(index, 1)"
                />
              </div>
            </div>

            <div class="q-mt-sm">
              <q-btn
                flat
                dense
                icon="add"
                label="新增時間區段"
                color="primary"
                @click="timeColors.push({ min: 0, color: '#cccccc' })"
              />
            </div>
          </div>

          <q-separator spaced />
          <q-input
            v-model.number="refreshInterval"
            type="number"
            min="3"
            label="自動更新間隔（秒）"
            dense
            outlined
            :rules="[(val) => val >= 3 || '不可小於 3 秒']"
            class="q-mt-md"
          />
        </q-card-section>

        <!-- 📋 顯示設定 -->
        <q-card-section class="bg-grey-1 rounded-borders q-mt-md q-pa-md">
          <div class="text-subtitle1 text-primary q-mb-sm">📋 顯示設定</div>
          <q-option-group
            v-model="sortBy"
            :options="[
              { label: '桌號排序：名稱', value: 'name' },
              { label: '桌號排序：位置', value: 'stable' },
            ]"
            type="radio"
            color="primary"
            inline
          />
        </q-card-section>

        <!-- 🖨️ 列印設定 -->
        <q-card-section class="bg-grey-1 rounded-borders q-mt-md q-pa-md">
          <div class="text-subtitle1 text-primary q-mb-sm">🖨️ 列印設定</div>

          <div class="row items-center q-col-gutter-md">
            <div class="col-auto">
              <q-toggle
                v-model="APIPrint"
                label="使用明訊 API 列印"
                color="primary"
                left-label
              />
            </div>

            <div class="col" v-if="APIPrint">
              <q-input
                v-model="hostName"
                label="電腦名稱"
                outlined
                dense
                :rules="[(val) => !!val || '請輸入電腦名稱']"
              />
            </div>
          </div>

          <q-separator spaced />

          <div class="row items-center q-col-gutter-md">
            <div class="col-auto">
              <q-toggle
                v-model="OpenTableOptions"
                label="開啟開桌選項"
                color="primary"
                left-label
              />
            </div>

            <div class="col-auto">
              <q-toggle
                v-model="singleItemMode"
                label="啟用單點模式"
                color="primary"
                left-label
              />
            </div>
          </div>

          <!-- ✅ 單點模式啟用後顯示 -->
          <div class="row q-col-gutter-md q-mt-sm" v-if="singleItemMode">
            <div class="col-6">
              <q-input
                v-model="singleItemCode"
                label="編號"
                outlined
                dense
                :rules="[(val) => !!val || '請輸入編號']"
              />
            </div>
            <div class="col-6">
              <q-input
                v-model="singleItemName"
                label="名稱"
                outlined
                dense
                :rules="[(val) => !!val || '請輸入名稱']"
              />
            </div>
          </div>
        </q-card-section>

        <!-- ✔️ 操作按鈕 -->
        <q-separator spaced />
        <q-card-actions align="right" class="q-gutter-sm">
          <q-btn label="取消" flat v-close-popup />
          <q-btn
            label="儲存"
            color="primary"
            @click="saveSettings"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="opentableDialog">
      <q-card style="min-width: 300px; max-width: 90vw">
        <q-card-section class="row items-center q-gutter-sm">
          <q-icon name="restaurant" color="deep-orange" size="30px" />
          <div class="text-h6">桌號：{{ selectedRoomName }}</div>
        </q-card-section>

        <q-separator />

        <q-card-section class="q-gutter-md">
          <!-- 🔸 消費方式 -->
          <q-select
            v-model="form.eatway"
            :options="eatwayOptions"
            label="消費方式"
            dense
            outlined
            class="q-mt-md"
            :disable="singleItemMode"
          >
            <template #prepend>
              <q-icon name="local_dining" color="primary" class="q-mr-sm" />
            </template>
          </q-select>
          <!-- 🔹 大人 -->
          <div class="row justify-center items-center q-gutter-sm q-py-xs">
            <div class="text-subtitle2" style="width: 60px">大人</div>

            <q-btn
              flat
              round
              icon="remove"
              color="primary"
              @click="form.adult = Math.max(0, form.adult - 1)"
            />

            <q-input
              v-model.number="form.adult"
              readonly
              dense
              outlined
              style="width: 80px; font-size: 18px"
              input-class="text-center"
            />

            <q-btn
              flat
              round
              icon="add"
              color="primary"
              @click="form.adult++"
            />
          </div>

          <!-- 🔹 小孩 -->
          <div class="row justify-center items-center q-gutter-sm q-py-xs">
            <div class="text-subtitle2" style="width: 60px">小孩</div>

            <q-btn
              flat
              round
              icon="remove"
              color="primary"
              @click="form.child = Math.max(0, form.child - 1)"
            />

            <q-input
              v-model.number="form.child"
              readonly
              dense
              outlined
              style="width: 80px; font-size: 18px"
              input-class="text-center"
            />

            <q-btn
              flat
              round
              icon="add"
              color="primary"
              @click="form.child++"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn
            v-close-popup
            unelevated
            color="positive"
            label="確認開桌"
            :disable="
              !form.eatway ||
              form.adult < 0 ||
              form.child < 0 ||
              form.adult + form.child === 0
            "
            @click="submitOpenTable"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="soupDialogVisible" persistent>
      <q-card
        style="
          width: 95vw;
          max-width: 540px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
        "
      >
        <!-- 🏷️ 標題 -->
        <q-card-section class="text-h6 text-center text-primary q-pa-sm">
          開桌選項
        </q-card-section>

        <!-- 主體卷軸區 -->
        <div
          class="q-pa-sm scroll q-pl-md q-pr-md"
          style="flex: 1; overflow-y: auto"
        >
          <!-- 🍲 湯底 -->
          <div class="q-mb-sm">
            <div class="row items-center justify-between q-mb-sm">
              <div class="text-subtitle1 text-negative">湯底</div>
              <q-btn-toggle
                v-model="selectedSuffix"
                :options="[
                  { label: '-', value: '', style: 'min-width: 50px' },
                  { label: 'A', value: 'A', style: 'min-width: 50px' },
                  { label: 'B', value: 'B', style: 'min-width: 50px' },
                  { label: 'C', value: 'C', style: 'min-width: 50px' },
                ]"
                dense
                size="md"
                unelevated
                toggle-color="primary"
                no-caps
              />
            </div>

            <div class="row q-col-gutter-sm q-mb-md">
              <div v-for="soup in soupOptions" :key="soup.id" class="col-6">
                <q-card
                  v-ripple
                  class="q-pa-sm cursor-pointer"
                  style="box-shadow: none; border: 1px solid #e0e0e0"
                  @click="handleAddItem(soup)"
                >
                  <div class="text-center">
                    {{ soup.name }}{{ selectedSuffix }}
                  </div>
                </q-card>
              </div>
            </div>
          </div>

          <!-- 🥗 基本盤 -->
          <div v-if="baseOptions.length !== 0" class="q-mb-md">
            <div class="text-subtitle1 text-negative q-mb-sm">基本盤</div>
            <div class="row q-col-gutter-sm">
              <div v-for="base in baseOptions" :key="base.id" class="col-6">
                <q-card
                  v-ripple
                  class="q-pa-sm cursor-pointer"
                  style="box-shadow: none; border: 1px solid #e0e0e0"
                  @click="handleAddItem(base)"
                >
                  <div class="text-center">
                    {{ base.name }}
                  </div>
                </q-card>
              </div>
            </div>
          </div>

          <!-- ✅ 預覽 -->
          <div class="q-mt-md">
            <div class="text-subtitle1 text-teal q-mb-sm">
              已選項目（點擊可減 1）
            </div>
            <q-list dense>
              <q-item
                v-for="item in groupedList"
                :key="item.key"
                clickable
                v-ripple
                class="bg-grey-2 rounded-borders q-mb-xs"
                @click="decreaseCount(item.key)"
              >
                <q-item-section class="text-weight-medium">
                  ✔ {{ item.name }}
                  <template v-if="item.spec && item.spec !== '標準'">
                    - {{ item.spec }}
                  </template>
                  <template v-if="item.tastes && item.tastes.length">
                    ｜{{ item.tastes.join("、") }}
                  </template>
                </q-item-section>

                <q-item-section
                  side
                  class="row items-center justify-end q-gutter-xs"
                >
                  <div
                    v-if="item.price && item.price !== 0"
                    class="text-weight-medium text-primary"
                  >
                    ${{ item.price }} × {{ item.count }}
                  </div>
                  <div v-else class="text-grey">× {{ item.count }}</div>
                </q-item-section>
              </q-item>

              <div
                v-if="groupedList.length === 0"
                class="text-grey text-center q-pt-sm"
              >
                尚未選擇任何項目
              </div>
            </q-list>
          </div>
        </div>

        <!-- 🚀 操作按鈕 -->
        <q-card-actions align="right">
          <q-btn flat label="取消" v-close-popup />
          <q-btn
            color="primary"
            label="確認"
            :disable="selectedItems.length === 0"
            @click="confirmSoupSelection"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="specDialogVisible">
      <q-card style="min-width: 300px; max-width: 90vw">
        <!-- 標題 -->
        <q-card-section class="text-h6 text-primary text-center">
          選擇 {{ selectedSoup?.name }}{{ selectedSuffix }}
        </q-card-section>

        <!-- 規格選擇 -->
        <q-card-section>
          <div class="text-subtitle1 text-bold">規格</div>
          <q-option-group
            v-model="selectedSpec"
            :options="selectedSoup?.specs || []"
            type="radio"
            :option-label="
              (opt) => `${opt.name}${opt.price > 0 ? `（$${opt.price}）` : ''}`
            "
            :option-value="(opt) => opt"
            color="primary"
          />
        </q-card-section>

        <!-- 口味選擇（可多選） -->
        <q-card-section v-if="selectedSoup?.tastes?.length">
          <div class="text-subtitle1 text-bold">口味（可複選）</div>
          <q-option-group
            v-model="selectedTastes"
            :options="selectedSoup.tastes"
            type="checkbox"
            :option-label="
              (opt) =>
                opt.price > 0 ? `${opt.name}（+$${opt.price}）` : opt.name
            "
            :option-value="(opt) => opt"
            color="secondary"
          />
        </q-card-section>

        <!-- 計算金額 -->
        <q-separator spaced />
        <q-card-section class="text-center text-weight-bold">
          總金額：$ {{ calculatedPrice }}
        </q-card-section>

        <!-- 操作按鈕 -->
        <q-card-actions align="right">
          <q-btn flat label="取消" v-close-popup />
          <q-btn color="primary" label="加入" @click="confirmSpecAndTastes" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
const $q = useQuasar();
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const props = defineProps({
  Branch: String,
  IP: String,
});
import { DateTime } from "luxon";

const connectionStatus = ref("connected");
const dialogVisible = ref(false);
const loading = ref(false);
let intervalId = null;
// 🔽 新增的變數
const OpenTableOptions = ref(true); // 開桌選項

// 單點模式相關欄位
const singleItemMode = ref(false); // 單點模式
const singleItemCode = ref("");
const singleItemName = ref("");

const opentableDialog = ref(false);

// 📌 桌號
const selectedRoomName = ref("");
const selectedRoomCode = ref("");
const selectedPerson = ref("");
// 📌 表單資料
const form = ref({
  adult: 1,
  child: 0,
  eatway: {},
  eatwayCode: "",
  eatwayName: "",
});

const emit = defineEmits(["openOrderPage"]);
const emitOrder = (table) => {
  const payload = {
    ...table, // 原本的 table
    hostname: hostName.value ?? "D",
    APIPrint: APIPrint.value ?? false,
    ip: props.IP.replace(/\\/g, "\\\\"),
    Branch: props.Branch,
  };
  emit("openOrderPage", payload); // 整包 table 傳出去
  //console.log(table);
};

const cancelServiceCall = async (code) => {
  try {
    await apiClient.post(`${apiBaseUrl}/tablestatus/cancel_Service`, {
      branch: props.Branch,
      code,
    });
    // 更新資料（例如重新 fetch）
    await fetchTables(sortBy.value);
  } catch (err) {
    console.error("❌ 取消服務鈴失敗", err);
    $q.notify({
      type: "negative",
      message: "取消服務鈴失敗",
    });
  }
};

// 📌 消費方式選項&桌況頁面名稱
const currentPage = ref(null);
const pageOptions = ref([]);
const eatwayOptions = ref([]);
const fetchOpenMode = async () => {
  try {
    const res = await apiClient.get(`${apiBaseUrl}/tablestatus/get_OpenMode`, {
      params: { branch: props.Branch }, // ✅ 加上 IP 傳給後端
    });

    if (res.data.success) {
      eatwayOptions.value = res.data.eatways.map((item) => ({
        label: item.name,
        value: item.code,
      }));

      pageOptions.value = res.data.pages;
      if (pageOptions.value.length > 0) {
        currentPage.value = pageOptions.value[0].value;
      }
    } else {
      $q.notify({ type: "negative", message: "載入消費方式失敗" });
    }
  } catch (err) {
    console.error("❌ 載入消費方式錯誤", err);
    $q.notify({ type: "negative", message: "載入失敗：" + err.message });
  }
};

// 📌 提交開桌
const submitOpenTable = async () => {
  const payload = {
    roomcode: selectedRoomCode.value,
    roomname: selectedRoomName.value,
    adult: form.value.adult,
    child: form.value.child,
    eatwayCode: form.value.eatway?.value,
    eatwayName: form.value.eatway?.label,
    branch: props.Branch,
  };
  //console.log(payload);
  try {
    const res = await apiClient.post(
      `${apiBaseUrl}/tablestatus/open_Table`,
      payload
    );
    if (res.data.success) {
      $q.notify({ type: "positive", message: `開桌成功！${res.data.code}` });
      openbase(
        res.data.code,
        selectedRoomCode.value,
        selectedRoomName.value,
        form.value.adult + form.value.child
      );
      opentableDialog.value = false;
    } else {
      $q.notify({ type: "negative", message: "開桌失敗：" + res.data.message });
    }
  } catch (err) {
    console.error("開桌錯誤", err);
    $q.notify({ type: "negative", message: "發生錯誤：" + err.message });
  }
};

// 點儲存時更新主程式的 tableColorRules

const getTableColor = (table) => {
  if (!table.code || !table.rtime || !table.ndate) return "#CDCDCD"; // 空桌或資料不完整

  const now = DateTime.now();

  // ✅ 組合日期與時間，例如 "2025/06/17 09:00"
  let openTime = DateTime.fromFormat(
    `${table.ndate} ${table.rtime}`,
    "yyyy/MM/dd HH:mm"
  );

  // ✅ 防呆：若開桌時間比現在還晚，可能是資料錯誤，退一天處理（極少見）
  if (openTime > now) {
    openTime = openTime.minus({ days: 1 });
  }

  const diff = now.diff(openTime, "minutes").toObject().minutes;

  for (const rule of tableColorRules.value) {
    if (diff >= rule.min) {
      return rule.color;
    }
  }

  return "grey-5"; // fallback 預設顏色
};
const serviceCallTables = computed(() =>
  tables.value.filter((table) => !!table.appmemo)
);

const fetchTables = async (sort) => {
  try {
    loading.value = true;

    const res = await apiClient.post(
      `${apiBaseUrl}/tablestatus/get_Tablestatus`,
      {
        branch: props.Branch,
        page: String(currentPage.value ?? 1),
        sort: sort,
      }
    );

    if (res.data.success) {
      tables.value = res.data.data;
      //console.log(tables.value);
      connectionStatus.value = "connected"; // ✅ 成功連線
    } else {
      connectionStatus.value = "disconnected"; // ❌ 有回應但不成功
    }
  } catch (err) {
    connectionStatus.value = "disconnected"; // ❌ API 錯誤（斷線、逾時等）
    console.error("❌ 查詢桌況失敗", err);
  } finally {
    loading.value = false;
  }
};

const tables = ref([]);
const showDialog = ref(false);
const selectedTable = ref(null);

const onClickTable = (table) => {
  selectedTable.value = table;

  if (!table.code) {
    // 空桌 → 直接進開桌流程
    selectedRoomName.value = table.name; // 設定桌號
    selectedRoomCode.value = table.roomcode; // 設定桌號
    form.value.adult = 1;
    form.value.child = 0;

    form.value.eatway = "";
    form.value.eatwayCode = "";
    form.value.eatwayName = "";
    if (eatwayOptions.value.length === 1) {
      form.value.eatway = eatwayOptions.value[0];
      form.value.eatwayCode = eatwayOptions.value[0].value;
      form.value.eatwayName = eatwayOptions.value[0].label;
    }
    if (singleItemMode.value) {
      form.value.eatway = {
        label: singleItemName.value,
        value: singleItemCode.value,
      };
      form.value.eatwayCode = singleItemName.value;
      form.value.eatwayName = singleItemName.value;
    }
    console.log();
    opentableDialog.value = true; // 開啟開桌 dialog
  } else {
    // 已開桌 → 顯示桌況資訊
    showDialog.value = true;
  }
};

// 在 Dialog 中修改 timeColors.value
const tableColorRules = ref([]);
const timeColors = ref([]);
const refreshInterval = ref(); // 秒，預設 5
const sortBy = ref("");
const printers = ref([]);
const printerError = ref([]);

const fetchSetting = async () => {
  try {
    loading.value = true;

    const res = await apiClient.get(`${apiBaseUrl}/tablestatus/get_Setting`, {
      params: { branch: props.Branch, page: "tablestatus" },
    });

    if (res.data.success) {
      const { settings, printerIps } = res.data.data;
      refreshInterval.value = settings.refresh_interval ?? 5;
      sortBy.value = settings.sort_by ?? "name";
      timeColors.value = settings.tableColorRules ?? []; // ✅ 不要 JSON.parse
      tableColorRules.value = settings.tableColorRules ?? [];
      hostName.value = settings.hostName ?? "D";
      APIPrint.value = settings.APIPrint ?? false;
      singleItemMode.value = settings.singleItemMode ?? false;
      singleItemName.value = settings.singleItemName ?? "";
      singleItemCode.value = settings.singleItemCode ?? "";
      OpenTableOptions.value = settings.OpenTableOptions ?? true;
      // ✅ 每 N秒輪詢一次
      intervalId = setInterval(() => {
        fetchTables(sortBy.value);
        if (printers.value.length !== 0 && !APIPrint.value) {
          fetchPrinterErrors();
        }
      }, refreshInterval.value * 1000);

      printers.value = printerIps;
    } else {
      $q.notify({ type: "negative", message: "讀取設定失敗" });
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "發生錯誤：" + err.message });
  } finally {
    loading.value = false;
  }
};

const saveSettings = async () => {
  try {
    const payload = {
      page: "tablestatus",
      branch: props.Branch,
      data: {
        refresh_interval: refreshInterval.value,
        sort_by: sortBy.value,
        tableColorRules: timeColors.value,
        hostName: hostName.value,
        APIPrint: APIPrint.value,
        singleItemMode: singleItemMode.value,
        singleItemName: singleItemName.value,
        singleItemCode: singleItemCode.value,
        OpenTableOptions: OpenTableOptions.value,
      },
    };

    const res = await apiClient.post(
      `${apiBaseUrl}/tablestatus/save_Setting`,
      payload
    );

    if (res.data.success) {
      $q.notify({ type: "positive", message: "設定已儲存" });
    } else {
      $q.notify({ type: "negative", message: "儲存失敗：" + res.data.error });
    }
  } catch (err) {
    console.error("❌ 儲存錯誤:", err);
    $q.notify({ type: "negative", message: "儲存錯誤：" + err.message });
  }
};

const fetchPrinterErrors = async () => {
  try {
    const ipList = Array.isArray(printers.value)
      ? printers.value
      : [printers.value];

    const res = await apiClient.get(
      `${apiBaseUrl}/tablestatus/get_PrintErrors`,
      {
        params: {
          ipList,
        },

        paramsSerializer: (params) => {
          const searchParams = new URLSearchParams();
          for (const ip of params.ipList) {
            searchParams.append("ipList", ip);
          }
          return searchParams.toString();
        },
      }
    );
    printerError.value = res.data.data;
  } catch (err) {
    console.error("查詢失敗:出單異常：", err.message);
  }
};

const initSetting = async () => {
  try {
    const res = await apiClient.post(
      `${apiBaseUrl}/tablestatus/check_Database`,
      {
        branch: props.Branch,
      }
    );

    if (res.data.success) {
    } else {
      $q.notify({ type: "negative", message: res.data.message });
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "初始化失敗：" + err.message });
  }
};

const APIPrint = ref(false);
const hostName = ref("");

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "連線中...",
  });
  await initSetting();
  await fetchSetting();
  await fetchTables(sortBy.value); // 一開始先跑一次
  $q.loading.hide();
  await fetchOpenMode();
});

onBeforeUnmount(() => {
  // ❗離開畫面前記得清除 interval，避免 memory leak
  if (intervalId) {
    clearInterval(intervalId);
  }
});

const selectedOrderCode = ref("");

const soupDialogVisible = ref(false);

const openbase = async (scode, roomCode, roomName, person) => {
  selectedOrderCode.value = scode;
  selectedRoomCode.value = roomCode;
  selectedRoomName.value = roomName;
  selectedPerson.value = person;
  await fetchOpenBase();
  selectedItems.value = [];
  selectedSoup.value = null;
  selectedSpec.value = null;
  selectedTastes.value = [];
  if (OpenTableOptions.value) {
    soupDialogVisible.value = true;
  }
};

const selectedSuffix = ref("");

const soupOptions = ref([]);
const baseOptions = ref([]);

const fetchOpenBase = async () => {
  try {
    const res = await apiClient.get(`${apiBaseUrl}/tablestatus/get_OpenBase`, {
      params: { branch: props.Branch }, // ✅ 加上 IP 傳給後端
    });

    if (res.data.success) {
      //console.log(res.data);
      soupOptions.value = res.data.soupOptions;
      baseOptions.value = res.data.baseOptions;
      //console.log(res.data);
    } else {
      $q.notify({ type: "negative", message: "載入開桌明細失敗" });
    }
  } catch (err) {
    console.error("❌ 載入開桌明細失敗", err);
    $q.notify({ type: "negative", message: "載入失敗：" + err.message });
  }
};

const selectedItems = ref([]); // 每次點擊都 push 一個完整物件
const selectedSpec = ref(null); // ✅ 單選的規格
const selectedTastes = ref([]); // ✅ 多選的口味
const selectedSoup = ref(null); // ✅ 當前選擇的品項
const specDialogVisible = ref(false); // ✅ 控制規格與口味選擇 Dialog

const handleAddItem = (item) => {
  const suffix = selectedSuffix.value || "";
  if (item.specs.length === 1 && item.tastes.length === 0) {
    const spec = item.specs[0];
    selectedItems.value.push({
      code: item.code,
      name: item.name + suffix,
      unit: item.unit,
      spec: spec.name,
      tastes: [],
      price: spec.price,
    });
  } else {
    // 否則展開 Dialog（多規格 或 有口味）
    selectedSoup.value = item;
    selectedSpec.value = item.specs?.[0] || null;
    selectedTastes.value = [];
    specDialogVisible.value = true;
  }
};

const calculatedPrice = computed(() => {
  const base = selectedSpec.value?.price || 0;
  const extra = Array.isArray(selectedTastes.value)
    ? selectedTastes.value.reduce((sum, t) => sum + (t.price || 0), 0)
    : 0;
  return base + extra;
});

const confirmSpecAndTastes = () => {
  selectedItems.value.push({
    code: selectedSoup.value.code,
    name: selectedSoup.value.name + (selectedSuffix.value || ""),
    unit: selectedSoup.value.unit,
    spec: selectedSpec.value?.name || "標準",
    price: calculatedPrice.value,
    tastes: selectedTastes.value.map((t) => t.name),
  });
  specDialogVisible.value = false;
};

// 統計與顯示用
const groupedList = computed(() => {
  const map = new Map();
  selectedItems.value.forEach((item) => {
    const specPart = item.spec && item.spec !== "標準" ? `-${item.spec}` : "";
    const tastePart =
      item.tastes && item.tastes.length ? `-${item.tastes.join(",")}` : "";
    const key = `${item.name}${item.suffix || ""}${specPart}${tastePart}`;

    if (!map.has(key)) {
      map.set(key, {
        key,
        code: item.code,
        name: item.name,
        spec: item.spec, // ✅ 實際資料保留「標準」
        item: item.unit,
        tastes: item.tastes || [],
        price: item.price,
        count: 0,
      });
    }
    map.get(key).count++;
  });
  return Array.from(map.values());
});

const decreaseCount = (targetKey) => {
  const index = selectedItems.value.findIndex((item) => {
    const specPart = item.spec && item.spec !== "標準" ? `-${item.spec}` : "";
    const tastePart =
      item.tastes && item.tastes.length ? `-${item.tastes.join(",")}` : "";
    const key = `${item.name}${item.suffix || ""}${specPart}${tastePart}`;
    return key === targetKey;
  });

  if (index !== -1) {
    selectedItems.value.splice(index, 1);
  }
};

// 最後送出處理
const confirmSoupSelection = async () => {
  // 可以送出 selectedItems  資料
  const payload = {
    ip: props.IP.replace(/\\/g, "\\\\"),
    scode: selectedOrderCode.value,
    room: selectedRoomCode.value,
    roomname: selectedRoomName.value,
    person: selectedPerson.value,
    hostname: hostName.value,
    APIPrint: APIPrint.value,
    items: groupedList.value.map((item) => ({
      code: item.code,
      name: item.name,
      spec: item.spec,
      unit: item.unit,
      price: item.price,
      tastes: (item.tastes || []).join(" "), // ✅ 將 Proxy 陣列轉成空格分隔的字串
      qty: item.count,
    })),
  };
  //console.log("🧾 最終送出：", payload);
  try {
    const res = await apiClient.post(
      `${apiBaseUrl}/weborder/insert_Order`,
      payload
    );
    if (res.data.success) {
      $q.notify({ type: "positive", message: res.data.message });
      soupDialogVisible.value = false;
    } else {
      $q.notify({ type: "negative", message: "開桌失敗：" + res.data.message });
    }
  } catch (err) {
    console.error("開桌錯誤", err);
    $q.notify({ type: "negative", message: "發生錯誤：" + err.message });
  }
};
</script>
<style scoped>
.animated-bounce {
  animation: bounce 0.8s infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.col-lg-2-4 {
  flex: 0 0 20%;
  max-width: 20%;
}
@media (max-width: 1279px) {
  .col-lg-2-4 {
    flex: 0 0 25%;
    max-width: 25%;
  }
}
@media (max-width: 1023px) {
  .col-lg-2-4 {
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
  }
}
@media (max-width: 599px) {
  .col-lg-2-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
</style>
