// services/dbPoolManager.js
const sql = require("mssql");
const getDbConfig = require("../config/getDbConfig"); // 你自己依 IP 傳回 dbConfig 的函式
const { getBranchIP } = require("./branchIP");

const poolMap = new Map(); // Map<ip, sql.ConnectionPool>

/**
 * 根據門市代號（cod_cust）取得連線池
 * @param {string} cod_cust
 * @returns {Promise<sql.ConnectionPool>}
 */
const getPoolByBranch = async (cod_cust) => {
  if (!cod_cust) throw new Error("❌ 缺少 cod_cust");

  // 查 IP
  const ip = await getBranchIP(cod_cust);

  if (!ip) throw new Error(`❌ 無對應 IP:${cod_cust}`);

  // 如果該 IP 已經有可用連線池
  if (poolMap.has(ip)) {
    const existingPool = poolMap.get(ip);
    if (existingPool.connected) {
      //console.log("已經有可用連線池:" + ip);
      return existingPool;
    }

    // 如果 pool 不可用，清除它
    poolMap.delete(ip);
  }

  // 建立新的連線池
  const config = getDbConfig(ip);
  const pool = new sql.ConnectionPool(config);

  try {
    await pool.connect();
    poolMap.set(ip, pool);
    console.log(`✅ 連線池建立：${cod_cust} (${ip})`);
    return pool;
  } catch (err) {
    console.error(`❌ 建立連線池失敗 (${ip})`, err.message);
    throw err;
  }
};

/**
 * 關閉所有連線池（通常在應用關閉時使用）
 */
const closeAllPools = async () => {
  for (const [ip, pool] of poolMap.entries()) {
    if (pool.connected) {
      await pool.close();
      console.log(`🔌 已關閉連線池：${ip}`);
    }
  }
  poolMap.clear();
};

module.exports = {
  getPoolByBranch,
  closeAllPools,
  sql, // 匯出 mssql 給 controller 使用
};
