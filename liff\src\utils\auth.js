// 🔹 解析 JWT，檢查是否過期
export const isTokenExpired = (token) => {
  try {
    const base64Url = token.split(".")[1]; // 取得 JWT Payload
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const decoded = JSON.parse(atob(base64));

    if (!decoded.exp) return true; // 如果沒有 `exp`，視為過期
    const now = Math.floor(Date.now() / 1000);

    return decoded.exp < now; // 🔹 過期回傳 true
  } catch (error) {
    console.error("❌ JWT 解析失敗:", error);
    return true; // 解析錯誤也視為過期
  }
};
