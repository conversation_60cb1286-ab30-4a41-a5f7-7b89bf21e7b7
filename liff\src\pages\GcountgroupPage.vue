<template>
  <q-page
    class="q-pa-sm flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">商品群組統計管理</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="green"
              @click="openCategoryDialog(null)"
            >
              <folder-plus :stroke-width="1" :size="26" />
              <q-tooltip>新增類別</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- 類別清單 -->
      <q-list separator>
        <div v-if="loading" class="q-pa-md flex flex-center">
          <q-spinner color="primary" size="3em" />
          <div class="q-ml-sm text-subtitle1 text-grey">載入中...</div>
        </div>
        <template v-else-if="productCategories.length > 0">
          <VueDraggable
            v-model="productCategories"
            item-key="cid"
            tag="div"
            handle=".category-drag-handle"
            ghost-class="bg-indigo-1"
            delay="100"
            @end="onCategorySortEnd"
          >
            <div
              v-for="category in productCategories"
              :key="category.cid"
              class="q-mb-sm"
              style="
                border: 1px solid #ccc;
                border-radius: 8px;
                overflow: hidden;
              "
            >
              <q-expansion-item
                expand-separator
                header-class="text-primary"
                class="q-pa-none"
              >
                <template #header>
                  <q-item
                    dense
                    class="q-pa-none q-gutter-none items-center"
                    style="width: 100%"
                  >
                    <!-- 拖曳手把 -->
                    <q-item-section avatar class="category-drag-handle">
                      <q-icon name="drag_handle" color="grey-7" />
                    </q-item-section>

                    <!-- 類別資訊 -->
                    <q-item-section>
                      <div class="row items-center">
                        <q-item-label>{{ category.cname }}</q-item-label>
                        <q-btn
                          v-if="category.total_display === 0"
                          flat
                          round
                          dense
                          color="grey"
                          size="xs"
                          class="q-ml-sm"
                        >
                          <eye-off :stroke-width="1.5" :size="16" />
                          <q-tooltip>不顯示門市統計</q-tooltip>
                        </q-btn>
                      </div>
                    </q-item-section>

                    <!-- 操作按鈕 -->
                    <q-item-section side>
                      <div class="row items-center q-gutter-xs">
                        <q-btn
                          flat
                          round
                          dense
                          color="primary"
                          @click.stop="openCategoryDialog(category)"
                        >
                          <folder-pen :stroke-width="1.5" :size="20" />
                          <q-tooltip>編輯類別</q-tooltip>
                        </q-btn>

                        <q-btn
                          flat
                          round
                          dense
                          color="negative"
                          @click.stop="confirmDeleteCategory(category)"
                        >
                          <folder-x :stroke-width="1.5" :size="20" />
                          <q-tooltip>刪除類別</q-tooltip>
                        </q-btn>
                      </div>
                    </q-item-section>
                  </q-item>
                </template>

                <!-- 群組列表 -->
                <q-card-section class="q-pa-none q-px-md">
                  <!-- 新增群組按鈕 -->
                  <div class="row justify-start q-mb-sm">
                    <q-btn
                      flat
                      dense
                      color="green"
                      @click="openGroupDialog(null, category.cid)"
                    >
                      <plus :stroke-width="1.5" :size="20" />
                      新增群組
                    </q-btn>
                  </div>

                  <q-list separator>
                    <template
                      v-if="getGroupsByCategory(category.cid).length > 0"
                    >
                      <VueDraggable
                        v-model="productGroups"
                        item-key="gid"
                        tag="div"
                        handle=".group-drag-handle"
                        ghost-class="bg-grey-3"
                        delay="100"
                        :group="{ name: category.cid }"
                        @end="onGroupSortEnd"
                      >
                        <div
                          v-for="group in getGroupsByCategory(category.cid)"
                          :key="`${category.cid}-${group.gid}`"
                          class="q-mb-xs"
                        >
                          <q-expansion-item
                            expand-separator
                            header-class="text-indigo"
                            class="bg-grey-1 q-mb-xs"
                          >
                            <template #header>
                              <q-item
                                dense
                                class="q-pa-none q-gutter-none items-center"
                                style="width: 100%"
                              >
                                <!-- 拖曳手把 -->
                                <q-item-section
                                  avatar
                                  class="group-drag-handle"
                                >
                                  <q-icon
                                    name="drag_indicator"
                                    color="grey-7"
                                  />
                                </q-item-section>

                                <!-- 群組資訊 -->
                                <q-item-section>
                                  <div class="row items-center justify-between">
                                    <q-item-label>{{
                                      group.gname
                                    }}</q-item-label>
                                    <div class="row items-center q-gutter-xs">
                                      <q-btn
                                        v-if="group.total === 0"
                                        flat
                                        round
                                        dense
                                        color="grey"
                                      >
                                        <square-x
                                          :stroke-width="1"
                                          :size="20"
                                        />
                                        <q-tooltip>不加入統計</q-tooltip>
                                      </q-btn>

                                      <q-btn
                                        v-if="group.display === 0"
                                        flat
                                        round
                                        dense
                                        color="grey"
                                      >
                                        <eye-off
                                          :stroke-width="1.5"
                                          :size="20"
                                        />
                                      </q-btn>
                                    </div>
                                  </div>
                                </q-item-section>
                              </q-item>
                            </template>

                            <!-- 群組商品項目列表 -->
                            <q-card-section class="q-px-md">
                              <div class="row justify-start q-mx-md">
                                <q-btn
                                  flat
                                  dense
                                  color="warning"
                                  @click.stop="openItemDialog(group)"
                                >
                                  <plus :stroke-width="1.5" :size="20" />
                                  新增品項
                                </q-btn>
                              </div>
                              <div
                                v-if="loadingItems"
                                class="text-center q-pa-sm"
                              >
                                <q-spinner color="primary" size="1.5em" />
                                <span class="q-ml-sm">載入商品中...</span>
                              </div>

                              <div
                                v-else-if="
                                  group.items && group.items.length > 0
                                "
                              >
                                <q-list bordered separator class="q-mx-md">
                                  <q-item
                                    v-for="item in group.items"
                                    :key="item.items_code"
                                    class="bg-grey-1 rounded-borders"
                                  >
                                    <q-item-section>
                                      <q-item-label>{{
                                        item.items_name
                                      }}</q-item-label>
                                      <q-item-label caption
                                        >商品編號:
                                        {{ item.items_code }}</q-item-label
                                      >
                                    </q-item-section>
                                    <q-item-section side>
                                      <q-btn
                                        flat
                                        round
                                        dense
                                        color="negative"
                                        @click.stop="removeItem(group, item)"
                                      >
                                        <X :stroke-width="1.5" :size="20" />
                                        <q-tooltip>移除商品</q-tooltip>
                                      </q-btn>
                                    </q-item-section>
                                  </q-item>
                                </q-list>
                              </div>
                              <div v-else class="text-grey text-center q-pa-sm">
                                尚未新增商品項目
                              </div>

                              <!-- 群組操作按鈕 - 移到展開內容底部 -->
                              <div class="row justify-end q-mt-md">
                                <div class="row items-center q-gutter-xs">
                                  <q-btn
                                    flat
                                    round
                                    dense
                                    color="primary"
                                    @click.stop="openGroupDialog(group)"
                                  >
                                    <file-pen :stroke-width="1.5" :size="20" />
                                    <q-tooltip>編輯群組</q-tooltip>
                                  </q-btn>
                                  <q-btn
                                    flat
                                    round
                                    dense
                                    color="negative"
                                    @click.stop="confirmDeleteGroup(group)"
                                  >
                                    <file-x-2 :stroke-width="1.5" :size="20" />
                                    <q-tooltip>刪除群組</q-tooltip>
                                  </q-btn>
                                </div>
                              </div>
                            </q-card-section>
                          </q-expansion-item>
                        </div>
                      </VueDraggable>
                    </template>
                    <div v-else class="text-grey text-center q-pa-sm">
                      尚未新增商品群組
                    </div>
                  </q-list>
                </q-card-section>
              </q-expansion-item>
            </div>
          </VueDraggable>
        </template>
        <div v-else class="text-grey text-center q-pa-lg">尚未新增商品類別</div>
      </q-list>
    </q-card>

    <!-- 類別對話框 -->
    <q-dialog v-model="categoryDialog.show" persistent>
      <q-card class="q-pa-sm bg-white" style="width: 500px; max-width: 90vw">
        <q-card-section class="row items-center justify-between q-pb-none">
          <div class="text-h6 text-primary">
            {{ categoryDialog.isEdit ? "編輯" : "新增" }}商品類別
          </div>
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-form @submit="saveCategory">
          <q-card-section class="q-pt-md q-gutter-md">
            <q-input
              v-if="!categoryDialog.isEdit"
              v-model="categoryDialog.data.cid"
              label="類別編號"
              hint="自動產生，可選擇性修改"
              outlined
              dense
              :readonly="categoryDialog.isEdit"
              :rules="[(val) => !!val || '請輸入類別編號']"
            />
            <q-input
              v-model="categoryDialog.data.cname"
              label="類別名稱"
              outlined
              dense
              :rules="[(val) => !!val || '請輸入類別名稱']"
            />

            <!-- 新增欄位：是否顯示門市統計 -->
            <div class="row items-center justify-between">
              <div class="text-subtitle2">是否顯示門市統計</div>
              <q-toggle
                v-model="categoryDialog.data.total_display"
                color="primary"
                :true-value="1"
                :false-value="0"
                :label="
                  categoryDialog.data.total_display === 1 ? '顯示' : '不顯示'
                "
              />
            </div>
          </q-card-section>

          <q-card-actions align="right" class="q-pt-md">
            <q-btn
              label="取消"
              color="grey"
              flat
              v-close-popup
              class="q-mr-sm"
            />
            <q-btn
              label="儲存"
              color="secondary"
              unelevated
              outline
              icon="save"
              type="submit"
            />
          </q-card-actions>
        </q-form>
      </q-card>
    </q-dialog>

    <!-- 群組對話框 -->
    <q-dialog v-model="groupDialog.show" persistent>
      <q-card class="q-pa-sm bg-white" style="width: 500px; max-width: 90vw">
        <q-card-section class="row items-center justify-between q-pb-none">
          <div class="text-h6 text-primary">
            {{ groupDialog.isEdit ? "編輯" : "新增" }}商品群組
          </div>
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-form @submit="saveGroup">
          <q-card-section class="q-pt-md q-gutter-md">
            <q-input
              v-if="!groupDialog.isEdit"
              v-model="groupDialog.data.gid"
              label="群組編號"
              hint="自動產生，可選擇性修改"
              outlined
              dense
              :readonly="groupDialog.isEdit"
              :rules="[(val) => !!val || '請輸入群組編號']"
            />
            <q-input
              v-model="groupDialog.data.gname"
              label="群組名稱"
              outlined
              dense
              :rules="[(val) => !!val || '請輸入群組名稱']"
            />
            <q-select
              v-if="!groupDialog.isEdit"
              v-model="groupDialog.data.gcid"
              :options="categoryOptions"
              label="所屬類別"
              outlined
              dense
              map-options
              emit-value
              :rules="[(val) => !!val || '請選擇所屬類別']"
            />

            <q-select
              v-model="groupDialog.data.branch"
              :options="branchOptions"
              label="參照門市"
              outlined
              dense
              map-options
              emit-value
              :rules="[(val) => !!val || '請選擇參照門市']"
              @update:model-value="
                (val) => (groupDialog.data.branch = String(val))
              "
            />

            <!-- 新增欄位：是否加入門市合計 -->
            <div class="row items-center justify-between">
              <div class="text-subtitle2">是否加入門市合計</div>
              <q-toggle
                v-model="groupDialog.data.total"
                color="primary"
                :true-value="1"
                :false-value="0"
                :label="groupDialog.data.total === 1 ? '加入' : '不加入'"
              />
            </div>

            <!-- 新增欄位：是否顯示 -->
            <div class="row items-center justify-between">
              <div class="text-subtitle2">是否顯示</div>
              <q-toggle
                v-model="groupDialog.data.display"
                color="primary"
                :true-value="1"
                :false-value="0"
                :label="groupDialog.data.display === 1 ? '顯示' : '不顯示'"
              />
            </div>
          </q-card-section>

          <q-card-actions align="right" class="q-pt-md">
            <q-btn
              label="取消"
              color="grey"
              flat
              v-close-popup
              class="q-mr-sm"
            />
            <q-btn
              label="儲存"
              color="secondary"
              unelevated
              outline
              icon="save"
              type="submit"
            />
          </q-card-actions>
        </q-form>
      </q-card>
    </q-dialog>

    <!-- 商品項目對話框 -->
    <q-dialog v-model="itemDialog.show" persistent>
      <q-card class="q-pa-sm bg-white" style="width: 600px; max-width: 95vw">
        <q-card-section class="row items-center justify-between q-pb-none">
          <div class="text-h6 text-primary">
            管理商品項目 - {{ itemDialog.group?.gname }}
          </div>
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-md">
          <q-input
            v-model="itemDialog.search"
            label="搜尋商品"
            outlined
            dense
            clearable
            @keyup.enter="performSearch"
            @update:model-value="onSearchInputChange"
          >
            <template v-slot:append>
              <q-btn
                flat
                dense
                icon="search"
                color="primary"
                @click="performSearch"
              >
                <q-tooltip>搜尋</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </q-card-section>

        <q-card-section style="max-height: 400px; overflow-y: auto">
          <div v-if="itemDialog.loading" class="text-center q-pa-md">
            <q-spinner color="primary" size="2em" />
            <div class="q-mt-sm">載入商品中...</div>
          </div>
          <q-list v-else bordered separator>
            <div
              v-for="group in filteredGroupedItems"
              :key="group.categoryName"
              class="q-mb-md"
            >
              <!-- 分類標題 -->
              <div
                class="text-subtitle2 text-primary q-pa-sm bg-grey-2 rounded-borders"
              >
                {{ group.categoryName }}
                <span class="text-caption text-grey">
                  ({{ group.items.length }} 項)
                </span>
              </div>

              <!-- 分類內的商品 -->
              <q-list bordered>
                <q-item
                  v-for="item in group.items"
                  :key="item.items_code"
                  tag="label"
                  clickable
                  class="q-mb-xs"
                >
                  <q-item-section avatar>
                    <q-checkbox
                      v-model="itemDialog.selectedItems"
                      :val="item"
                      color="primary"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ item.items_name }}</q-item-label>
                    <q-item-label caption
                      >商品編號: {{ item.items_code }}</q-item-label
                    >
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div
              v-if="filteredGroupedItems.length === 0"
              class="text-grey text-center q-pa-md"
            >
              無符合的商品項目
            </div>
          </q-list>
        </q-card-section>

        <q-card-actions align="right" class="q-pt-md">
          <q-btn label="取消" color="grey" flat v-close-popup class="q-mr-sm" />
          <q-btn
            label="加入所選商品"
            color="secondary"
            unelevated
            outline
            icon="add"
            @click="addItems"
            :disable="itemDialog.selectedItems.length === 0"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 確認刪除對話框 -->
    <q-dialog v-model="deleteDialog.show" persistent>
      <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
        <q-card-section class="text-center">
          <q-icon
            name="delete_forever"
            color="negative"
            size="xl"
            class="q-mb-md"
          />
          <div class="text-h6 text-negative">確定刪除？</div>
          <div class="text-body2 text-grey-8 q-mt-sm">
            {{
              deleteDialog.type === "category"
                ? "類別"
                : deleteDialog.type === "group"
                ? "群組"
                : "商品項目"
            }}
            <strong>{{ deleteDialog.name }}</strong> 刪除後無法復原，是否繼續？
          </div>
        </q-card-section>

        <q-separator class="q-mt-md" />

        <q-card-actions class="q-mt-sm row justify-between">
          <q-btn
            label="取消"
            color="grey"
            flat
            class="full-width q-mr-xs"
            v-close-popup
          />
          <q-btn
            label="刪除"
            color="negative"
            unelevated
            class="full-width q-ml-xs"
            @click="handleDelete"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from "vue";
import { useQuasar } from "quasar";
import { FolderPlus, X, EyeOff } from "lucide-vue-next";
import { VueDraggable } from "vue-draggable-plus";
import apiClient from "../api";

const $q = useQuasar();
const props = defineProps({ Branch: String });
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 數據
const productCategories = ref([]);
const productGroups = ref([]);
const loading = ref(false);
const loadingItems = ref(false);
const branchOptions = ref([]);
const debouncedSearch = ref(""); // 添加用於實際搜尋的變數

// 類別對話框
const categoryDialog = reactive({
  show: false,
  isEdit: false,
  data: {
    cid: "",
    cname: "",
    sno: 0,
    total_display: 1, // 默认显示门市统计
  },
});

// 群組對話框
const groupDialog = reactive({
  show: false,
  isEdit: false,
  data: {
    gid: "",
    gname: "",
    gcid: "",
    sno: 0,
    branch: "",
    total: 1, // 默认加入合计
    display: 1, // 默认显示
  },
});

// 商品項目對話框
const itemDialog = reactive({
  show: false,
  group: null,
  search: "",
  loading: false,
  items: [],
  selectedItems: [],
});

// 計算屬性
const categoryOptions = computed(() => {
  return productCategories.value.map((cat) => ({
    label: cat.cname,
    value: cat.cid,
  }));
});

// 按分類分組的商品項目
const groupedItems = computed(() => {
  if (!itemDialog.items.length) return [];

  // 根據category_name對商品進行分組
  const groups = {};
  itemDialog.items.forEach((item) => {
    const categoryName = item.category_name || "未分類";
    if (!groups[categoryName]) {
      groups[categoryName] = [];
    }
    groups[categoryName].push(item);
  });

  // 轉換為陣列格式
  return Object.keys(groups).map((key) => ({
    categoryName: key,
    items: groups[key],
  }));
});

const filteredItems = computed(() => {
  if (!debouncedSearch.value) {
    return itemDialog.items;
  }
  const searchText = debouncedSearch.value.toLowerCase();
  return itemDialog.items.filter(
    (item) =>
      item.items_name.toLowerCase().includes(searchText) ||
      item.items_code.toLowerCase().includes(searchText) ||
      (item.category_name &&
        item.category_name.toLowerCase().includes(searchText))
  );
});

// 按分類分組的過濾後商品項目
const filteredGroupedItems = computed(() => {
  if (!debouncedSearch.value) return groupedItems.value;

  const searchText = debouncedSearch.value.toLowerCase();
  const filteredGroups = {};

  itemDialog.items.forEach((item) => {
    if (
      item.items_name.toLowerCase().includes(searchText) ||
      item.items_code.toLowerCase().includes(searchText) ||
      (item.category_name &&
        item.category_name.toLowerCase().includes(searchText))
    ) {
      const categoryName = item.category_name || "未分類";
      if (!filteredGroups[categoryName]) {
        filteredGroups[categoryName] = [];
      }
      filteredGroups[categoryName].push(item);
    }
  });

  return Object.keys(filteredGroups).map((key) => ({
    categoryName: key,
    items: filteredGroups[key],
  }));
});

// 執行搜尋
const performSearch = () => {
  // 只有當搜尋字串長度大於 0 時才更新 debouncedSearch
  if (!itemDialog.search || itemDialog.search.trim() === "") {
    debouncedSearch.value = "";
    $q.notify({
      color: "warning",
      message: "請輸入搜尋關鍵字",
      icon: "warning",
      position: "top",
      timeout: 1500,
    });
  } else {
    debouncedSearch.value = itemDialog.search;
  }
};

// 當搜尋輸入框值變更時
const onSearchInputChange = (val) => {
  // 如果清空了搜尋框，也清空 debouncedSearch
  if (!val || val.trim() === "") {
    debouncedSearch.value = "";
  }
  // 否則不做任何事，等待用戶按下 Enter 或點擊搜尋按鈕
};

// 刪除確認對話框
const deleteDialog = reactive({
  show: false,
  type: "", // category, group, item
  id: "",
  name: "",
  parentId: "", // 用於群組刪除
});

// 方法
const getGroupsByCategory = (categoryId) => {
  return productGroups.value.filter((group) => group.gcid === categoryId);
};

const generateId = (prefix, list, idField) => {
  let maxNum = 0;
  list.forEach((item) => {
    const idNumber = parseInt(item[idField].substring(1));
    if (idNumber > maxNum) maxNum = idNumber;
  });

  const newNum = maxNum + 1;
  return `${prefix}${newNum.toString().padStart(5, "0")}`;
};

const openCategoryDialog = (category) => {
  categoryDialog.isEdit = !!category;

  if (category) {
    categoryDialog.data = { ...category };
  } else {
    categoryDialog.data = {
      cid: generateId("C", productCategories.value, "cid"),
      cname: "",
      sno: productCategories.value.length + 1,
    };
  }

  categoryDialog.show = true;
};

const openGroupDialog = (group, categoryId = null) => {
  groupDialog.isEdit = !!group;

  if (group) {
    // 确保branch是正确的门市代号
    const branch = extractBranchCode(group.branch);

    groupDialog.data = {
      ...group,
      branch: branch,
    };
  } else {
    // 确保branch是正确的门市代号
    const branch = extractBranchCode(props.Branch);

    groupDialog.data = {
      gid: generateId("G", productGroups.value, "gid"),
      gname: "",
      gcid: categoryId || "",
      sno: getGroupsByCategory(categoryId).length + 1,
      branch: branch,
      total: 1, // 默认加入合计
      display: 1, // 默认显示
    };
  }

  groupDialog.show = true;
};

const saveCategory = async () => {
  try {
    const payload = { ...categoryDialog.data };

    const response = await apiClient.post(
      `${apiBaseUrl}/gcounts/save_category`,
      {
        category: payload,
      }
    );

    if (response.data.success) {
      $q.notify({
        color: "positive",
        message: `${categoryDialog.isEdit ? "更新" : "新增"}類別成功`,
        icon: "check_circle",
      });

      await loadCategories();
      categoryDialog.show = false;
    } else {
      throw new Error(response.data.message || "操作失敗");
    }
  } catch (error) {
    console.error("類別儲存錯誤:", error);
    $q.notify({
      color: "negative",
      message: `${categoryDialog.isEdit ? "更新" : "新增"}類別失敗: ${
        error.message
      }`,
      icon: "error",
    });
  }
};

const loadBranchOptions = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/gcounts/get_branches`);

    if (response.data.success) {
      branchOptions.value = response.data.data.map((branch) => {
        // 确保cod_cust是字符串并去除空格
        const branchCode = branch.cod_cust
          ? String(branch.cod_cust).trim()
          : "";
        return {
          label: `${branch.cod_name} (${branchCode})`,
          value: branchCode, // 只使用门市代号作为值
          group: branch.cod_group,
          status: branch.sts,
        };
      });
    }
  } catch (error) {
    console.error("載入門市選項錯誤:", error);
    $q.notify({
      color: "negative",
      message: `載入門市選項失敗: ${error.message}`,
      icon: "error",
    });
    branchOptions.value = [];
  }
};

// 确保branch值只包含门市代号
const extractBranchCode = (branchValue) => {
  if (!branchValue) return "";

  // 如果是字符串且包含括号，尝试提取括号中的代号
  if (typeof branchValue === "string" && branchValue.includes("(")) {
    const match = branchValue.match(/\(([^)]+)\)/);
    if (match && match[1]) {
      return match[1].trim();
    }
  }

  // 否则直接返回字符串化的值
  return String(branchValue).trim();
};

const saveGroup = async () => {
  try {
    // 确保branch只包含门市代号
    groupDialog.data.branch = extractBranchCode(groupDialog.data.branch);

    const payload = { ...groupDialog.data };

    const response = await apiClient.post(`${apiBaseUrl}/gcounts/save_group`, {
      group: payload,
    });

    if (response.data.success) {
      $q.notify({
        color: "positive",
        message: `${groupDialog.isEdit ? "更新" : "新增"}群組成功`,
        icon: "check_circle",
      });

      await loadGroups();
      groupDialog.show = false;
    } else {
      throw new Error(response.data.message || "操作失敗");
    }
  } catch (error) {
    console.error("群組儲存錯誤:", error);
    $q.notify({
      color: "negative",
      message: `${groupDialog.isEdit ? "更新" : "新增"}群組失敗: ${
        error.message
      }`,
      icon: "error",
    });
  }
};

const openItemDialog = async (group) => {
  itemDialog.group = group;
  itemDialog.search = "";
  itemDialog.selectedItems = [];
  itemDialog.show = true;
  itemDialog.loading = true;

  try {
    // 載入參照門市的商品項目
    const response = await apiClient.get(
      `${apiBaseUrl}/gcounts/get_branch_items`,
      {
        params: {
          branch: group.branch,
          gid: group.gid,
        },
      }
    );

    if (response.data.success) {
      itemDialog.items = response.data.data.filter((item) => {
        // 排除已在群組中的商品
        const existingItems = group.items || [];
        return !existingItems.some(
          (existingItem) => existingItem.items_code === item.items_code
        );
      });
    } else {
      throw new Error(response.data.message || "載入失敗");
    }
  } catch (error) {
    console.error("載入商品項目錯誤:", error);
    $q.notify({
      color: "negative",
      message: `載入商品項目失敗: ${error.message}`,
      icon: "error",
    });
  } finally {
    itemDialog.loading = false;
  }
};

const addItems = async () => {
  if (!itemDialog.group || itemDialog.selectedItems.length === 0) return;

  try {
    const response = await apiClient.post(`${apiBaseUrl}/gcounts/add_items`, {
      gid: itemDialog.group.gid,
      items: itemDialog.selectedItems,
    });

    if (response.data.success) {
      $q.notify({
        color: "positive",
        message: `新增商品項目成功`,
        icon: "check_circle",
      });

      // 更新群組的商品項目
      await loadGroupItems(itemDialog.group.gid);
      itemDialog.show = false;
    } else {
      throw new Error(response.data.message || "操作失敗");
    }
  } catch (error) {
    console.error("新增商品項目錯誤:", error);
    $q.notify({
      color: "negative",
      message: `新增商品項目失敗: ${error.message}`,
      icon: "error",
    });
  }
};

const removeItem = (group, item) => {
  deleteDialog.type = "item";
  deleteDialog.id = item.items_code;
  deleteDialog.name = item.items_name;
  deleteDialog.parentId = group.gid;
  deleteDialog.show = true;
};

const confirmDeleteCategory = (category) => {
  deleteDialog.type = "category";
  deleteDialog.id = category.cid;
  deleteDialog.name = category.cname;
  deleteDialog.show = true;
};

const confirmDeleteGroup = (group) => {
  deleteDialog.type = "group";
  deleteDialog.id = group.gid;
  deleteDialog.name = group.gname;
  deleteDialog.show = true;
};

const handleDelete = async () => {
  try {
    let response;

    switch (deleteDialog.type) {
      case "category":
        response = await apiClient.post(
          `${apiBaseUrl}/gcounts/delete_category`,
          {
            cid: deleteDialog.id,
          }
        );
        if (response.data.success) {
          await loadCategories();
          await loadGroups();
        }
        break;

      case "group":
        response = await apiClient.post(`${apiBaseUrl}/gcounts/delete_group`, {
          gid: deleteDialog.id,
        });
        if (response.data.success) {
          await loadGroups();
        }
        break;

      case "item":
        response = await apiClient.post(`${apiBaseUrl}/gcounts/delete_item`, {
          gid: deleteDialog.parentId,
          items_code: deleteDialog.id,
        });
        if (response.data.success) {
          await loadGroupItems(deleteDialog.parentId);
        }
        break;
    }

    if (response && response.data.success) {
      $q.notify({
        color: "positive",
        message: `刪除${
          deleteDialog.type === "category"
            ? "類別"
            : deleteDialog.type === "group"
            ? "群組"
            : "商品項目"
        }成功`,
        icon: "check_circle",
      });
    } else {
      throw new Error(response?.data?.message || "刪除失敗");
    }
  } catch (error) {
    console.error("刪除錯誤:", error);
    $q.notify({
      color: "negative",
      message: `刪除失敗: ${error.message}`,
      icon: "error",
    });
  }
};

const loadCategories = async () => {
  try {
    loading.value = true;
    const response = await apiClient.get(
      `${apiBaseUrl}/gcounts/get_categories`
    );

    if (response.data.success) {
      productCategories.value = response.data.data;
    } else {
      throw new Error(response.data.message || "載入失敗");
    }
  } catch (error) {
    console.error("載入類別錯誤:", error);
    $q.notify({
      color: "negative",
      message: `載入類別失敗: ${error.message}`,
      icon: "error",
    });
    productCategories.value = [];
  } finally {
    loading.value = false;
  }
};

const loadGroups = async () => {
  try {
    loading.value = true;
    const response = await apiClient.get(`${apiBaseUrl}/gcounts/get_groups`);

    if (response.data.success) {
      // 确保branch字段是正确的门市代号
      productGroups.value = response.data.data.map((group) => ({
        ...group,
        branch: extractBranchCode(group.branch),
      }));

      // 載入各群組的商品項目
      await Promise.all(
        productGroups.value.map((group) => loadGroupItems(group.gid))
      );
    } else {
      throw new Error(response.data.message || "載入失敗");
    }
  } catch (error) {
    console.error("載入群組錯誤:", error);
    $q.notify({
      color: "negative",
      message: `載入群組失敗: ${error.message}`,
      icon: "error",
    });
    productGroups.value = [];
  } finally {
    loading.value = false;
  }
};

const loadGroupItems = async (groupId) => {
  try {
    loadingItems.value = true;
    const response = await apiClient.get(`${apiBaseUrl}/gcounts/get_items`, {
      params: { gid: groupId },
    });

    if (response.data.success) {
      const groupIndex = productGroups.value.findIndex(
        (g) => g.gid === groupId
      );
      if (groupIndex >= 0) {
        productGroups.value[groupIndex].items = response.data.data;
      }
    } else {
      throw new Error(response.data.message || "載入失敗");
    }
  } catch (error) {
    console.error(`載入群組 ${groupId} 商品項目錯誤:`, error);
  } finally {
    loadingItems.value = false;
  }
};

// 類別排序處理
const onCategorySortEnd = async () => {
  // 更新排序號
  productCategories.value.forEach((category, index) => {
    category.sno = index + 1;
  });

  try {
    const response = await apiClient.post(
      `${apiBaseUrl}/gcounts/save_category_sort`,
      {
        categories: productCategories.value.map((cat) => ({
          cid: cat.cid,
          sno: cat.sno,
        })),
      }
    );

    if (response.data.success) {
      $q.notify({
        color: "positive",
        message: "類別排序已儲存",
        icon: "check_circle",
      });
    } else {
      throw new Error(response.data.message || "儲存失敗");
    }
  } catch (error) {
    console.error("儲存類別排序錯誤:", error);
    $q.notify({
      color: "negative",
      message: `儲存類別排序失敗: ${error.message}`,
      icon: "error",
    });
  }
};

// 群組排序處理
const onGroupSortEnd = async () => {
  // 更新排序號
  productGroups.value.forEach((group, index) => {
    group.sno = index + 1;
  });

  try {
    const response = await apiClient.post(
      `${apiBaseUrl}/gcounts/save_group_sort`,
      {
        groups: productGroups.value.map((group) => ({
          gid: group.gid,
          sno: group.sno,
        })),
      }
    );

    if (response.data.success) {
      $q.notify({
        color: "positive",
        message: "群組排序已儲存",
        icon: "check_circle",
      });
    } else {
      throw new Error(response.data.message || "儲存失敗");
    }
  } catch (error) {
    console.error("儲存群組排序錯誤:", error);
    $q.notify({
      color: "negative",
      message: `儲存群組排序失敗: ${error.message}`,
      icon: "error",
    });
  }
};

// 商品項目不需要排序功能

// 移除了全選相關功能

// 初始化
onMounted(async () => {
  await loadBranchOptions();
  await loadCategories();
  await loadGroups();
});
</script>

<style scoped>
.title-section {
  padding-bottom: 8px;
}

.custom-separator {
  margin-bottom: 16px;
}
</style>
