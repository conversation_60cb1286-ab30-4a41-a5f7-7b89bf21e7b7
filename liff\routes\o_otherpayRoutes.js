const express = require("express");
const {
  getOtherpayList,
  saveOtherpay,
  deleteOtherpay,
  saveOtherpaySort,
  getHeadOtherpayList,
} = require("../controllers/o_otherpayController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_otherpayList", authMiddleware, getOtherpayList);
router.post("/save_Otherpay", authMiddleware, saveOtherpay);
router.post("/delete_Otherpay", authMiddleware, deleteOtherpay);
router.post("/save_OtherpaySort", authMiddleware, saveOtherpaySort);
router.get("/get_HeadOtherpayList", authMiddleware, getHeadOtherpayList);

module.exports = router;
