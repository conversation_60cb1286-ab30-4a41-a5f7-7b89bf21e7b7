<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="
        width: 100%;
        max-width: 500px;
        overflow: hidden;
        border-radius: 12px;
      "
    >
      <!-- 標題 -->
      <q-card-section
        class="text-h6 text-primary text-center"
        style="
          position: sticky;
          top: 0;
          z-index: 10;
          border-bottom: 1px solid #eee;
        "
      >
        其他付款管理
      </q-card-section>
      <!-- 付款方式清單 -->
      <q-list separator>
        <VueDraggable
          v-model="otherpayList"
          item-key="code"
          tag="div"
          handle=".otherpay-drag-handle"
          ghost-class="bg-grey-3"
          delay="200"
          @end="onSortEnd"
        >
          <div
            v-for="item in otherpayList"
            :key="item.code"
            class="q-mb-xs"
            style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
          >
            <q-item
              class="bg-grey-1 q-pa-sm rounded-borders"
              clickable
              v-ripple
              style="align-items: center"
            >
              <!-- 拖曳手把 -->
              <q-item-section avatar class="otherpay-drag-handle">
                <q-icon name="drag_indicator" color="grey-7" />
              </q-item-section>
              <!-- 付款資訊 -->
              <q-item-section>
                <div class="text-subtitle2">{{ item.name }}</div>
                <div class="text-caption text-grey">編號: {{ item.code }}</div>
                <div class="text-caption text-grey">
                  種類: {{ paykindLabel(item.paykind) }}
                </div>
              </q-item-section>
              <!-- 操作按鈕 -->
              <q-item-section side>
                <div class="row items-center q-gutter-xs">
                  <q-btn
                    flat
                    round
                    dense
                    icon="edit"
                    color="primary"
                    @click.stop="editItem(item)"
                  />
                  <q-btn
                    flat
                    round
                    dense
                    icon="delete"
                    color="negative"
                    @click.stop="openDeleteDialog(item)"
                  />
                </div>
              </q-item-section>
            </q-item>
          </div>
        </VueDraggable>
      </q-list>
      <!-- 新增付款方式按鈕 -->
      <q-card-section class="q-pa-sm q-pt-none">
        <q-btn
          icon="add"
          label="新增付款方式"
          dense
          flat
          color="primary"
          @click="editItem(null)"
        />
      </q-card-section>
    </q-card>
  </q-page>

  <!-- 編輯/新增 Dialog -->
  <q-dialog v-model="editDialog" persistent>
    <q-card
      class="q-pa-sm bg-white"
      style="width: 100%; max-width: 500px; max-height: 90vh; overflow-y: auto"
    >
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-primary">
          {{ isEditing ? "編輯付款" : "新增付款" }}
        </div>
        <div style="padding: 4px; margin-right: -8px">
          <q-btn icon="close" flat round dense size="md" v-close-popup />
        </div>
      </q-card-section>
      <q-form @submit.prevent="saveItem(form)">
        <q-card-section class="q-pt-md q-gutter-md">
          <q-select
            v-if="!isEditing"
            v-model="form.selectedHead"
            :options="filteredHeadOtherpayOptions"
            label="選擇總公司付款名稱"
            option-label="name"
            option-value="chtot"
            emit-value
            map-options
            dense
            outlined
            @update:model-value="onHeadSelect"
          />
          <div class="row q-col-gutter-sm">
            <div class="col-5">
              <q-input
                v-model="form.code"
                label="編號"
                dense
                readonly
                class="q-mb-none"
              />
            </div>
            <div class="col">
              <q-input
                v-model="form.name"
                label="名稱"
                dense
                readonly
                class="q-mb-none"
              />
            </div>
          </div>
          <q-select
            v-model="form.paykind"
            label="種類"
            :options="paykindOptions"
            emit-value
            map-options
            dense
            outlined
          />
          <!-- 移除排序欄位 <q-input v-model.number="form.sn" label="排序" type="number" dense /> -->
        </q-card-section>
        <q-card-actions align="right" class="q-pt-md">
          <q-btn
            unelevated
            outline
            icon="save"
            label="儲存"
            type="submit"
            color="secondary"
            :disable="!form.name || !form.code"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>

  <!-- 刪除確認 Dialog -->
  <q-dialog v-model="deleteDialog">
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          付款方式刪除後無法復原，是否繼續？
        </div>
      </q-card-section>
      <q-separator class="q-mt-md" />
      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteConfirmed()"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { VueDraggable } from "vue-draggable-plus";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";

const props = defineProps({ Branch: String });
const $q = useQuasar();

const otherpayList = ref([]);
const headOtherpayOptions = ref([]);
const editDialog = ref(false);
const deleteDialog = ref(false);
const isEditing = ref(false);
const deleteTarget = ref(null);
const form = ref({
  code: "",
  name: "",
  paykind: "A",
  sn: 1,
  selectedHead: null,
});

const paykindOptions = [
  { label: "其他付款", value: "A" },
  { label: "禮券折價", value: "B" },
  { label: "禮券支付", value: "C" },
];

const filteredHeadOtherpayOptions = computed(() => {
  const existingCodes = new Set(otherpayList.value.map((item) => item.code));
  return headOtherpayOptions.value.filter(
    (opt) => !existingCodes.has(opt.chtot)
  );
});

function paykindLabel(val) {
  const found = paykindOptions.find((o) => o.value === val);
  return found ? found.label : val;
}

function onHeadSelect(val) {
  const selected = headOtherpayOptions.value.find((o) => o.chtot === val);
  if (selected) {
    form.value.name = selected.name;
    form.value.code = selected.chtot;
  }
}

function editItem(item) {
  if (item && item.code) {
    isEditing.value = true;
    form.value = {
      code: item.code,
      name: item.name,
      paykind: item.paykind,
      sn: item.sn,
      selectedHead: null,
    };
  } else {
    isEditing.value = false;
    form.value = {
      code: "",
      name: "",
      paykind: "A",
      sn: otherpayList.value.length + 1,
      selectedHead: null,
    };
  }
  editDialog.value = true;
}

async function fetchOtherpay() {
  try {
    const res1 = await apiClient.get("/otherpay/get_otherpayList", {
      params: { branch: props.Branch },
    });
    if (res1.data.success) {
      otherpayList.value = res1.data.data;
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "讀取失敗" });
  }
}

async function fetchHeadOtherpay() {
  try {
    const res2 = await apiClient.get("/otherpay/get_HeadOtherpayList");
    if (res2.data.success) {
      headOtherpayOptions.value = res2.data.data;
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "讀取總公司清單失敗" });
  }
}

async function saveItem(formData) {
  if (!formData.code || !formData.name) {
    $q.notify({ type: "warning", message: "請選擇總公司付款名稱" });
    return;
  }
  try {
    await apiClient.post(
      "/otherpay/save_Otherpay",
      {
        code: formData.code,
        name: formData.name,
        paykind: formData.paykind,
        sn: formData.sn,
      },
      { params: { branch: props.Branch } }
    );
    $q.notify({ type: "positive", message: "儲存成功" });
    editDialog.value = false;
    await fetchOtherpay();
  } catch (err) {
    $q.notify({ type: "negative", message: "儲存失敗" });
  }
}

function openDeleteDialog(item) {
  deleteTarget.value = item;
  deleteDialog.value = true;
}

async function deleteConfirmed() {
  if (!deleteTarget.value?.code) return;
  try {
    await apiClient.post(
      "/otherpay/delete_Otherpay",
      { code: deleteTarget.value.code },
      { params: { branch: props.Branch } }
    );
    $q.notify({ type: "positive", message: "刪除成功" });
    editDialog.value = false;
    deleteDialog.value = false;
    await fetchOtherpay();
  } catch (err) {
    $q.notify({ type: "negative", message: "刪除失敗" });
  }
}

async function onSortEnd() {
  // 更新排序 sn
  otherpayList.value.forEach((item, idx) => {
    item.sn = idx + 1;
  });
  try {
    await apiClient.post("/otherpay/save_OtherpaySort", {
      branch: props.Branch,
      list: otherpayList.value.map((item) => ({
        code: item.code,
        sn: item.sn,
      })),
    });
    $q.notify({ type: "positive", message: "排序已儲存" });
  } catch (err) {
    $q.notify({ type: "negative", message: "排序儲存失敗" });
  }
}

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await fetchOtherpay();
  await fetchHeadOtherpay();
  $q.loading.hide();
});
</script>
