const { getPoolByBranch, sql } = require("../services/dbPoolManager");

const getKclass = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查 kclass 表是否存在
    const checkKclass = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'kclass'
    `);

    if (checkKclass.recordset.length === 0) {
      console.log("🔧 建立資料表 kclass");
      await pool.request().query(`
        CREATE TABLE kclass (
          TB_ID INT IDENTITY(1,1) PRIMARY KEY,
          code NVARCHAR(10) NOT NULL,
          name NVARCHAR(50),
          vcode1 NVARCHAR(10),
          vcode2 NVARCHAR(10)
        )
      `);
    }

    const result = await pool.request().query(`
      SELECT 
        k.TB_ID, 
        k.code, 
        k.name, 
        k.vcode1, 
        k.vcode2,
        v1.name AS vcode1_name,
        v2.name AS vcode2_name
      FROM kclass k
      LEFT JOIN vclass v1 ON k.vcode1 = v1.code
      LEFT JOIN vclass v2 ON k.vcode2 = v2.code
      ORDER BY k.code
    `);

    // 處理資料，確保所有欄位都有值
    const processedData = result.recordset.map((row) => ({
      TB_ID: row.TB_ID || null,
      code: row.code || "",
      name: row.name || "",
      vcode1: row.vcode1 || "",
      vcode2: row.vcode2 || "",
      vcode1_name: row.vcode1_name || "",
      vcode2_name: row.vcode2_name || "",
    }));

    return res.json({ success: true, data: processedData });
  } catch (err) {
    console.error("❌ getKclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const createKclass = async (req, res) => {
  const { branch } = req.query;
  const { code, name, vcode1, vcode2 } = req.body;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  if (!code || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查廚房編號是否已存在
    const checkResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("SELECT code FROM kclass WHERE code = @code");

    if (checkResult.recordset.length > 0) {
      return res
        .status(400)
        .json({ success: false, message: "廚房編號已存在" });
    }

    // 新增廚房資料
    const insertResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.VarChar, name)
      .input("vcode1", sql.VarChar, vcode1 || null)
      .input("vcode2", sql.VarChar, vcode2 || null).query(`
        INSERT INTO kclass (code, name, vcode1, vcode2)
        VALUES (@code, @name, @vcode1, @vcode2)
      `);

    return res.json({ success: true, message: "廚房部門新增成功" });
  } catch (err) {
    console.error("❌ createKclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const updateKclass = async (req, res) => {
  const { branch } = req.query;
  const { code, name, vcode1, vcode2 } = req.body;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  if (!code || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 更新廚房資料
    const updateResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.VarChar, name)
      .input("vcode1", sql.VarChar, vcode1 || null)
      .input("vcode2", sql.VarChar, vcode2 || null).query(`
        UPDATE kclass 
        SET name = @name, vcode1 = @vcode1, vcode2 = @vcode2
        WHERE code = @code
      `);

    if (updateResult.rowsAffected[0] === 0) {
      return res
        .status(404)
        .json({ success: false, message: "廚房部門不存在" });
    }

    return res.json({ success: true, message: "廚房部門更新成功" });
  } catch (err) {
    console.error("❌ updateKclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const deleteKclass = async (req, res) => {
  const { branch } = req.query;
  const { code } = req.params;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  if (!code) {
    return res.status(400).json({ success: false, message: "缺少廚房編號" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查是否有訂單使用此廚房部門
    const checkUsage = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("SELECT COUNT(*) as count FROM omenum WHERE C1 = @code");

    if (checkUsage.recordset[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: "此廚房部門正在使用中，無法刪除",
      });
    }

    // 刪除廚房部門
    const deleteResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM kclass WHERE code = @code");

    if (deleteResult.rowsAffected[0] === 0) {
      return res
        .status(404)
        .json({ success: false, message: "廚房部門不存在" });
    }

    return res.json({ success: true, message: "廚房部門刪除成功" });
  } catch (err) {
    console.error("❌ deleteKclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得可用的出據主機列表
const getAvailableVclass = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const result = await pool.request().query(`
      SELECT code, name
      FROM vclass
      ORDER BY code
    `);

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getAvailableVclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getKclass,
  createKclass,
  updateKclass,
  deleteKclass,
  getAvailableVclass,
};
