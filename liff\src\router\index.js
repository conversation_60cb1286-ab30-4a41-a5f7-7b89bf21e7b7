import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from "vue-router";
import routes from "./routes";

// ✅ 直接執行 `createWebHistory()` 或 `createWebHashHistory()`
const historyMode =
  process.env.VUE_ROUTER_MODE === "history"
    ? createWebHistory()
    : createWebHashHistory();

const router = createRouter({
  scrollBehavior: () => ({ left: 0, top: 0 }),
  history: historyMode, // ✅ 正確的 history 設定
  routes,
});

export default router;
