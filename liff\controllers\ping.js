const { getPoolByBranch } = require("../services/dbPoolManager");

async function checkSqlConnection(cod_cust) {
  try {
    const pool = await getPoolByBranch(cod_cust);
    // 連線成功，立即關閉（不影響全域池）
    if (pool.connected) {
      // pool.close() 會影響全域池，這裡不主動關閉
      return { status: "online", message: "連線池建立成功" };
    } else {
      return { status: "offline", message: "連線池未連線" };
    }
  } catch (err) {
    // 常見錯誤：帳密錯、IP錯、DB連不上
    return { status: "offline", message: "連線失敗", error: err.message };
  }
}

module.exports = checkSqlConnection;
