const express = require("express");
const {
  getTicketList,
  createTicket,
  updateTicket,
  deleteTicket,
} = require("../controllers/o_ticketController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_ticketList", authMiddleware, getTicketList);
router.post("/create_ticket", authMiddleware, createTicket);
router.put("/update_ticket", authMiddleware, updateTicket);
router.delete("/delete_ticket/:productid", authMiddleware, deleteTicket);

module.exports = router;
