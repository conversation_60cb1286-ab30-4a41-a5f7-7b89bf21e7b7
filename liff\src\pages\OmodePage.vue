<template>
  <q-page
    class="q-pa-sm flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">消費方式管理</div>
          <div>
            <q-btn flat round size="sm" color="green" @click="openModeDialog()">
              <folder-plus :stroke-width="1" :size="26" />
              <q-tooltip>新增消費方式</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <div v-if="loading" class="q-pa-md flex flex-center">
        <q-spinner color="primary" size="3em" />
        <div class="q-ml-sm text-subtitle1 text-grey">載入中...</div>
      </div>
      <!-- 消費方式清單 -->
      <q-list separator class="q-py-sm">
        <template v-if="consumptionModes.length > 0">
          <VueDraggable
            v-model="consumptionModes"
            item-key="code"
            tag="div"
            handle=".mode-drag-handle"
            ghost-class="bg-indigo-1"
            delay="100"
            @end="onModeSortEnd"
          >
            <div
              v-for="mode in consumptionModes"
              :key="mode.code"
              class="q-mb-sm"
              style="
                border: 1px solid #ccc;
                border-radius: 8px;
                overflow: hidden;
              "
            >
              <q-expansion-item
                expand-separator
                header-class="text-primary"
                class="q-pa-none"
                @show="loadHiddenItems(mode)"
              >
                <template #header>
                  <q-item
                    dense
                    class="q-pa-none q-gutter-none items-center"
                    style="width: 100%"
                  >
                    <!-- 拖曳手把 -->
                    <q-item-section avatar class="mode-drag-handle">
                      <q-icon name="drag_handle" color="grey-7" />
                    </q-item-section>

                    <!-- 消費方式資訊 -->
                    <q-item-section>
                      <div class="row items-center">
                        <q-item-label>{{ mode.name }}</q-item-label>
                        <q-item-label
                          v-if="mode.spec"
                          class="q-ml-sm text-caption text-grey"
                        >
                          ({{ mode.spec }})
                        </q-item-label>
                      </div>

                      <q-item-label caption>
                        隱藏商品: {{ mode.hiddenCount || 0 }} 項
                      </q-item-label>
                    </q-item-section>

                    <!-- 操作按鈕 -->
                  </q-item>
                </template>

                <!-- 展開後顯示的隱藏品項列表 -->
                <q-card-section class="q-pa-md q-pb-lg">
                  <div class="row items-center justify-between q-mb-sm">
                    <q-btn
                      flat
                      dense
                      color="green"
                      @click.stop="openHiddenItemsDialog(mode)"
                    >
                      <folder-cog
                        :stroke-width="1"
                        :size="26"
                        class="q-mr-xs"
                      />
                      管理隱藏商品
                    </q-btn>
                  </div>

                  <div v-if="mode.hiddenItems && mode.hiddenItems.length > 0">
                    <q-list bordered separator class="q-mb-md">
                      <q-item
                        v-for="item in mode.hiddenItems"
                        :key="item.code"
                        class="bg-white q-mb-xs"
                      >
                        <q-item-section>
                          <q-item-label>{{ item.name }}</q-item-label>
                          <q-item-label caption>
                            編號: {{ item.code }}
                            <span v-if="item.spec" class="q-ml-sm"
                              >規格: {{ item.spec }}</span
                            >
                          </q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </div>
                  <div v-else class="text-grey text-center q-py-sm">
                    尚未設定隱藏商品
                  </div>

                  <div class="row justify-end q-mt-sm">
                    <q-btn
                      flat
                      round
                      dense
                      color="negative"
                      @click.stop="openDeleteDialog(mode)"
                    >
                      <folder-x :stroke-width="1" :size="26" />
                      <q-tooltip>刪除消費方式</q-tooltip>
                    </q-btn>
                  </div>
                </q-card-section>
              </q-expansion-item>
            </div>
          </VueDraggable>
        </template>
        <div v-else class="text-grey text-center q-pa-lg">尚未新增消費方式</div>
      </q-list>
    </q-card>
  </q-page>

  <!-- ✏️ 編輯消費方式 Dialog -->
  <q-dialog v-model="modeDialog.show" persistent>
    <q-card class="q-pa-sm bg-white" style="width: 500px; max-width: 90vw">
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-primary">新增消費方式</div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-form @submit.prevent="saveMode">
        <q-card-section class="q-pt-md q-gutter-md">
          <!-- 新增時只顯示下拉選單，不顯示編號 -->
          <q-select
            v-model="modeDialog.data.code"
            :options="availableModeOptions"
            label="消費方式名稱"
            option-label="label"
            option-value="value"
            emit-value
            map-options
            dense
            outlined
            @update:model-value="
              (val) => {
                const opt = availableModeOptions.find((o) => o.value === val);
                if (opt) modeDialog.data.name = opt.name;
              }
            "
            :rules="[(val) => !!val || '請選擇消費方式']"
          />
        </q-card-section>

        <!-- 按鈕列 -->
        <q-card-actions align="right" class="q-pt-md">
          <q-btn label="取消" color="grey" flat v-close-popup class="q-mr-sm" />
          <q-btn
            unelevated
            outline
            icon="save"
            label="儲存"
            type="submit"
            color="secondary"
            :disable="!modeDialog.data.name || !modeDialog.data.code"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>

  <!-- 管理隱藏商品 Dialog -->
  <q-dialog v-model="hiddenItemsDialog.show" persistent>
    <q-card style="width: 600px; max-width: 95vw; max-height: 80vh">
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-primary">
          管理隱藏商品 - {{ hiddenItemsDialog.mode?.name }}
        </div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <!-- 搜尋 + 清單 -->
      <q-card-section class="q-pt-md">
        <q-input
          v-model="hiddenItemsDialog.search"
          dense
          outlined
          label="搜尋商品"
          debounce="300"
          clearable
        >
          <template #append>
            <q-btn
              flat
              dense
              icon="search"
              color="primary"
              @click="selectAllVisibleItems"
              :disable="!filteredAvailableItems.length"
            />
          </template>
        </q-input>
      </q-card-section>

      <q-card-section style="max-height: 400px; overflow-y: auto">
        <div v-if="loadingItems" class="text-center q-pa-md">
          <q-spinner color="primary" size="2em" />
          <div class="q-mt-sm">載入商品中...</div>
        </div>
        <div v-else>
          <div
            v-for="category in filteredGroupedItems"
            :key="category.categoryCode"
            class="q-mb-md"
          >
            <!-- 分類標題 -->
            <div
              class="text-subtitle2 text-primary q-pa-sm bg-grey-2 rounded-borders"
            >
              {{ category.categoryName }}
              <span class="text-caption text-grey">
                ({{ getSelectedCountInCategory(category) }}/{{
                  category.items.length
                }})
              </span>
              <q-btn
                flat
                dense
                size="sm"
                :label="isCategoryAllSelected(category) ? '取消全選' : '全選'"
                color="primary"
                class="q-ml-sm"
                @click="toggleCategorySelection(category)"
              />
            </div>

            <!-- 分類內的商品 -->
            <q-list bordered>
              <q-item
                v-for="item in category.items"
                :key="item.code"
                tag="label"
                clickable
                class="q-mb-xs"
              >
                <q-item-section avatar>
                  <q-checkbox
                    v-model="hiddenItemsDialog.selectedKeys"
                    :val="getItemKey(item)"
                    color="primary"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ item.name }}</q-item-label>
                  <q-item-label caption>
                    編號: {{ item.code }}
                    <span v-if="item.spec" class="q-ml-sm">
                      規格: {{ item.spec }}
                    </span>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>

          <div
            v-if="!filteredGroupedItems.length"
            class="text-grey text-center q-pa-md"
          >
            無可選擇的商品
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pt-md">
        <q-btn label="取消" color="grey" flat v-close-popup class="q-mr-sm" />
        <q-btn
          label="儲存"
          color="secondary"
          unelevated
          outline
          icon="save"
          @click="saveHiddenItems"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 刪除確認 Dialog -->
  <q-dialog v-model="deleteDialog">
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          消費方式
          <strong>{{ modeToDelete?.name }}</strong> 刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteModeConfirmed"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { VueDraggable } from "vue-draggable-plus";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
import { FilePlus2 } from "lucide-vue-next";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const props = defineProps({ Branch: String });
const $q = useQuasar();

// 消費方式資料
const consumptionModes = ref([]);
const loading = ref(false);
const loadingItems = ref(false);

// Dialog 控制
const modeDialog = ref({
  show: false,
  data: {
    no: null,
    code: "",
    name: "",
  },
});

const hiddenItemsDialog = ref({
  show: false,
  search: "",
  selectedKeys: [], // checkbox v-model 改為 selectedKeys
  mode: null,
  groupedItems: [],
});

const deleteDialog = ref(false);
const modeToDelete = ref(null);

// 計算屬性
// 搜尋功能 filteredGroupedItems 以 hiddenItemsDialog.groupedItems 為基礎
const filteredGroupedItems = computed(() => {
  const keyword = hiddenItemsDialog.value.search?.toLowerCase() || "";
  if (!keyword) return hiddenItemsDialog.value.groupedItems;
  return hiddenItemsDialog.value.groupedItems
    .map((category) => ({
      ...category,
      items: category.items.filter(
        (item) =>
          item.name.toLowerCase().includes(keyword) ||
          item.code.toLowerCase().includes(keyword) ||
          (category.categoryName &&
            category.categoryName.toLowerCase().includes(keyword))
      ),
    }))
    .filter((category) => category.items.length > 0);
});

const filteredAvailableItems = computed(() => {
  return filteredGroupedItems.value.flatMap((category) => category.items);
});

const availableModeOptions = ref([]);

// 方法
const getHiddenItemsCount = (mode) => {
  return (
    mode.hiddenItems?.reduce(
      (total, category) => total + category.items.length,
      0
    ) || 0
  );
};

// 勾選唯一 key
function getItemKey(item) {
  return `${item.code}__${item.spec || ""}`;
}

// 載入已隱藏商品時
const getSelectedCountInCategory = (category) => {
  const selectedKeys = new Set(hiddenItemsDialog.value.selectedKeys);
  return category.items.filter((item) => selectedKeys.has(getItemKey(item)))
    .length;
};
const isCategoryAllSelected = (category) => {
  const selectedKeys = new Set(hiddenItemsDialog.value.selectedKeys);
  return category.items.every((item) => selectedKeys.has(getItemKey(item)));
};
const toggleCategorySelection = (category) => {
  const selected = hiddenItemsDialog.value.selectedKeys;
  const categoryItemKeys = new Set(category.items.map(getItemKey));
  const selectedKeys = new Set(selected);
  const allSelected = category.items.every((item) =>
    selectedKeys.has(getItemKey(item))
  );
  if (allSelected) {
    // 取消全選該分類
    hiddenItemsDialog.value.selectedKeys = selected.filter(
      (key) => !categoryItemKeys.has(key)
    );
  } else {
    // 全選該分類
    const newSelected = selected.filter((key) => !categoryItemKeys.has(key));
    category.items.forEach((item) => {
      const key = getItemKey(item);
      if (!selectedKeys.has(key)) {
        newSelected.push(key);
      }
    });
    hiddenItemsDialog.value.selectedKeys = newSelected;
  }
};
const selectAllVisibleItems = () => {
  const visible = filteredGroupedItems.value.flatMap(
    (category) => category.items
  );
  const selected = hiddenItemsDialog.value.selectedKeys;
  const selectedKeys = new Set(selected);
  const allSelected = visible.every((item) =>
    selectedKeys.has(getItemKey(item))
  );
  if (allSelected) {
    // 取消全選可見項目
    const visibleKeys = new Set(visible.map(getItemKey));
    hiddenItemsDialog.value.selectedKeys = selected.filter(
      (key) => !visibleKeys.has(key)
    );
  } else {
    // 全選可見項目
    const newSelected = [...selected];
    visible.forEach((item) => {
      const key = getItemKey(item);
      if (!selectedKeys.has(key)) {
        newSelected.push(key);
      }
    });
    hiddenItemsDialog.value.selectedKeys = newSelected;
  }
};

// 開啟 Dialog
const openModeDialog = async () => {
  modeDialog.value.data = {
    no: null,
    code: "",
    name: "",
  };
  // 取得可新增消費方式清單
  try {
    const res = await apiClient.get("/omode/get_available_modes", {
      params: { branch: props.Branch },
    });
    availableModeOptions.value = (res.data.data || []).map((opt) => ({
      label: opt.name,
      value: opt.code,
      name: opt.name,
      code: opt.code,
    }));
    modeDialog.value.show = true;
  } catch (err) {
    console.error("❌ 載入可用消費方式失敗", err);
    $q.notify({ type: "negative", message: "無法載入可用消費方式" });
  }
};

// openHiddenItemsDialog 載入已隱藏商品後，將隱藏商品數量寫入對應的 mode.hiddenCount
const openHiddenItemsDialog = async (mode) => {
  hiddenItemsDialog.value.mode = mode;
  hiddenItemsDialog.value.search = "";
  hiddenItemsDialog.value.selectedKeys = [];
  loadingItems.value = true;

  try {
    // 載入所有可用商品
    const res = await apiClient.get("/omode/get_available_items", {
      params: { branch: props.Branch },
    });
    hiddenItemsDialog.value.groupedItems = (res.data.data || []).map(
      (category) => ({
        ...category,
        items: category.items.map((item) => ({
          ...item,
          code: String(item.code).trim(),
        })),
      })
    );
    // 載入當前已隱藏的商品
    const hiddenRes = await apiClient.get("/omode/get_hidden_items", {
      params: {
        branch: props.Branch,
        menumodcode: mode.code,
      },
    });
    hiddenItemsDialog.value.selectedKeys = hiddenRes.data.data.map(getItemKey);
    // 將隱藏商品數量寫入 mode.hiddenCount
    mode.hiddenCount = hiddenItemsDialog.value.selectedKeys.length;
    hiddenItemsDialog.value.show = true;
  } catch (err) {
    console.error("❌ 載入商品清單失敗", err);
    $q.notify({ type: "negative", message: "無法載入商品清單" });
  } finally {
    loadingItems.value = false;
  }
};

const openDeleteDialog = (mode) => {
  modeToDelete.value = mode;
  deleteDialog.value = true;
};

// 儲存方法
const saveMode = async () => {
  const mode = { ...modeDialog.value.data };

  if (!mode.name || !mode.code) {
    $q.notify({ type: "negative", message: "請填寫完整資訊" });
    return;
  }

  try {
    const res = await apiClient.post("/omode/save_consumption_mode", {
      branch: props.Branch,
      mode: mode,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "儲存成功" });
      modeDialog.value.show = false;
      await fetchConsumptionModes();
    } else {
      $q.notify({ type: "negative", message: res.data.message || "儲存失敗" });
    }
  } catch (err) {
    console.error("❌ 儲存消費方式失敗:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

// 儲存隱藏商品設定後，重新查詢並更新主列表的隱藏商品數量
const saveHiddenItems = async () => {
  const mode = hiddenItemsDialog.value.mode;
  const selectedItems = hiddenItemsDialog.value.selectedKeys;
  try {
    const res = await apiClient.post("/omode/save_hidden_items", {
      branch: props.Branch,
      menumodcode: mode.code,
      items: selectedItems.map((key) => {
        const [code, spec] = key.split("__");
        return { code, spec };
      }),
    });
    if (res.data.success) {
      $q.notify({ type: "positive", message: "隱藏商品設定已儲存" });
      hiddenItemsDialog.value.show = false;
      // 重新查詢並更新主列表的隱藏商品數量
      const hiddenRes = await apiClient.get("/omode/get_hidden_items", {
        params: {
          branch: props.Branch,
          menumodcode: mode.code,
        },
      });
      const uniqueCodes = new Set(
        hiddenRes.data.data.map((item) => String(item.code).trim())
      );
      mode.hiddenCount = uniqueCodes.size;
      await fetchConsumptionModes(); // 重新載入以更新主列表
    } else {
      $q.notify({ type: "negative", message: res.data.message || "儲存失敗" });
    }
  } catch (err) {
    console.error("❌ 儲存隱藏商品失敗:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

const deleteModeConfirmed = async () => {
  if (!modeToDelete.value) return;

  try {
    const res = await apiClient.post("/omode/delete_consumption_mode", {
      branch: props.Branch,
      code: modeToDelete.value.code,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "刪除成功" });
      await fetchConsumptionModes();
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    console.error("❌ 刪除消費方式失敗:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  } finally {
    deleteDialog.value = false;
    modeToDelete.value = null;
  }
};

// 排序方法
const onModeSortEnd = async () => {
  // 更新每個消費方式的排序欄位
  consumptionModes.value.forEach((mode, index) => {
    mode.no = index + 1;
  });

  try {
    await apiClient.post("/omode/save_mode_sort", {
      branch: props.Branch,
      modes: consumptionModes.value.map((mode) => ({
        code: mode.code,
        no: mode.no,
      })),
    });

    $q.notify({ type: "positive", message: "消費方式排序已儲存" });
  } catch (err) {
    console.error("❌ 消費方式排序儲存失敗:", err);
    $q.notify({ type: "negative", message: "消費方式排序儲存失敗" });
  }
};

// 載入資料
const fetchConsumptionModes = async () => {
  try {
    loading.value = true;
    const res = await apiClient.get("/omode/get_consumption_modes", {
      params: { branch: props.Branch },
    });
    if (res.data?.success) {
      // fetchConsumptionModes 直接用後端回傳的每筆（每個規格一筆）
      const modes = res.data.data.map((item) => ({
        ...item,
        code: String(item.code).trim(),
      }));
      // 先查一次所有可用商品
      const availableRes = await apiClient.get("/omode/get_available_items", {
        params: { branch: props.Branch },
      });
      // 展平成所有可用商品 code
      const availableCodes = new Set();
      (availableRes.data.data || []).forEach((category) => {
        (category.items || []).forEach((item) => {
          availableCodes.add(String(item.code).trim());
        });
      });
      // 並行查詢每個 mode 的隱藏商品數量（只算可用商品中有的 code）
      await Promise.all(
        modes.map(async (mode) => {
          const hiddenRes = await apiClient.get("/omode/get_hidden_items", {
            params: { branch: props.Branch, menumodcode: mode.code },
          });
          // 用 code+spec 組合唯一 key
          const hiddenKeys = new Set(
            hiddenRes.data.data.map(
              (item) => `${item.code}__${item.spec || ""}`
            )
          );
          mode.hiddenCount = hiddenKeys.size;
        })
      );
      consumptionModes.value = modes;
    } else {
      console.error("❌ 載入消費方式失敗:", res.data.message);
    }
  } catch (err) {
    console.error("❌ 載入消費方式時發生錯誤:", err.message);
  } finally {
    loading.value = false;
    $q.loading.hide();
  }
};

// 載入特定消費方式的隱藏品項
const loadHiddenItems = async (mode) => {
  // 如果已經載入過，就不重複載入
  if (mode.hiddenItems) return;

  // 設置載入中狀態
  mode.loadingHiddenItems = true;

  try {
    const res = await apiClient.get("/omode/get_hidden_items", {
      params: {
        branch: props.Branch,
        menumodcode: mode.code,
      },
    });

    // 設置隱藏品項列表
    mode.hiddenItems = res.data.data;
  } catch (err) {
    console.error("❌ 載入隱藏商品失敗", err);
    $q.notify({ type: "negative", message: "無法載入隱藏商品" });
  } finally {
    mode.loadingHiddenItems = false;
  }
};

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await fetchConsumptionModes();
});
</script>

<style scoped>
.title-section {
  padding-bottom: 8px;
}
.custom-separator {
  margin-bottom: 16px;
}

/* 拖曳時的樣式 */
.bg-indigo-1 {
  background-color: rgba(63, 81, 181, 0.1);
}
</style>
