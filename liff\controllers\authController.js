const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const sql = require("mssql");
const dbConfig = require("../config/db");

// **🔹 JWT 驗證 API**
const verifyToken = (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(401).json({ success: false, message: "❌ Token 不存在" });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    //console.log("✅ Token 驗證成功:", decoded);
    res.json({ success: true, decoded });
  } catch (error) {
    console.error("❌ Token 驗證失敗:", error.message);
    res.status(401).json({ success: false, message: "❌ Token 無效或已過期" });
  }
};

// **🔹 LINE 登入 API**
const lineLogin = async (req, res) => {
  const { userId, displayName, pictureUrl } = req.body;

  if (!userId) {
    return res.status(400).json({ success: false, message: "❌ 缺少 userId" });
  }

  try {
    console.log("🔍 查詢使用者 UID:", userId);

    // 🔹 查詢資料庫，確認使用者是否存在
    const pool = await sql.connect(dbConfig);
    const user = await pool
      .request()
      .input("UID", sql.VarChar, userId)
      .query("SELECT * FROM users WHERE UID = @UID");

    if (user.recordset.length === 0) {
      console.error("❌ 使用者不存在");
      return res
        .status(401)
        .json({ success: false, message: "❌ 使用者不存在，請聯繫管理員" });
    }

    console.log("✅ 使用者已存在，產生 JWT");

    // 🔹 產生 JWT
    const userToken = jwt.sign(
      {
        userId: userId,
        displayName: displayName,
        picture: pictureUrl,
      },
      process.env.JWT_SECRET,
      { expiresIn: "1H" }
    );

    res.json({ success: true, token: userToken });
  } catch (error) {
    console.error("❌ LINE 登入失敗:", error);
    res.status(500).json({ success: false, message: "❌ LINE 登入失敗" });
  }
};

const login = async (req, res) => {
  const { username, password } = req.body;

  try {
    const pool = await sql.connect(dbConfig);

    // **🔍 查詢使用者**
    const user = await pool
      .request()
      .input("username", sql.VarChar, username)
      .query(
        "SELECT id, username, password FROM users WHERE username = @username"
      );

    if (user.recordset.length === 0) {
      return res.status(401).json({ success: false, message: "❌ 用戶不存在" });
    }

    const userData = user.recordset[0];

    // **🔍 檢查密碼**
    const isMatch = await bcrypt.compare(password, userData.password);
    if (!isMatch) {
      return res.status(401).json({ success: false, message: "❌ 密碼錯誤" });
    }

    // **🔍 產生 JWT**
    const token = jwt.sign(
      { userId: userData.id, username: userData.username },
      process.env.JWT_SECRET,
      { expiresIn: "1H" }
    );

    res.json({ success: true, token });
  } catch (error) {
    console.error("❌ 登錄錯誤:", error);
    res.status(500).json({ success: false, message: "❌ 伺服器錯誤" });
  }
};

const changePassword = async (req, res) => {
  const { userId, currentPassword, newPassword } = req.body;

  try {
    const pool = await sql.connect(dbConfig);

    // **🔍 取得使用者原密碼**
    const user = await pool
      .request()
      .input("userId", sql.VarChar, userId)
      .query("SELECT password FROM users WHERE id = @userId");

    if (user.recordset.length === 0) {
      return res.status(404).json({ success: false, message: "❌ 用戶不存在" });
    }

    const storedPassword = user.recordset[0].password;

    // **🔍 檢查當前密碼是否正確**
    const isMatch = await bcrypt.compare(currentPassword, storedPassword);
    if (!isMatch) {
      return res
        .status(401)
        .json({ success: false, message: "❌ 當前密碼錯誤" });
    }

    // **🔑 加密新密碼**
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // **🔄 更新密碼**
    await pool
      .request()
      .input("userId", sql.VarChar, userId)
      .input("newPassword", sql.VarChar, hashedPassword)
      .query("UPDATE users SET password = @newPassword WHERE id = @userId");

    res.json({ success: true, message: "✅ 密碼修改成功" });
  } catch (error) {
    console.error("❌ 修改密碼錯誤:", error);
    res.status(500).json({ success: false, message: "❌ 伺服器錯誤" });
  }
};

const resetPassword = async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return res
      .status(400)
      .json({ success: false, message: "❌ 請提供使用者 ID" });
  }
  try {
    const pool = await sql.connect(dbConfig);
    // 預設密碼
    const defaultPassword = "!54311261";
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    // 更新為預設密碼
    await pool
      .request()
      .input("userId", sql.VarChar, userId)
      .input("newPassword", sql.VarChar, hashedPassword)
      .query("UPDATE users SET password = @newPassword WHERE id = @userId");

    res.json({ success: true, message: " 密碼已重設為預設值" });
  } catch (error) {
    console.error("❌ 重設密碼錯誤:", error);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const keepAlive = async (req, res) => {
  const auth = req.headers.authorization;

  if (!auth || !auth.startsWith("Bearer ")) {
    return res.status(401).json({ success: false, message: "未授權" });
  }

  const token = auth.split(" ")[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const now = Math.floor(Date.now() / 1000);
    const timeLeft = decoded.exp - now;
    const userId = decoded.userId;

    const pool = await sql.connect(dbConfig);
    await pool.request().input("userId", sql.VarChar, userId).query(`
      UPDATE Users
      SET LastActiveTime = GETUTCDATE()
      WHERE id = @userId or uid = @userId
    `);

    let newToken = null;
    let message = "未達3分鐘";

    if (timeLeft < 10 * 60) {
      newToken = jwt.sign(
        { userId: decoded.userId, username: decoded.username },
        process.env.JWT_SECRET,
        { expiresIn: "1H" }
      );
      message = "✅ 更新成功，已續簽";
    }

    res.json({
      success: true,
      message,
      token: newToken,
    });
  } catch (err) {
    console.error("❌ KeepAlive 錯誤:", err);
    res.status(401).json({
      success: false,
      message: "❌ Token 無效或已過期",
    });
  }
};

const getOnlineUsers = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool.request().query(`
      SELECT UID, Name, LastActiveTime
      FROM Users
      WHERE DATEDIFF(MINUTE, LastActiveTime, GETUTCDATE()) <= 5
      ORDER BY LastActiveTime DESC
    `);

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 查詢線上使用者失敗:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

const getUserInfo = async (req, res) => {
  const auth = req.headers.authorization;
  if (!auth || !auth.startsWith("Bearer ")) {
    return res.status(401).json({ success: false, message: "未授權" });
  }

  const token = auth.split(" ")[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 從資料庫查出使用者詳細資訊（避免 token 被偽造）
    const pool = await sql.connect(dbConfig);
    const result = await pool
      .request()
      .input("userId", sql.VarChar, decoded.userId).query(`
        SELECT username, name
        FROM users WHERE id = @userId or uid = @userId
      `);

    if (result.recordset.length === 0) {
      return res.json({
        success: true,
        user: null, // 讓前端知道「沒有 user」但不是錯誤
      });
    }

    const user = result.recordset[0];

    res.json({
      success: true,
      user: {
        username: user.username,
        name: user.name,
      },
    });
  } catch (err) {
    console.error("❌ Token 驗證失敗:", err);
    return res
      .status(403)
      .json({ success: false, message: "Token 無效或過期" });
  }
};

// **✅ 匯出 `verifyToken` & `lineLogin`**
module.exports = {
  verifyToken,
  lineLogin,
  login,
  changePassword,
  resetPassword,
  keepAlive,
  getOnlineUsers,
  getUserInfo,
};
