<template>
  <q-page class="q-pa-sm" style="width: 100%; max-width: 800px; margin: 0 auto">
    <!-- 標題和搜尋區域 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">類部統計</div>
        </div>
      </q-card-section>

      <!-- 查詢條件 -->
      <q-card-section class="q-pt-none">
        <div class="row items-center no-wrap">
          <!-- 日期區間輸入框 -->
          <q-input
            v-model="formattedDate"
            label="選擇日期區間"
            dense
            outlined
            square
            class="col"
            readonly
          >
            <!-- 左側 icon -->
            <template v-slot:prepend>
              <q-icon name="event" size="sm" />
            </template>

            <!-- 日期選擇 popup -->
            <template v-slot:append>
              <q-icon
                name="calendar_month"
                color="primary"
                class="cursor-pointer"
                size="18px"
              >
                <q-popup-proxy
                  transition-show="scale"
                  transition-hide="scale"
                  cover
                >
                  <q-date
                    v-model="dateRange"
                    range
                    mask="YYYY-MM-DD"
                    emit-immediately
                  >
                    <div
                      class="row items-center justify-end q-gutter-sm q-pa-sm"
                    >
                      <q-btn label="確定" color="primary" flat v-close-popup />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>

          <!-- 查詢按鈕 -->
          <q-btn
            flat
            round
            size="sm"
            color="red"
            class="search-button q-ml-sm"
            :disable="
              !dateRange ||
              (typeof dateRange === 'object' &&
                (!dateRange.from || !dateRange.to))
            "
            @click="filterDialog = true"
          >
            <Search size="20" />

            <q-tooltip>篩選統計</q-tooltip>
          </q-btn>
        </div>
      </q-card-section>

      <!-- 查詢區間顯示 -->
      <q-card-section v-if="resultDateRange.from" class="q-pt-none q-pb-xs">
        <q-chip outline color="teal" class="full-width justify-center">
          <q-icon name="date_range" class="q-mr-xs" />
          查詢區間：{{ resultDateRange.from }} ~ {{ resultDateRange.to }}
        </q-chip>
      </q-card-section>
    </q-card>

    <!-- 主內容區域 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <!-- 載入中狀態 -->
      <div v-if="isLoading" class="q-pa-xl">
        <div class="fancy-loader">
          <svg
            class="loader-svg"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">資料整理中...</div>
        </div>
      </div>

      <!-- 無資料顯示 -->
      <q-card-section
        v-else-if="
          classStats.length === 0 && resultDateRange.from && !isLoading
        "
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無類部統計資料</div>
          <div class="empty-subtext">
            請嘗試更改查詢條件或選擇不同的日期區間
          </div>
        </div>
      </q-card-section>

      <!-- 結果列表 -->
      <q-card-section
        v-if="classStats.length > 0 && !isLoading"
        class="q-px-xs q-py-sm"
      >
        <q-list bordered separator class="rounded-borders">
          <q-expansion-item
            v-for="(group, index) in classStats"
            :key="index"
            expand-separator
            class="q-mb-xs"
            header-class="class-group-header"
          >
            <template v-slot:header>
              <q-item-section>
                <div class="text-subtitle2 text-weight-medium">
                  {{ group.className }}
                </div>
              </q-item-section>
              <q-item-section side>
                <div class="text-subtitle2 text-weight-bold text-deep-orange">
                  ${{ Math.round(getTotal(group.details)).toLocaleString() }}
                </div>
              </q-item-section>
            </template>

            <!-- 商品明細 -->
            <q-list class="class-details">
              <q-item
                v-for="(merged, i) in groupByCode(group.details)"
                :key="i"
                class="q-py-sm class-item"
              >
                <q-item-section>
                  <!-- 顯示商品名稱與總數量 -->
                  <div class="text-subtitle2 text-weight-bold text-brown-10">
                    {{ merged.name }} x {{ merged.qty }}
                  </div>

                  <!-- 展開每一筆明細 -->
                  <div
                    v-for="(subItem, si) in merged.subItems"
                    :key="si"
                    class="text-caption text-grey-7 q-ml-sm"
                  >
                    ‣ {{ subItem.sname !== "標準" ? subItem.sname : "" }} ${{
                      subItem.oprice.toLocaleString()
                    }}
                    x {{ subItem.qty }}
                    <span
                      v-if="subItem.oremname && subItem.oremname.trim() !== ''"
                    >
                      - {{ subItem.oremname }}
                    </span>
                    <span v-if="subItem.damt !== 100" class="text-teal q-ml-sm">
                      ({{ subItem.damt }} 折)
                    </span>
                  </div>
                </q-item-section>
                <q-item-section side>
                  <div class="text-subtitle2 text-weight-bold text-right">
                    ${{ Math.round(merged.totalAmount).toLocaleString() }}
                  </div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-expansion-item>
        </q-list>
      </q-card-section>
    </q-card>

    <q-dialog v-model="filterDialog" class="filter-dialog">
      <q-card style="min-width: 350px; max-width: 90vw; border-radius: 10px">
        <!-- 標題區 -->
        <q-card-section class="row items-center q-gutter-sm">
          <q-avatar icon="filter_alt" color="primary" text-color="white" />
          <div class="text-h6 text-primary">篩選條件</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <!-- 篩選選項 -->
        <q-card-section class="q-pt-md q-gutter-md">
          <div class="text-subtitle2 text-grey-8 q-mb-xs">排序方式</div>
          <q-option-group
            v-model="sortBy"
            :options="[
              { label: '依編號排序', value: 'code' },
              { label: '依數量排序', value: 'qty' },
              { label: '依金額排序', value: 'amount' },
            ]"
            type="radio"
            color="primary"
            inline
            dense
          />

          <div class="q-mt-md">
            <q-toggle
              v-model="includeZero"
              label="是否統計 0 元項目"
              color="primary"
            />
          </div>
        </q-card-section>

        <q-separator />

        <!-- 動作按鈕 -->
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" v-close-popup class="text-grey-7" />
          <q-btn
            unelevated
            color="primary"
            label="搜尋"
            @click="fetchData()"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed } from "vue";
import { DateTime } from "luxon"; // 你有用 luxon
import { useQuasar } from "quasar";
const $q = useQuasar();
const today = DateTime.now().toFormat("yyyy-MM-dd");

const filterDialog = ref(false);
const sortBy = ref("code");
const includeZero = ref(false);
const props = defineProps({ Branch: String, Permissions: String });
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const resultDateRange = ref({ from: "", to: "" });
const classStats = ref([]);
const isLoading = ref(false);

const dateRange = ref({
  from: today,
  to: today,
});

async function fetchData() {
  const from = dateRange.value?.from || dateRange.value;
  const to = dateRange.value?.to || dateRange.value; // ✅ 若未選 to，使用 from 當作區間結尾

  if (!from) return;
  isLoading.value = true;
  try {
    const res = await apiClient.get(`${apiBaseUrl}/bclassstat/get_ClassStats`, {
      params: {
        branch: props.Branch,
        from,
        to,
        sortBy: sortBy.value,
        includeZero: includeZero.value,
      },
    });

    if (res.data.success) {
      classStats.value = res.data.data; // ✅ 更新資料
      resultDateRange.value = { from, to }; // ⬅️ 儲存日期區間
      isLoading.value = false;
    } else {
      console.error("❌ 查詢失敗:", res.data.message);
      isLoading.value = false;
      $q.notify({ type: "negative", message: "查詢失敗: " + res.data.message });
    }
  } catch (err) {
    console.error("❌ API 錯誤:", err);
    isLoading.value = false;
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
}

const formattedDate = computed(() => {
  const value = dateRange.value;

  if (!value) return "請選擇日期";

  if (typeof value === "string") {
    // ✅ 單日情況（只選一天）
    return value;
  }

  if (typeof value === "object" && value.from) {
    const { from, to } = value;
    return !to || from === to ? from : `${from} ~ ${to}`;
  }

  return "請選擇日期";
});

function getTotal(details) {
  return details.reduce((sum, item) => {
    const discount =
      item.damt === 100
        ? 1
        : item.damt >= 10
        ? item.damt / 100
        : item.damt / 10;

    return sum + item.oprice * item.qty * discount;
  }, 0);
}

function groupByCode(details) {
  const map = new Map();

  for (const item of details) {
    const discount =
      item.damt === 100
        ? 1
        : item.damt >= 10
        ? item.damt / 100
        : item.damt / 10;

    // 分組 key 使用 code 固定，顯示順序依照 details
    const key = item.code;

    if (!map.has(key)) {
      map.set(key, {
        code: item.code,
        name: item.name,
        qty: 0,
        totalAmount: 0,
        subItems: [],
      });
    }

    const entry = map.get(key);
    entry.qty += item.qty;
    entry.totalAmount += item.qty * item.oprice * discount;
    entry.subItems.push(item);
  }

  return Array.from(map.values());
}
</script>
<style>
.title-section {
  padding-bottom: 8px;
}

.search-button {
  min-height: 32px;
  min-width: 32px;
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #ff9800;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}

.loader-message {
  margin-top: 1rem;
  color: #ff9800;
  font-size: 0.9rem;
}

.class-item:hover {
  background-color: rgba(255, 236, 225, 0.5);
}

.class-details {
  background-color: #fff;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #757575;
  margin-bottom: 0.5rem;
}

.empty-subtext {
  font-size: 0.9rem;
  color: #9e9e9e;
}
</style>
