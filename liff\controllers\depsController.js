//單據查詢主功能
const sql = require("mssql");
const dbConfig = require("../config/db");

const getDepartments = async (req, res) => {
  try {
    // 連接資料庫
    await sql.connect(dbConfig);
    const result = await sql.query(`
         SELECT 
    Dep.Id, 
    Dep.Ulevel, 
    d.Name AS Uname,  -- 正確的上層部門名稱
    Dep.Name, 
    Dep.Manager AS ManagerId, 
    Users.Name AS ManagerName,
    Dep.Type
FROM Dep
LEFT JOIN Users ON Dep.Manager = Users.ID
LEFT JOIN Dep AS d ON Dep.Ulevel = d.Id  
ORDER BY Dep.Type, Dep.Id;
      `);

    res.json(result.recordset); // 回傳 JSON 格式的部門資料
  } catch (err) {
    console.error("查詢部門失敗:", err);
    res.status(500).json({ error: "無法獲取部門資料" });
  }
};

const addDepartment = async (req, res) => {
  const { Id, Name, Ulevel, Manager, Type } = req.body;

  try {
    await sql.connect(dbConfig);
    await sql.query(`
      INSERT INTO Dep (Id, Name, Ulevel, Manager, Type)
      VALUES (
        '${Id}', 
        '${Name}', 
        ${Ulevel ? `'${Ulevel}'` : "NULL"}, 
        ${Manager ? `'${Manager}'` : "NULL"},
        ${Type || 0}
      )
    `);

    res.status(201).json({ message: "部門新增成功" });
  } catch (err) {
    console.error("❌ 新增部門失敗:", err);
    res.status(500).json({ error: "無法新增部門" });
  }
};

const updateDepartment = async (req, res) => {
  const { Name, Ulevel, Manager, Type } = req.body;
  const deptId = req.params.id;

  try {
    await sql.connect(dbConfig);
    await sql.query(`
      UPDATE Dep
      SET Name = '${Name}', 
          Ulevel = ${Ulevel ? `'${Ulevel}'` : "NULL"},
          Manager = ${Manager ? `'${Manager}'` : "NULL"},
          Type = ${Type || 0}
      WHERE Id = '${deptId}'
    `);

    res.json({ message: "部門更新成功" });
  } catch (err) {
    console.error("❌ 編輯部門失敗:", err);
    res.status(500).json({ error: "無法更新部門" });
  }
};

const deleteDepartment = async (req, res) => {
  const deptId = req.params.id;

  try {
    await sql.connect(dbConfig);

    // 檢查是否有子部門
    const checkResult = await sql.query(`
      SELECT COUNT(*) as count FROM Dep WHERE Ulevel = '${deptId}'
    `);

    if (checkResult.recordset[0].count > 0) {
      return res.status(400).json({ error: "此部門下還有子部門，無法刪除" });
    }

    await sql.query(`
      DELETE FROM Dep WHERE Id = '${deptId}'
    `);

    res.json({ message: "部門刪除成功" });
  } catch (err) {
    console.error("❌ 刪除部門失敗:", err);
    res.status(500).json({ error: "無法刪除部門" });
  }
};

module.exports = {
  getDepartments,
  addDepartment,
  updateDepartment,
  deleteDepartment,
};
