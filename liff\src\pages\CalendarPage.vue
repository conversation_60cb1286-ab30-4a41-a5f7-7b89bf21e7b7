<template>
  <q-page
    class="q-pa-sm calendar-page"
    style="
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      height: calc(100vh - 50px);
    "
  >
    <!-- 標題卡片 -->
    <q-card class="no-shadow" style="border-radius: 12px; overflow: hidden">
      <q-card-section class="q-pb-none q-pt-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">
            {{
              currentView === "listDay"
                ? `行事曆 - ${formatSelectedDate()}`
                : "行事曆"
            }}
          </div>
          <div class="row items-center">
            <!-- 返回月曆視圖按鈕 (只在 list 視圖時顯示) -->
            <q-btn
              v-if="currentView === 'listDay'"
              flat
              round
              size="sm"
              color="primary"
              icon="calendar_view_month"
              @click="backToMonthView"
              class="q-mr-xs"
              aria-label="返回月曆"
            />
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              icon="filter_list"
              @click="showFilterDialog = true"
              class="q-mr-xs"
              aria-label="篩選"
            />
            <!-- 月份導航按鈕 (只在月曆視圖時顯示) -->
            <template v-if="currentView === 'dayGridMonth'">
              <q-btn
                flat
                round
                size="sm"
                icon="chevron_left"
                @click="prevMonth"
                color="primary"
              />
              <q-input
                v-model="displayMonth"
                readonly
                dense
                outlined
                class="month-selector"
                input-class="text-center"
              />
              <q-btn
                flat
                round
                size="sm"
                icon="chevron_right"
                @click="nextMonth"
                color="primary"
              />
            </template>
            <q-btn
              flat
              round
              size="sm"
              icon="add"
              @click="showAddEventDialog = true"
              color="primary"
              class="q-ml-xs"
              aria-label="新增行事曆"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
    <!-- 行事曆卡片 -->
    <q-card
      class="no-shadow calendar-card"
      style="border-radius: 12px; overflow: hidden; flex: 1"
    >
      <q-card-section class="q-pt-sm q-pb-none calendar-section">
        <div class="calendar-container">
          <FullCalendar
            ref="fullCalendar"
            :options="calendarOptions"
            class="full-height-calendar"
          />
        </div>
      </q-card-section>
    </q-card>
    <!-- 事件詳情對話框 -->
    <q-dialog v-model="showEventDialog" persistent>
      <q-card style="min-width: 350px; max-width: 500px" class="event-dialog">
        <q-card-section class="row items-center" :style="eventDialogStyle">
          <div class="text-h6" :class="eventDialogTextClass">
            <q-icon name="event" class="q-mr-sm" size="sm" />
            {{ selectedEvent.title }}
          </div>
          <q-space />

          <!-- 編輯和刪除按鈕（只有發布者可見） -->
          <div v-if="isEventOwner(selectedEvent)" class="q-mr-sm">
            <q-btn
              icon="edit"
              flat
              round
              dense
              :class="eventDialogTextClass"
              @click="editEvent(selectedEvent)"
              title="編輯活動"
            />
            <q-btn
              icon="delete"
              flat
              round
              dense
              :class="eventDialogTextClass"
              @click="hideEvent(selectedEvent)"
              title="刪除活動"
            />
          </div>

          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            :class="eventDialogTextClass"
          />
        </q-card-section>

        <q-card-section class="q-pt-lg">
          <div class="row items-center q-mb-md" v-if="selectedEvent.dep">
            <q-icon name="business" size="sm" class="q-mr-sm text-grey-7" />
            <div class="event-department">
              發布部門：{{ getDepartmentName(selectedEvent.dep) }}
            </div>
          </div>

          <!-- 發布人員信息 -->
          <div
            class="row items-center q-mb-md"
            v-if="
              selectedEvent.created_by_name ||
              selectedEvent.created_by ||
              selectedEvent.createdBy
            "
          >
            <q-icon name="person" size="sm" class="q-mr-sm text-grey-7" />
            <div class="event-publisher">
              發布人員：{{
                getUserName(
                  selectedEvent.created_by_name ||
                    selectedEvent.created_by ||
                    selectedEvent.createdBy
                )
              }}
            </div>
          </div>

          <div class="row items-center q-mb-md">
            <q-icon name="schedule" size="sm" class="q-mr-sm text-grey-7" />
            <div class="event-time">
              {{
                formatEventDate(
                  selectedEvent.start,
                  selectedEvent.end,
                  selectedEvent.allDay
                )
              }}
            </div>
            <q-badge
              v-if="selectedEvent.allDay"
              class="q-ml-sm"
              color="blue-grey-2"
              text-color="blue-grey-8"
              >全天</q-badge
            >
          </div>

          <q-separator class="q-my-md" />

          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              <q-icon
                name="description"
                size="sm"
                class="q-mr-sm text-grey-7"
              />
              詳細說明
            </div>
            <div
              class="event-description q-pa-sm q-ml-lg bg-grey-1 rounded-borders"
            >
              {{ selectedEvent.description || "無詳細說明" }}
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pb-md q-pr-md">
          <q-btn flat label="關閉" color="grey-7" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <!-- 篩選對話框 -->
    <q-dialog v-model="showFilterDialog">
      <q-card style="min-width: 350px; max-width: 500px" class="filter-dialog">
        <q-card-section class="bg-primary text-white">
          <div class="row items-center no-wrap">
            <div class="text-h6">
              <q-icon name="filter_list" class="q-mr-sm" size="sm" />
              篩選條件
            </div>
            <q-space />
            <q-btn icon="close" flat round dense v-close-popup />
          </div>
        </q-card-section>
        <q-card-section class="q-pa-md">
          <!-- Admin 專用選項 -->
          <div v-if="isAdmin" class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              <q-icon name="admin_panel_settings" size="xs" class="q-mr-xs" />
              管理員選項
            </div>
            <q-checkbox
              v-model="showAllEventsForAdmin"
              label="查看全部行程"
              @update:model-value="handleAdminToggle"
              color="primary"
              class="q-mb-sm"
            />
          </div>

          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              <q-icon name="business" size="xs" class="q-mr-xs" />
              部門
            </div>
            <q-select
              v-model="selectedDepartment"
              :options="departmentOptions"
              outlined
              dense
              emit-value
              map-options
              clearable
              @update:model-value="filterEvents"
              class="filter-select"
              placeholder="選擇部門"
            />
          </div>
          <div class="row items-center justify-center q-mt-md">
            <q-chip
              v-if="selectedDepartment"
              outline
              removable
              @remove="clearFilters"
              color="primary"
              class="q-mr-sm"
              icon="clear_all"
            >
              清除篩選
            </q-chip>
          </div>
        </q-card-section>
        <q-card-actions align="right" class="q-pb-md q-pr-md">
          <q-btn
            unelevated
            label="套用"
            color="primary"
            v-close-popup
            icon-right="check"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <!-- 新增行事曆對話框 -->
    <q-dialog v-model="showAddEventDialog" persistent>
      <q-card
        style="
          width: 100%;
          max-width: 800px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
          border-radius: 12px;
        "
        class="add-event-dialog"
      >
        <!-- 固定標題 -->
        <q-card-section
          class="row items-center justify-between bg-primary text-white q-pb-sm"
          style="
            position: sticky;
            top: 0;
            z-index: 2;
            border-radius: 12px 12px 0 0;
          "
        >
          <div class="text-h6">
            <q-icon name="event_note" class="q-mr-sm" />
            {{ editingEventId ? "編輯行事曆" : "新增行事曆" }}
          </div>
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            class="text-white"
          />
        </q-card-section>

        <q-separator />

        <!-- 可滾動內容區域 -->
        <q-card-section
          class="q-pa-md scroll"
          style="overflow-y: auto; flex: 1"
        >
          <!-- 發送部門與色系 -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              <q-icon name="business" size="xs" class="q-mr-xs" />
              發送部門
            </div>
            <div class="row q-col-gutter-sm items-center">
              <div class="col">
                <q-select
                  v-model="newEvent.depObject"
                  :options="departmentOptions"
                  outlined
                  dense
                  option-label="label"
                  option-value="value"
                  class="event-input"
                  placeholder="選擇發送部門"
                  :readonly="!isAdmin"
                  @update:model-value="handleDepartmentChange"
                />
              </div>
              <div class="col-auto">
                <div
                  class="department-color-indicator"
                  :style="{
                    backgroundColor: getCurrentColor(),
                    '--gradient-bg': getCurrentGradient(),
                    '--border-gradient': getCurrentBorderGradient(),
                  }"
                  :title="getCurrentColorName()"
                ></div>
              </div>
            </div>
          </div>

          <q-separator class="q-my-md" />

          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              <q-icon name="title" size="xs" class="q-mr-xs" />
              標題
            </div>
            <q-input
              v-model="newEvent.title"
              outlined
              dense
              placeholder="請輸入事件標題"
              class="event-input"
            />
          </div>
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              <q-icon name="description" size="xs" class="q-mr-xs" />
              描述
            </div>
            <q-input
              v-model="newEvent.description"
              outlined
              dense
              type="textarea"
              rows="3"
              placeholder="請輸入事件描述"
              class="event-input"
            />
          </div>
          <q-separator class="q-my-md" />
          <div class="text-subtitle2 q-mb-md">
            <q-icon name="event" size="xs" class="q-mr-xs" />
            日期與時間
          </div>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-caption q-mb-sm text-grey-8">開始日期</div>
              <q-input
                v-model="newEvent.startDate"
                outlined
                dense
                type="date"
                class="event-input"
                hint="開始日期"
              />
            </div>
            <div class="col-12 col-md-6">
              <div class="text-caption q-mb-sm text-grey-8">結束日期</div>
              <q-input
                v-model="newEvent.endDate"
                outlined
                dense
                type="date"
                class="event-input"
                hint="結束日期"
              />
            </div>
          </div>
          <div class="row q-col-gutter-md q-mt-md" v-if="!newEvent.allDay">
            <div class="col-12 col-md-6">
              <div class="text-caption q-mb-sm text-grey-8">開始時間</div>
              <q-input
                v-model="newEvent.startTime"
                outlined
                dense
                type="time"
                class="event-input"
                hint="開始時間"
              />
            </div>
            <div class="col-12 col-md-6">
              <div class="text-caption q-mb-sm text-grey-8">結束時間</div>
              <q-input
                v-model="newEvent.endTime"
                outlined
                dense
                type="time"
                class="event-input"
                hint="結束時間"
              />
            </div>
          </div>
          <div class="q-mt-md">
            <q-checkbox v-model="newEvent.allDay" label="全天事件" />
          </div>
          <q-separator class="q-my-md" />

          <div class="q-mb-md">
            <div class="row items-center justify-between q-mb-sm">
              <div class="text-subtitle2 text-primary">
                <q-icon name="people" class="q-mr-xs" />
                目標對象設定
              </div>
              <q-btn
                color="secondary"
                icon="add"
                label="新增對象"
                flat
                dense
                @click="addTarget"
                title="新增目標對象"
              />
            </div>

            <div
              v-for="(target, index) in newEvent.targets"
              :key="index"
              class="q-mb-sm bg-grey-1 rounded-borders"
            >
              <div class="q-pa-sm">
                <div class="row q-col-gutter-sm items-center">
                  <div class="col-4">
                    <q-select
                      v-model="target.target_type"
                      :options="getAvailableTargetTypeOptions(index)"
                      label="類型"
                      dense
                      emit-value
                      map-options
                      @update:model-value="
                        (val) => handleTargetTypeChange(val, index)
                      "
                      :disable="target.isExisting"
                    />
                  </div>
                  <div class="col-7">
                    <q-btn
                      flat
                      outline
                      class="full-width"
                      color="primary"
                      @click="openSelectionDialog(target)"
                    >
                      <div class="row items-center no-wrap">
                        <q-icon name="people" class="q-mr-sm" />
                        <div class="text-truncate">
                          {{ target.displayText || "點擊選擇對象" }}
                        </div>
                      </div>
                    </q-btn>
                  </div>
                  <div class="col-1 flex justify-center">
                    <q-btn
                      flat
                      round
                      dense
                      icon="delete"
                      color="negative"
                      @click="removeTarget(index)"
                      :disable="target.isExisting"
                      :title="
                        target.isExisting
                          ? '原有的目標對象不能刪除，只能修改選擇的項目'
                          : '刪除此目標對象'
                      "
                    />
                  </div>
                </div>
              </div>
            </div>

            <div
              v-if="newEvent.targets.length === 0"
              class="text-center q-pa-md bg-grey-1 rounded-borders"
            >
              <q-icon name="info" color="grey" size="24px" />
              <div class="text-grey q-mt-sm">尚未設定目標對象</div>
            </div>
          </div>
        </q-card-section>

        <!-- 固定按鈕區域 -->
        <q-card-actions
          align="right"
          style="flex-shrink: 0; border-top: 1px solid #e0e0e0"
          class="q-pa-md"
        >
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn
            unelevated
            :label="editingEventId ? '更新' : '新增'"
            color="primary"
            @click="addNewEvent"
            :icon-right="editingEventId ? 'save' : 'add'"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 使用者選擇對話框 -->
    <q-dialog v-model="selectionDialog.show" persistent>
      <q-card
        style="
          width: 700px;
          max-width: 90vw;
          height: 600px;
          max-height: 80vh;
          display: flex;
          flex-direction: column;
        "
      >
        <!-- 固定標題區 -->
        <q-card-section
          class="row items-center bg-primary text-white"
          style="flex-shrink: 0"
        >
          <div class="text-h6">
            <q-icon name="people" class="q-mr-sm" />
            {{ selectionDialog.title }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <!-- 固定搜尋區 -->
        <q-card-section style="flex-shrink: 0">
          <q-input
            v-model="selectionDialog.searchQuery"
            label="搜尋對象"
            outlined
            dense
            clearable
          >
            <template v-slot:append>
              <q-btn
                flat
                dense
                icon="select_all"
                color="primary"
                size="sm"
                @click="selectAllVisibleItems"
                :disable="!filteredItems.length"
                class="q-mr-xs"
              />
            </template>
          </q-input>
        </q-card-section>

        <!-- 可捲動的列表區 -->
        <q-card-section
          class="q-pt-none"
          style="flex: 1; overflow-y: auto; min-height: 0"
        >
          <q-list bordered separator>
            <q-item
              v-for="item in filteredItems"
              :key="item.id"
              clickable
              v-ripple
              @click="
                () => {
                  // 檢查是否為已選對象（來自初始數據）
                  const isInitialSelected =
                    selectionDialog.initialSelectedItems.includes(item.id);
                  const index = selectionDialog.selectedItems.indexOf(item.id);

                  // 如果是初始已選對象，則不允許取消選擇
                  if (index === -1) {
                    selectionDialog.selectedItems.push(item.id);
                  } else if (!isInitialSelected) {
                    // 只有非初始選擇的項目才能取消選擇
                    selectionDialog.selectedItems.splice(index, 1);
                  }
                }
              "
            >
              <q-item-section avatar>
                <q-checkbox
                  v-model="selectionDialog.selectedItems"
                  :val="item.id"
                  color="primary"
                  :disable="
                    selectionDialog.initialSelectedItems.includes(item.id)
                  "
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ item.name }}</q-item-label>
                <q-item-label caption>{{ item.id }}</q-item-label>
              </q-item-section>
              <q-item-section
                side
                v-if="selectionDialog.initialSelectedItems.includes(item.id)"
              >
                <q-badge color="primary" label="已選" />
              </q-item-section>
            </q-item>

            <q-inner-loading :showing="selectionDialog.loading">
              <q-spinner-dots size="50px" color="primary" />
            </q-inner-loading>
          </q-list>
        </q-card-section>

        <!-- 固定按鈕區 -->
        <q-card-actions
          align="right"
          style="flex-shrink: 0; border-top: 1px solid #e0e0e0"
        >
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            label="確認選擇"
            color="primary"
            @click="confirmSelection"
            :disable="selectionDialog.selectedItems.length === 0"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>
<script setup>
import { ref, computed, onMounted, nextTick, watch, reactive } from "vue";
import { useQuasar } from "quasar";
import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 定義 props - 從 HomePage 直接傳遞用戶信息
const props = defineProps({
  userId: String, // 用戶ID
  userDep: String, // 用戶所屬部門
  userBranch: String, // 用戶所屬門市
  userPermissions: Array, // 用戶權限部門列表
  // 保留舊的 props 以兼容性
  Branch: {
    type: String,
    default: "",
  },
  Userid: String,
  UserDep: String,
  Did: String,
  Dname: String,
  Permissions: Array,
}); // 獲取當前日期和月份
const currentDate = new Date();
const currentYear = currentDate.getFullYear();
const currentMonth = currentDate.getMonth(); // 格式化日期函數
const formatDateString = (year, month, day, hour = null, minute = null) => {
  const date = new Date(year, month, day);
  if (hour !== null && minute !== null) {
    date.setHours(hour, minute);
    return date.toISOString();
  }
  return date.toISOString().split("T")[0];
};

const departments = [];

// 目標選項
const branchOptions = ref([]);
const departmentOptions = ref([]);
const groupOptions = ref([]);
const userOptions = ref([]);

// 用戶ID到姓名的對映
const userNameMap = ref({});

// 目標類型選項
const allTargetTypeOptions = [
  { label: "人員", value: "user" },
  { label: "群組", value: "group" },
  { label: "部門", value: "department" },
  { label: "門市", value: "branch" },
];

// 可用的目標類型選項（過濾已使用的類型）
const getAvailableTargetTypeOptions = (currentIndex) => {
  const usedTypes = newEvent.value.targets
    .map((target, index) =>
      index !== currentIndex ? target.target_type : null
    )
    .filter((type) => type);

  return allTargetTypeOptions.filter(
    (option) => !usedTypes.includes(option.value)
  );
};

// 判斷是否處於編輯模式
const isEditingMode = computed(() => {
  return editingEventId.value !== null;
});

// 色系選項（參考 UpayslipPage 漸層設計）
const colorOptions = [];

// 部門色系對應（從資料庫載入）
const departmentColors = ref({});

// 載入部門顏色設定
const fetchDepartmentColors = async () => {
  try {
    const response = await apiClient.get("/events/departments");

    if (response.data.success) {
      let departments = response.data.data;
      const colorMap = {};

      departments = cleanStringsDeep(departments);
      departments.forEach((dept) => {
        // 去除部門ID的空格
        const depId = dept.id ? dept.id.trim() : dept.id;

        // 直接使用資料庫中的顏色值，並去除空格
        let color = dept.color || "#e3f2fd";

        // 去除前後空格
        color = color.trim();

        // 如果是 "info" 等非顏色值，轉換為對應的顏色
        if (color === "info") {
          color = "#e3f2fd";
        }

        colorMap[depId] = color;
      });

      departmentColors.value = colorMap;

      // 部門顏色載入完成後，重新初始化當前事件的顏色
      if (newEvent.value.dep) {
        updateEventColor(newEvent.value.dep);
      }

      // 重新設定沒有 background_color 的事件顏色
      if (events.value.length > 0) {
        events.value = events.value.map((event) => {
          // 只有當事件沒有 background_color 時才使用部門顏色
          if (
            !event.backgroundColor &&
            event.dep &&
            departmentColors.value[event.dep]
          ) {
            const baseColor = departmentColors.value[event.dep];
            const gradients = generateGradientFromColor(baseColor);
            event.backgroundColor = gradients.lighterColor;
          }
          return event;
        });
        filterEvents(); // 重新應用篩選以更新顯示
      }
    }
  } catch (error) {
    console.error("載入部門顏色失敗:", error);
  }
};

// API 調用函數
const fetchEvents = async () => {
  try {
    loading.value = true;

    // 獲取當前月曆視圖的日期範圍
    const calendarApi = fullCalendar.value?.getApi();
    const view = calendarApi?.view;

    let start, end;
    if (view) {
      start = view.activeStart.toISOString().split("T")[0];
      end = view.activeEnd.toISOString().split("T")[0];
    }

    // 從 HomePage 傳遞的用戶信息，用於過濾只有該用戶能看到的公布欄
    // 後端過濾邏輯：只有 target_id 包含在以下任一列表中的事件才顯示
    // - userId: 直接指定給該用戶的事件
    // - userDep: 指定給該用戶所屬部門的事件
    // - userBranch: 指定給該用戶所屬門市的事件
    // - userPermissions: 指定給該用戶有權限部門的事件
    // - 群組事件需要後端額外查詢用戶群組關係
    const params = {
      // 優先使用新的 props，如果沒有則使用舊的 props (兼容性)
      userId: props.userId || props.Userid,
      userDep: props.userDep || props.UserDep,
      userBranch: props.userBranch || props.Branch,
      userPermissions: props.userPermissions
        ? props.userPermissions.join(",")
        : props.Permissions
        ? props.Permissions.join(",")
        : "",
    };

    if (start) params.start = start;
    if (end) params.end = end;

    const response = await apiClient.get("/events/get_events", { params });

    if (response.data.success) {
      events.value = response.data.data;
      events.value = cleanStringsDeep(events.value);
      console.log("事件:", events.value);

      // 為每個事件設定顏色，優先使用 background_color
      events.value = events.value.map((event) => {
        if (event.backgroundColor) {
          // 如果事件已有 background_color，直接使用
          // 不需要修改，保持原始顏色
        } else if (event.dep && departmentColors.value[event.dep]) {
          // 如果沒有 background_color 但有部門，使用部門顏色
          const baseColor = departmentColors.value[event.dep];
          const gradients = generateGradientFromColor(baseColor);
          event.backgroundColor = gradients.lighterColor;
        } else {
          // 都沒有的話使用預設顏色
          event.backgroundColor = "#f0f8ff";
        }
        return event;
      });

      // 檢查是否為 Admin 且選擇查看全部行程
      const showAllEvents = isAdmin.value && showAllEventsForAdmin.value;

      if (!showAllEvents) {
        // 一般模式：只顯示針對用戶所屬目標對象的行程
        const userRelatedIds = [
          props.Userid, // 用戶ID
          props.Did, // 用戶所屬部門
          props.Branch, // 用戶所屬門市
          ...(props.Permissions || []), // 用戶權限部門列表
        ].filter((id) => id); // 移除空值

        console.log("用戶相關的 ID 列表:", userRelatedIds);

        // 前端過濾：只保留目標對象包含用戶相關 ID 的事件
        if (userRelatedIds.length > 0) {
          const originalCount = events.value.length;
          events.value = events.value.filter((event) => {
            // 檢查事件是否有目標對象
            if (!event.targets || !Array.isArray(event.targets)) {
              return false; // 沒有目標對象的事件不顯示
            }

            // 檢查是否有任何目標對象的 target_id 在用戶相關 ID 列表中
            const hasMatchingTarget = event.targets.some((target) => {
              return userRelatedIds.includes(target.target_id);
            });

            return hasMatchingTarget;
          });

          console.log(
            `用戶過濾：從 ${originalCount} 個事件過濾到 ${events.value.length} 個事件`
          );
        } else {
          // 如果沒有用戶相關 ID，顯示空列表
          events.value = [];
          console.log("沒有用戶相關 ID，顯示空列表");
        }
      } else {
        console.log("Admin 模式：顯示全部行程，事件數量:", events.value.length);
      }

      filterEvents(); // 應用當前篩選
    }
  } catch (error) {
    console.error("載入事件失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入事件失敗",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};

// 新增事件到資料庫
const saveEventToDatabase = async (eventData) => {
  try {
    const response = await apiClient.post("/events/events", {
      title: eventData.title,
      description: eventData.description,
      start_datetime: eventData.start,
      end_datetime: eventData.end,
      all_day: eventData.allDay,
      background_color: eventData.backgroundColor,
      event_type: eventData.eventType || "department",
      source_type: eventData.sourceType || "manual",
      source_id: eventData.sourceId || "",
      dep: eventData.dep,
      targets: eventData.targets || [],
      created_by: props.Userid,
    });

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "事件新增成功",
        position: "top",
      });

      // 重新載入事件
      await fetchEvents();
    }
  } catch (error) {
    console.error("新增事件失敗:", error);
    $q.notify({
      type: "negative",
      message: "新增事件失敗",
      position: "top",
    });
  }
};

// 更新事件到資料庫
const updateEventToDatabase = async () => {
  try {
    const startDateTime = newEvent.value.allDay
      ? newEvent.value.startDate
      : `${newEvent.value.startDate}T${newEvent.value.startTime || "00:00"}`;

    const endDateTime = newEvent.value.allDay
      ? (() => {
          // 對於全天事件，結束日期需要加一天（FullCalendar 的排他性結束時間）
          const endDate = new Date(
            newEvent.value.endDate || newEvent.value.startDate
          );
          endDate.setDate(endDate.getDate() + 1);
          return endDate.toISOString().split("T")[0];
        })()
      : `${newEvent.value.endDate || newEvent.value.startDate}T${
          newEvent.value.endTime || "23:59"
        }`;

    // 根據部門設定背景色
    let backgroundColor = "#E3F2FD";
    if (newEvent.value.dep && departmentColors.value[newEvent.value.dep]) {
      const baseColor = departmentColors.value[newEvent.value.dep];
      const gradients = generateGradientFromColor(baseColor);
      backgroundColor = gradients.lighterColor;
    }

    // 轉換目標對象格式以符合後端需求，將逗號分隔的 ID 拆分為獨立記錄
    const formattedTargets = [];
    (newEvent.value.targets || []).forEach((target) => {
      if (target.target_type && target.target_id && target.target_id.trim()) {
        // 將逗號分隔的 ID 拆分為獨立記錄
        const ids = target.target_id.split(",").map((id) => id.trim());
        ids.forEach((id) => {
          if (id) {
            formattedTargets.push({
              target_type: target.target_type,
              target_id: id,
            });
          }
        });
      }
    });

    const response = await apiClient.put(
      `/events/events/${editingEventId.value}`,
      {
        title: newEvent.value.title,
        description: newEvent.value.description,
        start_datetime: startDateTime,
        end_datetime: endDateTime,
        all_day: newEvent.value.allDay,
        background_color: backgroundColor,
        event_type: "department",
        source_type: "manual",
        source_id: "",
        dep: newEvent.value.dep,
        targets: formattedTargets,
        updated_by: props.Userid,
      }
    );

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "事件更新成功",
        position: "top",
      });

      // 重置編輯狀態
      editingEventId.value = null;

      // 重置表單
      resetEventForm();

      showAddEventDialog.value = false;

      // 重新載入事件
      await fetchEvents();
    }
  } catch (error) {
    console.error("更新事件失敗:", error);
    $q.notify({
      type: "negative",
      message: "更新事件失敗",
      position: "top",
    });
  }
};

// 重置事件表單
const resetEventForm = () => {
  const defaultDepId = props.Did || "2";

  console.log("重置表單 - 預設部門ID:", defaultDepId);
  console.log("重置表單 - 可用部門選項:", departmentOptions.value);

  const defaultDepObject = departmentOptions.value.find(
    (dept) => dept.value?.trim() === defaultDepId?.trim()
  );

  console.log("重置表單 - 找到的預設部門對象:", defaultDepObject);

  newEvent.value = {
    title: "",
    description: "",
    startDate: getTodayString(), // 預設今天
    endDate: getTodayString(), // 預設今天
    startTime: "",
    endTime: "",
    allDay: true,
    type: "department",
    targetType: "",
    targetId: "",
    dep: defaultDepId, // 預設使用者部門
    depObject: defaultDepObject, // 部門對象（用於顯示 label）
    targets: [
      {
        target_type: "user",
        target_id: "",
        displayText: "點擊選擇對象",
        isExisting: false,
      },
    ], // 預設添加一個目標對象
    color: departmentColors.value[defaultDepId] || "#e3f2fd", // 預設顏色
  };

  console.log(
    "重置表單 - 設置後的 newEvent.depObject:",
    newEvent.value.depObject
  );
  console.log("重置表單 - 設置後的 newEvent.dep:", newEvent.value.dep);

  editingEventId.value = null;
};

// 根據顏色值生成漸層
const generateGradientFromColor = (baseColor) => {
  // 將 hex 顏色轉換為 RGB
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };

  // 將 RGB 轉換為 hex
  const rgbToHex = (r, g, b) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  };

  // 調整顏色亮度
  const adjustBrightness = (color, amount) => {
    const rgb = hexToRgb(color);
    if (!rgb) return color;

    const r = Math.max(0, Math.min(255, rgb.r + amount));
    const g = Math.max(0, Math.min(255, rgb.g + amount));
    const b = Math.max(0, Math.min(255, rgb.b + amount));

    return rgbToHex(r, g, b);
  };

  // 調整顏色深度 - 讓整體顏色更深
  const lighterColor = adjustBrightness(baseColor, -10); // 從 +20 改為 -10，讓較亮色也更深
  const darkerColor = adjustBrightness(baseColor, -60); // 從 -40 改為 -60，讓較暗色更深
  const borderColor = adjustBrightness(baseColor, -100); // 從 -80 改為 -100，讓邊框更深

  return {
    backgroundColor: baseColor,
    lighterColor: lighterColor,
    darkerColor: darkerColor,
    borderColor: borderColor,
    backgroundGradient: `linear-gradient(135deg, ${darkerColor} 0%, ${baseColor} 50%, ${lighterColor} 100%)`, // 使用三色漸層，更有層次
    iconGradient: `linear-gradient(135deg, ${baseColor} 0%, ${darkerColor} 100%)`,
    borderGradient: `linear-gradient(180deg, ${darkerColor} 0%, ${borderColor} 100%)`,
  };
};

// 選擇對話框相關變數
const selectionDialog = reactive({
  show: false,
  loading: false,
  items: [],
  selectedItems: [],
  searchQuery: "",
  currentTarget: null,
  targetType: "",
  title: "",
  initialSelectedItems: [],
});
// 判斷是否為 Admin
const isAdmin = computed(() => {
  return props.Permissions && props.Permissions.includes("Admin");
});

// Admin 是否查看全部行程
const showAllEventsForAdmin = ref(false);

// 處理 Admin 切換查看全部行程
const handleAdminToggle = (value) => {
  console.log("Admin 切換查看全部行程:", value);
  showAllEventsForAdmin.value = value;
  // 重新獲取事件以應用新的過濾邏輯
  fetchEvents();
};
const $q = useQuasar();
const fullCalendar = ref(null);
const events = ref([]);
const filteredEvents = ref([]);
const loading = ref(false);

const selectedDepartment = ref(null);
const showEventDialog = ref(false);
const showFilterDialog = ref(false);
const showAddEventDialog = ref(false);
const selectedEvent = ref({});
const eventDialogColor = ref("bg-primary");
const eventDialogStyle = ref({
  background: "linear-gradient(135deg, #1976d2 0%, #1565c0 100%)",
  color: "white",
});
const eventDialogTextClass = ref("text-white");
const editingEventId = ref(null); // 正在編輯的事件ID

// 新增：當前視圖狀態
const currentView = ref("dayGridMonth");
const selectedDate = ref(null);

// 獲取今天日期字串
const getTodayString = () => {
  const today = new Date();
  return today.toISOString().split("T")[0]; // YYYY-MM-DD 格式
};

const newEvent = ref({
  title: "",
  description: "",
  startDate: getTodayString(), // 預設今天
  endDate: getTodayString(), // 預設今天
  startTime: "",
  endTime: "",
  allDay: true,
  type: "department",
  targetType: "",
  targetId: "",
  dep: props.Did || "2", // 預設使用者部門
  targets: [],
  color: departmentColors.value[props.Did || "2"] || "#e3f2fd", // 預設顏色
});
// 月份顯示和切換相關
const displayMonth = ref(formatYearMonth(currentDate));

// 檢查是否為事件發布者
function isEventOwner(event) {
  if (!event || !event.createdBy) return false;
  return event.createdBy === props.Userid;
}

// 根據用戶ID獲取用戶姓名
const getUserName = (userId) => {
  if (!userId) return "未知";

  // 如果已經是姓名格式（包含中文字符），直接返回
  if (/[\u4e00-\u9fa5]/.test(userId)) {
    return userId;
  }

  // 從用戶對映中查找姓名
  if (userNameMap.value[userId]) {
    return userNameMap.value[userId];
  }

  // 從用戶選項中查找姓名
  const user = userOptions.value.find((user) => user.value === userId);
  if (user) {
    // 緩存到對映中
    userNameMap.value[userId] = user.label;
    return user.label;
  }

  // 如果都找不到，返回用戶ID
  return userId;
};

// 編輯事件
async function editEvent(event) {
  try {
    // 獲取完整的事件數據（包含目標對象）
    const response = await apiClient.get(`/events/events/${event.id}`);
    if (!response.data.success) {
      $q.notify({
        type: "negative",
        message: "獲取事件詳情失敗",
        position: "top",
      });
      return;
    }

    const fullEvent = response.data.data;

    // 確保所有選項都已載入
    if (
      userOptions.value.length === 0 ||
      departmentOptions.value.length === 0 ||
      groupOptions.value.length === 0 ||
      branchOptions.value.length === 0
    ) {
      await loadTargetOptions();
    }

    // 處理目標對象格式轉換 - 按類型分組，將多筆相同類型的 target_id 整理成一個陣列
    let processedTargets = [];
    if (fullEvent.targets && fullEvent.targets.length > 0) {
      // 按類型分組處理目標對象
      const targetGroups = {};
      fullEvent.targets.forEach((target) => {
        const targetType = target.target_type.trim();
        const targetId = target.target_id.trim();

        if (!targetGroups[targetType]) {
          targetGroups[targetType] = [];
        }
        targetGroups[targetType].push(targetId);
      });

      // 為每個類型創建一個目標對象，將多個 ID 用逗號連接
      Object.keys(targetGroups).forEach((targetType) => {
        const targetIds = targetGroups[targetType];
        processedTargets.push({
          target_type: targetType,
          target_id: targetIds.join(","), // 將多個 ID 用逗號連接
          displayText: "",
          isExisting: true, // 編輯模式下原有的目標對象設為唯讀，類型不可修改
        });
      });

      // 更新顯示文本
      for (const target of processedTargets) {
        target.displayText = await getTargetDisplayTextForEdit(target);
      }
    }

    // 處理日期格式，特別是全天事件的結束日期
    let startDate = fullEvent.start ? fullEvent.start.split("T")[0] : "";
    let endDate = fullEvent.end ? fullEvent.end.split("T")[0] : "";

    // 對於全天事件，需要將排他性結束日期減一天來顯示正確的結束日期
    if (fullEvent.allDay && fullEvent.end) {
      const endDateTime = new Date(fullEvent.end);
      // 減一天來獲取實際的結束日期
      endDateTime.setDate(endDateTime.getDate() - 1);
      endDate = endDateTime.toISOString().split("T")[0];
    }

    // 填入現有事件資料到新增表單
    const depId = fullEvent.dep || props.Did;

    // 匹配部門對象，使用 trim 處理空格問題
    let depObject = departmentOptions.value.find(
      (dept) => dept.value?.trim() === depId?.trim()
    );

    console.log("編輯事件 - 部門ID:", depId);
    console.log("編輯事件 - 找到的部門對象:", depObject);
    console.log("編輯事件 - 將設置的 depObject:", depObject);

    newEvent.value = {
      title: fullEvent.title,
      description: fullEvent.description,
      startDate: startDate,
      endDate: endDate,
      startTime: fullEvent.allDay
        ? ""
        : fullEvent.start
        ? fullEvent.start.split("T")[1]?.substring(0, 5)
        : "",
      endTime: fullEvent.allDay
        ? ""
        : fullEvent.end
        ? fullEvent.end.split("T")[1]?.substring(0, 5)
        : "",
      allDay: fullEvent.allDay || false,
      dep: depId,
      depObject: depObject, // 設置部門對象用於顯示 label
      targets: processedTargets,
      color:
        fullEvent.backgroundColor || departmentColors.value[depId] || "#e3f2fd",
    };

    console.log(
      "編輯事件 - 設置後的 newEvent.depObject:",
      newEvent.value.depObject
    );
    console.log("編輯事件 - 設置後的 newEvent.dep:", newEvent.value.dep);

    // 設定為編輯模式
    editingEventId.value = event.id;
    showEventDialog.value = false; // 關閉詳情對話框
    showAddEventDialog.value = true; // 開啟編輯對話框

    // 確保 DOM 更新後再檢查
    await nextTick();
    console.log("DOM 更新後 - newEvent.depObject:", newEvent.value.depObject);
  } catch (error) {
    console.error("編輯事件失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入事件資料失敗",
      position: "top",
    });
  }
}

// 隱藏事件（更新 active 狀態）
async function hideEvent(event) {
  try {
    const confirmed = await new Promise((resolve) => {
      $q.dialog({
        title: "確認刪除",
        message: "確定要刪除此活動嗎？刪除後將無法復原。",
        cancel: true,
        persistent: true,
      })
        .onOk(() => resolve(true))
        .onCancel(() => resolve(false));
    });

    if (!confirmed) return;

    const response = await apiClient.put(`/events/hide/${event.id}`);

    if (response.data.success) {
      $q.notify({
        type: "positive",
        message: "活動已刪除",
        position: "top",
      });

      showEventDialog.value = false;
      await fetchEvents(); // 重新載入事件
    } else {
      throw new Error(response.data.message || "隱藏失敗");
    }
  } catch (error) {
    console.error("隱藏事件失敗:", error);
    $q.notify({
      type: "negative",
      message: "刪除活動失敗",
      position: "top",
    });
  }
}

// 根據部門ID獲取部門名稱
function getDepartmentName(depId) {
  // console.log("查找部門名稱，ID:", depId);
  // console.log("部門選項:", departmentOptions.value);

  // 先從 departmentOptions 中查找
  const dept = departmentOptions.value.find((d) => d.value === depId);
  if (dept) {
    // console.log("從部門選項找到:", dept);
    return dept.label;
  }

  // 如果沒找到，嘗試從硬編碼的部門名稱對應表查找
  const departmentNameMap = {
    C1: "中一區",
    FIN: "財務部",
    GA: "總務部",
    GM: "總管理處",
    HR: "人資部",
    IT: "資訊部",
    MK: "行銷部",
    N1: "北一區",
    N2: "北二區",
    OPD: "營運處",
    PR: "採購部",
    S1: "南一區",
  };

  const name = departmentNameMap[depId];
  // console.log("從硬編碼對應表找到:", name);

  return name || depId || "未知部門";
}

// 處理部門變更
const handleDepartmentChange = (selectedDept) => {
  if (selectedDept) {
    newEvent.value.dep = selectedDept.value;
    newEvent.value.depObject = selectedDept;
    updateEventColor(selectedDept.value);
  } else {
    newEvent.value.dep = "";
    newEvent.value.depObject = null;
    newEvent.value.color = "#e3f2fd";
  }
};

// 更新事件色系（根據部門）
const updateEventColor = (depId) => {
  if (depId && departmentColors.value[depId]) {
    // 直接使用部門的顏色值
    newEvent.value.color = departmentColors.value[depId];
  } else {
    // 如果沒有對應的部門顏色，使用預設顏色
    newEvent.value.color = "#e3f2fd";
  }
};

// 監聽部門變更，自動更新顏色
watch(
  () => newEvent.value.dep,
  (newDep) => {
    if (newDep && Object.keys(departmentColors.value).length > 0) {
      updateEventColor(newDep);
    }
  }
);

// 監聽新增事件對話框打開，初始化顏色和預設部門
watch(
  () => showAddEventDialog.value,
  (isOpen) => {
    if (isOpen) {
      // 對話框打開時，確保預設部門被正確設置
      if (!editingEventId.value) {
        // 只有在新增模式下才重新設置預設部門
        const defaultDepId = props.Did || "2";
        const defaultDepObject = departmentOptions.value.find(
          (dept) => dept.value?.trim() === defaultDepId?.trim()
        );

        console.log("對話框打開 - 重新設置預設部門");
        console.log("對話框打開 - 預設部門ID:", defaultDepId);
        console.log("對話框打開 - 找到的部門對象:", defaultDepObject);

        if (defaultDepObject && !newEvent.value.depObject) {
          newEvent.value.dep = defaultDepId;
          newEvent.value.depObject = defaultDepObject;
          console.log("對話框打開 - 已設置預設部門對象");
        }
      }

      // 根據當前選擇的部門設定顏色
      if (
        newEvent.value.dep &&
        Object.keys(departmentColors.value).length > 0
      ) {
        updateEventColor(newEvent.value.dep);
      }
    } else if (!isOpen && editingEventId.value) {
      // 對話框關閉時，如果是編輯模式，重置編輯狀態
      editingEventId.value = null;
    }
  }
);

// 獲取當前色系的顏色值
const getCurrentColor = () => {
  // 如果 newEvent.color 是顏色值（以 # 開頭），直接使用
  if (newEvent.value.color && newEvent.value.color.startsWith("#")) {
    return newEvent.value.color;
  }

  // 否則從 colorOptions 中查找
  const selectedColor = colorOptions.find(
    (c) => c.value === newEvent.value.color
  );
  return selectedColor ? selectedColor.color : "#e3f2fd";
};

// 獲取當前色系的名稱
const getCurrentColorName = () => {
  const selectedColor = colorOptions.find(
    (c) => c.value === newEvent.value.color
  );
  return selectedColor ? selectedColor.label : "預設色系";
};

// 獲取當前色系的漸層
const getCurrentGradient = () => {
  // 如果是直接的顏色值，生成漸層
  if (newEvent.value.color && newEvent.value.color.startsWith("#")) {
    const gradients = generateGradientFromColor(newEvent.value.color);
    return gradients.backgroundGradient;
  }

  const selectedColor = colorOptions.find(
    (c) => c.value === newEvent.value.color
  );
  return selectedColor
    ? selectedColor.gradient
    : "linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)";
};

// 獲取當前色系的邊框漸層
const getCurrentBorderGradient = () => {
  // 如果是直接的顏色值，生成邊框漸層
  if (newEvent.value.color && newEvent.value.color.startsWith("#")) {
    const gradients = generateGradientFromColor(newEvent.value.color);
    return gradients.borderGradient;
  }

  const selectedColor = colorOptions.find(
    (c) => c.value === newEvent.value.color
  );
  return selectedColor
    ? selectedColor.borderGradient
    : "linear-gradient(180deg, #64b5f6 0%, #1976d2 100%)";
};

// 添加目標
function addTarget() {
  // 獲取可用的目標類型選項
  const availableOptions = getAvailableTargetTypeOptions(-1);

  if (availableOptions.length === 0) {
    $q.notify({
      type: "warning",
      message: "所有目標類型都已新增",
      position: "top",
    });
    return;
  }

  // 使用第一個可用的目標類型
  const firstAvailableType = availableOptions[0].value;

  newEvent.value.targets.push({
    target_type: firstAvailableType,
    target_id: "",
    displayText: getTargetDisplayText({
      target_type: firstAvailableType,
      target_id: "",
    }),
    isExisting: false,
  });
}

// 移除目標
function removeTarget(index) {
  newEvent.value.targets.splice(index, 1);
}

// 處理目標類型變更
const handleTargetTypeChange = async (newType, index) => {
  // 當目標類型變更時，清空目標ID
  if (newEvent.value.targets[index]) {
    // 清空目標ID
    newEvent.value.targets[index].target_id = "";
    newEvent.value.targets[index].displayText = getTargetDisplayText({
      target_type: newType,
      target_id: "",
    });

    // 強制更新目標對象
    newEvent.value.targets[index] = {
      ...newEvent.value.targets[index],
      target_type: newType,
      target_id: "",
      displayText: getTargetDisplayText({
        target_type: newType,
        target_id: "",
      }),
    };

    // 自動打開選擇對話框
    await openSelectionDialog(newEvent.value.targets[index]);
  }
};

// 打開選擇對話框
const openSelectionDialog = async (target) => {
  try {
    selectionDialog.show = true;
    selectionDialog.loading = true;
    selectionDialog.searchQuery = "";
    // 處理逗號分隔的 target_id
    selectionDialog.selectedItems = target.target_id
      ? target.target_id.split(",").map((id) => id.trim())
      : [];
    // 不再需要唯讀項目，允許取消勾選
    selectionDialog.initialSelectedItems = [];
    selectionDialog.currentTarget = target;
    selectionDialog.targetType = target.target_type.trim();

    // 根據目標類型設置標題和獲取相應的數據
    let response;
    switch (selectionDialog.targetType) {
      case "user":
        selectionDialog.title = "選擇人員";
        break;
      case "group":
        selectionDialog.title = "選擇群組";
        break;
      case "department":
        selectionDialog.title = "選擇部門";
        break;
      case "branch":
        selectionDialog.title = "選擇門市";
        break;
      default:
        selectionDialog.title = "選擇對象";
    }

    // 清空之前的選項列表
    selectionDialog.items = [];

    // 根據目標類型獲取不同的數據（參考 BtinmanagePage）
    switch (selectionDialog.targetType) {
      case "user":
        response = await apiClient.post(`${apiBaseUrl}/users/get_enable_users`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data.map((item) => ({
            id: item.ID.trim(),
            name: item.Name.trim(),
            label: `${item.Name.trim()} (${item.ID.trim()})`,
            value: item.ID.trim(),
          }));
        }
        break;
      case "group":
        response = await apiClient.get(`${apiBaseUrl}/users/get_usersgroup`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data.map((item) => ({
            id: item.Code.trim(),
            name: item.Name.trim(),
            label: item.Name.trim(),
            value: item.Code.trim(),
          }));
        }
        break;
      case "department":
        response = await apiClient.get(`${apiBaseUrl}/deps/get_departments`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data
            .sort((a, b) => (a.Type || 0) - (b.Type || 0)) // 按 type 排序
            .map((item) => ({
              id: item.Id.trim(),
              name: item.Name.trim(),
              label: item.Name.trim(),
              value: item.Id.trim(),
            }));
        }
        break;
      case "branch":
        response = await apiClient.get(`${apiBaseUrl}/branch/get_branch`);
        if (response.data && Array.isArray(response.data)) {
          selectionDialog.items = response.data
            .filter((item) => item.Sts === "1") // 篩選 sts='1' 的門市
            .map((item) => ({
              id: item.Cod_cust.trim(),
              name: item.Cod_name.trim(),
              label: item.Cod_name.trim(),
              value: item.Cod_cust.trim(),
            }));
        }
        break;
    }

    // 驗證已選項目是否在新的選項列表中
    if (selectionDialog.selectedItems.length > 0) {
      // 過濾出在新選項列表中存在的項目
      selectionDialog.selectedItems = selectionDialog.selectedItems.filter(
        (id) => selectionDialog.items.some((item) => item.value === id)
      );

      // 如果過濾後沒有有效的選中項，清空選中項
      if (selectionDialog.selectedItems.length === 0) {
        // 清空目標ID
        if (selectionDialog.currentTarget) {
          selectionDialog.currentTarget.target_id = "";
          selectionDialog.currentTarget.displayText = "點擊選擇對象";
        }
      }
    }
  } catch (error) {
    console.error(`載入${selectionDialog.title}列表失敗:`, error);
    $q.notify({
      type: "negative",
      message: `無法載入${selectionDialog.title}列表`,
    });
    selectionDialog.items = [];
    // 清空選中項
    selectionDialog.selectedItems = [];
    // 清空目標ID
    if (selectionDialog.currentTarget) {
      selectionDialog.currentTarget.target_id = "";
      selectionDialog.currentTarget.displayText = "點擊選擇對象";
    }
  } finally {
    selectionDialog.loading = false;
  }
};

// 確認選擇
const confirmSelection = () => {
  try {
    if (
      selectionDialog.currentTarget &&
      selectionDialog.selectedItems.length > 0
    ) {
      // 將選中項目設置為目標ID，用逗號分隔
      selectionDialog.currentTarget.target_id = selectionDialog.selectedItems
        .map((id) => id.trim())
        .join(",");

      // 更新顯示文本
      const displayText = getTargetDisplayText(selectionDialog.currentTarget);
      selectionDialog.currentTarget.displayText = displayText;

      // 找到對應的目標索引並強制更新
      const targetIndex = newEvent.value.targets.findIndex(
        (t) => t === selectionDialog.currentTarget
      );

      if (targetIndex !== -1) {
        // 使用 Vue 的響應式更新方式
        newEvent.value.targets[targetIndex] = {
          ...newEvent.value.targets[targetIndex],
          target_id: selectionDialog.currentTarget.target_id,
          displayText: displayText,
        };
      }
    } else if (selectionDialog.selectedItems.length === 0) {
      // 如果沒有選擇任何項目，清空目標ID
      if (selectionDialog.currentTarget) {
        selectionDialog.currentTarget.target_id = "";
        selectionDialog.currentTarget.displayText = "點擊選擇對象";

        // 找到對應的目標索引
        const targetIndex = newEvent.value.targets.findIndex(
          (t) => t === selectionDialog.currentTarget
        );

        if (targetIndex !== -1) {
          // 強制更新目標對象
          newEvent.value.targets[targetIndex] = {
            ...newEvent.value.targets[targetIndex],
            target_id: "",
            displayText: "點擊選擇對象",
          };
        }
      }
    }
    selectionDialog.show = false;
  } catch (error) {
    console.error("確認選擇時發生錯誤:", error);
    $q.notify({
      type: "negative",
      message: "確認選擇時發生錯誤",
      timeout: 2000,
    });
  }
};

// 過濾項目
const filteredItems = computed(() => {
  if (!selectionDialog.searchQuery) return selectionDialog.items;

  const query = selectionDialog.searchQuery.toLowerCase();
  return selectionDialog.items.filter(
    (item) =>
      item.name.toLowerCase().includes(query) ||
      item.id.toLowerCase().includes(query)
  );
});

// 獲取目標對象的顯示文字（只顯示類型和數量）
const getTargetDisplayText = (target) => {
  if (!target.target_id) {
    // 根據類型顯示預設文字
    switch (target.target_type) {
      case "user":
        return "點擊選擇人員";
      case "group":
        return "點擊選擇群組";
      case "department":
        return "點擊選擇部門";
      case "branch":
        return "點擊選擇門市";
      default:
        return "點擊選擇對象";
    }
  }

  // 計算選中的數量
  const ids = target.target_id
    .split(",")
    .map((id) => id.trim())
    .filter((id) => id);
  const count = ids.length;

  // 根據類型和數量顯示文字
  switch (target.target_type) {
    case "user":
      return `已選擇 ${count} 位人員`;
    case "group":
      return `已選擇 ${count} 個群組`;
    case "department":
      return `已選擇 ${count} 個部門`;
    case "branch":
      return `已選擇 ${count} 個門市`;
    default:
      return `已選擇 ${count} 個對象`;
  }
};

// 獲取編輯模式下目標對象的顯示文字（只顯示類型和數量）
const getTargetDisplayTextForEdit = async (target) => {
  // 直接使用 getTargetDisplayText 函數
  return getTargetDisplayText(target);
};

// 載入各種選項的輔助函數
const loadUserOptions = async () => {
  try {
    const response = await apiClient.post(
      `${apiBaseUrl}/users/get_enable_users`
    );
    if (response.data && Array.isArray(response.data)) {
      userOptions.value = response.data.map((item) => ({
        id: item.ID.trim(),
        name: item.Name.trim(),
        label: `${item.Name.trim()} (${item.ID.trim()})`,
        value: item.ID.trim(),
      }));
    }
  } catch (error) {
    console.error("載入使用者選項失敗:", error);
  }
};

const loadGroupOptions = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/users/get_usersgroup`);
    if (response.data && Array.isArray(response.data)) {
      groupOptions.value = response.data.map((item) => ({
        id: item.Code.trim(),
        name: item.Name.trim(),
        label: item.Name.trim(),
        value: item.Code.trim(),
      }));
    }
  } catch (error) {
    console.error("載入群組選項失敗:", error);
  }
};

const loadDepartmentOptions = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/deps/get_departments`);
    if (response.data && Array.isArray(response.data)) {
      departmentOptions.value = response.data
        .sort((a, b) => (a.Type || 0) - (b.Type || 0))
        .map((item) => ({
          id: item.Id.trim(),
          name: item.Name.trim(),
          label: item.Name.trim(),
          value: item.Id.trim(),
        }));
    }
  } catch (error) {
    console.error("載入部門選項失敗:", error);
  }
};

// const loadBranchOptions = async () => {
//   try {
//     const response = await apiClient.get(`${apiBaseUrl}/branch/get_branch`);
//     if (response.data && Array.isArray(response.data)) {
//       branchOptions.value = response.data
//         .filter((item) => item.Sts === "1")
//         .map((item) => ({
//           id: item.Cod_cust.trim(),
//           name: item.Cod_name.trim(),
//           label: item.Cod_name.trim(),
//           value: item.Cod_cust.trim(),
//         }));
//     }
//   } catch (error) {
//     console.error("載入門市選項失敗:", error);
//   }
// };

// 全選/取消全選可見項目
const selectAllVisibleItems = () => {
  // 獲取當前過濾後的項目ID列表
  const visibleItemIds = filteredItems.value.map((item) => item.id);

  // 檢查是否所有可見項目都已選中
  const allSelected = visibleItemIds.every((id) =>
    selectionDialog.selectedItems.includes(id)
  );

  if (allSelected) {
    // 如果全部已選中，則取消全選（但保留初始選擇的項目）
    selectionDialog.selectedItems = selectionDialog.selectedItems.filter(
      (id) =>
        selectionDialog.initialSelectedItems.includes(id) ||
        !visibleItemIds.includes(id)
    );
  } else {
    // 如果不是全部選中，則全選
    // 先創建一個新的Set來去重
    const uniqueIds = new Set([
      ...selectionDialog.selectedItems,
      ...visibleItemIds,
    ]);
    selectionDialog.selectedItems = Array.from(uniqueIds);
  }
}; // 行事曆配置 - 優化顯示風格
const calendarOptions = computed(() => ({
  handleWindowResize: true,
  stickyHeaderDates: false,
  plugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
  initialView: currentView.value,
  headerToolbar: false, // 取消原始的工具欄
  events: filteredEvents.value,
  eventClick: handleEventClick,
  dateClick: handleDateClick, // 新增：日期點擊事件
  locale: "zh-tw",
  height: "100%",
  aspectRatio: 3.5, // 增加寬高比，讓月曆更扁平
  expandRows: true, // 確保行高能夠填滿可用空間
  firstDay: 0, // 星期日為每週第一天
  buttonText: {
    today: "今天",
    month: "月",
    week: "週",
  },
  // 事件顯示配置
  eventTimeFormat: {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  },
  // 改進日期顯示格式，只顯示數字
  dayCellContent: (data) => {
    return data.dayNumberText.replace("日", "");
  },
  // 自定義星期標題顯示
  dayHeaderFormat: { weekday: "narrow" }, // 只顯示一個字符
  // 優化視覺效果
  dayMaxEvents: true, // 自動計算每個日期格可以顯示的最大事件數量
  moreLinkText: (n) => `+${n}`,
  eventDisplay: "block", // 塊狀顯示，更容易閱讀
  dayMaxEventRows: 3, // 設定最大行數為3
  // 處理 more 連結
  moreLinkDidMount: function (info) {
    try {
      if (info && info.el) {
        info.el.classList.add("custom-more-link");
      }
    } catch (error) {
      // 錯誤處理
    }
  },
  moreLinkClick: function (info) {
    // 使用 FullCalendar 預設行為，但記錄彈出框狀態
    setTimeout(() => {
      // 為彈出框中的事件添加點擊處理和顏色調整
      const popover = document.querySelector(".fc-popover");
      if (popover) {
        const eventElements = popover.querySelectorAll(".fc-event");
        eventElements.forEach((eventEl) => {
          // 添加點擊處理
          eventEl.addEventListener("click", () => {
            // 關閉彈出框
            const closeBtn = popover.querySelector(".fc-popover-close");
            if (closeBtn) {
              closeBtn.click();
            }
          });

          // 確保事件顏色與月曆一致
          const bgColor = eventEl.style.backgroundColor;
          if (bgColor) {
            // 如果事件有背景顏色，確保使用漸層效果
            const eventData = info.allSegs.find(
              (seg) =>
                seg.el === eventEl ||
                (seg.event && eventEl.textContent.includes(seg.event.title))
            );

            if (
              eventData &&
              eventData.event &&
              eventData.event.backgroundColor
            ) {
              const baseColor = eventData.event.backgroundColor;
              // 應用與月曆相同的漸層效果
              if (baseColor.startsWith("#")) {
                const gradients = generateGradientFromColor(baseColor);
                eventEl.style.background = gradients.backgroundGradient;
              }
            }
          }
        });
      }
    }, 100);
    return undefined; // 使用 FullCalendar 預設行為
  },
  fixedWeekCount: true, // 固定週數顯示
  contentHeight: "100%", // 設置內容高度為100%
  // List 視圖配置
  listDayFormat: {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  },
  listDaySideFormat: false,
  noEventsText: "此日期沒有行程",
  // 禁用默認的時間格式顯示
  eventTimeFormat: {
    hour: "numeric",
    minute: "2-digit",
    omitZeroMinute: false,
    meridiem: false,
  },
  // 事件渲染後處理
  eventDidMount: function (info) {
    // 為事件元素添加統一樣式
    info.el.classList.add("department-event");

    // 如果是 list 視圖，自定義顯示
    if (info.view.type === "listDay") {
      info.el.classList.add("list-view-event");

      // 獲取部門名稱和顏色
      const depName = getDepartmentName(info.event.extendedProps.dep || "");
      const depId = info.event.extendedProps.dep || "";
      const depColor = departmentColors.value[depId] || "#e3f2fd";

      // 處理時間顯示
      const timeEl = info.el.querySelector(".fc-list-event-time");
      const titleEl = info.el.querySelector(".fc-list-event-title");

      if (timeEl) {
        // 使用 setTimeout 確保在 FullCalendar 完全渲染後再處理
        setTimeout(() => {
          // 強制清空所有內容，包括子元素和文本節點
          timeEl.innerHTML = "";
          timeEl.textContent = "";

          // 移除所有可能的子節點
          while (timeEl.firstChild) {
            timeEl.removeChild(timeEl.firstChild);
          }

          if (info.event.allDay) {
            timeEl.innerHTML = '<span class="all-day-badge">整天</span>';
          } else {
            // 處理時間顯示邏輯
            const event = info.event;
            const viewDate = info.view.currentStart;

            let timeDisplay = "";

            if (event.start && event.end) {
              // 轉換 UTC 時間為本地時間
              const startDate = convertUTCToLocal(event.start);
              const endDate = convertUTCToLocal(event.end);
              const currentDate = convertUTCToLocal(viewDate);

              // 使用本地時間進行日期比較
              const startSameDay = isSameLocalDay(startDate, currentDate);
              const endSameDay = isSameLocalDay(endDate, currentDate);

              if (startSameDay && endSameDay) {
                // 同一天事件，顯示完整時間區間
                const startTime = formatLocalTime(startDate);
                const endTime = formatLocalTime(endDate);
                timeDisplay = `${startTime} - ${endTime}`;
              } else if (startSameDay) {
                // 跨日事件開始日，只顯示開始時間
                const startTime = formatLocalTime(startDate);
                timeDisplay = startTime;
              } else if (endSameDay) {
                // 跨日事件結束日，只顯示結束時間
                const endTime = formatLocalTime(endDate);
                timeDisplay = endTime;
              } else {
                // 跨日事件的中間日期，顯示整天
                timeDisplay = "all-day";
              }
            }

            if (timeDisplay === "all-day") {
              timeEl.innerHTML = '<span class="all-day-badge">整天</span>';
            } else {
              timeEl.innerHTML = `<span class="time-text">${timeDisplay}</span>`;
            }
          }
        }, 0);
      }

      if (titleEl) {
        // 使用 setTimeout 確保在 FullCalendar 完全渲染後再處理
        setTimeout(() => {
          // 強制清空所有內容，包括子元素和文本節點
          titleEl.innerHTML = "";
          titleEl.textContent = "";

          // 移除所有可能的子節點
          while (titleEl.firstChild) {
            titleEl.removeChild(titleEl.firstChild);
          }

          // 只顯示標題，不顯示詳細內容
          const eventTitle = info.event.title || "";
          // 如果標題包含詳細說明，只取第一部分作為標題
          const cleanTitle = eventTitle
            .split("\n")[0]
            .split("：")[0]
            .split(":")[0];
          titleEl.innerHTML = `
            <span class="dept-color-indicator" style="background-color: ${depColor}"></span>
            <span class="dept-badge">[${depName}]</span>
            ${cleanTitle}
          `;
        }, 0);
      }
    }
  },
}));

// 處理日期點擊 - 切換到 list 視圖
function handleDateClick(info) {
  selectedDate.value = info.dateStr;
  currentView.value = "listDay";

  // 更新月份顯示器到點選的日期所在月份
  updateDisplayMonth(info.date);

  // 更新 FullCalendar 視圖
  const calendarApi = fullCalendar.value?.getApi();
  if (calendarApi) {
    calendarApi.changeView("listDay", info.date);
  }
}

// 返回月曆視圖
function backToMonthView() {
  currentView.value = "dayGridMonth";
  selectedDate.value = null;

  // 更新 FullCalendar 視圖
  const calendarApi = fullCalendar.value?.getApi();
  if (calendarApi) {
    calendarApi.changeView("dayGridMonth");
    // 更新月份顯示器到當前視圖的月份
    const currentViewDate = calendarApi.getDate();
    updateDisplayMonth(currentViewDate);
  }
}

// UTC 時間轉換為本地時間的輔助函數
function convertUTCToLocal(utcDate) {
  if (!utcDate) return null;
  // 如果已經是 Date 對象，直接使用
  if (utcDate instanceof Date) {
    return utcDate;
  }
  // 如果是字符串，創建 Date 對象（會自動處理 UTC 轉換）
  return new Date(utcDate);
}

// 格式化時間為本地時間字符串
function formatLocalTime(date) {
  if (!date) return "";
  const localDate = convertUTCToLocal(date);
  return localDate.toLocaleTimeString("zh-TW", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });
}

// 比較兩個日期是否為同一天（本地時間）
function isSameLocalDay(date1, date2) {
  if (!date1 || !date2) return false;
  const localDate1 = convertUTCToLocal(date1);
  const localDate2 = convertUTCToLocal(date2);

  return (
    localDate1.getFullYear() === localDate2.getFullYear() &&
    localDate1.getMonth() === localDate2.getMonth() &&
    localDate1.getDate() === localDate2.getDate()
  );
}

// 格式化選中的日期
function formatSelectedDate() {
  if (!selectedDate.value) return "清單檢視";

  const date = new Date(selectedDate.value);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
  const weekday = weekdays[date.getDay()];

  return `${year}年${month}月${day}日 (${weekday})`;
}

// 處理事件點擊
function handleEventClick(info) {
  const eventId = info.event.id;
  const event = events.value.find((e) => e.id === eventId);
  if (event) {
    selectedEvent.value = event;
    // console.log("點擊事件:", event);
    // console.log("部門ID:", event.dep);
    // console.log("部門顏色對應:", departmentColors.value);

    // 根據事件的顏色設定 dialog 顏色，優先使用 background_color
    let baseColor = null;

    if (event.backgroundColor) {
      // 優先使用事件的 background_color
      baseColor = event.backgroundColor;
      // console.log("使用事件背景色:", baseColor);
    } else if (event.dep && departmentColors.value[event.dep]) {
      // 如果沒有 background_color，使用部門顏色
      baseColor = departmentColors.value[event.dep];
      // console.log("使用部門顏色:", baseColor);
    }

    if (baseColor) {
      // console.log("設定 Dialog 背景色:", baseColor);

      eventDialogStyle.value = {
        background: baseColor, // 直接使用背景色，不使用漸層
        color: "#333333", // 使用深色文字，適合淺色背景
      };
      eventDialogTextClass.value = "text-grey-8"; // 深色文字類別
    } else {
      // console.log("使用預設顏色");
      // 使用預設顏色
      eventDialogStyle.value = {
        background: "#1976d2", // 預設也不使用漸層
        color: "white", // 深色背景使用白色文字
      };
      eventDialogTextClass.value = "text-white"; // 白色文字類別
    }

    showEventDialog.value = true;
  }
}

// 調整顏色亮度的輔助函數
function adjustBrightness(color, amount) {
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };

  const rgbToHex = (r, g, b) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  };

  const rgb = hexToRgb(color);
  if (!rgb) return color;

  const r = Math.max(0, Math.min(255, rgb.r + amount));
  const g = Math.max(0, Math.min(255, rgb.g + amount));
  const b = Math.max(0, Math.min(255, rgb.b + amount));

  return rgbToHex(r, g, b);
}

// 清除篩選
function clearFilters() {
  selectedDepartment.value = null;
  filterEvents();
}

// 過濾事件（只按部門篩選）
function filterEvents() {
  if (!selectedDepartment.value) {
    filteredEvents.value = [...events.value];
  } else {
    filteredEvents.value = events.value.filter((event) => {
      // 根據事件的 dep 屬性進行篩選
      return event.dep === selectedDepartment.value;
    });
  }

  // 強制行事曆重新渲染 (v6 版本的 API 可能略有不同)
  nextTick(() => {
    if (fullCalendar.value) {
      const calendarApi = fullCalendar.value.getApi();
      calendarApi.refetchEvents();
    }
  });
}

// 格式化事件日期 - 參考 UleavePage 的設計
function formatEventDate(start, end, allDay) {
  if (!start) return "";

  // 轉換 UTC 時間為本地時間
  const startDate = convertUTCToLocal(start);
  const endDate = end ? convertUTCToLocal(end) : null;

  // 使用本地化格式 (只顯示數字，不顯示 "日")
  const formatDateOption = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  };

  if (allDay) {
    const formattedStart = startDate.toLocaleDateString(
      "zh-TW",
      formatDateOption
    );

    if (!endDate) {
      return formattedStart;
    }

    // 對於全天事件，需要將排他性結束時間減一天來顯示正確的結束日期
    const displayEndDate = new Date(endDate);
    displayEndDate.setDate(displayEndDate.getDate() - 1);

    // 檢查是否為同一天
    if (
      startDate.getFullYear() === displayEndDate.getFullYear() &&
      startDate.getMonth() === displayEndDate.getMonth() &&
      startDate.getDate() === displayEndDate.getDate()
    ) {
      return formattedStart;
    } else {
      const formattedEnd = displayEndDate.toLocaleDateString(
        "zh-TW",
        formatDateOption
      );
      return `${formattedStart} - ${formattedEnd}`;
    }
  } else {
    // 時間事件
    const formattedStart =
      startDate.toLocaleDateString("zh-TW", formatDateOption) +
      " " +
      formatLocalTime(startDate);

    if (!endDate) {
      return formattedStart;
    } else {
      // 如果是同一天，只顯示結束時間
      if (
        startDate.getFullYear() === endDate.getFullYear() &&
        startDate.getMonth() === endDate.getMonth() &&
        startDate.getDate() === endDate.getDate()
      ) {
        const formattedEndTime = formatLocalTime(endDate);
        return `${formattedStart} - ${formattedEndTime}`;
      } else {
        // 不同天，顯示完整日期和時間
        const formattedEnd =
          endDate.toLocaleDateString("zh-TW", formatDateOption) +
          " " +
          formatLocalTime(endDate);
        return `${formattedStart} - ${formattedEnd}`;
      }
    }
  }
}

// 格式化年月顯示
function formatYearMonth(date) {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  return `${year}-${month < 10 ? "0" + month : month}`;
}

// 月份切換函數
function prevMonth() {
  try {
    if (fullCalendar.value && typeof fullCalendar.value.getApi === "function") {
      const calendarApi = fullCalendar.value.getApi();
      calendarApi.prev(); // 使用 FullCalendar API 切換到上個月
      updateDisplayMonth(calendarApi.getDate());
    }
  } catch (error) {
    // 錯誤處理
  }
}

function nextMonth() {
  try {
    if (fullCalendar.value && typeof fullCalendar.value.getApi === "function") {
      const calendarApi = fullCalendar.value.getApi();
      calendarApi.next(); // 使用 FullCalendar API 切換到下個月
      updateDisplayMonth(calendarApi.getDate());
    }
  } catch (error) {
    // 錯誤處理
  }
}

// 更新顯示月份
function updateDisplayMonth(date) {
  displayMonth.value = formatYearMonth(date);
}

// 處理視窗大小變化
function handleResize() {
  // 使用 setTimeout 確保 DOM 已經更新，延長延遲時間
  setTimeout(() => {
    try {
      if (
        document.querySelector(".fc") &&
        fullCalendar.value &&
        typeof fullCalendar.value.getApi === "function"
      ) {
        const calendarApi = fullCalendar.value.getApi();
        if (calendarApi) {
          calendarApi.updateSize();
        }
      }
    } catch (error) {
      // 錯誤處理
    }
  }, 200); // 增加延遲時間
}

// 新增/編輯事件函數
async function addNewEvent() {
  if (!newEvent.value.title || !newEvent.value.startDate) {
    $q.notify({
      type: "negative",
      message: "請填寫標題和開始日期",
    });
    return;
  }

  // 檢查是否有有效的目標對象
  const validTargets = (newEvent.value.targets || []).filter(
    (target) =>
      target.target_type && target.target_id && target.target_id.trim()
  );

  if (validTargets.length === 0) {
    $q.notify({
      type: "negative",
      message: "請至少選擇一個目標對象",
    });
    return;
  }

  if (editingEventId.value) {
    // 編輯模式
    await updateEventToDatabase();
  } else {
    // 新增模式
    await createNewEvent();
  }
}

// 新增事件到資料庫
async function createNewEvent() {
  const startDateTime = newEvent.value.allDay
    ? newEvent.value.startDate
    : `${newEvent.value.startDate}T${newEvent.value.startTime || "00:00"}`;

  const endDateTime = newEvent.value.allDay
    ? (() => {
        // 對於全天事件，結束日期需要加一天（FullCalendar 的排他性結束時間）
        const endDate = new Date(
          newEvent.value.endDate || newEvent.value.startDate
        );
        endDate.setDate(endDate.getDate() + 1);
        return endDate.toISOString().split("T")[0];
      })()
    : `${newEvent.value.endDate || newEvent.value.startDate}T${
        newEvent.value.endTime || "23:59"
      }`;

  // 根據部門設定背景色，如果沒有部門則使用選擇的色系
  let backgroundColor = "#E3F2FD";
  let backgroundGradient = "linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%)";

  if (newEvent.value.dep && departmentColors.value[newEvent.value.dep]) {
    // 使用部門顏色的較亮版本
    const baseColor = departmentColors.value[newEvent.value.dep];
    const gradients = generateGradientFromColor(baseColor);
    backgroundColor = gradients.lighterColor;
    backgroundGradient = gradients.backgroundGradient;
  } else {
    // 使用選擇的色系
    const selectedColor = colorOptions.find(
      (c) => c.value === newEvent.value.color
    );
    backgroundColor = selectedColor ? selectedColor.color : "#E3F2FD";
    backgroundGradient = selectedColor
      ? selectedColor.gradient
      : "linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%)";
  }

  // 轉換目標對象格式以符合後端需求，將逗號分隔的 ID 拆分為獨立記錄
  const formattedTargets = [];
  (newEvent.value.targets || []).forEach((target) => {
    if (target.target_type && target.target_id && target.target_id.trim()) {
      // 將逗號分隔的 ID 拆分為獨立記錄
      const ids = target.target_id.split(",").map((id) => id.trim());
      ids.forEach((id) => {
        if (id) {
          formattedTargets.push({
            target_type: target.target_type,
            target_id: id,
          });
        }
      });
    }
  });

  const event = {
    id: Date.now().toString(),
    title: newEvent.value.title,
    description: newEvent.value.description,
    start: startDateTime,
    end: endDateTime,
    allDay: newEvent.value.allDay,
    type: newEvent.value.type,
    backgroundColor: backgroundColor,
    backgroundGradient: backgroundGradient,
    dep: newEvent.value.dep,
    targetType: newEvent.value.targetType,
    targetId: newEvent.value.targetId,
    color: newEvent.value.color,
    targets: formattedTargets,
  };

  // 保存到資料庫
  await saveEventToDatabase({
    title: event.title,
    description: event.description,
    start: event.start,
    end: event.end,
    allDay: event.allDay,
    backgroundColor: event.backgroundColor,
    eventType: event.eventType || "department",
    sourceType: "manual",
    sourceId: "",
    dep: event.dep,
    targets: event.targets,
  });

  // 重置表單
  resetEventForm();

  showAddEventDialog.value = false;
}

// 載入目標選項
const loadTargetOptions = async () => {
  try {
    // 載入部門選項（使用新的 events API 端點）
    const depsResponse = await apiClient.get("/events/departments");
    if (depsResponse.data.success && Array.isArray(depsResponse.data.data)) {
      departmentOptions.value = depsResponse.data.data
        .map((dep) => ({
          label: dep.name.trim(),
          value: dep.id.trim(),
        }))
        .sort((a, b) => a.value.localeCompare(b.value)); // 按 ID 排序
      console.log("部門選項:", departmentOptions.value);
    } else {
      console.error("部門資料格式不正確:", depsResponse.data);
      departmentOptions.value = [];
    }

    // 載入門市選項（參考 BtinmanagePage，篩選 sts='1' 的門市）
    const branchResponse = await apiClient.get(
      `${apiBaseUrl}/branch/get_branch`
    );
    if (branchResponse.data && Array.isArray(branchResponse.data)) {
      branchOptions.value = branchResponse.data
        .filter((branch) => branch.Sts === "1") // 篩選 sts='1' 的門市
        .map((branch) => ({
          label: branch.Cod_name.trim(),
          value: branch.Cod_cust.trim(),
        }));
    } else {
      console.error("門市資料格式不正確:", branchResponse.data);
      branchOptions.value = [];
    }

    // 載入群組選項（參考 BtinmanagePage）
    const groupResponse = await apiClient.get(
      `${apiBaseUrl}/users/get_usersgroup`
    );
    if (groupResponse.data && Array.isArray(groupResponse.data)) {
      groupOptions.value = groupResponse.data.map((group) => ({
        label: group.Name.trim(),
        value: group.Code.trim(),
      }));
    } else {
      console.error("群組資料格式不正確:", groupResponse.data);
      groupOptions.value = [];
    }

    // 載入用戶選項（參考 BtinmanagePage）
    try {
      const usersResponse = await apiClient.post(
        `${apiBaseUrl}/users/get_enable_users`
      );
      if (usersResponse.data && Array.isArray(usersResponse.data)) {
        userOptions.value = usersResponse.data.map((user) => ({
          label: `${user.Name.trim()} (${user.ID.trim()})`,
          value: user.ID.trim(),
        }));
      } else {
        console.error("用戶資料格式不正確:", usersResponse.data);
        userOptions.value = [];
      }
    } catch (error) {
      console.error("載入用戶選項錯誤:", error);
      userOptions.value = [];
    }
  } catch (error) {
    console.error("載入目標選項錯誤:", error);
    departmentOptions.value = [];
    branchOptions.value = [];
    groupOptions.value = [];
    userOptions.value = [];
  }
};

// 清除字串前後空白
function cleanStringsDeep(obj) {
  if (Array.isArray(obj)) {
    return obj.map(cleanStringsDeep);
  } else if (obj && typeof obj === "object") {
    const cleaned = {};
    for (const key in obj) {
      const val = obj[key];
      cleaned[key] =
        typeof val === "string" ? val.trim() : cleanStringsDeep(val); // 遞迴處理巢狀物件
    }
    return cleaned;
  } else {
    return obj;
  }
}

onMounted(async () => {
  // 添加視窗大小變化的監聽器
  window.addEventListener("resize", handleResize);

  // 檢查是否為Admin
  if (Array.isArray(props.Permissions)) {
    isAdmin.value = props.Permissions.some(
      (p) => p.trim().toLowerCase() === "admin"
    );
  } else if (typeof props.Permissions === "string") {
    isAdmin.value = props.Permissions.trim().toLowerCase() === "admin";
  }

  // 載入目標選項
  loadTargetOptions();

  // 載入部門顏色設定
  await fetchDepartmentColors();

  // 載入事件數據
  fetchEvents();

  // 初始化事件顏色
  if (newEvent.value.dep) {
    updateEventColor(newEvent.value.dep);
  }

  // 初始調整大小 - 延遲執行以確保DOM完全渲染
  setTimeout(() => {
    try {
      if (
        document.querySelector(".fc") &&
        fullCalendar.value &&
        typeof fullCalendar.value.getApi === "function"
      ) {
        const calendarApi = fullCalendar.value.getApi();
        if (calendarApi) {
          calendarApi.updateSize();
        }
      }
    } catch (error) {
      // 錯誤處理
    }
  }, 300); // 增加延遲時間

  // 監聽布局變化
  const layoutElement = document.querySelector(".q-layout");
  if (layoutElement) {
    const layoutObserver = new MutationObserver(handleResize);
    layoutObserver.observe(layoutElement, {
      attributes: true,
      childList: true,
      subtree: true,
      attributeFilter: ["class", "style"],
    });
  }

  // 監聽側邊欄變化
  const drawerElements = document.querySelectorAll(".q-drawer");
  if (drawerElements.length > 0) {
    const drawerObserver = new MutationObserver(handleResize);
    drawerElements.forEach((drawer) => {
      drawerObserver.observe(drawer, {
        attributes: true,
        attributeFilter: ["style", "class"],
      });
    });
  }

  // 監聽選單按鈕點擊
  const menuButtons = document.querySelectorAll('button[icon="menu"]');
  if (menuButtons.length > 0) {
    menuButtons.forEach((button) => {
      button.addEventListener("click", () => setTimeout(handleResize, 300));
    });
  }
});
</script>
<style scoped>
.calendar-container {
  margin-top: 3px;
  height: 100%; /* 填滿父容器 */
  min-height: 425px; /* 縮小電腦模式下的最小高度50px */
  width: 100%;
  position: relative;
  overflow: hidden;
  flex: 1 1 auto; /* 讓容器可以彈性擴展 */
  display: flex; /* 使用 flex 布局 */
  flex-direction: column; /* 垂直方向排列 */
} /* 確保日曆頁面可以填滿整個空間 */
.calendar-page {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 50px);
} /* 確保行事曆卡片能夠填滿可用空間 */
.calendar-card {
  display: flex;
  flex-direction: column;
  flex: 1;
} /* 確保卡片內容區域填滿卡片 */
.calendar-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding-bottom: 0 !important;
}
.filter-select {
  width: 100%;
}
.event-input {
  width: 100%;
} /* 月份選擇器樣式 */
.month-selector {
  min-width: 110px;
} /* 自定義行事曆樣式 */
:deep(.fc) {
  font-family: inherit;
  --fc-border-color: #e5e5e5;
  --fc-today-bg-color: transparent; /* 移除當天底色 */
  --fc-event-border-color: transparent;
  --fc-page-bg-color: #ffffff;
  --fc-event-text-color: #333333;
  --fc-list-event-hover-bg-color: #f5f5f5;
  height: 100% !important; /* 強制 FullCalendar 填滿容器 */
  border-radius: 8px;
  overflow: hidden;
}

/* 專門針對 popover 覆蓋 CSS 變數 */
:deep(.fc-popover) {
  --fc-border-color: transparent !important;
  --fc-today-bg-color: transparent !important;
}

:deep(.fc-popover.fc-more-popover) {
  --fc-border-color: transparent !important;
  --fc-today-bg-color: transparent !important;
}
:deep(.fc-view-harness) {
  height: 100% !important; /* 確保視圖容器填滿整個空間 */
}
.full-height-calendar {
  height: 100% !important;
  flex: 1;
}
:deep(.fc-scroller) {
  overflow: visible !important; /* 允許內容溢出顯示 */
}
:deep(.fc-toolbar-title) {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}
:deep(.fc-button) {
  border-radius: 4px !important;
  text-transform: none !important;
  box-shadow: none !important;
  transition: all 0.2s ease;
}
:deep(.fc-button-primary) {
  background-color: #f5f5f5 !important;
  border-color: #e0e0e0 !important;
  color: #333 !important;
}
:deep(.fc-button-primary:hover) {
  background-color: #e0e0e0 !important;
  border-color: #d0d0d0 !important;
}
:deep(.fc-button-primary.fc-button-active) {
  background-color: #1976d2 !important;
  border-color: #1976d2 !important;
  color: white !important;
}
:deep(.fc-event) {
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 4px;
  margin: 1px 3px 2px 3px; /* 增加間距使視覺更清晰 */
  font-size: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 增強陰影效果 */
  border-left: 2px solid rgba(0, 0, 0, 0.15);
  height: auto;
  min-height: 18px; /* 稍微增加高度改善可讀性 */
  line-height: 1.2;
  opacity: 1 !important; /* 確保不透明 */
  position: relative; /* 啟用 z-index */
  z-index: 5; /* 確保行程在日期下方 */
  transition: all 0.2s ease;
}

:deep(.fc-event:hover) {
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.12);
}
:deep(.fc-event-title) {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.75rem;
  letter-spacing: 0.01em;
}
:deep(.fc-event-time) {
  font-weight: 500;
  opacity: 0.9;
  font-size: 0.7rem;
  margin-right: 2px;
}
:deep(.fc-day-today) {
  background-color: transparent !important; /* 移除當天底色 */
  position: relative;
}

:deep(.fc-day-today)::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(25, 118, 210, 0.4); /* 加深當天外框顏色 */
  pointer-events: none;
  box-sizing: border-box;
  border-radius: 0;
  z-index: 1;
}

/* 確保當天外框不會覆蓋到 popover */
:deep(.fc-day-today):has(.fc-popover)::after,
:deep(.fc-day-today):has(.fc-popover.fc-more-popover)::after {
  z-index: -1 !important;
} /* 確保事件背景色不透明 */
:deep(.fc-event-main) {
  opacity: 1 !important;
}
:deep(.fc-col-header-cell) {
  background-color: transparent; /* 移除星期底色 */
  padding: 8px 0;
  border-bottom: 2px solid #e0e0e0;
}
:deep(.fc-col-header-cell-cushion) {
  font-weight: 600;
  color: #555; /* 調整顏色，增強對比度 */
  text-decoration: none !important;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
:deep(.fc-daygrid-day-number) {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  text-decoration: none !important;
  padding: 4px 6px;
  background-color: transparent;
  z-index: 11; /* 確保日期號碼在最上層 */
  position: relative; /* 啟用 z-index */
  border-radius: 50%;
  min-width: 24px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

:deep(.fc-daygrid-day-number:hover) {
  background-color: transparent;
}

:deep(.fc-daygrid-day.fc-day-today .fc-daygrid-day-number) {
  color: #1976d2;
  font-weight: 700;
  background-color: transparent;
}
:deep(.fc-daygrid-more-link) {
  background-color: rgba(25, 118, 210, 0.1); /* 使用主題色，半透明 */
  border-radius: 20px;
  padding: 3px 10px;
  font-size: 0.75rem;
  color: #1976d2; /* 使用主題色 */
  font-weight: 600; /* 加粗顯示 */
  margin-top: 2px;
  margin-bottom: 2px;
  display: inline-block;
  text-align: center;
  position: relative; /* 啟用 z-index */
  z-index: 5; /* 確保在日期下方 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 輕微陰影 */
  cursor: pointer; /* 增加指針樣式，提示可點擊 */
  transition: all 0.2s ease; /* 添加過渡效果 */
  border: none; /* 移除邊框 */
  width: auto;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}

:deep(.fc-daygrid-more-link:hover) {
  background-color: rgba(25, 118, 210, 0.15); /* 懸停時加深背景色 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15); /* 懸停時增強陰影 */
  transform: translateY(-1px);
}

/* 移除當天「+更多」按鈕的外框效果 */
:deep(.fc-day-today .fc-daygrid-more-link) {
  border: none !important;
  position: relative;
  z-index: 10; /* 確保在當天外框之上 */
}

/* 確保當天的外框不會影響到其他元素 */

/* 移除所有 popover 的邊框 */
:deep(.fc-popover) {
  border: none !important;
  outline: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  --fc-border-color: transparent !important;
}

/* 強制移除 FullCalendar 預設的 popover 邊框 */
:deep(.fc-popover),
:deep(.fc-popover *) {
  border: none !important;
  outline: none !important;
  --fc-border-color: transparent !important;
}

/* 針對當天的 popover 特別處理 */
:deep(.fc-popover) {
  border-top: none !important;
  border-right: none !important;
  border-bottom: none !important;
  border-left: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
}

/* 移除彈出窗口中的當天外框效果 */
:deep(.fc-popover .fc-day-today)::after {
  display: none !important;
}

:deep(.fc-popover .fc-day-today) {
  border: none !important;
}

/* 強制移除當天彈出窗口的外框 - 針對從當天觸發的 popover */
:deep(.fc-day-today .fc-popover),
:deep(.fc-day-today .fc-popover.fc-more-popover) {
  border: none !important;
  outline: none !important;
}

/* 移除當天外框對 popover 的影響 */
:deep(.fc-day-today)::after {
  z-index: 1 !important;
  pointer-events: none !important;
}

/* 終極解決方案：覆蓋所有可能的 popover 邊框 */
:deep(.fc-popover),
:deep(.fc-popover.fc-more-popover),
:deep(.fc .fc-popover),
:deep(.fc .fc-popover.fc-more-popover),
:deep(div.fc-popover),
:deep(div.fc-popover.fc-more-popover) {
  border: 0px solid transparent !important;
  border-width: 0px !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: 0px solid transparent !important;
  outline-width: 0px !important;
  outline-style: none !important;
  outline-color: transparent !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  position: relative !important;
  z-index: 10000 !important;
}

/* 阻止 popover 繼承當天的 ::after 偽元素 */
:deep(.fc-popover)::after,
:deep(.fc-popover.fc-more-popover)::after,
:deep(.fc-popover)::before,
:deep(.fc-popover.fc-more-popover)::before {
  display: none !important;
  content: none !important;
  border: none !important;
}

/* 強制移除當天 popover 的所有偽元素邊框 */
:deep(.fc-day-today .fc-popover)::after,
:deep(.fc-day-today .fc-popover.fc-more-popover)::after,
:deep(.fc-day-today .fc-popover)::before,
:deep(.fc-day-today .fc-popover.fc-more-popover)::before {
  display: none !important;
  content: none !important;
  border: none !important;
  background: none !important;
}

:deep(.fc-popover-header) {
  background-color: #1976d2 !important;
  background-image: linear-gradient(135deg, #1976d2, #2196f3) !important;
  color: white !important;
  padding: 10px 12px !important;
  font-weight: 600 !important;
  border-radius: 8px 8px 0 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

:deep(.fc-popover-title) {
  font-size: 0.95rem !important;
  letter-spacing: 0.01em !important;
}

:deep(.fc-popover-close) {
  color: white !important;
  opacity: 0.9 !important;
  font-size: 1.2rem !important;
  transition: all 0.2s ease !important;
  border-radius: 50% !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.fc-popover-close:hover) {
  background-color: rgba(255, 255, 255, 0.2) !important;
  opacity: 1 !important;
} /* 彈出窗口中事件的樣式 */
:deep(.fc-popover-body) {
  padding: 8px !important;
  max-height: 300px !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent !important;
  background-color: white !important;
}

:deep(.fc-popover-body::-webkit-scrollbar) {
  width: 6px !important;
}

:deep(.fc-popover-body::-webkit-scrollbar-thumb) {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-radius: 3px !important;
}

:deep(.fc-popover-body .fc-daygrid-event-harness) {
  margin-bottom: 6px !important;
}

:deep(.fc-popover) .fc-event {
  margin: 3px 0 !important;
  padding: 5px 8px !important;
  border-radius: 6px !important;
  max-width: 100% !important;
  overflow: hidden !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
  background-color: var(
    --fc-page-bg-color,
    white
  ) !important; /* 設置彈出窗口事件背景色 */
}

/* List 視圖樣式 - 重新設計 */
:deep(.fc-list) {
  border: none !important;
  background-color: white !important;
}

:deep(.fc-list-table) {
  border: none !important;
  border-collapse: collapse !important;
}

:deep(.fc-list-table td) {
  border: none !important;
  border-top: none !important;
  border-bottom: none !important;
  border-left: none !important;
  border-right: none !important;
}

:deep(.fc-list-table tr) {
  border: none !important;
}

:deep(.fc-list-day-cushion) {
  background-color: #f5f5f5 !important;
  color: #1976d2 !important;
  font-weight: 600 !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 8px 0 !important;
  border: none !important;
  display: none !important; /* 隱藏日期顯示 */
}

:deep(.fc-list-day-cushion td) {
  border: none !important;
}

/* 隱藏包含日期的 th 元素，避免留下空白區域 */
:deep(th.fc-list-day) {
  display: none !important;
}

:deep(.fc-list-event) {
  border: none !important;
  border-radius: 0 !important;
  margin: 4px 0 !important;
  box-shadow: none !important;
  display: flex !important;
  align-items: center !important;
  background-color: transparent !important;
}

/* 移除懸停效果 */
:deep(.fc-list-event:hover) {
  box-shadow: none !important;
  transform: none !important;
  background-color: transparent !important;
}

/* 移除選中狀態的底色 */
:deep(.fc-list-event:active) {
  background-color: transparent !important;
}

:deep(.fc-list-event:focus) {
  background-color: transparent !important;
  outline: none !important;
}

:deep(.fc-list-event.fc-event-selected) {
  background-color: transparent !important;
}

:deep(.fc-list-event.fc-event-today) {
  background-color: transparent !important;
}

/* 移除所有可能的背景色狀態 */
:deep(.fc-list-event td) {
  background-color: transparent !important;
  border: none !important;
  border-top: none !important;
  border-bottom: none !important;
  border-left: none !important;
  border-right: none !important;
}

:deep(.fc-list-event-time),
:deep(.fc-list-event-title) {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

:deep(.fc-list-event-time:hover),
:deep(.fc-list-event-title:hover),
:deep(.fc-list-event-time:active),
:deep(.fc-list-event-title:active),
:deep(.fc-list-event-time:focus),
:deep(.fc-list-event-title:focus) {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

:deep(.fc-list-event-dot) {
  display: none !important;
}

/* 時間顯示區域 - 靠左對齊 */
:deep(.fc-list-event-time) {
  min-width: 80px !important;
  padding: 12px 8px 12px 16px !important;
  font-size: 0.85rem !important;
  text-align: left !important;
  vertical-align: middle !important;
}

/* 隱藏 FullCalendar 原始的時間文字 */
:deep(.fc-list-event-time .fc-list-event-time-text) {
  display: none !important;
}

:deep(.fc-list-event-time .fc-event-time) {
  display: none !important;
}

/* 隱藏所有可能的原始時間顯示 */
:deep(.fc-list-event-time > *:not(.all-day-badge):not(.time-text)) {
  display: none !important;
}

/* 隱藏所有可能的原始標題顯示 */
:deep(.fc-list-event-title > *:not(.dept-badge)) {
  display: none !important;
}

/* 強制隱藏 FullCalendar 的原始文本節點 */
:deep(.fc-list-event-time)::before {
  content: none !important;
}

:deep(.fc-list-event-title)::before {
  content: none !important;
}

/* 全天標籤樣式 */
:deep(.all-day-badge) {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  padding: 3px 8px !important;
  border-radius: 12px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
  display: inline-block !important;
}

/* 時間文字樣式 */
:deep(.time-text) {
  color: #666 !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
}

/* 標題顯示區域 - 緊鄰時間 */
:deep(.fc-list-event-title) {
  padding: 12px 16px 12px 0 !important;
  font-weight: 500 !important;
  color: #333 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
}

/* 移除標題懸停效果 */
:deep(.fc-list-event-title:hover) {
  background-color: transparent !important;
  color: #333 !important;
}

/* 部門顏色指示器 */
:deep(.dept-color-indicator) {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  margin-right: 8px !important;
  vertical-align: middle !important;
  flex-shrink: 0 !important;
}

/* 部門標籤樣式 - 無底色 */
:deep(.dept-badge) {
  background-color: transparent !important;
  color: #666 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  margin-right: 8px !important;
  display: inline-block !important;
}

:deep(.fc-list-empty) {
  text-align: center !important;
  padding: 40px 20px !important;
  color: #999 !important;
  font-size: 1.1rem !important;
}

:deep(.fc-popover) .fc-event:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
}

:deep(.fc-popover) .fc-event-title {
  white-space: normal !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  font-weight: 500 !important;
  font-size: 0.8rem !important;
  letter-spacing: 0.01em !important;
} /* 為不同類型的事件添加左邊框標記 */
:deep(.brand-event) {
  border-left: 3px solid #ff5722 !important;
  position: relative !important;
}

:deep(.brand-event)::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 87, 34, 0.1),
    transparent 70%
  ) !important;
  pointer-events: none;
}

:deep(.department-event) {
  border-left: 3px solid #2196f3 !important;
  position: relative !important;
}

:deep(.department-event)::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0.1),
    transparent 70%
  ) !important;
  pointer-events: none;
} /* 確保日期單元格內容一致 */
:deep(.fc-daygrid-day-events) {
  min-height: 1.5em;
  margin-bottom: 0 !important;
  margin-top: 2px; /* 添加與日期的間距 */
  position: relative; /* 啟用 z-index */
  z-index: 5; /* 確保行程在日期下方 */
}
:deep(.fc-daygrid-day-frame) {
  display: flex;
  flex-direction: column;
  min-height: 23px; /* 縮小日期單元格高度45px */
  position: relative; /* 設置相對定位，用於子元素的絕對定位 */
  padding-top: 20px; /* 減少頂部間距，讓行程更靠近日期 */
}
:deep(.fc-daygrid-day-top) {
  position: absolute; /* 絕對定位 */
  top: 2px; /* 距離頂部的距離 */
  left: 0;
  right: 0;
  z-index: 10; /* 確保日期在行程上方 */
  justify-content: flex-end; /* 靠右顯示日期號碼 */
  padding-right: 5px; /* 右側間距 */
}
:deep(.fc-daygrid-day-bottom) {
  margin-top: auto;
} /* 事件詳情對話框樣式 */
:deep(.q-dialog__inner) {
  backdrop-filter: blur(2px);
}
:deep(.q-card) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.filter-dialog,
.add-event-dialog {
  border-radius: 12px;
  overflow: hidden;
}
.filter-dialog :deep(.q-field__control),
.add-event-dialog :deep(.q-field__control) {
  background-color: #f8f8f8;
}
.add-event-dialog :deep(.q-checkbox__inner) {
  color: var(--q-primary);
}
.add-event-dialog :deep(.q-scrollarea__thumb) {
  background: rgba(0, 0, 0, 0.2);
  width: 6px;
  opacity: 0.6;
  transition: opacity 0.3s;
}
.add-event-dialog :deep(.q-scrollarea__thumb:hover) {
  opacity: 0.9;
}

/* 部門色系指示器樣式（參考 UpayslipPage）*/
.department-color-indicator {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: default;
  position: relative;
  overflow: hidden;
  background: var(--gradient-bg);
}

.department-color-indicator::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--border-gradient);
  opacity: 0.8;
}

.department-color-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 事件漸層樣式（參考 UpayslipPage）*/
.fc-event {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.fc-event::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 4px !important;
  height: 100% !important;
  background: var(
    --event-border-gradient,
    linear-gradient(180deg, #64b5f6 0%, #1976d2 100%)
  ) !important;
  opacity: 0.8 !important;
}

.fc-event:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}

/* 事件內容樣式 */
.fc-event-title {
  font-weight: 500 !important;
  font-size: 0.85rem !important;
}

.fc-event-time {
  font-weight: 600 !important;
  opacity: 0.9 !important;
}

/* 新增事件對話框響應式設計 */
.add-event-dialog {
  width: 100%;
  max-width: 800px;
}

/* 手機版調整 */
@media (max-width: 599px) {
  .add-event-dialog {
    max-width: 95vw !important;
    margin: 8px !important;
  }

  .add-event-dialog .q-card {
    border-radius: 8px !important;
  }

  .add-event-dialog .q-card-section {
    padding: 12px !important;
  }

  .department-color-indicator {
    width: 28px;
    height: 28px;
  }

  .event-input {
    font-size: 14px;
  }

  .text-subtitle2 {
    font-size: 13px;
  }
}

/* 超小螢幕調整 */
@media (max-width: 340px) {
  .add-event-dialog {
    max-width: 98vw !important;
    margin: 4px !important;
  }

  .add-event-dialog .q-card-section {
    padding: 8px !important;
  }

  .department-color-indicator {
    width: 24px;
    height: 24px;
  }

  .event-input {
    font-size: 13px;
  }

  .text-subtitle2 {
    font-size: 12px;
  }
} /* 響應式調整 */
/* 彈出窗口樣式 */
:deep(.fc-popover.fc-more-popover) {
  border: none !important;
  outline: none !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1000 !important;
  max-width: 300px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 90% !important;
  background-color: white !important;
  --fc-border-color: transparent !important;
  border-top: none !important;
  border-right: none !important;
  border-bottom: none !important;
  border-left: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
}

/* 強制移除所有 popover 相關元素的邊框 */
:deep(.fc-popover.fc-more-popover),
:deep(.fc-popover.fc-more-popover *),
:deep(.fc-popover-header),
:deep(.fc-popover-body) {
  border: none !important;
  outline: none !important;
  --fc-border-color: transparent !important;
  border-top: none !important;
  border-right: none !important;
  border-bottom: none !important;
  border-left: none !important;
  border-width: 0 !important;
  border-style: none !important;
  border-color: transparent !important;
}
@media (max-width: 600px) {
  /* 手機模式下彈出窗口樣式調整 */
  :deep(.fc-popover.fc-more-popover) {
    width: 85% !important;
    max-width: 250px !important;
    right: auto !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }

  :deep(.fc-popover-body) {
    max-height: 200px !important;
    overflow-y: auto !important;
    word-break: break-word !important;
  }
  :deep(.fc-toolbar) {
    flex-direction: column;
  }
  :deep(.fc-toolbar-chunk) {
    margin-bottom: 0.5rem;
  }
  :deep(.fc-daygrid-day-number) {
    font-size: 0.8rem;
    padding: 2px 4px;
  }
  :deep(.fc-event) {
    padding: 1px 2px;
    font-size: 0.7rem;
    min-height: 16px;
    line-height: 1.1;
  }
  :deep(.fc-daygrid-day) {
    min-height: 10px !important;
  }
  :deep(.fc-daygrid-day-frame) {
    min-height: 10px !important;
    padding-top: 18px; /* 手機模式下進一步減少頂部間距 */
  }
  :deep(.fc-daygrid-more-link) {
    font-size: 0.65rem;
    padding: 1px 4px;
    margin-top: 0;
    background-color: rgba(25, 118, 210, 0.15); /* 在手機模式下更明顯的背景色 */
    font-weight: 500; /* 加粗顯示 */
    color: #1976d2; /* 使用主題色 */
  }
  :deep(.fc-col-header-cell-cushion) {
    font-size: 0.75rem;
    color: #666; /* 手機模式下也保持一致的顏色 */
  }
  .calendar-container {
    height: 75vh; /* 縮小手機模式下的高度比例 */
    min-height: 305px; /* 縮小50px */
  }
  .calendar-page {
    min-height: calc(100vh - 50px);
    padding: 4px !important; /* 減少內邊距，增加可用空間 */
  }
  .month-selector {
    min-width: 90px;
  }
  .filter-select {
    font-size: 0.8rem;
  } /* 手機模式下調整間距 */
  .q-card-section.q-py-sm {
    padding-top: 4px;
    padding-bottom: 4px;
  }
  .row.q-col-gutter-md {
    margin: -4px;
  }
  .row.q-col-gutter-md > * {
    padding: 4px;
  }
}

/* 更多行程彈出框中的事件樣式 */
:deep(.fc-popover) {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
}

:deep(.fc-popover .fc-event) {
  border: none !important;
  border-radius: 6px !important;
  margin: 2px 0 !important;
  padding: 4px 8px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

:deep(.fc-popover .fc-event:hover) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

:deep(.fc-popover .fc-event-title) {
  font-weight: 500 !important;
  color: #333 !important;
  text-shadow: none !important;
}

:deep(.fc-popover .fc-event-time) {
  color: #555 !important;
  font-size: 0.85em !important;
  text-shadow: none !important;
}

.event-publisher {
  color: #666;
  font-size: 14px;
}
</style>
