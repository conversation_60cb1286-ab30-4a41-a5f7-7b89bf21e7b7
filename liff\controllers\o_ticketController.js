const { getPoolByBranch, sql } = require("../services/dbPoolManager");

// 取得票券列表
const getTicketList = async (req, res) => {
  try {
    const { branch } = req.query;
    const pool = await getPoolByBranch(branch);

    if (!pool) {
      return res.status(400).json({
        success: false,
        message: "無效的門市代號",
      });
    }

    const query = `
      SELECT 
        t.productid,
        t.mname AS ticket_name,
        t.bcode,
        t.mcode,
        t.eprmcode,
        m.name AS product_name,
        em.name AS expired_product_name,
        t.otherpay_code,
        t.otherpay_paykind,
        t.otherpay_amt
      FROM ticketrd t
      LEFT JOIN omenum m ON m.code = t.mcode
      LEFT JOIN omenum em ON em.code = t.eprmcode
      WHERE t.bcode = @bcode
      ORDER BY t.tb_id DESC
    `;

    const request = pool.request();
    request.input("bcode", sql.VarChar, branch.substring(0, 2));
    const result = await request.query(query);

    res.json({
      success: true,
      data: result.recordset,
    });
  } catch (error) {
    console.error("取得票券列表失敗:", error);
    res.status(500).json({
      success: false,
      message: "取得票券列表失敗",
    });
  }
};

// 新增票券
const createTicket = async (req, res) => {
  try {
    let {
      productid,
      mname,
      bcode,
      mcode,
      eprmcode,
      otherpay_code,
      otherpay_paykind,
      otherpay_amt,
      branch,
    } = req.body;

    const pool = await getPoolByBranch(branch);

    if (!pool) {
      return res.status(400).json({
        success: false,
        message: "無效的門市代號",
      });
    }

    bcode = branch.substring(0, 2); // 強制取前2碼

    // 檢查票券編號是否已存在
    const checkQuery =
      "SELECT productid FROM ticketrd WHERE productid = @productid";
    const checkRequest = pool.request();
    checkRequest.input("productid", sql.VarChar, productid);
    const checkResult = await checkRequest.query(checkQuery);

    if (checkResult.recordset.length > 0) {
      return res.status(400).json({
        success: false,
        message: "票券編號已存在",
      });
    }

    const insertQuery = `
      INSERT INTO ticketrd (
        productid, 
        mname, 
        bcode, 
        mcode, 
        eprmcode, 
        otherpay_code, 
        otherpay_paykind, 
        otherpay_amt
      ) VALUES (@productid, @mname, @bcode, @mcode, @eprmcode, @otherpay_code, @otherpay_paykind, @otherpay_amt)
    `;

    const insertRequest = pool.request();
    insertRequest.input("productid", sql.VarChar, productid);
    insertRequest.input("mname", sql.NVarChar, mname);
    insertRequest.input("bcode", sql.VarChar, bcode);
    insertRequest.input("mcode", sql.VarChar, mcode || null);
    insertRequest.input("eprmcode", sql.VarChar, eprmcode || null);
    insertRequest.input("otherpay_code", sql.VarChar, otherpay_code || null);
    insertRequest.input(
      "otherpay_paykind",
      sql.VarChar,
      otherpay_paykind || null
    );
    insertRequest.input(
      "otherpay_amt",
      sql.Decimal(10, 2),
      otherpay_amt || null
    );

    await insertRequest.query(insertQuery);

    res.json({
      success: true,
      message: "票券新增成功",
    });
  } catch (error) {
    console.error("新增票券失敗:", error);
    res.status(500).json({
      success: false,
      message: "新增票券失敗",
    });
  }
};

// 更新票券
const updateTicket = async (req, res) => {
  try {
    let {
      productid,
      mname,
      bcode,
      mcode,
      eprmcode,
      otherpay_code,
      otherpay_paykind,
      otherpay_amt,
      branch,
    } = req.body;

    const pool = await getPoolByBranch(branch);

    if (!pool) {
      return res.status(400).json({
        success: false,
        message: "無效的門市代號",
      });
    }

    bcode = branch.substring(0, 2); // 強制取前2碼

    const updateQuery = `
      UPDATE ticketrd 
      SET 
        mname = @mname,
        bcode = @bcode,
        mcode = @mcode,
        eprmcode = @eprmcode,
        otherpay_code = @otherpay_code,
        otherpay_paykind = @otherpay_paykind,
        otherpay_amt = @otherpay_amt
      WHERE productid = @productid
    `;

    const updateRequest = pool.request();
    updateRequest.input("mname", sql.NVarChar, mname);
    updateRequest.input("bcode", sql.VarChar, bcode);
    updateRequest.input("mcode", sql.VarChar, mcode || null);
    updateRequest.input("eprmcode", sql.VarChar, eprmcode || null);
    updateRequest.input("otherpay_code", sql.VarChar, otherpay_code || null);
    updateRequest.input(
      "otherpay_paykind",
      sql.VarChar,
      otherpay_paykind || null
    );
    updateRequest.input(
      "otherpay_amt",
      sql.Decimal(10, 2),
      otherpay_amt || null
    );
    updateRequest.input("productid", sql.VarChar, productid);

    const result = await updateRequest.query(updateQuery);

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({
        success: false,
        message: "票券不存在",
      });
    }

    res.json({
      success: true,
      message: "票券更新成功",
    });
  } catch (error) {
    console.error("更新票券失敗:", error);
    res.status(500).json({
      success: false,
      message: "更新票券失敗",
    });
  }
};

// 刪除票券
const deleteTicket = async (req, res) => {
  try {
    const { productid } = req.params;
    const { branch } = req.query;

    const pool = await getPoolByBranch(branch);

    if (!pool) {
      return res.status(400).json({
        success: false,
        message: "無效的門市代號",
      });
    }

    const deleteQuery = "DELETE FROM ticketrd WHERE productid = @productid";
    const deleteRequest = pool.request();
    deleteRequest.input("productid", sql.VarChar, productid);
    const result = await deleteRequest.query(deleteQuery);

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({
        success: false,
        message: "票券不存在",
      });
    }

    res.json({
      success: true,
      message: "票券刪除成功",
    });
  } catch (error) {
    console.error("刪除票券失敗:", error);
    res.status(500).json({
      success: false,
      message: "刪除票券失敗",
    });
  }
};

module.exports = {
  getTicketList,
  createTicket,
  updateTicket,
  deleteTicket,
};
