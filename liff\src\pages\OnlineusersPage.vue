<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">線上使用者</div>
            <div class="row items-center q-gutter-sm">
              <q-badge
                :color="isOnline ? 'positive' : 'grey'"
                :label="isOnline ? '已連接' : '離線'"
                class="q-mr-sm"
              />
              <q-btn
                flat
                round
                size="sm"
                color="primary"
                class="refresh-button"
                icon="refresh"
                :loading="loading"
                @click="fetchOnlineUsers"
              >
                <q-tooltip>重新整理</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>

        <!-- 統計卡片區域 -->
        <q-card-section class="q-py-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部使用者統計卡片 -->
              <div class="col-6 col-sm-6 col-md-4">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': !filterActive }"
                  @click="filterActive = false"
                >
                  <div class="stats-icon">
                    <Users :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ users.length }}</div>
                    <div class="stats-label">全部使用者</div>
                  </div>
                </div>
              </div>

              <!-- 活躍使用者統計卡片 -->
              <div class="col-6 col-sm-6 col-md-4">
                <div
                  class="stats-card active-users-card"
                  :class="{ 'active-filter': filterActive }"
                  @click="filterActive = true"
                >
                  <div class="stats-icon">
                    <UserCheck :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ activeUsersCount }}</div>
                    <div class="stats-label">活躍使用者</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 使用者列表卡片 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <q-card-section class="q-pa-xs">
        <!-- 加載中狀態 -->
        <div v-if="loading && !users.length" class="loader-container">
          <q-spinner size="40px" color="grey-5" />
          <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
            載入使用者中...
          </div>
        </div>

        <!-- 錯誤狀態 -->
        <div v-else-if="error" class="empty-state-container">
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <AlertCircle
              :size="50"
              :stroke-width="1"
              style="width: 60px; height: 50px; margin-bottom: 12px"
              class="text-negative"
            />
            <div class="empty-text text-negative">{{ error }}</div>
            <q-btn
              color="primary"
              label="重試"
              @click="fetchOnlineUsers"
              class="q-mt-md"
              unelevated
            />
          </div>
        </div>

        <!-- 無資料狀態 -->
        <div
          v-else-if="filteredUsers.length === 0"
          class="empty-state-container"
        >
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <Users
              :size="50"
              :stroke-width="1"
              style="width: 60px; height: 50px; margin-bottom: 12px"
            />
            <div class="empty-text text-grey-5">目前沒有線上使用者</div>
            <div class="text-grey-6 q-mt-sm">
              當有使用者登入時，將會顯示在此處
            </div>
          </div>
        </div>

        <!-- 使用者列表 -->
        <div v-else class="user-list-container">
          <q-list separator class="user-list">
            <q-item
              v-for="user in filteredUsers"
              :key="user.UID"
              class="bg-white q-mb-xs user-item"
              :class="{ 'active-user': isUserActive(user.LastActiveTime) }"
            >
              <q-item-section avatar>
                <q-avatar
                  :color="
                    isUserActive(user.LastActiveTime) ? 'primary' : 'grey-5'
                  "
                  text-color="white"
                >
                  {{ user.Name ? user.Name.charAt(0) : "U" }}
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <!-- 使用者名稱 -->
                <div class="text-weight-medium user-name">
                  {{ user.Name || "未知使用者" }}
                </div>
                <!-- 最後活動時間 -->
                <div class="text-caption text-grey-8 user-meta">
                  <div class="flex items-center q-gutter-xs">
                    <Clock :stroke-width="1.5" :size="14" />
                    <span>最後操作：{{ formatTime(user.LastActiveTime) }}</span>
                  </div>
                </div>
              </q-item-section>

              <!-- 狀態顯示 -->
              <q-item-section side>
                <q-badge
                  outline
                  :color="
                    isUserActive(user.LastActiveTime) ? 'positive' : 'grey'
                  "
                  :label="isUserActive(user.LastActiveTime) ? '活躍' : '閒置'"
                  class="q-px-sm q-py-xs"
                />
              </q-item-section>
            </q-item>
          </q-list>

          <!-- 統計資訊 -->
          <div class="row justify-between items-center q-px-sm q-py-md">
            <div class="text-caption text-grey">
              共 {{ filteredUsers.length }} 位{{
                filterActive ? "活躍" : ""
              }}使用者
            </div>
            <div class="text-caption text-grey">
              最後更新：{{ lastUpdateTime }}
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import apiClient from "../api";
import { DateTime } from "luxon";
import { Users, UserCheck, Clock, AlertCircle } from "lucide-vue-next";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const users = ref([]);
const loading = ref(false);
const error = ref(null);
const lastUpdateTime = ref("");
const refreshInterval = ref(null);
const isOnline = ref(true);
const filterActive = ref(false);

// 計算屬性：檢查是否有活躍使用者
const activeUsersCount = computed(() => {
  return users.value.filter((user) => isUserActive(user.LastActiveTime)).length;
});

// 篩選使用者列表
const filteredUsers = computed(() => {
  if (filterActive.value) {
    return users.value.filter((user) => isUserActive(user.LastActiveTime));
  }
  return users.value;
});

// 檢查使用者是否活躍（15分鐘內有活動）
const isUserActive = (lastActiveTime) => {
  if (!lastActiveTime) return false;
  const lastActive = DateTime.fromISO(lastActiveTime);
  const now = DateTime.now();
  const diffInMinutes = now.diff(lastActive, "minutes").minutes;
  return diffInMinutes <= 15;
};

// 格式化時間顯示
const formatTime = (dt) => {
  if (!dt) return "未知";

  const dateTime = DateTime.fromISO(dt);
  const now = DateTime.now();
  const diffInMinutes = now.diff(dateTime, "minutes").minutes;

  // 如果是今天，顯示相對時間
  if (dateTime.hasSame(now, "day")) {
    if (diffInMinutes < 1) return "剛剛";
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)}分鐘前`;
    const diffInHours = now.diff(dateTime, "hours").hours;
    if (diffInHours < 24) return `${Math.floor(diffInHours)}小時前`;
  }

  // 否則顯示完整日期時間
  return dateTime.toFormat("MM/dd HH:mm");
};

// 更新最後更新時間
const updateLastUpdateTime = () => {
  lastUpdateTime.value = DateTime.now().toFormat("HH:mm:ss");
};

// 取得線上使用者
const fetchOnlineUsers = async () => {
  if (loading.value) return;

  loading.value = true;
  error.value = null;

  try {
    const res = await apiClient.get(`${apiBaseUrl}/auth/get-OnlineUsers`);
    users.value = res.data || [];
    updateLastUpdateTime();
    isOnline.value = true;
  } catch (err) {
    console.error("❌ 取得線上使用者失敗:", err);
    error.value = "無法取得線上使用者資料，請檢查網路連線";
    isOnline.value = false;
  } finally {
    loading.value = false;
  }
};

// 設定自動刷新
const setupAutoRefresh = () => {
  // 每30秒自動刷新一次
  refreshInterval.value = setInterval(() => {
    if (!loading.value) {
      fetchOnlineUsers();
    }
  }, 30000);
};

// 清理定時器
const cleanupInterval = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
};

onMounted(() => {
  fetchOnlineUsers();
  setupAutoRefresh();
});

onUnmounted(() => {
  cleanupInterval();
});
</script>

<style scoped>
/* 基本樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.refresh-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.all-card::before {
  background-color: #1976d2;
}

.all-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.active-users-card::before {
  background-color: #4caf50;
}

.active-users-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 使用者列表樣式 */
.user-list-container {
  position: relative;
}

.user-list {
  margin: 0;
  padding: 0;
}

.user-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.user-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.active-user {
  border-left: 4px solid #4caf50;
}

.user-name {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  padding: 0 2px;
}

.user-meta {
  font-size: 11px;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

/* 空狀態 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  font-weight: 500;
}

/* 響應式調整 */
@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .stats-card {
    padding: 8px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 16px;
  }

  .stats-label {
    font-size: 10px;
  }
}
</style>
