const express = require("express");
const {
  getDepartments,
  addDepartment,
  updateDepartment,
  deleteDepartment,
} = require("../controllers/depsController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_departments", authMiddleware, getDepartments);
router.post("/add_department", authMiddleware, addDepartment);
router.put("/update_department/:id", authMiddleware, updateDepartment);
router.delete("/delete_department/:id", authMiddleware, deleteDepartment);

module.exports = router;
