const printerLocks = {};

function getPrinterKey(ip, port = 9100) {
  return `${ip}:${port}`;
}

async function withPrinterLock(ip, port = 9100, taskFn) {
  const key = getPrinterKey(ip, port);
  const timeout = 5000;
  const start = Date.now();

  while (printerLocks[key]) {
    if (Date.now() - start > timeout) {
      throw new Error(`印表機 ${key} 等待鎖逾時`);
    }
    await new Promise((res) => setTimeout(res, 30)); // 排隊等待鎖
  }

  printerLocks[key] = true;

  try {
    return await taskFn(); // ✅ 執行送單任務
  } finally {
    printerLocks[key] = false; // 釋放鎖
  }
}

module.exports = { withPrinterLock };
