<template>
  <router-view />
  <StagewiseToolbar v-if="isDevelopment" :config="stagewiseConfig" />
</template>

<script setup>
import { ref } from "vue";
import { StagewiseToolbar } from "@stagewise/toolbar-vue";
import VuePlugin from "@stagewise-plugins/vue";

defineOptions({
  name: "App",
});

const isDevelopment = ref(process.env.NODE_ENV === "development");
const stagewiseConfig = ref({
  plugins: [VuePlugin],
});
</script>
