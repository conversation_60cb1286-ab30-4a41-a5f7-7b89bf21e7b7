<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題卡片 -->
    <q-card class="no-shadow" style="border-radius: 12px; overflow: hidden">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">出勤紀錄</div>
          <div class="row items-center">
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              @click="showDetailDialog = true"
              class="q-mr-xs"
            >
              <info :stroke-width="1" :size="20" />
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_left"
              @click="adjustMonth(-1)"
              color="primary"
            />
            <q-input
              v-model="selectedMonth"
              readonly
              dense
              outlined
              class="month-selector"
              input-class="text-center"
            >
            </q-input>
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_right"
              @click="adjustMonth(1)"
              color="primary"
              :disable="!canGoToNextMonth"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 主內容 -->
    <q-card
      class="no-shadow q-mt-sm"
      style="border-radius: 12px; overflow: hidden"
    >
      <!-- 載入中 -->
      <div v-if="loading" class="q-pa-xl">
        <div class="fancy-loader">
          <svg class="loader-svg" viewBox="0 0 100 100">
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">出勤資料載入中...</div>
        </div>
      </div>
      <!-- 查無資料 -->
      <q-card-section
        v-else-if="!attendanceRecords || attendanceRecords.length === 0"
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無出勤資料</div>
          <div class="empty-subtext">請選擇其他年月或聯絡人資</div>
        </div>
      </q-card-section>
      <!-- 出勤內容 -->
      <div v-else>
        <!-- 基本資訊卡片 -->
        <q-card-section class="q-pt-md q-pb-sm">
          <div class="row q-col-gutter-sm info-cards-container">
            <div class="col-6">
              <div class="stats-card person-card">
                <div class="stats-icon">
                  <user-round :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">姓名</div>
                  <div class="person-value">
                    {{ userData ? userData.name : "載入中..." }}
                  </div>
                </div>
              </div>
            </div>

            <div class="col-6">
              <div class="stats-card warning-card" v-if="missedPunchCount >= 2">
                <div class="stats-icon">
                  <alert-triangle :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">未打卡次數</div>
                  <div class="warning-value">{{ missedPunchCount }} 次</div>
                </div>
              </div>
              <div class="stats-card normal-card" v-else>
                <div class="stats-icon">
                  <check-circle :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">未打卡次數</div>
                  <div class="normal-value">{{ missedPunchCount }} 次</div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <q-separator inset />

        <!-- 出勤紀錄表格 -->
        <q-card-section>
          <div class="text-primary q-mb-md flex items-center">
            <calendar-days :stroke-width="1" :size="20" class="q-mr-sm" />
            出勤紀錄
          </div>

          <!-- 出勤紀錄卡片列表 -->
          <div class="mobile-attendance-list q-mt-md">
            <div
              v-for="record in attendanceRecords"
              :key="record.att_date"
              class="q-mb-md"
            >
              <q-card class="attendance-card">
                <q-card-section class="q-py-sm attendance-header">
                  <div class="row items-center justify-between">
                    <div class="attendance-date">
                      {{ formatDate(record.att_date) }}
                    </div>
                    <q-badge
                      :color="record.late_time > 0 ? 'negative' : 'positive'"
                      class="attendance-badge"
                    >
                      {{
                        record.late_time > 0
                          ? `遲到 ${Math.round(record.late_time / 60)} 分鐘`
                          : "準時"
                      }}
                    </q-badge>
                  </div>
                </q-card-section>

                <q-separator />

                <q-card-section class="q-py-sm">
                  <div class="row q-col-gutter-sm">
                    <!-- 上午 -->
                    <div class="col-6">
                      <div class="text-caption text-grey-7 q-mb-xs">上午</div>
                      <div class="row q-col-gutter-xs">
                        <div class="col-6">
                          <div class="punch-label">上班</div>
                          <div
                            class="punch-time"
                            :class="{
                              'text-negative': record.first_in_f === 1,
                            }"
                          >
                            {{ formatTime(record.first_in) }}
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="punch-label">下班</div>
                          <div
                            class="punch-time"
                            :class="{
                              'text-negative': record.first_out_f === 1,
                            }"
                          >
                            {{ formatTime(record.first_out) }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 下午 -->
                    <div class="col-6">
                      <div class="text-caption text-grey-7 q-mb-xs">下午</div>
                      <div class="row q-col-gutter-xs">
                        <div class="col-6">
                          <div class="punch-label">上班</div>
                          <div
                            class="punch-time"
                            :class="{
                              'text-negative': record.second_in_f === 1,
                            }"
                          >
                            {{ formatTime(record.second_in) }}
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="punch-label">下班</div>
                          <div
                            class="punch-time"
                            :class="{
                              'text-negative': record.second_out_f === 1,
                            }"
                          >
                            {{ formatTime(record.second_out) }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </div>
    </q-card>

    <!-- 詳細說明對話框 -->
    <q-dialog v-model="showDetailDialog">
      <q-card style="min-width: 350px; max-width: 700px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">出勤紀錄說明</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-separator />
        <q-card-section>
          <p>出勤紀錄顯示您當月的上下班打卡時間及遲到紀錄。</p>
          <p>
            如有未打卡紀錄，將以紅色標示。一個月內若有2次以上未打卡，將會特別提示。
          </p>
          <p>如有任何疑問，請聯絡人資部門。</p>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="關閉" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { DateTime } from "luxon";
import apiClient from "../api";
import { useQuasar } from "quasar";

const props = defineProps({ Userid: String });
const $q = useQuasar();

const loading = ref(false);
const attendanceRecords = ref([]);
const userData = ref(null);
const selectedMonth = ref(
  DateTime.now().setZone("Asia/Taipei").toFormat("yyyy-MM")
);
const showDetailDialog = ref(false);

// 表格列定義
const columns = [
  {
    name: "att_date",
    align: "left",
    label: "日期",
    field: "att_date",
    sortable: true,
  },
  {
    name: "first_in",
    align: "center",
    label: "上午上班",
    field: "first_in",
    sortable: true,
  },
  {
    name: "first_out",
    align: "center",
    label: "上午下班",
    field: "first_out",
    sortable: true,
  },
  {
    name: "second_in",
    align: "center",
    label: "下午上班",
    field: "second_in",
    sortable: true,
  },
  {
    name: "second_out",
    align: "center",
    label: "下午下班",
    field: "second_out",
    sortable: true,
  },
  {
    name: "late_time",
    align: "center",
    label: "遲到時間",
    field: "late_time",
    sortable: true,
  },
];

// 格式化日期 - 使用台灣時區
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  try {
    // 處理不同的日期格式
    let date;
    if (typeof dateStr === "string") {
      // 如果是字串格式，嘗試解析
      if (dateStr.includes("T")) {
        // ISO 格式
        date = DateTime.fromISO(dateStr);
      } else {
        // MySQL date 格式
        date = DateTime.fromSQL(dateStr);
      }
    } else if (dateStr instanceof Date) {
      // 如果是 Date 物件
      date = DateTime.fromJSDate(dateStr);
    } else {
      return dateStr;
    }

    if (!date.isValid) {
      return dateStr;
    }

    // 轉換為台灣時區並格式化
    return date
      .setZone("Asia/Taipei")
      .toFormat("yyyy/MM/dd (ccc)", { locale: "zh-TW" });
  } catch (e) {
    console.error("日期格式化錯誤:", e, "原始值:", dateStr);
    return dateStr;
  }
};

// 格式化時間 - 使用台灣時區24小時制
const formatTime = (timeStr) => {
  if (!timeStr) return "-";
  try {
    // 處理不同的時間格式
    let date;
    if (typeof timeStr === "number") {
      // 如果是數字，可能是 Unix timestamp
      date = DateTime.fromSeconds(timeStr);
    } else if (typeof timeStr === "string") {
      // 如果是字串格式，嘗試解析
      if (timeStr.includes("T")) {
        // ISO 格式
        date = DateTime.fromISO(timeStr);
      } else if (timeStr.includes("-") && timeStr.includes(":")) {
        // MySQL datetime 格式 (YYYY-MM-DD HH:mm:ss)
        date = DateTime.fromSQL(timeStr);
      } else {
        // 其他字串格式，嘗試直接解析
        date = DateTime.fromISO(timeStr);
      }
    } else if (timeStr instanceof Date) {
      // 如果是 Date 物件
      date = DateTime.fromJSDate(timeStr);
    } else {
      console.log("未知的時間格式:", timeStr, "類型:", typeof timeStr);
      return "-";
    }

    if (!date.isValid) {
      console.log("無效的時間格式:", timeStr);
      return "-";
    }

    // 轉換為台灣時區並格式化為24小時制
    return date.setZone("Asia/Taipei").toFormat("HH:mm");
  } catch (e) {
    console.error("時間格式化錯誤:", e, "原始值:", timeStr);
    return "-";
  }
};

// 獲取用戶資訊
const fetchUserData = async () => {
  try {
    const { data } = await apiClient.get(`/attendance/user-info`, {
      params: {
        sn: props.Userid,
      },
    });
    userData.value = data;
  } catch (e) {
    console.error("獲取用戶資料失敗:", e);
    $q.notify({
      type: "negative",
      message: "獲取用戶資料失敗",
      position: "top",
      timeout: 3000,
    });
  }
};

// 獲取出勤紀錄
const fetchAttendanceRecords = async () => {
  loading.value = true;
  try {
    // 確保使用有效的日期格式
    let yearMonth = selectedMonth.value;
    if (yearMonth) {
      yearMonth = yearMonth.trim();
    }

    if (
      !yearMonth ||
      yearMonth === "Invalid DateTime" ||
      !DateTime.fromFormat(yearMonth, "yyyy-MM").isValid
    ) {
      yearMonth = DateTime.now().setZone("Asia/Taipei").toFormat("yyyy-MM");
      selectedMonth.value = yearMonth; // 更新為有效的日期
    }

    const { data } = await apiClient.get(`/attendance/records`, {
      params: {
        sn: props.Userid,
        year_month: yearMonth,
      },
    });

    attendanceRecords.value = data;
  } catch (e) {
    console.error("獲取出勤紀錄失敗:", e);
    attendanceRecords.value = [];
    $q.notify({
      type: "negative",
      message: "獲取出勤資料失敗",
      position: "top",
      timeout: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 調整月份
const adjustMonth = (offset) => {
  try {
    // 確保 selectedMonth 是有效的日期格式
    let currentDate;
    let monthValue = selectedMonth.value;

    if (monthValue) {
      monthValue = monthValue.trim();
    }

    if (!monthValue || monthValue === "Invalid DateTime") {
      currentDate = DateTime.now().setZone("Asia/Taipei");
    } else {
      currentDate = DateTime.fromFormat(monthValue, "yyyy-MM").setZone(
        "Asia/Taipei"
      );
      if (!currentDate.isValid) {
        currentDate = DateTime.now().setZone("Asia/Taipei");
      }
    }

    const newMonth = currentDate.plus({ months: offset }).toFormat("yyyy-MM");

    // 檢查是否超過當前月份
    const now = DateTime.now().setZone("Asia/Taipei");
    const targetMonth = DateTime.fromFormat(newMonth, "yyyy-MM").setZone(
      "Asia/Taipei"
    );

    if (targetMonth > now) {
      return; // 不允許超過當前月份
    }

    selectedMonth.value = newMonth;
  } catch (error) {
    console.error("日期調整錯誤:", error);
    selectedMonth.value = DateTime.now()
      .setZone("Asia/Taipei")
      .toFormat("yyyy-MM");
  }
};

// 檢查是否可以切換到下個月
const canGoToNextMonth = computed(() => {
  const currentMonth = DateTime.fromFormat(
    selectedMonth.value,
    "yyyy-MM"
  ).setZone("Asia/Taipei");
  const now = DateTime.now().setZone("Asia/Taipei");
  return currentMonth < now;
});

// 計算未打卡次數
const missedPunchCount = computed(() => {
  if (!attendanceRecords.value || attendanceRecords.value.length === 0)
    return 0;

  return attendanceRecords.value.reduce((count, record) => {
    return (
      count +
      (record.first_in_f === 1 ? 1 : 0) +
      (record.first_out_f === 1 ? 1 : 0) +
      (record.second_in_f === 1 ? 1 : 0) +
      (record.second_out_f === 1 ? 1 : 0)
    );
  }, 0);
});

onMounted(async () => {
  await fetchUserData();
  await fetchAttendanceRecords();
});

watch(selectedMonth, fetchAttendanceRecords);
</script>

<style>
.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

.month-selector {
  min-width: 110px;
}

@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .month-selector {
    min-width: 90px;
    width: 90px !important;
  }
}

/* 載入動畫樣式 */
.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}

.loader-message {
  margin-top: 1rem;
  color: #1976d2;
  font-size: 0.9rem;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #757575;
  margin-bottom: 0.5rem;
}

.empty-subtext {
  font-size: 0.9rem;
  color: #9e9e9e;
}

/* 基本資訊卡片樣式優化 */
.info-cards-container {
  margin: 0 -6px;
}

@media (max-width: 599px) {
  .info-cards-container {
    margin: 0 -2px;
  }

  .info-cards-container > div {
    padding: 0 2px;
  }

  /* 響應式調整 */
  .stats-card {
    padding: 8px 6px 8px 20px;
  }

  .stats-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  .stats-label {
    font-size: 10px;
  }
}

/* 統計卡片樣式 */
.stats-card {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eaeaea;
  transition: all 0.3s ease;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 6px;
}

.stats-content {
  flex: 1;
  min-width: 0; /* 確保文字可以正確縮減 */
}

.stats-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.person-value {
  color: #8e24aa;
  font-weight: 600;
}

.warning-value {
  color: #f44336;
  font-weight: 600;
}

.normal-value {
  color: #4caf50;
  font-weight: 600;
}

/* 卡片特定樣式 */
.person-card::before {
  background: linear-gradient(180deg, #ce93d8 0%, #8e24aa 100%);
}

.person-card .stats-icon {
  color: #8e24aa;
  background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
}

.month-card::before {
  background: linear-gradient(180deg, #ffcc80 0%, #ff8f00 100%);
}

.month-card .stats-icon {
  color: #ff8f00;
  background: linear-gradient(135deg, #fff8e1 0%, #ffe082 100%);
}

.warning-card::before {
  background: linear-gradient(180deg, #ef9a9a 0%, #f44336 100%);
}

.warning-card .stats-icon {
  color: #f44336;
  background: linear-gradient(135deg, #ffebee 0%, #ef9a9a 100%);
}

.normal-card::before {
  background: linear-gradient(180deg, #a5d6a7 0%, #388e3c 100%);
}

.normal-card .stats-icon {
  color: #388e3c;
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}

/* 表格樣式 */
.attendance-table {
  border-radius: 8px;
  overflow: hidden;
}

.attendance-table .q-table__top,
.attendance-table .q-table__bottom,
.attendance-table thead tr:first-child th {
  background-color: #f5f7fa;
}

.attendance-table thead tr th {
  font-weight: 600;
  color: #1976d2;
}

/* 出勤卡片樣式 */
.attendance-card {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.attendance-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #64b5f6, #1976d2);
  opacity: 0.8;
}

.attendance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.attendance-card:active {
  transform: translateY(-1px);
}

.punch-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
  font-weight: 500;
}

.punch-time {
  font-size: 16px;
  font-weight: 600;
}

/* 卡片標題樣式 */
.attendance-header {
  padding-bottom: 8px !important;
}

.attendance-date {
  font-size: 15px;
  font-weight: 600;
  color: #1976d2;
}

.attendance-badge {
  font-size: 11px;
  padding: 4px 8px;
  font-weight: 500;
}

@media (max-width: 599px) {
  .attendance-card {
    padding: 8px;
  }

  .attendance-date {
    font-size: 14px;
  }

  .attendance-badge {
    font-size: 10px;
    padding: 2px 6px;
  }

  .punch-time {
    font-size: 14px;
  }
}
</style>
