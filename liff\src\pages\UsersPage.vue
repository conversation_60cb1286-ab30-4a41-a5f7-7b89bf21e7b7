<template>
  <q-page
    class="q-pa-sm flex justify-center"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">使用者管理</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="green"
              @click="openUserDialog"
              :loading="isLoadingUsers"
            >
              <users :stroke-width="1" :size="26" />
              <q-tooltip>選擇使用者</q-tooltip>
            </q-btn>
            <!-- 可以在這裡添加新增使用者按鈕，如果需要的話 -->
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- 選擇使用者提示 -->
      <q-card-section v-if="!selectedUser.Uid" class="q-pt-none">
        <q-banner class="bg-blue-1 text-grey-8" rounded>
          <template v-slot:avatar>
            <Info class="text-primary" :stroke-width="1" :size="30" />
          </template>
          請點擊右上角按鈕選擇要管理的使用者
        </q-banner>
      </q-card-section>
    </q-card>

    <!-- 主內容區域 - 使用者設定表單 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
      v-if="selectedUser.Uid"
    >
      <!-- 基本資訊設定區塊 -->
      <q-card-section>
        <div class="text-primary q-mb-md flex items-center">
          <shieldUser :stroke-width="1.5" :size="24" class="q-mr-xs" />
          使用者基本資訊
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6">
            <q-input
              v-model="selectedUser.Uid"
              label="LINE ID (UID)"
              readonly
              outlined
              dense
            >
              <template v-slot:prepend>
                <hash :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-input
              v-model="selectedUser.Name"
              label="姓名"
              outlined
              dense
              :rules="[(val) => !!val || '姓名為必填項目']"
            >
              <template v-slot:prepend>
                <user :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-select
              emit-value
              map-options
              v-model="selectedUser.Sts"
              :options="STATUS_OPTIONS"
              label="狀態"
              outlined
              dense
            >
              <template v-slot:prepend>
                <activity :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>

          <div class="col-12 col-sm-6">
            <q-input
              v-model="selectedUser.ID"
              label="工號"
              outlined
              dense
              :rules="[(val) => !!val || '工號為必填項目']"
            >
              <template v-slot:prepend>
                <idCardLanyard :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <!-- 部門代號移到這裡 -->
          <div class="col-12 col-sm-6">
            <q-select
              emit-value
              map-options
              v-model="selectedUser.Dep"
              :options="departmentOptions"
              label="部門代號"
              outlined
              dense
            >
              <template v-slot:prepend>
                <briefcase :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 帳號設定區塊 -->
        <div class="text-primary q-mb-md flex items-center">
          <lock :stroke-width="1.5" :size="24" class="q-mr-xs" />
          帳號設定
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12">
            <div class="row items-center no-wrap">
              <!-- 使用者帳號輸入框 -->
              <q-input
                v-model="selectedUser.Username"
                label="使用者帳號"
                outlined
                dense
                class="col"
              >
                <template v-slot:prepend>
                  <atSign :stroke-width="1" :size="18" />
                </template>
              </q-input>
              <!-- 恢復預設密碼按鈕 -->
              <q-btn
                unelevated
                color="negative"
                @click="openResetPasswordDialog"
                :loading="isResettingPassword"
                class="q-ml-sm"
              >
                <template v-slot:default>
                  <keyRound :stroke-width="1.5" :size="20" class="q-mr-xs" />
                  恢復密碼
                </template>
              </q-btn>
            </div>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 聯絡資訊設定區塊 -->
        <div class="text-primary q-mb-md flex items-center">
          <mail :stroke-width="1.5" :size="24" class="q-mr-xs" />
          聯絡資訊
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12">
            <q-input
              v-model="selectedUser.Email"
              label="E-mail"
              outlined
              dense
              type="email"
            >
              <template v-slot:prepend>
                <mail :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 權限設定區塊 - 直接顯示 -->
        <div class="q-px-none">
          <div class="text-primary q-mb-md flex items-center">
            <shield :stroke-width="1.5" :size="18" />
            <span class="q-ml-xs">權限設定</span>
          </div>

          <!-- 權限設定內容 -->
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6">
              <!-- 群組選擇區塊 -->
              <div class="row items-center q-mb-md">
                <q-select
                  v-model="newUsergroup"
                  :options="filteredGroupOptions"
                  label="選擇權限群組"
                  outlined
                  dense
                  class="col"
                  input-debounce="300"
                  option-label="label"
                  option-value="value"
                >
                  <template v-slot:prepend>
                    <shield-check :stroke-width="1" :size="18" />
                  </template>
                  <template v-slot:option="scope">
                    <q-item v-bind="scope.itemProps">
                      <q-item-section avatar>
                        <q-avatar color="primary" text-color="white">
                          {{ scope.opt.label.charAt(0) }}
                        </q-avatar>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.label }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  <template v-slot:no-option>
                    <q-item>
                      <q-item-section class="text-grey">
                        無符合的權限群組
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
                <q-btn
                  flat
                  round
                  class="q-ml-sm"
                  @click="handleAddUsergroup"
                  :disable="!newUsergroup"
                >
                  <plus class="text-primary" size="24" :stroke-width="1.5" />
                </q-btn>
              </div>

              <!-- 授權群組提示 -->
              <div
                v-if="userAccessGroups.length === 0"
                class="text-center q-pa-lg bg-grey-2 rounded-borders q-mt-md"
              >
                <info size="28" class="text-grey q-mb-sm" />
                <div class="text-grey">
                  尚未設定授權群組，請選擇群組並點擊新增按鈕
                </div>
              </div>

              <!-- 授權群組列表 -->
              <div v-else-if="userAccessGroups.length > 0" class="q-mt-md">
                <div class="row q-col-gutter-sm">
                  <div
                    v-for="group in groupDisplayData"
                    :key="group.value"
                    class="col-12"
                  >
                    <q-card class="group-card">
                      <q-card-section class="text-center q-py-sm">
                        <div
                          class="text-subtitle2 ellipsis row items-center justify-center"
                          style="position: relative"
                        >
                          <users
                            class="text-primary"
                            :size="20"
                            style="position: absolute; left: 2px"
                          />
                          <span
                            class="full-width text-center"
                            style="padding-left: 18px; padding-right: 18px"
                          >
                            {{ group.label || "未命名群組" }}
                          </span>
                        </div>

                        <q-btn
                          flat
                          round
                          icon="close"
                          color="negative"
                          size="sm"
                          class="absolute-top-right q-mt-xs q-mr-xs"
                          @click="handleRemoveUsergroup(group.value)"
                        />
                      </q-card-section>
                    </q-card>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-12 col-sm-6">
              <!-- 門市選擇區塊 -->
              <template
                v-if="
                  !userAccessGroups.some(
                    (g) =>
                      (typeof g === 'object' ? g.value : g).toLowerCase() ===
                      'admin'
                  )
                "
              >
                <div class="row items-center q-mb-md">
                  <q-select
                    v-model="newBranch"
                    :options="filteredBranchOptions"
                    label="選擇門市"
                    outlined
                    dense
                    class="col"
                    input-debounce="300"
                  >
                    <template v-slot:prepend>
                      <store :stroke-width="1" :size="18" />
                    </template>
                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section avatar>
                          <q-avatar color="primary" text-color="white">
                            {{ scope.opt.label.charAt(0) }}
                          </q-avatar>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ scope.opt.label }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>
                    <template v-slot:no-option>
                      <q-item>
                        <q-item-section class="text-grey">
                          無符合的門市
                        </q-item-section>
                      </q-item>
                    </template>
                  </q-select>
                  <q-btn
                    flat
                    round
                    class="q-ml-sm"
                    @click="handleAddBranch"
                    :disable="!newBranch"
                  >
                    <plus class="text-primary" size="24" :stroke-width="1.5" />
                  </q-btn>
                </div>

                <!-- 授權門市提示 -->
                <div
                  v-if="authorizedBranches.length === 0"
                  class="text-center q-pa-lg bg-grey-2 rounded-borders q-mt-md"
                >
                  <info size="28" class="text-grey q-mb-sm" />
                  <div class="text-grey">
                    尚未設定授權門市，請選擇門市並點擊新增按鈕
                  </div>
                </div>

                <!-- 授權門市列表 -->
                <div v-else-if="authorizedBranches.length > 0" class="q-mt-md">
                  <div class="row q-col-gutter-sm">
                    <div
                      v-for="branch in branchDisplayData"
                      :key="branch.value"
                      class="col-12"
                    >
                      <q-card class="branch-card">
                        <q-card-section class="text-center q-py-sm">
                          <div
                            class="text-subtitle2 ellipsis row items-center justify-center"
                            style="position: relative"
                          >
                            <store
                              class="text-primary"
                              :size="18"
                              style="position: absolute; left: 2px"
                            />
                            <span
                              class="full-width text-center"
                              style="padding-left: 18px; padding-right: 18px"
                            >
                              {{
                                typeof branch.label === "string"
                                  ? branch.label
                                  : branch.label?.label || branch.value
                              }}
                            </span>
                          </div>

                          <q-btn
                            flat
                            round
                            icon="close"
                            color="negative"
                            size="sm"
                            class="absolute-top-right q-mt-xs q-mr-xs"
                            @click="handleRemoveBranch(branch.value)"
                          />
                        </q-card-section>
                      </q-card>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="q-pa-md bg-grey-2 rounded-borders text-center">
                  <info
                    size="28"
                    class="text-grey q-mb-sm"
                    :stroke-width="1.5"
                  />
                  <div class="text-primary">
                    管理員無需設定授權門市，擁有全部門市權限
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <q-separator class="q-my-md" />
      </q-card-section>

      <!-- 按鈕 -->
      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          flat
          label="重設"
          color="grey"
          class="q-mr-sm"
          @click="handleResetForm"
        />
        <q-btn
          outline
          unelevated
          color="secondary"
          class="q-px-md"
          @click="handleSubmitForm"
          :loading="isSaving"
        >
          <template v-slot:default>
            <save class="q-mr-xs" />
            儲存
          </template>
        </q-btn>
      </q-card-actions>
    </q-card>

    <!-- 🔹 使用者選擇 Dialog -->
    <q-dialog v-model="userDialog" persistent>
      <q-card
        class="page-dialog-card"
        style="width: 100%; max-width: 420px; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="text-center page-dialog-title">
          <users class="w-5 h-5 text-primary q-mr-sm" />
          選擇使用者
        </q-card-section>

        <!-- 搜尋框 -->
        <q-card-section class="q-pt-none q-pb-xs">
          <q-input
            v-model="searchQuery"
            dense
            outlined
            placeholder="輸入姓名或工號"
            clearable
            @clear="resetSearch"
            @keyup.enter="triggerSearch"
            @update:model-value="debouncedSearch"
          >
            <template v-slot:prepend>
              <search class="w-4 h-4 text-grey-7" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 載入狀態 -->
        <q-card-section v-if="isLoadingUsers" class="text-center">
          <q-spinner-dots color="primary" size="40px" />
          <div class="text-grey-8 q-mt-sm">載入使用者中...</div>
        </q-card-section>

        <!-- 使用者列表 -->
        <q-card-section
          class="page-dialog-content"
          style="max-height: 400px; overflow-y: auto"
          v-else
        >
          <q-list bordered separator v-if="paginatedUsers.length">
            <q-item
              v-for="user in paginatedUsers"
              :key="user.Uid"
              clickable
              v-ripple
              @click="handleSelectUser(user)"
            >
              <q-item-section>
                <q-item-label class="text-weight-medium">
                  {{ user.Name }}
                </q-item-label>
                <q-item-label caption class="text-grey-8">
                  工號: {{ user.ID }} | 部門: {{ user.Dep }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-badge
                  class="q-pa-xs"
                  :color="getStatusColor(user.Sts)"
                  outline
                >
                  {{ getStatusLabel(user.Sts) }}
                </q-badge>
              </q-item-section>
            </q-item>
          </q-list>

          <!-- 無結果時 -->
          <div v-else class="text-center text-grey-8 q-pa-md">
            <search class="w-6 h-6 q-mb-sm" />
            <div>沒有符合的使用者</div>
          </div>
        </q-card-section>

        <!-- 分頁 -->
        <q-card-section class="row justify-center" v-if="totalPages > 1">
          <q-pagination
            v-model="currentPage"
            :max="totalPages"
            :max-pages="5"
            boundary-numbers
            color="primary"
          />
        </q-card-section>

        <!-- 按鈕區 -->
        <q-card-actions align="right" class="q-pa-sm page-dialog-actions">
          <q-btn label="取消" color="grey" flat @click="closeUserDialog" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 授權門市 Dialog -->
    <q-dialog v-model="branchDialog" persistent>
      <q-card
        class="page-dialog-card"
        style="width: 400px; max-width: 90vw; border-radius: 10px"
      >
        <q-card-section class="text-center page-dialog-title">
          <store class="w-5 h-5 text-primary q-mr-sm" />
          授權門市設定
        </q-card-section>

        <q-card-section>
          <q-select
            v-model="authorizedBranches"
            :options="branchOptions"
            label="選擇授權門市"
            multiple
            use-chips
            emit-value
            map-options
            outlined
            dense
            option-label="label"
            option-value="value"
            :display-value="displayValue"
            :hide-dropdown-icon="true"
            style="min-height: 56px"
          >
            <template v-slot:prepend>
              <store class="w-4 h-4 text-grey-7" />
            </template>
            <template v-slot:selected-item="scope">
              <q-chip
                removable
                dense
                @remove="scope.removeAtIndex(scope.index)"
                :tabindex="scope.tabindex"
                color="primary"
                text-color="white"
              >
                {{ scope.opt.label }}
              </q-chip>
            </template>
          </q-select>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-sm page-dialog-actions">
          <q-btn flat label="取消" color="grey" @click="closeBranchDialog" />
          <q-btn
            outline
            unelevated
            color="secondary"
            @click="handleSaveAuthorizedBranches"
            :loading="isSavingBranches"
          >
            <template v-slot:default>
              <save class="q-mr-xs" />
              儲存
            </template>
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!--🟢 還原帳號密碼 🟢 -->
    <q-dialog
      v-model="showResetPasswordDialog"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <q-card class="reset-password-dialog">
        <q-card-section class="column items-center q-pt-lg">
          <div class="reset-password-icon-wrapper">
            <keyRound
              :stroke-width="1.5"
              :size="35"
              class="q-mr-xs"
              color="#C10015"
            />
          </div>
          <h6 class="text-h6 text-weight-bold q-mt-md q-mb-sm">
            還原使用者密碼
          </h6>
          <p class="text-body2 text-grey-7 text-center q-ma-none">
            密碼將被重置為預設值。此操作無法撤銷，<br />請確認是否繼續？
          </p>
        </q-card-section>

        <q-card-actions align="center" class="q-pa-md">
          <q-btn
            flat
            label="取消"
            color="grey-7"
            class="reset-dialog-btn"
            v-close-popup
          />
          <q-btn
            unelevated
            class="reset-dialog-btn"
            color="negative"
            @click="handleResetPassword"
            :loading="isResettingPassword"
          >
            <template v-slot:default>
              <span class="row items-center">
                <keyRound :size="18" class="q-mr-xs" />
                確認還原
              </span>
            </template>
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { useQuasar } from "quasar";
import { useUserManagement } from "../composables/useUserManagement";
import { useDepartmentAndGroup } from "../composables/useDepartmentAndGroup";
import { useBranchAuthorization } from "../composables/useBranchAuthorization";
import apiClient from "../api";

// Quasar 實例
const $q = useQuasar();

// 使用 composables
const {
  users: usersList,
  selectedUser,
  filteredUsers,
  currentPage,
  searchQuery,
  isLoadingUsers,
  isSaving,
  isResettingPassword,
  STATUS_OPTIONS,
  ROWS_PER_PAGE,
  totalPages,
  paginatedUsers,
  getStatusLabel,
  getStatusColor,
  debouncedSearch,
  fetchUsers,
  selectUser,
  resetSearch,
  triggerSearch,
  submitForm,
  resetPassword,
  resetForm,
} = useUserManagement();

const { departmentOptions, groupOptions, fetchDepartments, fetchUserGroups } =
  useDepartmentAndGroup();

const {
  branchOptions,
  authorizedBranches,
  isSavingBranches,
  newBranch,
  isLoadingBranches,
  displayValue,
  branchDisplayData,
  filteredBranchOptions,
  fetchBranches,
  fetchUserBranch,
  saveAuthorizedBranches,
  resetAuthorizedBranches,
  addBranch,
  removeBranch,
} = useBranchAuthorization();

// Dialog 狀態
const userDialog = ref(false);
const branchDialog = ref(false);
const showResetPasswordDialog = ref(false);

// 新增權限群組
const newUsergroup = ref(null);
const userAccessGroups = ref([]);
const filteredGroupOptions = ref([]);
const groupDisplayData = ref([]);

// 事件處理函數
const openUserDialog = async () => {
  await fetchUsers();
  userDialog.value = true;
};

const closeUserDialog = () => {
  userDialog.value = false;
  searchQuery.value = "";
  resetSearch();
};

const openBranchDialog = async () => {
  await fetchBranches();
  await fetchUserBranch(selectedUser.value.ID);
  branchDialog.value = true;
};

const closeBranchDialog = () => {
  branchDialog.value = false;
};

const openResetPasswordDialog = () => {
  showResetPasswordDialog.value = true;
};

const closeResetPasswordDialog = () => {
  showResetPasswordDialog.value = false;
};

const handleSelectUser = async (user) => {
  await selectUser(user);

  // 獲取部門和群組信息
  await Promise.all([fetchDepartments(), fetchUserGroups()]);

  // 獲取門市數據
  await fetchBranches();
  await fetchUserBranch(user.ID);

  // 獲取用戶權限群組
  await initUserAccessGroups(user.ID);

  userDialog.value = false;
};

const handleSaveAuthorizedBranches = async () => {
  const success = await saveAuthorizedBranches(selectedUser.value.ID);
  if (success) {
    branchDialog.value = false;
  }
};

const handleResetPassword = async () => {
  await resetPassword();
  showResetPasswordDialog.value = false;
};

// 處理提交表單
const handleSubmitForm = async () => {
  // 調用 useUserManagement 中的 submitForm，
  // 並在成功後處理授權門市和權限群組的保存
  submitForm(async (userId) => {
    // 保存授權門市
    const branchSuccess = await saveAuthorizedBranches(userId);

    // 保存授權群組
    const accessSuccess = await saveUserAccessGroups(userId);

    // 不需要額外的提示，submitForm 已經顯示了成功消息
  });
};

const handleResetForm = () => {
  resetForm();
  resetAuthorizedBranches();
  userAccessGroups.value = [];
  groupDisplayData.value = [];
};

// 處理添加門市
const handleAddBranch = () => {
  if (newBranch.value) {
    addBranch(newBranch.value);
  }
};

// 處理移除門市
const handleRemoveBranch = (branchCode) => {
  $q.dialog({
    title: "確認移除",
    message: "確定要移除此授權門市嗎？",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    removeBranch(branchCode);
  });
};

// 處理添加權限群組
const handleAddUsergroup = () => {
  if (!newUsergroup.value) return;

  // 獲取群組代碼
  const groupCode =
    typeof newUsergroup.value === "object"
      ? newUsergroup.value.value
      : newUsergroup.value;

  // 檢查是否已存在
  if (
    !userAccessGroups.value.some((group) => {
      const code = typeof group === "object" ? group.value : group;
      return code === groupCode;
    })
  ) {
    userAccessGroups.value.push(groupCode);
    newUsergroup.value = null;
    updateGroupDisplayData();
  }
};

// 處理移除權限群組
const handleRemoveUsergroup = (groupCode) => {
  $q.dialog({
    title: "確認移除",
    message: "確定要移除此授權群組嗎？",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    userAccessGroups.value = userAccessGroups.value.filter((group) => {
      const code = typeof group === "object" ? group.value : group;
      return code !== groupCode;
    });
    updateGroupDisplayData();
  });
};

// 更新群組顯示數據
const updateGroupDisplayData = () => {
  if (userAccessGroups.value.length === 0) {
    groupDisplayData.value = [];
    return;
  }

  // 確保所有項目都有一致的對象格式 {value, label}
  groupDisplayData.value = userAccessGroups.value.map((group) => {
    if (typeof group === "object" && group !== null) {
      // 如果已經是對象格式，保持不變
      return {
        value: group.value,
        label: group.label || group.value,
      };
    } else {
      // 如果是純字符串，根據代碼找到對應的群組對象
      const foundGroup = groupOptions.value.find((opt) => opt.value === group);
      return {
        value: group,
        label: foundGroup ? foundGroup.label : group,
      };
    }
  });
};

// 初始化用戶權限群組
const initUserAccessGroups = async (userId) => {
  try {
    const response = await apiClient.get("/users/get_user_access", {
      params: { userId },
    });

    if (response.data.success) {
      // 直接使用後端返回的格式化數據，已包含value和label
      userAccessGroups.value = response.data.access || [];
      updateGroupDisplayData();
    }
  } catch (error) {
    console.error("載入用戶權限失敗:", error);
    $q.notify({ type: "negative", message: "載入授權群組失敗" });
  }
};

// 保存用戶權限群組
const saveUserAccessGroups = async (userId) => {
  try {
    // 提取權限群組的value值
    const accessCodes = userAccessGroups.value.map((group) =>
      typeof group === "object" ? group.value : group
    );

    const payload = {
      userId,
      access: accessCodes,
    };

    const response = await apiClient.post("/users/save_user_access", payload);

    if (response.data.success) {
      // 移除成功提示
      return true;
    } else {
      $q.notify({ type: "negative", message: "儲存授權群組失敗" });
      return false;
    }
  } catch (error) {
    console.error("儲存授權群組失敗:", error);
    $q.notify({ type: "negative", message: "儲存錯誤" });
    return false;
  }
};

// 監聽 groupOptions 變化，更新 filteredGroupOptions
watch(
  [groupOptions, userAccessGroups],
  () => {
    if (groupOptions.value.length === 0) {
      filteredGroupOptions.value = [];
      return;
    }

    const selectedSet = new Set(
      userAccessGroups.value.map((group) =>
        typeof group === "object" ? group.value : group
      )
    );

    filteredGroupOptions.value = groupOptions.value.filter(
      (group) => !selectedSet.has(group.value)
    );
  },
  { immediate: true }
);

// 移除 watch authorizedBranches 的代碼，因為這些功能已經在 useBranchAuthorization composable 中實現

// 生命週期
onMounted(async () => {
  await Promise.all([fetchDepartments(), fetchUserGroups()]);
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.custom-separator {
  height: 0.5px;
  margin-bottom: 18px;
}

.page-dialog-card {
  overflow: hidden;
}

.page-dialog-title {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
}

.page-dialog-content {
  padding-top: 0;
}

.page-dialog-actions {
  background-color: #f5f5f5;
}

/* 修正 q-banner 中 icon 的垂直置中問題 */
.q-banner__avatar {
  align-self: center !important;
}

.reset-password-dialog {
  width: 360px;
  border-radius: 18px;
}

.reset-password-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(193, 0, 21, 0.1);
}

.reset-dialog-btn {
  min-width: 120px;
  border-radius: 8px;
}

.auth-select {
  background: white;
  border-radius: 8px;
}

.auth-select :deep(.q-field__control) {
  height: 40px;
}

.auth-btn {
  height: 40px;
  border-radius: 8px;
  padding: 0 18px;
}

.auth-btn:not(:disabled) {
  background: var(--q-secondary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.auth-btn:not(:disabled):hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.group-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  will-change: transform, box-shadow;
  border-left: 3px solid var(--q-primary);
}

.group-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.branch-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  will-change: transform, box-shadow;
  border-left: 3px solid var(--q-primary);
}

.branch-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
