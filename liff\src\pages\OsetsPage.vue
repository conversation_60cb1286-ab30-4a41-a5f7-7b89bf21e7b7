<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="
        width: 100%;
        max-width: 500px;
        overflow: hidden;
        border-radius: 12px;
      "
    >
      <!-- 🔶 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        套餐群組管理
      </q-card-section>

      <!-- 🧂 群組清單 -->
      <q-list separator>
        <VueDraggable
          v-model="setsGroups"
          item-key="sets_code"
          tag="div"
          handle=".group-drag-handle"
          ghost-class="bg-indigo-1"
          delay="200"
          @end="onGroupSortEnd"
        >
          <div
            v-for="group in setsGroups"
            :key="group.sets_code"
            class="bg-white q-mb-sm"
            style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
          >
            <q-expansion-item
              expand-separator
              header-class="text-indigo"
              class="q-pa-none"
            >
              <template #header>
                <q-item
                  dense
                  class="q-pa-none q-gutter-none items-center"
                  style="width: 100%"
                >
                  <!-- 拖曳手把 -->
                  <q-item-section avatar class="group-drag-handle">
                    <q-icon name="drag_handle" color="grey-7" />
                  </q-item-section>

                  <!-- 群組資訊 -->
                  <q-item-section>
                    <q-item-label>{{ group.sets_title }}</q-item-label>
                    <q-item-label caption>
                      最少選 {{ group.c_min }}、最多選 {{ group.c_max }}
                      {{ group.type === "1" ? "(加點)" : "(套餐)" }}
                      {{ group.mcq === "1" ? "(多選)" : "(單選)" }}
                    </q-item-label>
                  </q-item-section>

                  <!-- ✅ 這個 section 必須存在，Quasar 才會自動把原生展開箭頭放進來 -->
                  <q-item-section side />
                </q-item>
              </template>

              <!-- ✅ 拖曳項目 -->
              <q-card-section class="q-pa-sm q-pt-none">
                <VueDraggable
                  v-model="group.items"
                  item-key="sno"
                  tag="div"
                  handle=".drag-handle"
                  ghost-class="bg-grey-3"
                  delay="200"
                  @end="() => onSetItemSortEnd(group)"
                >
                  <div
                    v-for="(item, index) in group.items"
                    :key="item.sno"
                    class="q-mb-xs"
                  >
                    <q-item
                      class="bg-grey-1 q-pa-sm rounded-borders"
                      clickable
                      v-ripple
                      style="align-items: center"
                    >
                      <!-- 拖曳手把 -->
                      <q-item-section avatar class="drag-handle">
                        <q-icon name="drag_indicator" color="grey-7" />
                      </q-item-section>

                      <!-- 名稱 + 價格 + 規格 -->
                      <q-item-section>
                        <div class="text-subtitle2">{{ item.name }}</div>
                        <div class="text-caption text-grey">
                          <span v-if="item.sname">規格: {{ item.sname }}</span>
                          <span v-if="item.price"
                            >（+{{ item.price.toLocaleString() }} 元）</span
                          >
                          <span>（{{ item.unit || "份" }}）</span>
                        </div>
                      </q-item-section>

                      <!-- 刪除按鈕 -->
                      <q-item-section side>
                        <q-btn
                          flat
                          round
                          dense
                          icon="close"
                          color="negative"
                          @click.stop="deleteItem(group, index)"
                        />
                      </q-item-section>
                    </q-item>
                  </div>
                </VueDraggable>
              </q-card-section>

              <!-- 編輯按鈕 -->
              <q-card-section class="q-pa-sm q-pt-none">
                <div class="row items-center q-gutter-sm justify-between">
                  <!-- 左邊：刪除 + 編輯 -->
                  <div class="row items-center q-gutter-sm">
                    <q-btn
                      icon="delete"
                      label="刪除"
                      dense
                      flat
                      color="negative"
                      @click="openDeleteDialog(group)"
                    />
                    <q-btn
                      icon="edit"
                      label="編輯"
                      dense
                      flat
                      color="blue"
                      @click="openGroupDialog(group)"
                    />
                  </div>

                  <!-- 右邊：新增商品 -->
                  <q-btn
                    icon="add"
                    label="新增商品"
                    dense
                    flat
                    color="primary"
                    @click="openItemDialog(group)"
                  />
                </div>
              </q-card-section>
            </q-expansion-item>
          </div>
        </VueDraggable>
      </q-list>

      <!-- ➕ 新增群組按鈕 -->
      <div class="q-pa-none">
        <q-btn
          label="新增套餐群組"
          icon="add"
          color="green"
          unelevated
          class="full-width"
          @click="openGroupDialog()"
        />
      </div>
    </q-card>
  </q-page>

  <!-- ✏️ 編輯群組 Dialog -->
  <q-dialog v-model="groupDialog.show" persistent>
    <q-card
      class="q-pa-sm bg-white"
      style="width: 100%; max-width: 500px; max-height: 90vh; overflow-y: auto"
    >
      <!-- 標題列 -->
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-primary">編輯套餐群組</div>
        <q-btn
          icon="close"
          flat
          round
          dense
          @click="groupDialog.show = false"
        />
      </q-card-section>

      <q-form @submit.prevent="saveGroup">
        <q-card-section class="q-pt-md q-gutter-md">
          <!-- 群組名稱 -->
          <q-input
            v-model="groupDialog.data.sets_title"
            label="群組名稱"
            dense
            outlined
          />

          <!-- 群組代碼（已移除，改由後端自動生成） -->
        </q-card-section>

        <!-- 最少選 -->
        <q-card-section
          class="row items-center justify-between q-px-md q-mt-none"
        >
          <div>最少選</div>
          <div class="row items-center q-gutter-sm">
            <q-btn
              flat
              round
              icon="remove"
              color="primary"
              @click="groupDialog.data.c_min > 0 && groupDialog.data.c_min--"
            />
            <q-input
              v-model.number="groupDialog.data.c_min"
              readonly
              dense
              outlined
              style="width: 80px"
              input-class="text-center"
              :input-style="{ fontSize: '18px' }"
            />
            <q-btn
              flat
              round
              icon="add"
              color="primary"
              @click="groupDialog.data.c_min++"
            />
          </div>
        </q-card-section>

        <!-- 最多選 -->
        <q-card-section
          class="row items-center justify-between q-px-md q-mt-none"
        >
          <div>最多選</div>
          <div class="row items-center q-gutter-sm">
            <q-btn
              flat
              round
              icon="remove"
              color="primary"
              @click="groupDialog.data.c_max > 0 && groupDialog.data.c_max--"
            />
            <q-input
              v-model.number="groupDialog.data.c_max"
              readonly
              dense
              outlined
              style="width: 80px"
              input-class="text-center"
              :input-style="{ fontSize: '18px' }"
            />
            <q-btn
              flat
              round
              icon="add"
              color="primary"
              @click="groupDialog.data.c_max++"
            />
          </div>
        </q-card-section>

        <!-- 類型選擇 -->
        <q-card-section class="q-px-md q-mt-none">
          <div class="text-subtitle2 q-mb-sm">類型</div>
          <div class="row q-gutter-md">
            <q-radio v-model="groupDialog.data.type" val="1" label="加點" />
            <q-radio v-model="groupDialog.data.type" val="2" label="套餐" />
          </div>
        </q-card-section>

        <!-- 選擇模式 -->
        <q-card-section class="q-px-md q-mt-none">
          <div class="text-subtitle2 q-mb-sm">選擇模式</div>
          <div class="row q-gutter-md">
            <q-radio v-model="groupDialog.data.mcq" val="0" label="單選" />
            <q-radio v-model="groupDialog.data.mcq" val="1" label="多選" />
          </div>
        </q-card-section>

        <!-- 按鈕列 -->
        <q-card-actions align="right" class="q-pt-md">
          <q-btn
            unelevated
            outline
            icon="save"
            label="儲存"
            type="submit"
            color="secondary"
            :disable="!groupDialog.data.sets_title"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>

  <!-- 刪除確認 Dialog -->
  <q-dialog v-model="deleteDialog">
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          群組刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteGroupConfirmed"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="itemDialog.show" persistent>
    <q-card style="width: 400px; max-width: 90vw">
      <q-card-section class="row items-center justify-between">
        <div class="text-h6 text-primary">選擇可加入的商品</div>
        <q-btn flat round dense icon="close" @click="itemDialog.show = false" />
      </q-card-section>

      <!-- 搜尋 + 清單 -->
      <q-card-section class="q-pt-none">
        <q-input
          v-model="itemDialog.search"
          dense
          outlined
          label="搜尋商品"
          debounce="300"
          clearable
          class="q-mb-sm"
        >
          <!-- 👉 右側 slot 放「全選」按鈕 -->
          <template #append>
            <q-btn
              flat
              dense
              icon="select_all"
              color="primary"
              size="sm"
              @click="selectAllVisibleItems"
              :disable="!filteredAvailableItems.length"
              class="q-mr-xs"
            />
          </template>
        </q-input>
        <q-list bordered style="max-height: 300px; overflow-y: auto">
          <q-item
            v-for="item in filteredAvailableItems"
            :key="item.code"
            tag="label"
            clickable
          >
            <q-item-section avatar>
              <q-checkbox
                v-model="itemDialog.selected"
                :val="item"
                color="primary"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ item.name }}</q-item-label>
              <q-item-label caption v-if="item.sname"
                >規格: {{ item.sname }}</q-item-label
              >
              <q-item-label caption v-if="item.price"
                >價格: {{ item.price }} 元</q-item-label
              >
              <q-item-label caption>
                單位: {{ item.unit || "份" }}
              </q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="!filteredAvailableItems.length" class="text-grey">
            <q-item-section>無可新增的商品</q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          label="加入"
          color="primary"
          unelevated
          outline
          :disable="!itemDialog.selected.length"
          @click="confirmAddItems"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 商品對應 Dialog -->
  <q-dialog v-model="mapDialog.show" persistent>
    <q-card style="width: 500px; max-width: 95vw">
      <q-card-section class="row items-center justify-between">
        <div class="text-h6 text-primary">套餐對應管理</div>
        <q-btn flat round dense icon="close" @click="mapDialog.show = false" />
      </q-card-section>

      <q-card-section>
        <div class="text-subtitle2 q-mb-sm">選擇套餐群組</div>
        <q-select
          v-model="mapDialog.selectedGroup"
          :options="setsGroups"
          option-value="sets_code"
          option-label="sets_title"
          outlined
          dense
          emit-value
          map-options
          @update:model-value="loadSetsMappings"
        />
      </q-card-section>

      <q-card-section v-if="mapDialog.selectedGroup" class="q-pt-none">
        <div class="text-subtitle2 q-mb-sm">套餐對應商品列表</div>
        <q-input
          v-model="mapDialog.search"
          dense
          outlined
          label="搜尋商品"
          debounce="300"
          clearable
          class="q-mb-sm"
        />

        <q-list bordered style="max-height: 300px; overflow-y: auto">
          <q-item
            v-for="item in filteredMainProducts"
            :key="item.code"
            tag="label"
            clickable
          >
            <q-item-section avatar>
              <q-checkbox
                v-model="item.selected"
                color="primary"
                @update:model-value="updateProductMapping(item)"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ item.name }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="!filteredMainProducts.length" class="text-grey">
            <q-item-section>無符合條件的商品</q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { VueDraggable } from "vue-draggable-plus";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const props = defineProps({ Branch: String });
const $q = useQuasar();

const selectAllVisibleItems = () => {
  const visible = filteredAvailableItems.value;
  const selected = itemDialog.value.selected;

  const selectedCodes = new Set(selected.map((i) => i.code));
  const allSelected = visible.every((item) => selectedCodes.has(item.code));

  if (allSelected) {
    // ❌ 全部已選 → 移除這些項目
    itemDialog.value.selected = selected.filter(
      (item) => !visible.some((v) => v.code === item.code)
    );
  } else {
    // ✅ 尚未全選 → 補齊所有可見項目
    const newSelected = [...selected];
    const selectedMap = new Set(selected.map((i) => i.code));

    visible.forEach((item) => {
      if (!selectedMap.has(item.code)) {
        newSelected.push(item);
      }
    });

    itemDialog.value.selected = newSelected;
  }
};

const itemDialog = ref({
  show: false,
  search: "",
  selected: [],
  group: null,
});

const mapDialog = ref({
  show: false,
  selectedGroup: null,
  search: "",
  mappings: [],
});

const allMenuItems = ref([]); // 從資料庫撈出來的完整清單
const mainProducts = ref([]); // 主商品清單 (type=1)

// 可選項目（排除已經在 group.items 裡的）
const availableItems = computed(() => {
  const existingCodes = new Set(
    itemDialog.value.group?.items?.map((i) => i.code)
  );
  return allMenuItems.value.filter((item) => !existingCodes.has(item.code));
});

// 搜尋過濾
const filteredAvailableItems = computed(() => {
  const keyword = itemDialog.value.search?.toLowerCase() || "";
  return availableItems.value.filter((item) =>
    item.name.toLowerCase().includes(keyword)
  );
});

// 過濾主商品列表用於mapping
const filteredMainProducts = computed(() => {
  const keyword = mapDialog.value.search?.toLowerCase() || "";
  return mainProducts.value.filter((item) =>
    item.name.toLowerCase().includes(keyword)
  );
});

// 開啟 Dialog 並撈資料
const openItemDialog = async (group) => {
  itemDialog.value.group = group;
  itemDialog.value.search = "";
  itemDialog.value.selected = [];

  try {
    const res = await apiClient.get("/osets/get_MenuItems", {
      params: { branch: props.Branch },
    });
    allMenuItems.value = res.data.data || [];
    itemDialog.value.show = true;
  } catch (err) {
    console.error("❌ 載入商品清單失敗", err);
    $q.notify({ type: "negative", message: "無法載入商品清單" });
  }
};

// 加入選取的商品到 group.items
const confirmAddItems = async () => {
  const group = itemDialog.value.group;
  const newItems = itemDialog.value.selected;

  try {
    const res = await apiClient.post("/osets/add_SetItems", {
      branch: props.Branch,
      sets_code: group.sets_code,
      items: newItems,
    });

    if (res.data.success) {
      // 實際寫入成功後也更新 local group.items
      const start = group.items ? group.items.length : 0;
      newItems.forEach((item, i) => {
        if (!group.items) group.items = [];
        group.items.push({
          code: item.code,
          name: item.name,
          sname: item.sname || "",
          price: item.price || 0,
          unit: item.unit || "份",
          sno: start + i + 1,
        });
      });

      $q.notify({ type: "positive", message: "新增商品成功" });
      itemDialog.value.show = false;
    } else {
      $q.notify({ type: "negative", message: res.data.message || "新增失敗" });
    }
  } catch (err) {
    console.error("❌ 新增商品失敗:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

const deleteDialog = ref(false); // 控制 Dialog 開關
const groupToDelete = ref(null); // 暫存欲刪除的群組

const groupDialog = ref({
  show: false,
  data: {
    sets_code: "",
    sets_title: "",
    c_min: 0,
    c_max: 1,
    type: "2", // 默認套餐
    mcq: "0", // 默認單選
    sort: 0,
  },
});

const onSetItemSortEnd = async (group) => {
  // 更新每個商品的排序欄位
  group.items.forEach((item, index) => {
    item.sno = index + 1;
  });

  try {
    await apiClient.post("/osets/save_SetItemSort", {
      branch: props.Branch,
      sets_code: group.sets_code,
      items: group.items.map((item) => ({
        code: item.code,
        sno: item.sno,
      })),
    });

    $q.notify({ type: "positive", message: "商品排序已儲存" });
  } catch (err) {
    console.error("❌ 商品排序儲存失敗:", err);
    $q.notify({ type: "negative", message: "商品排序儲存失敗" });
  }
};

const onGroupSortEnd = async () => {
  // 更新 sort 欄位
  setsGroups.value.forEach((g, index) => {
    g.sort = index + 1;
  });

  try {
    await apiClient.post("/osets/save_GroupSort", {
      branch: props.Branch,
      groups: setsGroups.value.map((g) => ({
        sets_code: g.sets_code,
        sort: g.sort,
      })),
    });

    $q.notify({ type: "positive", message: "群組排序已儲存" });
  } catch (err) {
    console.error("❌ 群組排序錯誤", err);
    $q.notify({ type: "negative", message: "群組排序儲存失敗" });
  }
};

const openGroupDialog = (group = null) => {
  if (group) {
    // 確保type和mcq是字符串類型
    const typeCopy = String(group.type);
    const mcqCopy = String(group.mcq);

    groupDialog.value.data = {
      ...group,
      type: typeCopy,
      mcq: mcqCopy,
    }; // 編輯模式：帶入資料
  } else {
    groupDialog.value.data = {
      sets_title: "",
      c_min: 0,
      c_max: 1,
      type: "2", // 默認套餐
      mcq: "0", // 默認單選
      items: [],
      sort: setsGroups.value.length + 1, // 預估排序
    };
  }

  groupDialog.value.show = true;
};

const openDeleteDialog = (group) => {
  groupToDelete.value = group;
  deleteDialog.value = true;
};

const deleteGroupConfirmed = async () => {
  if (!groupToDelete.value) return;

  try {
    const res = await apiClient.post("/osets/delete_SetGroup", {
      branch: props.Branch,
      sets_code: groupToDelete.value.sets_code,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "刪除成功" });
      await fetchSets(); // 🔁 重新抓群組
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    console.error("❌ 刪除錯誤:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  } finally {
    deleteDialog.value = false;
    groupToDelete.value = null;
  }
};

const saveGroup = async () => {
  const group = { ...groupDialog.value.data };

  // ✅ 驗證
  if (!group.sets_title) {
    $q.notify({ type: "negative", message: "群組名稱不得為空" });
    return;
  }

  if (group.c_max < group.c_min) {
    $q.notify({ type: "negative", message: "最多選不能小於最少選" });
    return;
  }

  // ✅ 新增時補 sort/items
  if (!group.sets_code) {
    group.sort = setsGroups.value.length + 1;
    group.items = [];
  }

  try {
    const res = await apiClient.post("/osets/save_SetGroup", {
      branch: props.Branch,
      group: group,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "儲存成功" });
      groupDialog.value.show = false;
      await fetchSets(); // ✅ 重新讀取最新資料
    } else {
      $q.notify({ type: "negative", message: res.data.message || "儲存失敗" });
    }
  } catch (err) {
    console.error("❌ 儲存錯誤:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

const setsGroups = ref([]);

const deleteItem = async (group, index) => {
  const item = group.items[index];

  try {
    const res = await apiClient.post("/osets/delete_SetItem", {
      branch: props.Branch,
      sets_code: group.sets_code,
      code: item.code,
      sno: item.sno,
    });

    if (res.data.success) {
      group.items.splice(index, 1);
      $q.notify({ type: "positive", message: "商品已刪除" });
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    console.error("❌ 刪除商品項目失敗", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

// 打開商品對應管理Dialog
const openMapDialog = async () => {
  mapDialog.value.show = true;
  mapDialog.value.selectedGroup = null;
  mapDialog.value.search = "";

  try {
    // 載入主要商品列表 (type=1)
    const res = await apiClient.get("/osets/get_MainProducts", {
      params: { branch: props.Branch },
    });
    mainProducts.value = res.data.data || [];
  } catch (err) {
    console.error("❌ 載入主商品失敗:", err);
    $q.notify({ type: "negative", message: "無法載入主商品列表" });
  }
};

// 載入特定套餐群組的商品映射
const loadSetsMappings = async () => {
  if (!mapDialog.value.selectedGroup) return;

  try {
    const res = await apiClient.get("/osets/get_SetsMap", {
      params: {
        branch: props.Branch,
        sets_code: mapDialog.value.selectedGroup,
      },
    });

    // 更新商品的選取狀態
    const mappedCodes = new Set((res.data.data || []).map((m) => m.code));

    mainProducts.value.forEach((product) => {
      product.selected = mappedCodes.has(product.code);
    });
  } catch (err) {
    console.error("❌ 載入套餐對應失敗:", err);
    $q.notify({ type: "negative", message: "無法載入套餐對應" });
  }
};

// 更新商品對應
const updateProductMapping = async (product) => {
  try {
    const res = await apiClient.post("/osets/update_SetsMap", {
      branch: props.Branch,
      sets_code: mapDialog.value.selectedGroup,
      code: product.code,
      isMap: product.selected,
    });

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: product.selected ? "已新增對應" : "已移除對應",
      });
    } else {
      // 發生錯誤時恢復原狀
      product.selected = !product.selected;
      $q.notify({ type: "negative", message: res.data.message || "更新失敗" });
    }
  } catch (err) {
    // 發生錯誤時恢復原狀
    product.selected = !product.selected;
    console.error("❌ 更新套餐對應失敗:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

const fetchSets = async () => {
  try {
    const res = await apiClient.get(`${apiBaseUrl}/osets/get_SetGroups`, {
      params: {
        branch: props.Branch,
      },
    });

    if (res.data?.success) {
      setsGroups.value = res.data.data;
    } else {
      console.error("❌ 載入套餐群組失敗:", res.data.message);
    }
  } catch (err) {
    console.error("❌ 載入套餐群組時發生錯誤:", err.message);
  } finally {
    $q.loading.hide();
  }
};

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await fetchSets();
});
</script>
