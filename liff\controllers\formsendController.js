//送出表單主功能
const sql = require("mssql");
const dbConfig = require("../config/db");
const sendEmail = require("./sendEmail");
const { generateApprovalFlow } = require("./generateApprovalFlow");

const submitot = async (req, res) => {
  const {
    user_id,
    otType,
    stime,
    etime,
    reason,
    attachments,
    totalHours,
    attachmentsCount,
  } = req.body;

  let transaction;

  try {
    const pool = await sql.connect(dbConfig);
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    // 🔹 取得當天日期 (YYYYMMDD)
    const now = new Date();
    const dateStr = `${now.getFullYear()}${(now.getMonth() + 1)
      .toString()
      .padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;

    // 🔹 查詢當天最大 Form ID
    const result = await transaction
      .request()
      .input("dateStr", sql.VarChar, `${dateStr}%`)
      .query(
        "SELECT MAX(Form_id) AS maxId FROM Forms WHERE Form_id LIKE @dateStr"
      );

    let newSerial = "0001"; // 預設流水號從 0001 開始
    if (result.recordset[0].maxId) {
      const lastId = result.recordset[0].maxId;
      const lastSerial = parseInt(lastId.slice(-4), 10);
      newSerial = (lastSerial + 1).toString().padStart(4, "0");
    }

    // 🔹 產生新的 `Form ID`
    const formId = `${dateStr}${newSerial}`;
    const formStatus = "waiting"; // 申請時表單狀態為待簽核

    // 🔹 新增加班單 (`Forms`)
    await transaction
      .request()
      .input("form_id", sql.VarChar, formId)
      .input("user_id", sql.VarChar, user_id)
      .query(
        "INSERT INTO Forms (Form_id, userid, Type, Status, Created, Updated) VALUES (@form_id, @user_id, 'ot', 'pending', GETUTCDATE(), GETUTCDATE())"
      );

    // 🔹 新增加班明細 (`Form_ot`)
    await transaction
      .request()
      .input("form_id", sql.VarChar, formId)
      .input("type", sql.VarChar, otType)
      .input("stime", sql.DateTime, stime)
      .input("etime", sql.DateTime, etime)
      .input("reason", sql.NVarChar, reason)
      .input("totalHours", sql.VarChar, totalHours)
      .input("attachmentsCount", sql.Int, attachmentsCount)
      .query(
        "INSERT INTO Form_ot (Form_id, Type, Stime, Etime, Reason, Thour, Attachment) VALUES (@form_id, @type, @stime, @etime, @reason, @totalHours, @attachmentsCount)"
      );

    // 🔹 插入附件 (`Forms_attachments`)
    if (attachments && attachments.length > 0) {
      for (let i = 0; i < attachments.length; i++) {
        await transaction
          .request()
          .input("form_id", sql.VarChar, formId)
          .input("sno", sql.Int, i + 1)
          .input("file_url", sql.NVarChar, attachments[i].url)
          .query(
            "INSERT INTO Forms_attachments (Form_id, sno, file_url, Created_at) VALUES (@form_id, @sno, @file_url, GETDATE())"
          );
      }
    }
    await generateApprovalFlow(formId, "ot", user_id, transaction); //產生流程generateApprovalFlow.js
    await transaction.commit();

    // 🔹 表單送出後立即執行通知
    sendEmail
      .sendPendingNotifications()
      .catch((err) => console.error("❌ 發送郵件時發生錯誤:", err));

    res.json({ status: "success", form_id: formId });
  } catch (err) {
    if (transaction) await transaction.rollback();
    console.error("❌ 提交加班單時發生錯誤:", err);
    res.status(500).json({ error: err.message });
  }
};

const submitleave = async (req, res) => {
  const {
    user_id,
    leaveType,
    stime,
    etime,
    reason,
    attachments,
    totalDays,
    totalHours,
    attachmentsCount,
  } = req.body;

  let transaction;

  try {
    const pool = await sql.connect(dbConfig);
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    // 🔹 產生 Form ID
    const now = new Date();
    const dateStr = `${now.getFullYear()}${(now.getMonth() + 1)
      .toString()
      .padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
    const result = await transaction
      .request()
      .input("dateStr", sql.VarChar, `${dateStr}%`)
      .query(
        "SELECT MAX(Form_id) AS maxId FROM Forms WHERE Form_id LIKE @dateStr"
      );

    let newSerial = "0001";
    if (result.recordset[0].maxId) {
      const lastId = result.recordset[0].maxId;
      const lastSerial = parseInt(lastId.slice(-4), 10);
      newSerial = (lastSerial + 1).toString().padStart(4, "0");
    }

    const formId = `${dateStr}${newSerial}`;

    // 🔹 新增請假單 (`Forms`)
    await transaction
      .request()
      .input("form_id", sql.VarChar, formId)
      .input("user_id", sql.VarChar, user_id)
      .query(
        "INSERT INTO Forms (Form_id, userid, Type, Status, Created, Updated) VALUES (@form_id, @user_id, 'leave', 'pending', GETUTCDATE(), GETUTCDATE())"
      );

    // 🔹 新增請假明細 (`Form_leave`)
    await transaction
      .request()
      .input("form_id", sql.VarChar, formId)
      .input("type", sql.VarChar, leaveType)
      .input("stime", sql.DateTime, stime)
      .input("etime", sql.DateTime, etime)
      .input("reason", sql.NVarChar, reason)
      .input("totalDays", sql.VarChar, totalDays)
      .input("totalHours", sql.VarChar, totalHours)
      .input("attachmentsCount", sql.Int, attachmentsCount)
      .query(
        "INSERT INTO Form_leave (Form_id, Type, Stime, Etime, Reason, Tday, Thour, Attachment) VALUES (@form_id, @type, @stime, @etime, @reason, @totalDays, @totalHours, @attachmentsCount)"
      );

    // 🔹 插入附件 (`Forms_attachments`)
    if (attachments && attachments.length > 0) {
      for (let i = 0; i < attachments.length; i++) {
        await transaction
          .request()
          .input("form_id", sql.VarChar, formId)
          .input("sno", sql.Int, i + 1)
          .input("file_url", sql.NVarChar, attachments[i].url)
          .query(
            "INSERT INTO Forms_attachments (Form_id, sno, file_url, Created_at) VALUES (@form_id, @sno, @file_url, GETUTCDATE())"
          );
      }
    }

    // 🔹 產生簽核流程
    await generateApprovalFlow(formId, "leave", user_id, transaction);

    // ✅ 提交事務
    await transaction.commit();

    res.json({ status: "success", form_id: formId });
  } catch (err) {
    if (transaction) await transaction.rollback();
    console.error("❌ 提交請假單時發生錯誤:", err);
    res.status(500).json({ error: err.message });
  }
};

const submitseal = async (req, res) => {
  const { user_id, sealType, copies, bgroup, reason, attachmentsCount } =
    req.body;

  console.log("📝 用印申請資料:", {
    user_id,
    sealType,
    copies,
    bgroup,
    reason,
    attachmentsCount,
  });

  let pool;
  let transaction;

  try {
    // 建立連接池
    pool = await new sql.ConnectionPool(dbConfig).connect();
    // 初始化交易
    transaction = new sql.Transaction(pool);
    // 開始交易
    await transaction.begin();

    // 🔹 取得當天日期 (YYYYMMDD)
    const now = new Date();
    const dateStr = `${now.getFullYear()}${(now.getMonth() + 1)
      .toString()
      .padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;

    // 🔹 查詢當天最大 Form ID
    const result = await transaction
      .request()
      .input("dateStr", sql.VarChar, `${dateStr}%`)
      .query(
        "SELECT MAX(Form_id) AS maxId FROM Forms WHERE Form_id LIKE @dateStr"
      );

    let newSerial = "0001"; // 預設流水號從 0001 開始
    if (result.recordset[0].maxId) {
      const lastId = result.recordset[0].maxId;
      const lastSerial = parseInt(lastId.slice(-4), 10);
      newSerial = (lastSerial + 1).toString().padStart(4, "0");
    }

    // 🔹 產生新的 `Form ID`
    const formId = `${dateStr}${newSerial}`;

    // 🔹 新增用印申請單 (`Forms`)
    await transaction
      .request()
      .input("form_id", sql.VarChar, formId)
      .input("user_id", sql.VarChar, user_id)
      .query(
        "INSERT INTO Forms (Form_id, userid, Type, Status, Created, Updated) VALUES (@form_id, @user_id, 'seal', 'pending', GETUTCDATE(), GETUTCDATE())"
      );

    // 🔹 新增用印申請明細 (`Form_seal`)
    await transaction
      .request()
      .input("form_id", sql.VarChar(50), formId)
      .input("type", sql.NVarChar(100), sealType)
      .input("copies", sql.Int, copies)
      .input("bgroup", sql.NVarChar(100), bgroup)
      .input("reason", sql.NVarChar(sql.MAX), reason)
      .input("attachmentsCount", sql.Int, attachmentsCount)
      .query(
        "INSERT INTO Form_seal (Form_id, Type, Copies, Bgroup, Reason, Attachment) VALUES (@form_id, @type, @copies, @bgroup, @reason, @attachmentsCount)"
      );

    // 🔹 產生簽核流程
    console.log(
      `🔄 生成簽核流程: formId=${formId}, type=seal, userId=${user_id}`
    );
    try {
      await generateApprovalFlow(formId, "seal", user_id, transaction);
      console.log("✅ 簽核流程生成成功");
    } catch (flowErr) {
      console.error("❌ 生成簽核流程時發生錯誤:", flowErr);
      throw flowErr; // 重新拋出錯誤，讓外層 catch 處理
    }

    // ✅ 提交事務
    await transaction.commit();

    // 🔹 表單送出後立即執行通知
    sendEmail
      .sendPendingNotifications()
      .catch((err) => console.error("❌ 發送郵件時發生錯誤:", err));

    res.json({ status: "success", form_id: formId });
  } catch (err) {
    console.error("❌ 提交用印申請單時發生錯誤:", err);

    // 回滾交易
    if (transaction) {
      try {
        await transaction.rollback();
        console.log("🔄 交易已回滾");
      } catch (rollbackErr) {
        console.error("❌ 回滾交易時發生錯誤:", rollbackErr);
      }
    }

    res.status(500).json({ error: err.message });
  } finally {
    // 關閉連接池
    if (pool) {
      try {
        await pool.close();
        console.log("🔒 資料庫連接已關閉");
      } catch (closeErr) {
        console.error("❌ 關閉資料庫連接時發生錯誤:", closeErr);
      }
    }
  }
};

module.exports = { submitot, submitleave, submitseal };
