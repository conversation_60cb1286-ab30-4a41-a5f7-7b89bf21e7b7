<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="
        width: 100%;
        max-width: 500px;
        overflow: hidden;
        border-radius: 12px;
      "
    >
      <!-- 標題 -->
      <q-card-section class="text-center">
        <div class="text-h6 text-primary">發票管理</div>
      </q-card-section>

      <!-- 查詢條件 -->
      <q-card-section class="q-px-none">
        <div class="row items-center no-wrap">
          <q-input
            v-model="filters.date"
            mask="####-##-##"
            placeholder="YYYY-MM-DD"
            label="發票日期"
            dense
            outlined
            square
            class="col"
            readonly
          >
            <!-- 左側 icon -->
            <template v-slot:prepend>
              <q-icon name="calendar_month" />
            </template>

            <!-- 右側點擊彈出日期選單 -->
            <template v-slot:append>
              <q-icon
                name="edit_calendar"
                color="primary"
                class="cursor-pointer"
                size="18px"
              >
                <q-popup-proxy
                  ref="datePopup"
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date
                    v-model="filters.date"
                    mask="YYYY-MM-DD"
                    @update:model-value="closeDatePopup"
                  >
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>

          <q-btn
            flat
            icon="tune"
            round
            class="q-ml-sm"
            @click="advancedSearch = true"
          />
        </div>
      </q-card-section>

      <!-- 發票列表 -->
      <q-list bordered separator>
        <q-expansion-item
          v-for="minvo in pagedOrders"
          :key="minvo.code"
          expand-separator
          :label="`單號：${minvo.code}`"
        >
          <template v-slot:header>
            <q-item-section>
              <q-item-label class="text-weight-bold">{{
                minvo.ino
              }}</q-item-label>
              <q-item-label caption class="row items-center no-wrap">
                <q-badge
                  outline
                  :color="minvo.chk === '1' ? 'negative' : 'secondary'"
                  :label="minvo.chk === '1' ? '作廢' : '正常'"
                  class="q-mr-sm"
                  v-if="minvo.chk !== undefined"
                />
                日期：{{ minvo.ndate }}
              </q-item-label>
            </q-item-section>
            <q-item-section side class="text-right">
              <div
                :class="
                  minvo.chk === '1'
                    ? 'text-grey text-strike'
                    : 'text-bold text-primary'
                "
              >
                ${{ minvo.amt + minvo.discount }}
              </div>
            </q-item-section>
          </template>

          <q-card>
            <q-card-section class="q-pa-sm q-gutter-y-sm">
              <div class="row">
                <div class="col text-grey-8">訂單編號</div>
                <div class="col-auto text-right">{{ minvo.code }}</div>
              </div>
              <div class="row" v-if="minvo.invo">
                <div class="col text-grey-8">統一編號</div>
                <div class="col-auto text-right">{{ minvo.invo }}</div>
              </div>
              <div class="row" v-if="minvo.C3">
                <div class="col text-grey-8">載具</div>
                <div class="col-auto text-right">{{ minvo.C3 }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">開立日期</div>
                <q-badge
                  v-if="minvo.ndate !== minvo.sdate"
                  outline
                  color="negative"
                  label="跨日"
                  style="font-size: 11px; margin-left: 8px"
                  class="q-ml-xs"
                />
                <div class="col-auto text-right q-ml-md">{{ minvo.ndate }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">開立時間</div>
                <div class="col-auto text-right">{{ minvo.ntime }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">機號</div>
                <div class="col-auto text-right">{{ minvo.imno }}</div>
              </div>

              <!-- 功能按鈕 -->
              <div class="q-mt-md row q-gutter-sm">
                <q-btn
                  :label="minvo.chk === '1' ? '還原發票' : '作廢發票'"
                  :color="minvo.chk === '1' ? 'positive' : 'negative'"
                  icon="history"
                  flat
                  class="col"
                  @click="toggleCancel(minvo)"
                />
              </div>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </q-list>
      <q-pagination
        v-model="currentPage"
        :max="Math.ceil(minvos.length / rowsPerPage)"
        max-pages="7"
        boundary-numbers
        color="primary"
        class="q-mt-md flex justify-center"
      />
    </q-card>
  </q-page>
  <!-- 進階查詢 Dialog -->
  <q-dialog v-model="advancedSearch">
    <q-card style="min-width: 300px; max-width: 400px">
      <q-card-section class="text-h6 text-primary">進階查詢</q-card-section>
      <q-card-section class="q-gutter-md">
        <q-input
          v-model="filters.sdate"
          mask="####-##-##"
          placeholder="YYYY-MM-DD"
          label="發票日期"
          dense
          filled
          square
          clearable
        />
        <q-input
          v-model="filters.code"
          label="訂單編號"
          filled
          dense
          square
          clearable
        />
        <q-input
          v-model="filters.ino"
          label="發票號碼"
          filled
          dense
          square
          clearable
        />
      </q-card-section>
      <q-card-actions align="right">
        <q-btn flat label="關閉" v-close-popup />
        <q-btn
          label="查詢"
          color="primary"
          @click="
            AsearchData();
            advancedSearch = false;
          "
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const $q = useQuasar();

const currentPage = ref(1); // 當前頁數
const rowsPerPage = 7; // 每頁筆數
const datePopup = ref(null);
const closeDatePopup = () => {
  datePopup.value?.hide(); // ✅ 手動關閉 popup
};
const pagedOrders = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage;
  return minvos.value.slice(start, start + rowsPerPage);
});

const today = new Date().toISOString().slice(0, 10);
const props = defineProps({
  Branch: String,
  Permissions: {
    type: [String, Array],
    default: () => [],
  },
});
const advancedSearch = ref(false);
const filters = ref({
  date: today,
  ino: "",
  invo: "",
  sdate: "",
});
const minvos = ref([]);

const filteredInvoices = computed(() =>
  minvos.value.filter(
    (inv) =>
      (!filters.value.date || inv.ndate === filters.value.date) &&
      (!filters.value.ino || inv.ino.includes(filters.value.ino)) &&
      (!filters.value.invo || inv.invo.includes(filters.value.invo))
  )
);

const getMinvos = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/minvo/get_Minvos`, {
      params: {
        branch: props.Branch,
        date: filters.value.date ? filters.value.date.replace(/-/g, "/") : "",
        invo: filters.value.invo,
        code: filters.value.code,
        ino: filters.value.ino,
      },
    });
    minvos.value = response.data;
  } catch (err) {
    console.error("❌ 查詢訂單失敗:", err);
  }
};

const toggleCancel = async (minvo) => {
  const newStatus = minvo.chk === "1" ? "0" : "1";

  try {
    await apiClient.post(`${apiBaseUrl}/minvo/update_Status`, {
      code: minvo.code,
      status: newStatus,
      group: props.Permissions, // 傳入群組權限，現在可能是數組
      branch: props.Branch,
    });

    minvo.chk = newStatus;

    $q.notify({
      type: "positive",
      message: `發票已${newStatus === "1" ? "作廢" : "還原"}`,
    });
  } catch (err) {
    if (err.response?.status === 403) {
      $q.notify({
        type: "negative",
        message: "無此操作權限",
      });
    } else {
      $q.notify({
        type: "negative",
        message: "更新訂單狀態失敗",
      });
    }
  }
};
const AsearchData = () => {
  const f = filters.value;

  const hasAnyFilter = !!f.sdate || !!f.code || !!f.invo || !!f.ino;

  if (hasAnyFilter) {
    filters.value.date = filters.value.sdate;
    getMinvos();
  } else {
    $q.notify({ type: "warning", message: "未輸入條件" });
  }
};

const searchData = () => {
  const f = filters.value;
  f.code = "";
  f.invo = "";
  f.ino = "";
  f.sdate = "";
  getMinvos();
};

watch(
  () => filters.value.date,
  (newDate) => {
    if (newDate && newDate.length === 10) {
      searchData();
    }
  }
);

onMounted(async () => {
  if (!props.Branch) {
    $q.notify({
      type: "warning",
      message: "請先選擇門市！",
    });
  } else {
    await getMinvos();
  }
});
</script>
