const jwt = require("jsonwebtoken");

const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.split(" ")[1];

  if (!token) {
    return res
      .status(401)
      .json({ success: false, message: "❌ 未授權，請提供 Token" });
  }
  //console.log(token);
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded; // ✅ 存入 `req.user`，供後續 API 使用
    next(); // ✅ 繼續執行後續 API
  } catch (error) {
    return res.status(401).json({ success: false, message: "❌ 無效 Token" });
  }
};

module.exports = authMiddleware;
