const express = require("express");
const {
  getonlineCclass,
  saveGroupSort,
  saveCclass,
  deleteCclass,
  getTspecList,
  deleteTaste,
  saveTaste,
  saveTasteSort,
  insertTspace,
  deleteTspec,
} = require("../controllers/o_cclassController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_onlineCclass", authMiddleware, getonlineCclass); // ✅ 讀取 `/api/order`
router.post("/save_GroupSort", authMiddleware, saveGroupSort); // ✅ 讀取 `/api/order`
router.post("/save_Cclass", authMiddleware, saveCclass); // ✅ 讀取 `/api/order`
router.post("/delete_Cclass", authMiddleware, deleteCclass); // ✅ 讀取 `/api/order`
router.get("/get_TspecList", authMiddleware, getTspecList); // ✅ 讀取 `/api/order`
router.post("/delete_Taste", authMiddleware, deleteTaste); // ✅ 讀取 `/api/order`
router.post("/save_Taste", authMiddleware, saveTaste); // ✅ 讀取 `/api/order`
router.post("/save_TasteSort", authMiddleware, saveTasteSort); // ✅ 讀取 `/api/order`
router.post("/insert_Tspace", authMiddleware, insertTspace); // ✅ 讀取 `/api/order`
router.post("/delete_Tspec", authMiddleware, deleteTspec); // ✅ 讀取 `/api/order`

module.exports = router;
