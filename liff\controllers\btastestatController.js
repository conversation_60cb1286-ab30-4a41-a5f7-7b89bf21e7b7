const sql = require("mssql");
const { DateTime } = require("luxon");
require("dotenv").config();

const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");
  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

// 新增取得口味選項的函數
const getTasteOptions = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ success: false, message: "缺少門市參數" });
  }
  //console.log(branch);
  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 查詢口味資料表
    const result = await pool.request().query(`
      SELECT name, price FROM taste
      ORDER BY name, price
    `);

    // 格式化資料 - 使用名稱和價格作為唯一識別
    const tasteOptions = result.recordset.map((taste) => {
      const displayName =
        taste.price > 0 ? `${taste.name}($${taste.price})` : taste.name;
      // 使用名稱和價格組合作為值，以確保不同價格的同名口味可以被分開識別
      const value = `${taste.name}|${taste.price}`;

      return {
        label: displayName,
        value: value,
        name: taste.name,
        price: taste.price,
      };
    });

    res.json({
      success: true,
      data: tasteOptions,
    });
  } catch (err) {
    console.error("❌ 查詢口味選項錯誤:", err);
    res.status(500).json({ success: false, message: "查詢失敗" });
  }
};

const getTasteStats = async (req, res) => {
  const { branch, from, to, sortBy, selectedTastes } = req.query;

  if (!from || !to) {
    return res.status(400).json({ success: false, message: "缺少日期區間" });
  }

  const fromFormatted = DateTime.fromISO(from).toFormat("yyyy/MM/dd");
  const toFormatted = DateTime.fromISO(to).toFormat("yyyy/MM/dd");

  let orderClause = "ORDER BY className, tasteName";
  if (sortBy === "qty") {
    orderClause = "ORDER BY count DESC";
  }

  // 解析選擇的口味 - 從複合值中提取名稱
  const tasteNameSet = new Set();
  if (selectedTastes) {
    try {
      const parsedTastes = JSON.parse(selectedTastes);
      parsedTastes.forEach((taste) => {
        // 從複合值中提取名稱部分
        const parts = taste.split("|");
        if (parts && parts.length > 0) {
          tasteNameSet.add(parts[0]);
        }
      });
    } catch (e) {
      console.error("解析選擇口味錯誤:", e);
    }
  }

  // 轉換為陣列，方便後續使用
  const tasteFilter = Array.from(tasteNameSet);

  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 首先獲取所有訂單資料
    const result = await pool
      .request()
      .input("from", sql.VarChar, fromFormatted)
      .input("to", sql.VarChar, toFormatted).query(`
        SELECT 
          ISNULL(Cclass.code, 'UNDEFINED') AS classCode,
          ISNULL(Cclass.name, '未分類') AS className,
          OrderMainN.OREMNAME
        FROM OrderMainN
        JOIN OrderNow ON OrderNow.code = OrderMainN.scode AND OrderNow.checkout = 1
        LEFT JOIN Omenum ON OrderMainN.code = Omenum.code
        LEFT JOIN Cclass ON Omenum.cclass = Cclass.code
        WHERE OrderNow.ndate BETWEEN @from AND @to
          AND OrderMainN.OREMNAME IS NOT NULL 
          AND OrderMainN.OREMNAME <> ''
      `);

    const rows = result.recordset;

    // 處理口味數據 - 將每筆訂單的口味拆分並統計
    const tastesByClass = {};
    let totalTastes = 0;

    for (const row of rows) {
      const className = row.className;
      const tastes = row.OREMNAME.split(" ").filter(
        (taste) => taste.trim() !== ""
      );

      if (!tastesByClass[className]) {
        tastesByClass[className] = {};
      }

      for (const taste of tastes) {
        // 如果有選擇特定口味且該口味不在選擇清單中，則跳過
        if (tasteFilter.length > 0 && !tasteFilter.includes(taste)) {
          continue;
        }

        if (!tastesByClass[className][taste]) {
          tastesByClass[className][taste] = 0;
        }
        tastesByClass[className][taste]++;
        totalTastes++;
      }
    }

    // 將數據轉換為前端需要的格式
    const finalData = Object.entries(tastesByClass).map(
      ([className, tastes]) => {
        // 計算該類別的總數
        const classTotalCount = Object.values(tastes).reduce(
          (sum, count) => sum + count,
          0
        );

        const tasteDetails = Object.entries(tastes).map(
          ([tasteName, count]) => ({
            tasteName,
            count,
            percentage:
              classTotalCount > 0
                ? ((count / classTotalCount) * 100).toFixed(1)
                : "0.0", // 類別內的百分比
            overallPercentage:
              totalTastes > 0
                ? ((count / totalTastes) * 100).toFixed(1)
                : "0.0", // 整體百分比
          })
        );

        // 根據排序方式進行排序
        if (sortBy === "qty") {
          tasteDetails.sort((a, b) => b.count - a.count);
        } else {
          tasteDetails.sort((a, b) => a.tasteName.localeCompare(b.tasteName));
        }

        return {
          className,
          tastes: tasteDetails,
          totalCount: classTotalCount,
          classPercentage:
            totalTastes > 0
              ? ((classTotalCount / totalTastes) * 100).toFixed(1)
              : "0.0", // 該類別佔總體的百分比
        };
      }
    );

    // 過濾掉沒有口味的類別
    const filteredData = finalData.filter((group) => group.tastes.length > 0);

    res.json({
      success: true,
      from: fromFormatted,
      to: toFormatted,
      data: filteredData,
      totalTastes,
    });
  } catch (err) {
    console.error("❌ 查詢口味統計錯誤:", err);
    res.status(500).json({ success: false, message: "查詢失敗" });
  }
};

module.exports = {
  getTasteStats,
  getTasteOptions,
};
