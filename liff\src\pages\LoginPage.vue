<template>
  <q-layout>
    <q-page-container>
      <q-page class="login-page">
        <div class="login-container">
          <div class="login-card-wrapper">
            <!-- Login Card -->
            <q-card class="login-card">
              <q-card-section class="title-section">
                <h4 class="text-center no-margin">企業資訊平台</h4>
                <p class="text-center text-orange-7 q-mt-sm">
                  系統閒置自動登出，請重新登入
                </p>
              </q-card-section>

              <q-card-section>
                <q-form @submit="login" class="q-gutter-md">
                  <q-input
                    v-model="username"
                    outlined
                    label="帳號"
                    class="input-field"
                    :disable="loading"
                    :rules="[(val) => !!val || '請輸入帳號']"
                    lazy-rules
                  >
                    <template v-slot:prepend>
                      <user-round
                        :stroke-width="1"
                        :size="24"
                        color="#1976D2"
                      />
                    </template>
                  </q-input>

                  <q-input
                    v-model="password"
                    outlined
                    label="密碼"
                    type="password"
                    class="input-field"
                    :disable="loading"
                    :rules="[(val) => !!val || '請輸入密碼']"
                    lazy-rules
                    @keyup.enter="login"
                  >
                    <template v-slot:prepend>
                      <lock-keyhole
                        :stroke-width="1"
                        :size="24"
                        color="#1976D2"
                      />
                    </template>
                  </q-input>

                  <div class="q-mt-lg">
                    <q-btn
                      unelevated
                      color="primary"
                      :label="loading ? '登入中...' : '登入'"
                      class="full-width login-btn"
                      :loading="loading"
                      :disable="!username || !password || loading"
                      @click="login"
                    />
                  </div>

                  <div class="separator">
                    <span class="separator-text">或者</span>
                  </div>

                  <q-btn
                    outline
                    color="green"
                    class="full-width line-login-btn"
                    :disable="loading"
                    @click="goToLINELogin"
                  >
                    <div class="row items-center justify-center full-width">
                      <message-square-text
                        :stroke-width="1"
                        :size="24"
                        color="green"
                        class="q-mr-sm"
                      />
                      <span>使用 LINE 登入</span>
                    </div>
                  </q-btn>
                </q-form>
              </q-card-section>
            </q-card>

            <!-- Footer -->
            <div class="footer-text text-center q-mt-md">
              <p class="text-grey-7">
                © {{ new Date().getFullYear() }} 小蒙牛餐飲集團
              </p>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from "vue";
import axios from "axios";
import { useRoute, useRouter } from "vue-router";
import { useQuasar } from "quasar";

const $q = useQuasar();
const username = ref("");
const password = ref("");
const loading = ref(false);
const router = useRouter();
const route = useRoute();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const goToLINELogin = () => {
  router.push("/linelogin");
};

// 檢查授權過期
if (route.query.expired === "true") {
  $q.notify({
    message: "授權到期，請重新登入",
    color: "negative",
    timeout: 3000,
    position: "top",
  });
}

// 檢查未授權
if (route.query.unauthorized === "true") {
  $q.notify({
    message: "請先登入後再訪問",
    color: "warning",
    timeout: 3000,
    position: "top",
  });
}

const login = async () => {
  try {
    loading.value = true;

    const response = await axios.post(`${apiBaseUrl}/auth/login`, {
      username: username.value,
      password: password.value,
    });

    if (response && response.data.success) {
      console.log("✅ 一般登入成功");

      localStorage.setItem("userLoggedIn", "true");
      localStorage.setItem("jwt_token", response.data.token);

      $q.notify({
        message: "登入成功！",
        color: "positive",
        timeout: 2000,
        position: "top",
      });

      router.push({ path: "/home" });
    } else {
      $q.notify({
        message: response.data.message || "登入失敗",
        color: "negative",
        position: "top",
      });
    }
  } catch (error) {
    console.error("❌ 登入失敗：", error);

    let errorMessage = "登入失敗，請檢查帳號密碼";
    if (error.response?.status === 401) {
      errorMessage = "帳號或密碼錯誤";
    }

    $q.notify({
      message: errorMessage,
      color: "negative",
      position: "top",
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e6e9f0 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 1200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card-wrapper {
  width: 100%;
  max-width: 480px;
}

.logo-section {
  text-align: center;
  margin-bottom: 24px;
}

.company-logo {
  max-width: 200px;
  height: auto;
  filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1));
}

.login-card {
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  padding: 32px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.title-section h4 {
  font-weight: 600;
  color: #333;
  font-size: 1.8rem;
}

.input-field {
  margin-bottom: 16px;
}

.login-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.separator {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 24px 0;
}

.separator::before,
.separator::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #e0e0e0;
}

.separator-text {
  padding: 0 16px;
  color: #9e9e9e;
  font-size: 14px;
}

.line-login-btn {
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.line-login-btn:hover:not(:disabled) {
  background-color: rgba(0, 128, 0, 0.05);
}

.footer-text {
  margin-top: 32px;
  font-size: 0.8rem;
  color: #757575;
}

/* 響應式調整 */
@media (max-width: 599px) {
  .login-card {
    padding: 24px 16px;
  }

  .title-section h4 {
    font-size: 1.5rem;
  }
}
</style>
