const express = require("express");
const {
  getVclass,
  createVclass,
  updateVclass,
  deleteVclass,
} = require("../controllers/o_vclassController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 所有路由都需要認證
router.use(authMiddleware);

// 讀取主機列表
router.get("/get_Vclass", getVclass);

// 新增主機
router.post("/create_Vclass", createVclass);

// 更新主機
router.put("/update_Vclass", updateVclass);

// 刪除主機
router.delete("/delete_Vclass/:code", deleteVclass);

module.exports = router;
