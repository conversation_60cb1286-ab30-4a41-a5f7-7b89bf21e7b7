const sql = require("mssql");
const dbConfig = require("../config/db");
const nodemailer = require("nodemailer");

const statusMap = {
  waiting: "待簽核 ⏳",
  approved: "已通過 ✅",
  rejected: "已駁回 ✖️",
  canceled: "已取消 🚫",
};

const statusColorMap = {
  waiting: "#FF9800", // 橙色
  approved: "#4CAF50", // 綠色
  rejected: "#F44336", // 紅色
  canceled: "#9E9E9E", // 灰色
};

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: "<EMAIL>", // 你的郵件
    pass: "cyasfpurcxtbpgnm", // 使用應用程式專用密碼（不要用原密碼）
  },
});

// 🔹 查詢待發送通知並發送郵件
const sendPendingNotifications = async () => {
  try {
    const pool = await sql.connect(dbConfig);

    // 🔹 查詢 `Forms_notify` 中 `Status = 'pending'`
    //    只發送 `Sent = NULL` 或 `Sent 超過 24 小時` 的通知
    //    同時查詢表單發送人和單證資訊
    const result = await pool.request().query(`
        SELECT 
          fn.Form_id, 
          fn.Step_number, 
          fn.Approver_id, 
          fn.Form_status, 
          u.Email AS Approver_email,
          f.Type AS Form_type,
          fn2.Name AS Form_name,
          u2.Name AS Submitter_name,
          u2.ID AS Submitter_id,
          (SELECT TOP 1 CASE WHEN Memo IS NULL OR LTRIM(RTRIM(Memo)) = '' THEN '如擬' ELSE Memo END 
           FROM Forms_approver 
           WHERE Form_id = fn.Form_id 
           AND (Status = 'approve' OR Status = 'reject')
           ORDER BY Updated DESC) AS Approval_memo
        FROM Forms_notify fn
        JOIN Users u ON fn.Approver_id = u.ID
        JOIN Forms f ON fn.Form_id = f.Form_id
        JOIN Forms_name fn2 ON f.Type = fn2.Type
        JOIN Users u2 ON f.userid = u2.id
        WHERE fn.Status = 'pending'
        AND (fn.Sent IS NULL OR DATEDIFF(hour, fn.Sent, GETUTCDATE()) > 24)
      `);

    if (result.recordset.length === 0) {
      console.log("✅ 沒有需要發送的通知");
      return;
    }

    for (const notification of result.recordset) {
      const {
        Form_id,
        Step_number,
        Approver_id,
        Form_status,
        Approver_email,
        Form_type,
        Form_name,
        Submitter_name,
        Submitter_id,
        Approval_memo,
      } = notification;

      const statusText =
        statusMap[Form_status.trim().toLowerCase()] || "未知狀態";
      const statusColor =
        statusColorMap[Form_status.trim().toLowerCase()] || "#757575";
      const today = new Date().toLocaleDateString("zh-TW", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });

      // 根據表單狀態決定顯示的提示文字
      let notificationMessage = "";
      let buttonText = "登入系統查看";
      let showMemo = false;

      if (Form_status.trim().toLowerCase() === "approved") {
        notificationMessage = "您的表單已完成簽核，詳細資訊如下：";
        showMemo = true;
      } else if (Form_status.trim().toLowerCase() === "rejected") {
        notificationMessage = "您的表單已被駁回，詳細資訊如下：";
        showMemo = true;
      } else {
        notificationMessage = "您有一個表單需要處理，詳細資訊如下：";
      }

      const mailOptions = {
        from: '"小蒙牛資訊部" <<EMAIL>>',
        to: Approver_email,
        subject: `📌 表單通知-${Form_name}${statusText}`,
        text: `請簽核表單 ${Form_id}。\n\n📌 狀態: ${statusText}\n📝 表單類型: ${Form_name}\n👤 申請人: ${Submitter_name} (${Submitter_id})\n${
          showMemo ? `\n💬 簽核意見: ${Approval_memo}\n` : ""
        }\n🔗 請登入系統查看: https://www.mongobeef.com\n\n✅ 謝謝您的配合！`,
        html: `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>表單通知</title>
  <style>
    body {
      font-family: 'Microsoft JhengHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #1976D2;
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 5px 5px 0 0;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .content {
      background-color: white;
      padding: 25px;
      border-left: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .footer {
      background-color: #f5f5f5;
      padding: 15px;
      text-align: center;
      font-size: 12px;
      color: #777;
      border-radius: 0 0 5px 5px;
      border: 1px solid #e0e0e0;
    }
    .btn {
      display: inline-block;
      color: #1976D2;
      padding: 10px 24px;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      margin-top: 20px;
      border: 2px solid #1976D2;
      transition: all 0.3s ease;
    }
    .btn:hover {
      background-color:rgb(201, 220, 240);
      color: white;
    }
    .info-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border-radius: 4px;
      overflow: hidden;
    }
    .info-table td {
      padding: 12px 15px;
      border-bottom: 1px solid #eee;
    }
    .info-table tr:last-child td {
      border-bottom: none;
    }
    .info-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .info-table td:first-child {
      font-weight: bold;
      width: 30%;
      background-color: #f2f2f2;
    }
    .status-badge {
      display: inline-block;
      padding: 5px 12px;
      border-radius: 20px;
      color: white;
      font-weight: bold;
      background-color: ${statusColor};
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .status-badge-rejected {
      display: inline-block;
      padding: 5px 12px;
      border-radius: 20px;
      color: white;
      font-weight: bold;
      background-color: #F44336;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border: 2px solid white;
      text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }
    .logo {
      max-width: 150px;
      margin-bottom: 10px;
    }
    .highlight {
      font-weight: bold;
      color: #1976D2;
    }
    .greeting {
      font-size: 16px;
      color: #555;
      margin-bottom: 20px;
    }
    .action-container {
      text-align: center;
      margin: 30px 0;
    }
    .contact-info {
      background-color: #f9f9f9;
      padding: 12px;
      border-radius: 4px;
      margin-top: 25px;
      border-left: 4px solid #1976D2;
    }
    .memo-box {
      background-color: #f5f5f5;
      border-left: 4px solid #1976D2;
      padding: 12px 15px;
      margin: 15px 0;
      border-radius: 4px;
      font-style: italic;
    }
    .rejected-memo-box {
      background-color: #FFF8F8;
      border-left: 4px solid #F44336;
      padding: 12px 15px;
      margin: 15px 0;
      border-radius: 4px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header" ${
      Form_status.trim().toLowerCase() === "rejected"
        ? 'style="background-color: #D32F2F;"'
        : ""
    }>
      <h1>表單簽核通知</h1>
    </div>
    <div class="content">
      <p class="greeting">您好，</p>
      <p>${notificationMessage}</p>
      
      <table class="info-table">
        <tr>
          <td>表單編號：</td>
          <td><span class="highlight">${Form_id}</span></td>
        </tr>
        <tr>
          <td>表單狀態：</td>
          <td>
            ${
              Form_status.trim().toLowerCase() === "rejected"
                ? `<span class="status-badge-rejected">${statusText}</span>`
                : `<span class="status-badge">${statusText}</span>`
            }
          </td>
        </tr>
        <tr>
          <td>表單類型：</td>
          <td>${Form_name}</td>
        </tr>
        <tr>
          <td>申請人：</td>
          <td>${Submitter_name} (${Submitter_id})</td>
        </tr>
        <tr>
          <td>通知日期：</td>
          <td>${today}</td>
        </tr>
      </table>
      
      ${
        showMemo
          ? `
      <div class="${
        Form_status.trim().toLowerCase() === "rejected"
          ? "rejected-memo-box"
          : "memo-box"
      }">
        <strong>簽核意見：</strong> ${Approval_memo}
      </div>
      `
          : ""
      }
      
      <div class="action-container">
        <a href="https://www.mongobeef.com" class="btn" target="_blank" 
           ${
             Form_status.trim().toLowerCase() === "rejected"
               ? 'style="color: #D32F2F; border-color: #D32F2F;"'
               : ""
           }>${buttonText}</a>
      </div>
      
      <div class="contact-info" ${
        Form_status.trim().toLowerCase() === "rejected"
          ? 'style="border-left: 4px solid #D32F2F;"'
          : ""
      }>
        <p style="margin: 0;">如有任何問題，請聯繫資訊部。</p>
      </div>
    </div>
    <div class="footer">
      <p>此為系統自動發送郵件，請勿直接回覆。</p>
      <p>&copy; ${new Date().getFullYear()} 小蒙牛資訊部</p>
    </div>
  </div>
</body>
</html>
        `,
      };

      try {
        // 🔹 發送郵件
        await transporter.sendMail(mailOptions);

        // 🔹 更新 `Forms_notify` 狀態為 `sent`，記錄發送時間
        await pool.request().query(`
            UPDATE Forms_notify 
            SET Status = 'sent', Sent = GETUTCDATE() 
            WHERE Form_id = '${Form_id}' AND Step_number = ${Step_number}
          `);

        console.log(`📧 通知已發送: ${Form_id} -> ${Approver_email}`);
      } catch (error) {
        // 🔹 若發送失敗，記錄 `failed` 狀態
        await pool.request().query(`
            UPDATE Forms_notify 
            SET Status = 'failed' 
            WHERE Form_id = '${Form_id}' AND Step_number = ${Step_number}
          `);

        console.error(
          `❌ 發送郵件失敗: ${Form_id} -> ${Approver_email}`,
          error
        );
      }
    }
  } catch (err) {
    console.error("❌ 發送通知時發生錯誤:", err);
  }
};

// 🔹 查詢待發送公告通知並發送郵件
const sendPendingBulletinNotifications = async (bulletinId = null) => {
  try {
    const pool = await sql.connect(dbConfig);

    // 獲取待發送的通知，如果指定了bulletinId，則只獲取該公告的通知
    let query = `
      SELECT bn.*, u.Email, u.Name, b.title, b.contents, b.priority, b.required, d.Name as dep_name
      FROM Bulletin_notify bn
      JOIN Users u ON bn.target_id = u.ID
      JOIN Bulletin b ON bn.id = b.id
      JOIN dep d ON b.dep = d.id
      WHERE bn.status = 'pending'
    `;

    // 如果指定了公告ID，則只處理該公告的通知
    if (bulletinId) {
      query += ` AND bn.id = @bulletinId`;
    }

    const request = pool.request();
    if (bulletinId) {
      request.input("bulletinId", sql.NChar(10), bulletinId);
    }

    const pendingResult = await request.query(query);

    const pendingNotifications = pendingResult.recordset;
    let successCount = 0;
    let failCount = 0;

    console.log(
      `📢 找到 ${pendingNotifications.length} 條待發送的公告通知${
        bulletinId ? ` (公告ID: ${bulletinId})` : ""
      }`
    );

    // 處理每一個待發送的通知
    for (const notification of pendingNotifications) {
      try {
        const {
          id,
          target_id,
          Email,
          Name,
          title,
          contents,
          priority,
          required,
          dep_name,
        } = notification;

        // 根據優先級設定不同顏色
        let priorityColor = "#1976D2"; // 默認藍色
        let priorityText = "一般";

        switch (priority) {
          case 1:
            priorityColor = "#F44336"; // 紅色
            priorityText = "緊急";
            break;
          case 2:
            priorityColor = "#FF9800"; // 橙色
            priorityText = "重要";
            break;
          case 3:
            priorityColor = "#1976D2"; // 藍色
            priorityText = "一般";
            break;
          default:
            priorityColor = "#1976D2";
            priorityText = "一般";
        }

        // 設定郵件主題
        const subject = required === 1 ? `📢 [必讀] ${title}` : `📢 ${title}`;

        // 今天日期
        const today = new Date().toLocaleDateString("zh-TW", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });

        // 郵件選項
        const mailOptions = {
          from: '"小蒙牛資訊部" <<EMAIL>>',
          to: Email,
          subject: subject,
          text: `公告通知: ${title}\n\n📌 優先級: ${priorityText}\n📝 發布單位: ${dep_name}\n�� 發布日期: ${today}\n\n請登入系統查看完整公告內容。\n\n🔗 系統登入: https://www.mongobeef.com\n\n✅ 謝謝您的配合！`,
          html: `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>公告通知</title>
  <style>
    body {
      font-family: 'Microsoft JhengHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: ${priorityColor};
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 5px 5px 0 0;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .content {
      background-color: white;
      padding: 25px;
      border-left: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    .footer {
      background-color: #f5f5f5;
      padding: 15px;
      text-align: center;
      font-size: 12px;
      color: #777;
      border-radius: 0 0 5px 5px;
      border: 1px solid #e0e0e0;
    }
    .btn {
      display: inline-block;
      color: ${priorityColor};
      padding: 10px 24px;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      margin-top: 20px;
      border: 2px solid ${priorityColor};
      transition: all 0.3s ease;
    }
    .btn:hover {
      background-color: ${priorityColor};
      color: white;
    }
    .info-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border-radius: 4px;
      overflow: hidden;
    }
    .info-table td {
      padding: 12px 15px;
      border-bottom: 1px solid #eee;
    }
    .info-table tr:last-child td {
      border-bottom: none;
    }
    .info-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .info-table td:first-child {
      font-weight: bold;
      width: 30%;
      background-color: #f2f2f2;
    }
    .priority-badge {
      display: inline-block;
      padding: 5px 12px;
      border-radius: 20px;
      color: white;
      font-weight: bold;
      background-color: ${priorityColor};
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .required-badge {
      display: inline-block;
      padding: 5px 12px;
      border-radius: 20px;
      color: white;
      font-weight: bold;
      background-color: #F44336;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      margin-left: 10px;
    }
    .highlight {
      font-weight: bold;
      color: ${priorityColor};
    }
    .greeting {
      font-size: 16px;
      color: #555;
      margin-bottom: 20px;
    }
    .action-container {
      text-align: center;
      margin: 30px 0;
    }
    .contact-info {
      background-color: #f9f9f9;
      padding: 12px;
      border-radius: 4px;
      margin-top: 25px;
      border-left: 4px solid ${priorityColor};
    }
    .notice-box {
      background-color: #f5f5f5;
      border-left: 4px solid ${priorityColor};
      padding: 12px 15px;
      margin: 15px 0;
      border-radius: 4px;
      text-align: center;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>公告通知</h1>
      ${
        required === 1
          ? '<span style="background-color: white; color: red; padding: 5px 10px; border-radius: 4px; font-weight: bold; margin-top: 10px; display: inline-block;">必讀公告</span>'
          : ""
      }
    </div>
    <div class="content">
      <p class="greeting">${Name} 您好，</p>
      <p>您有一則新的公告通知：</p>
      
      <table class="info-table">
        <tr>
          <td>公告標題：</td>
          <td><span class="highlight">${title}</span></td>
        </tr>
        <tr>
          <td>優先級：</td>
          <td>
            <span class="priority-badge">${priorityText}</span>
            ${required === 1 ? '<span class="required-badge">必讀</span>' : ""}
          </td>
        </tr>
        <tr>
          <td>發布單位：</td>
          <td>${dep_name}</td>
        </tr>
        <tr>
          <td>通知日期：</td>
          <td>${today}</td>
        </tr>
      </table>
      
      <div class="notice-box">
        請登入系統查看完整公告內容
      </div>
      
      <div class="action-container">
        <a href="https://www.mongobeef.com" class="btn" target="_blank">立即登入查看</a>
      </div>
      
      <div class="contact-info">
        <p style="margin: 0;">如有任何問題，請聯繫資訊部。</p>
      </div>
    </div>
    <div class="footer">
      <p>此為系統自動發送郵件，請勿直接回覆。</p>
      <p>&copy; ${new Date().getFullYear()} 小蒙牛資訊部</p>
    </div>
  </div>
</body>
</html>
          `,
        };

        // 發送郵件
        await transporter.sendMail(mailOptions);

        // 更新通知狀態為已發送
        await pool
          .request()
          .input("id", sql.NChar(10), id)
          .input("target_id", sql.NChar(6), target_id)
          .input("sent", sql.DateTime, new Date()).query(`
            UPDATE Bulletin_notify
            SET status = 'sent', Sent = @sent
            WHERE id = @id AND target_id = @target_id
          `);

        console.log(`📧 公告通知已發送: ${id} -> ${Email}`);
        successCount++;
      } catch (error) {
        console.error("發送公告通知失敗:", error);

        // 標記為發送失敗
        await pool
          .request()
          .input("id", sql.NChar(10), notification.id)
          .input("target_id", sql.NChar(6), notification.target_id).query(`
            UPDATE Bulletin_notify
            SET status = 'failed'
            WHERE id = @id AND target_id = @target_id
          `);

        failCount++;
      }
    }

    console.log(`📢 公告通知發送完成: 成功 ${successCount}, 失敗 ${failCount}`);
    return { success: successCount, fail: failCount };
  } catch (error) {
    console.error("處理待發送公告通知時發生錯誤:", error);
    throw error;
  }
};

// 🔹 匯出函式，讓其他模組可以使用
module.exports = {
  sendPendingNotifications,
  sendPendingBulletinNotifications,
};
