<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="
        width: 100%;
        max-width: 500px;
        overflow: hidden;
        border-radius: 12px;
      "
    >
      <!-- 標題 -->
      <q-card>
        <q-card-section class="text-center">
          <div class="text-h6 text-primary">消費結帳</div>
        </q-card-section>

        <!-- 查詢條件：取消左右 padding -->
        <q-card-section class="q-px-none">
          <div class="row items-center no-wrap">
            <q-input
              v-model="filters.date"
              mask="####-##-##"
              placeholder="YYYY-MM-DD"
              label="交易日期"
              dense
              outlined
              square
              class="col"
              readonly
            >
              <!-- 左邊 prepend 日期 icon -->
              <template v-slot:prepend>
                <q-icon name="calendar_month" />
              </template>

              <!-- 右邊 append 為可點選的 icon + popup 日期選單 -->
              <template v-slot:append>
                <q-icon
                  name="edit_calendar"
                  color="primary"
                  class="cursor-pointer"
                  size="18px"
                >
                  <q-popup-proxy
                    ref="datePopup"
                    transition-show="scale"
                    transition-hide="scale"
                    cover
                  >
                    <q-date
                      v-model="filters.date"
                      mask="YYYY-MM-DD"
                      @update:model-value="closeDatePopup"
                    >
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>

            <q-btn
              flat
              icon="tune"
              round
              class="q-ml-sm"
              @click="advancedSearch = true"
            />
          </div>
        </q-card-section>
      </q-card>

      <!-- 訂單列表 -->
      <q-list bordered separator>
        <q-expansion-item
          v-for="order in pagedOrders"
          :key="order.code"
          expand-separator
          :label="`單號：${order.code}`"
        >
          <template v-slot:header>
            <q-item-section>
              <q-item-label class="text-weight-bold">
                {{ order.code }}
              </q-item-label>

              <q-item-label caption class="row items-center no-wrap">
                <q-badge
                  outline
                  dense
                  class="q-mr-sm"
                  :label="getCheckoutInfo(order.checkout).label"
                  :color="getCheckoutInfo(order.checkout).color"
                />
                <template v-if="order.ino">
                  <span :class="{ 'text-strike': order.chk === '1' }">{{
                    order.ino
                  }}</span>
                </template>
              </q-item-label>
            </q-item-section>

            <q-item-section side class="text-right">
              <div class="text-bold text-red">${{ order.Amount }}</div>
            </q-item-section>
          </template>

          <q-card>
            <q-card-section class="q-pa-sm q-gutter-y-sm">
              <!-- 🧾 基本明細 -->
              <div class="row">
                <div class="col text-grey-8">桌號</div>
                <div class="col-auto text-right">{{ order.name }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">日期</div>
                <div class="col-auto text-right">{{ order.ndate }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">開桌時間</div>
                <div class="col-auto text-right">{{ order.rtime }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">結帳時間</div>
                <div class="col-auto text-right">{{ order.ctime }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">用餐人數</div>
                <div class="col-auto text-right">{{ order.person }} 人</div>
              </div>
              <div class="row" v-if="order.invo !== null">
                <div class="col text-grey-8">統一編號</div>
                <div class="col-auto text-right">{{ order.invo }}</div>
              </div>

              <!-- 💰 結帳金額 -->
              <div class="q-mt-sm text-subtitle2">金額明細</div>
              <div class="row">
                <div class="col text-grey-8">消費金額</div>
                <div class="col-auto text-right">${{ order.vamt }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">服務費</div>
                <div class="col-auto text-right">${{ order.stot }}</div>
              </div>
              <div class="row">
                <div class="col text-grey-8">折扣</div>
                <div class="col-auto text-right text-negative">
                  -${{ order.dtot }}
                </div>
              </div>
              <div class="row">
                <div class="col text-grey-8">招待</div>
                <div class="col-auto text-right text-negative">
                  -${{ order.ramt }}
                </div>
              </div>
              <div class="row">
                <div class="col text-grey-8">補差額</div>
                <div class="col-auto text-right">${{ order.tamt }}</div>
              </div>

              <!-- 💳 付款方式（摺疊） -->
              <q-expansion-item
                dense
                label="付款方式"
                icon="payments"
                class="q-mt-sm"
              >
                <div class="q-gutter-y-xs">
                  <div class="row">
                    <div class="col text-grey-8">現金</div>
                    <div class="col-auto text-right">${{ order.cntot }}</div>
                  </div>
                  <div class="row">
                    <div class="col text-grey-8">信用卡</div>
                    <div class="col-auto text-right">${{ order.cdtot }}</div>
                  </div>
                  <div class="row">
                    <div class="col text-grey-8">其他付款</div>
                    <div class="col-auto text-right">${{ order.chtot }}</div>
                  </div>
                  <div class="row">
                    <div class="col text-grey-8">折讓</div>
                    <div class="col-auto text-negative text-right">
                      -${{ order.damt1 }}
                    </div>
                  </div>

                  <!-- 其他付款明細（摺疊） -->
                  <q-expansion-item
                    v-if="order.chtot !== `0`"
                    dense
                    label="其他付款明細"
                    icon="account_balance_wallet"
                    class="q-mt-sm"
                    @show="() => loadOtherPayments(order)"
                  >
                    <div class="q-pl-sm q-mt-sm q-gutter-sm">
                      <div
                        v-for="(p, i) in order.otherPayments"
                        :key="i"
                        class="row items-center justify-between"
                      >
                        <div class="text-body2 text-dark">{{ p.name }}</div>
                        <div class="text-right text-bold">${{ p.amt }}</div>
                      </div>
                    </div>
                  </q-expansion-item>
                </div>
              </q-expansion-item>

              <!-- 功能按鈕 -->
              <div class="q-mt-md row q-gutter-sm">
                <q-btn
                  label="訂單明細"
                  flat
                  color="secondary"
                  icon="restaurant"
                  @click="showDiningDetails(order)"
                  class="col"
                />
                <q-btn
                  :label="order.checkout === '2' ? '還原訂單' : '作廢訂單'"
                  :color="order.checkout === '2' ? 'positive' : 'negative'"
                  icon="history"
                  flat
                  class="col"
                  @click="toggleCancel(order)"
                />
              </div>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </q-list>
      <q-pagination
        v-model="currentPage"
        :max="Math.ceil(orders.length / rowsPerPage)"
        max-pages="7"
        boundary-numbers
        color="primary"
        class="q-mt-md flex justify-center"
      />
    </q-card>

    <!-- 進階查詢 Dialog -->
    <q-dialog v-model="advancedSearch">
      <q-card style="min-width: 300px; max-width: 400px">
        <q-card-section class="text-h6 text-primary">進階查詢</q-card-section>
        <q-card-section class="q-gutter-md">
          <q-input
            v-model="filters.sdate"
            mask="####-##-##"
            placeholder="YYYY-MM-DD"
            label="交易日期"
            dense
            filled
            square
            clearable
          />
          <q-input
            v-model="filters.code"
            label="交易單號"
            filled
            dense
            square
            clearable
          />
          <q-input
            v-model="filters.ino"
            label="發票號碼"
            filled
            dense
            square
            clearable
          />
          <q-input
            v-model="filters.invo"
            label="統一編號"
            filled
            dense
            square
            clearable
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="關閉" v-close-popup />
          <q-btn
            label="查詢"
            color="primary"
            @click="
              AsearchData();
              advancedSearch = false;
            "
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 用餐明細 Dialog -->
    <q-dialog v-model="diningDialog">
      <q-card style="min-width: 320px; max-width: 540px">
        <!-- 標題列 -->
        <q-card-section class="q-px-md q-pt-md q-pb-sm">
          <div class="row items-center justify-between">
            <!-- 左邊：圖示 + 主標題 -->
            <div class="row items-center">
              <q-avatar
                icon="receipt_long"
                color="secondary"
                text-color="white"
                class="q-mr-sm"
              />
              <div class="column">
                <div class="text-h6 text-secondary">訂單明細</div>
              </div>
            </div>

            <!-- 右邊：Toggle -->
            <q-toggle
              v-model="showZero"
              label="0元"
              dense
              color="deep-orange"
              class="text-grey-7"
            />
          </div>
        </q-card-section>

        <q-separator />

        <!-- 明細清單 -->
        <q-card-section
          style="max-height: 400px"
          class="scroll q-pt-sm q-px-sm"
        >
          <q-list dense>
            <q-item
              v-for="(dish, i) in currentDiningDetails"
              :key="i"
              class="q-py-sm"
              v-show="showZero || Number(dish.OPRICE) !== 0"
            >
              <q-item-section>
                <div class="text-subtitle2 text-bold row items-center no-wrap">
                  <div class="ellipsis">{{ dish.NAME }} × {{ dish.QTY }}</div>
                  <q-badge
                    v-if="dish.DAMT && Number(dish.DAMT) !== 100"
                    outline
                    color="red-12"
                    :label="`${Number(dish.DAMT)}折`"
                    style="font-size: 11px; margin-left: 8px"
                    class="q-ml-xs"
                  />
                  <q-badge
                    v-if="dish.DAMT && dish.R1 === `G`"
                    outline
                    color="negative"
                    :label="`招待`"
                    style="font-size: 11px; margin-left: 8px"
                    class="q-ml-xs"
                  />
                </div>

                <div class="row justify-between items-center q-mt-xs">
                  <!-- 規格 -->
                  <div v-if="dish.SNAME" class="text-caption text-grey-7">
                    規格：{{ dish.SNAME }}
                  </div>

                  <!-- 折扣 Badge -->
                </div>

                <div
                  class="row justify-between text-caption text-grey-8 q-mt-xs"
                >
                  <div>單價：${{ dish.OPRICE }}</div>
                  <div
                    :class="{
                      'text-primary': dish.OPRICE * dish.QTY >= 0,
                      'text-negative': dish.OPRICE * dish.QTY < 0,
                    }"
                  >
                    小計：${{ dish.OPRICE * dish.QTY }}
                  </div>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 動作列 -->
        <q-card-actions align="right">
          <q-btn flat label="關閉" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const $q = useQuasar();
const today = new Date().toISOString().slice(0, 10);
const datePopup = ref(null);
const closeDatePopup = () => {
  datePopup.value?.hide(); // ✅ 手動關閉 popup
};
const filters = ref({
  date: today,
  code: "",
  ino: "",
  invo: "",
  sdate: "",
});

const advancedSearch = ref(false);
const diningDialog = ref(false);
const showZero = ref(false);
const currentDiningDetails = ref([]);
const props = defineProps({
  Branch: String,
  Permissions: {
    type: [String, Array],
    default: () => [],
  },
});
const orders = ref([]);

const currentPage = ref(1); // 當前頁數
const rowsPerPage = 7; // 每頁筆數

const pagedOrders = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage;
  return orders.value.slice(start, start + rowsPerPage);
});

const getCheckoutInfo = (code) => {
  return checkoutMap[code] || { label: "未知", color: "grey" };
};
const checkoutMap = {
  0: { label: "開桌", color: "green" },
  1: { label: "結帳", color: "primary" },
  2: { label: "作廢", color: "red" },
  5: { label: "併帳", color: "orange" },
};

const getOrders = async () => {
  try {
    //console.log(props.Branch);
    //console.log(filters.value);

    const response = await apiClient.get(`${apiBaseUrl}/order/get_Orders`, {
      params: {
        branch: props.Branch,
        date: filters.value.date ? filters.value.date.replace(/-/g, "/") : "",
        invo: filters.value.invo,
        code: filters.value.code,
        ino: filters.value.ino,
      },
    });
    orders.value = response.data;
    //console.log(orders.value);
  } catch (err) {
    console.error("❌ 查詢訂單失敗:", err);
  }
};

const searchData = () => {
  const f = filters.value;
  f.code = "";
  f.invo = "";
  f.ino = "";
  f.sdate = "";
  getOrders();
};

const showDiningDetails = async (order) => {
  try {
    const res = await apiClient.get(`${apiBaseUrl}/order/get_Ordermainn`, {
      params: {
        scode: order.code,
        branch: props.Branch,
      },
    });

    currentDiningDetails.value = res.data || [];
    diningDialog.value = true;
  } catch (err) {
    $q.notify({
      type: "negative",
      message: "載入訂單明細失敗",
    });
  }
};

const filteredDiningDetails = computed(() => currentDiningDetails.value);

const loadOtherPayments = async (order) => {
  // 如果已有資料就不重抓（避免重複 request）
  if (order.otherPaymentsLoaded || Number(order.chtot) <= 0) return;

  try {
    const response = await apiClient.get(
      `${apiBaseUrl}/order/get_Orderotherpay`,
      {
        params: {
          code: order.code,
          branch: props.Branch,
        },
      }
    );

    order.otherPayments = response.data || [];
    order.otherPaymentsLoaded = true; // 標記為已載入
    //console.log(order.otherPayments);
  } catch (err) {
    $q.notify({
      type: "negative",
      message: "載入其他付款明細失敗",
    });
  }
};

const toggleCancel = async (item) => {
  let newStatus = null;

  if (item.checkout === "1") {
    newStatus = "2"; // 作廢
  } else if (item.checkout === "2") {
    newStatus = "1"; // 還原
  } else {
    $q.notify({
      type: "warning",
      message: "只能作廢或還原已結帳 / 已作廢的訂單",
    });
    return;
  }

  try {
    const response = await apiClient.post(`${apiBaseUrl}/order/update_Status`, {
      branch: props.Branch,
      code: item.code,
      checkout: newStatus,
      group: props.Permissions, // 傳入群組權限，現在可能是數組
    });

    if (response.data.success) {
      item.checkout = newStatus;
      item.chk = newStatus === "2" ? "1" : "0";
      $q.notify({
        type: "positive",
        message: newStatus === "2" ? "訂單已作廢" : "訂單已還原",
      });
    } else {
      // 錯誤但非權限錯誤的情況（如邏輯錯誤）
      throw new Error(response.data.message || "更新失敗");
    }
  } catch (err) {
    if (err.response?.status === 403) {
      // ❌ 權限不足
      $q.notify({
        type: "negative",
        message: "無此操作權限",
      });
    } else {
      // 其他錯誤
      $q.notify({
        type: "negative",
        message: "更新訂單狀態失敗",
      });
    }
  }
};

const AsearchData = () => {
  const f = filters.value;

  const hasAnyFilter = !!f.sdate || !!f.code || !!f.invo || !!f.ino;

  if (hasAnyFilter) {
    filters.value.date = filters.value.sdate;
    getOrders();
  } else {
    $q.notify({ type: "warning", message: "未輸入條件" });
  }
};

watch(
  () => filters.value.date,
  (newDate) => {
    if (newDate && newDate.length === 10) {
      searchData();
    }
  }
);

onMounted(async () => {
  if (!props.Branch) {
    $q.notify({
      type: "warning",
      message: "請先選擇門市！",
    });
  } else {
    await getOrders();
  }
});
</script>
