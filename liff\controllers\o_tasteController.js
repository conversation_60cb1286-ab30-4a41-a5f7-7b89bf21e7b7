const { getPoolByBranch, sql } = require("../services/dbPoolManager");

const getonlineTaste = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少參數 branch 門市代號" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const [groupsResult, itemsResult] = await Promise.all([
      pool.request().query(`
        SELECT taste_code, taste_title, c_min, c_max, sort
        FROM TasteGroup
        ORDER BY sort
      `),
      pool.request().query(`
        SELECT taste, sno, name, price
        FROM TasteItems
        ORDER BY taste, sno
      `),
    ]);

    const groups = groupsResult.recordset;
    const items = itemsResult.recordset;

    const data = groups.map((group) => {
      const groupItems = items.filter(
        (item) => item.taste === group.taste_code
      );
      return {
        group_id: group.taste_code, // ⬅️ 改這裡
        group_name: group.taste_title,
        c_min: group.c_min,
        c_max: group.c_max,
        sort: group.sort,
        items: groupItems.map((i) => ({
          sno: i.sno,
          name: i.name,
          price: i.price || 0,
        })),
      };
    });

    res.json({ success: true, data });
  } catch (err) {
    console.error("❌ 載入口味資料失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveTasteGroups = async (req, res) => {
  const { branch, groups } = req.body;

  if (!branch || !Array.isArray(groups)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    const request = transaction.request();

    for (const group of groups) {
      let groupId = group.group_id;

      // ✅ 若無 group_id，則產生 taste_code（如 T00001）
      if (!groupId) {
        const result = await request.query(`
          SELECT ISNULL(MAX(taste_code), 'T00000') AS max_code FROM TasteGroup
        `);

        const maxCode = result.recordset[0].max_code;
        const nextNumber = parseInt(maxCode.slice(1)) + 1;
        groupId = "T" + String(nextNumber).padStart(5, "0");
      }

      // ✅ upsert TasteGroup
      const existsResult = await request.query(`
        SELECT COUNT(*) AS count FROM TasteGroup WHERE taste_code = '${groupId}'
      `);

      if (existsResult.recordset[0].count > 0) {
        await request.query(`
          UPDATE TasteGroup
          SET taste_title = N'${group.group_name}',
              c_min = ${group.c_min},
              c_max = ${group.c_max},
              sort = ${group.sort}
          WHERE taste_code = '${groupId}'
        `);

        await request.query(`
          DELETE FROM TasteItems WHERE taste = '${groupId}'
        `);
      } else {
        await request.query(`
          INSERT INTO TasteGroup (taste_code, taste_title, c_min, c_max, sort)
          VALUES ('${groupId}', N'${group.group_name}', ${group.c_min}, ${group.c_max}, ${group.sort})
        `);
      }

      // ✅ 寫入 taste items
      if (Array.isArray(group.items)) {
        for (let i = 0; i < group.items.length; i++) {
          const item = group.items[i];
          await request.query(`
            INSERT INTO TasteItems (taste, sno, name, price)
            VALUES (
              '${groupId}',
              ${item.sno ?? i},
              N'${item.name}',
              ${item.price || 0}
            )
          `);
        }
      }
    }

    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存口味群組失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const deleteTasteGroup = async (req, res) => {
  const { branch, group_id } = req.body;

  if (!branch || !group_id) {
    return res.status(400).json({ success: false, message: "缺少參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const request = pool.request();

    await request.query(`DELETE FROM TasteItems WHERE taste = '${group_id}'`);
    await request.query(
      `DELETE FROM TasteGroup WHERE taste_code = '${group_id}'`
    );

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 刪除口味群組失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveGroupSort = async (req, res) => {
  const { branch, groups } = req.body;

  if (!branch || !Array.isArray(groups)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    for (const group of groups) {
      const request = transaction.request(); // ✅ 每次新建

      await request
        .input("group_id", sql.VarChar, group.group_id)
        .input("sort", sql.Int, group.sort).query(`
          UPDATE TasteGroup
          SET sort = @sort
          WHERE taste_code = @group_id
        `);
    }

    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存群組排序失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveTasteItemSort = async (req, res) => {
  const { branch, group_id, items } = req.body;

  if (!branch || !group_id || !Array.isArray(items)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    // ❌ 原本這裡不能重用 request
    // ✅ 用新的 request
    const deleteRequest = transaction.request();
    await deleteRequest
      .input("group_id", sql.VarChar, group_id)
      .query("DELETE FROM TasteItems WHERE taste = @group_id");

    for (const item of items) {
      const insertRequest = transaction.request(); // ✅ 新建 request

      await insertRequest
        .input("group_id", sql.VarChar, group_id)
        .input("sno", sql.Int, item.sno)
        .input("name", sql.NVarChar, item.name)
        .input("price", sql.Int, item.price || 0).query(`
          INSERT INTO TasteItems (taste, sno, name, price)
          VALUES (@group_id, @sno, @name, @price)
        `);
    }

    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存口味排序失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const getTasteOptions = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ success: false, message: "缺少 branch" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().query(`
      SELECT DISTINCT name, price FROM Taste ORDER BY name
    `);

    res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ 載入口味選項失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const addTasteItems = async (req, res) => {
  const { branch, group_id, items } = req.body;

  if (!branch || !group_id || !Array.isArray(items)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    // 取得目前該群組已有的最大 sno
    const request = transaction.request();
    const snoResult = await request
      .input("group_id", sql.VarChar, group_id)
      .query(
        `SELECT ISNULL(MAX(sno), 0) AS max_sno FROM TasteItems WHERE taste = @group_id`
      );

    let sno = snoResult.recordset[0].max_sno;

    for (const item of items) {
      sno += 1;

      const insertRequest = transaction.request();
      await insertRequest
        .input("group_id", sql.VarChar, group_id)
        .input("sno", sql.Int, sno)
        .input("name", sql.NVarChar, item.name)
        .input("price", sql.Int, item.price || 0).query(`
          INSERT INTO TasteItems (taste, sno, name, price)
          VALUES (@group_id, @sno, @name, @price)
        `);
    }

    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    console.error("❌ 新增口味項目失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};
const deleteTasteItem = async (req, res) => {
  const { branch, group_id, sno } = req.body;

  if (!branch || !group_id || typeof sno !== "number") {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const request = pool.request();

    await request
      .input("group_id", sql.VarChar, group_id)
      .input("sno", sql.Int, sno).query(`
        DELETE FROM TasteItems
        WHERE taste = @group_id AND sno = @sno
      `);

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 刪除口味失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getonlineTaste,
  saveTasteGroups,
  deleteTasteGroup,
  saveGroupSort,
  saveTasteItemSort,
  getTasteOptions,
  addTasteItems,
  deleteTasteItem,
};
