require("dotenv").config();
// config/getDbConfig.js
const getDbConfig = (ip) => {
  if (!ip) throw new Error("❌ 缺少 IP");

  return {
    user: process.env.BRANCH_USER,
    password: process.env.BR<PERSON>CH_PASSWORD,
    server: ip.trim(), // 移除空白
    database: process.env.BRANCH_DB_NAME,
    port: Number(process.env.BRANCH_PORT) || 1433, // 預設 1433

    options: {
      encrypt: false, // 若使用 Azure 請改 true
      trustServerCertificate: true,
      connectTimeout: 1000, // 連線 timeout 1 秒
    },

    pool: {
      max: 10,
      min: 0,
      idleTimeoutMillis: 30000,
    },
  };
};

module.exports = getDbConfig;
