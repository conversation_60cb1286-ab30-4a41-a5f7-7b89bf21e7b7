const sql = require("mssql");
const { DateTime } = require("luxon");
require("dotenv").config();

const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");
  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

const getClassStats = async (req, res) => {
  const { branch, from, to, sortBy, includeZero } = req.query;

  if (!from || !to) {
    return res.status(400).json({ success: false, message: "缺少日期區間" });
  }

  const fromFormatted = DateTime.fromISO(from).toFormat("yyyy/MM/dd");
  const toFormatted = DateTime.fromISO(to).toFormat("yyyy/MM/dd");

  let orderClause = "ORDER BY classCode, OrderMainN.name";
  if (sortBy === "qty") {
    orderClause = "ORDER BY qty DESC";
  } else if (sortBy === "amount") {
    orderClause = "ORDER BY totalAmount DESC";
  } else if (sortBy === "code") {
    orderClause = "ORDER BY OrderMainN.code ";
  }

  // 0 元排除條件
  let zeroFilter = "";
  if (includeZero === "false") {
    zeroFilter = " AND OrderMainN.oprice <> 0 ";
  }

  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    const result = await pool
      .request()
      .input("from", sql.VarChar, fromFormatted)
      .input("to", sql.VarChar, toFormatted).query(`
        SELECT 
        ISNULL(Cclass.code, 'UNDEFINED') AS classCode,
        ISNULL(Cclass.name, '未分類') AS className,
        OrderMainN.code,
        OrderMainN.name,
        OrderMainN.sname,
        ISNULL(OrderMainN.oremname,'') AS oremname,
        OrderMainN.oprice,
        OrderMainN.damt,
        SUM(OrderMainN.qty) AS qty,
        SUM(OrderMainN.qty * OrderMainN.oprice * (OrderMainN.damt / 100.0)) AS totalAmount
      FROM OrderMainN
      JOIN OrderNow ON OrderNow.code = OrderMainN.scode AND OrderNow.checkout = 1
      LEFT JOIN Omenum ON Omenum.code = OrderMainN.code
      LEFT JOIN Cclass ON Cclass.code = Omenum.cclass
      WHERE OrderNow.ndate BETWEEN @from AND @to
        ${zeroFilter}
      GROUP BY
        ISNULL(Cclass.code, 'UNDEFINED'),
        ISNULL(Cclass.name, '未分類'),
        OrderMainN.code,
        OrderMainN.name,
        OrderMainN.sname,
        ISNULL(OrderMainN.oremname,''),
        OrderMainN.oprice,
        OrderMainN.damt
      ${orderClause}
      `);

    const rows = result.recordset;

    // 分類成分組
    const grouped = {};
    for (const row of rows) {
      if (!grouped[row.className]) {
        grouped[row.className] = [];
      }
      grouped[row.className].push({
        code: row.code,
        name: row.name,
        sname: row.sname,
        qty: row.qty,
        oprice: row.oprice,
        damt: row.damt,
        oremname: row.oremname,
      });
    }

    const finalData = Object.entries(grouped).map(([className, details]) => ({
      className,
      details,
    }));

    res.json({
      success: true,
      from: req.body.from,
      to: req.body.to,
      data: finalData,
    });
  } catch (err) {
    console.error("❌ 查詢類別統計錯誤:", err);
    res.status(500).json({ success: false, message: "查詢失敗" });
  }
};

module.exports = {
  getClassStats,
};
