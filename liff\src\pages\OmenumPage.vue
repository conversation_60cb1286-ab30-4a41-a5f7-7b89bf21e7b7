<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <q-card class="q-pa-sm no-shadow" style="width: 100%; border-radius: 12px">
      <!-- 標題 -->
      <q-card-section class="text-h6 text-primary text-center"
        >商品管理</q-card-section
      >
      <!-- 搜尋和篩選 -->
      <q-card-section class="q-pa-sm">
        <div class="row q-col-gutter-sm items-center">
          <!-- 類部選擇 -->
          <div class="col-12 col-md-4">
            <q-select
              v-model="selectedCclass"
              :options="cclassOptions"
              label="選擇類部"
              dense
              outlined
              option-label="label"
              option-value="value"
              emit-value
              map-options
              :clearable="false"
              @update:model-value="onCclassChange"
            >
              <template #prepend>
                <q-icon name="category" color="primary" />
              </template>
            </q-select>
          </div>
          <!-- 搜尋框 -->
          <div class="col-12 col-md-6">
            <q-input
              v-model="searchKeyword"
              label="搜尋商品"
              dense
              outlined
              clearable
              debounce="300"
              @update:model-value="onSearch"
            >
              <template #prepend>
                <q-icon name="search" color="primary" />
              </template>
            </q-input>
          </div>
          <!-- 新增按鈕 -->
          <div class="col-12 col-md-2">
            <q-btn
              color="green"
              icon="add"
              label="新增商品"
              class="full-width"
              @click="openMenuDialog()"
            />
          </div>
        </div>
      </q-card-section>
      <!-- 菜單清單 -->
      <q-card-section class="q-pa-sm">
        <div v-if="loading" class="text-center q-pa-lg">
          <q-spinner-dots color="primary" size="40px" />
          <div class="text-grey q-mt-sm">載入中...</div>
        </div>
        <div v-else-if="filteredMenus.length === 0" class="text-center q-pa-lg">
          <q-icon name="restaurant_menu" size="60px" color="grey-4" />
          <div class="text-grey q-mt-sm">
            {{ searchKeyword ? "沒有找到符合的商品" : "請選擇類部或搜尋商品" }}
          </div>
        </div>
        <q-list separator v-else>
          <div
            v-for="menu in filteredMenus"
            :key="menu.code"
            class="bg-white q-mb-sm"
            style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
          >
            <q-expansion-item
              expand-separator
              class="q-pa-none"
              header-class="text-indigo"
            >
              <!-- 菜單主列 -->
              <template #header>
                <q-item
                  dense
                  class="q-pa-none q-gutter-none items-center"
                  style="width: 100%; position: relative"
                >
                  <!-- 菜單資訊 -->
                  <q-item-section>
                    <div class="row items-center">
                      <!-- 狀態 QBadge 放最前面，垂直置中 -->
                      <q-badge
                        outline
                        :label="statusMap[menu.omenu] || '未知'"
                        :color="
                          menu.omenu === 1
                            ? 'secondary'
                            : menu.omenu === 3
                            ? 'negative'
                            : 'grey'
                        "
                        class="q-mr-sm"
                        style="
                          font-size: 13px;

                          display: flex;
                          align-items: center;
                          justify-content: center;
                        "
                      />
                      <!-- 顏色指示器 -->
                      <div
                        v-if="menu.mcolor"
                        class="q-mr-sm"
                        :style="{
                          width: '16px',
                          height: '16px',
                          backgroundColor: colorMap[menu.mcolor],
                          border: '1px solid #ccc',
                          borderRadius: '20%',
                        }"
                      />
                      <div class="text-indigo-10">
                        {{ menu.simname || menu.code }}
                      </div>
                    </div>
                    <div class="text-caption text-grey-8">
                      <span class="text-brown-10">{{ menu.code }}</span>
                      <!-- 不顯示類型 -->
                      | 廚房：{{ getKitchenName(menu.c1) || "未設定" }}
                    </div>
                  </q-item-section>
                  <!-- 最右側展開箭頭 -->
                  <q-item-section side />
                  <!-- 編輯按鈕 -->
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="edit"
                    color="primary"
                    @click.stop="openMenuDialog(menu)"
                    style="
                      position: absolute;
                      right: 14px;
                      top: 50%;
                      transform: translateY(-50%);
                      z-index: 1;
                    "
                  />
                </q-item>
              </template>
              <!-- 詳細內容 -->
              <q-card-section class="q-pa-sm q-pt-none bg-grey-1">
                <!-- 基本資訊 -->
                <div class="row q-mb-sm">
                  <div class="col-12">
                    <q-icon
                      name="label"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    全稱：{{ menu.name }}
                  </div>
                </div>
                <div class="row q-col-gutter-sm q-mb-md">
                  <div class="col-6">
                    <q-icon
                      name="category"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    類部：{{ getCclassName(menu.cclass) }}
                  </div>
                  <div class="col-6">
                    <q-icon
                      name="restaurant"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    計價方式：{{ typeMap[menu.type] || "未知" }}
                  </div>
                  <div class="col-6">
                    <q-icon
                      name="visibility"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    狀態：{{ statusMap[menu.omenu] || "未知" }}
                  </div>
                  <div class="col-6">
                    <q-icon
                      name="kitchen"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    廚房：{{ getKitchenName(menu.c1) || "未設定" }}
                  </div>
                  <div class="col-6" v-if="menu.unit">
                    <q-icon
                      name="straighten"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    單位：{{ menu.unit }}
                  </div>

                  <div class="col-6" v-if="menu.ct1">
                    <q-icon
                      name="limit"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    數量限制：{{ menu.ct1 }}
                  </div>
                  <div class="col-6" v-if="menu.b1">
                    <q-icon
                      name="restaurant_menu"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    餐別：{{ orderTypeMap[menu.b1] || "未知" }}
                  </div>
                  <div class="col-12" v-if="menu.rcode && menu.rname">
                    <q-icon
                      name="numbers"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    實際金額：{{ menu.rcode }} | {{ menu.rname }}
                    <span v-if="menu.rm_n1" class="text-grey-6"
                      >({{ menu.rm_n1 }})</span
                    >
                  </div>

                  <div class="col-12" v-if="menu.k1 && menu.k2">
                    <q-icon
                      name="grid_on"
                      size="16px"
                      class="q-mr-xs text-primary"
                    />
                    位置：第 {{ menu.k1 }} 頁第 {{ menu.k2 }} 個
                  </div>
                </div>
                <!-- 實際金額明細 -->

                <!-- 規格清單（僅瀏覽） -->
                <div v-if="menu.specs && menu.specs.length" class="q-mb-md">
                  <div
                    class="row items-center text-subtitle2 text-primary q-pa-sm q-mb-sm rounded-borders bg-blue-1"
                  >
                    <q-icon name="list" class="q-mr-sm" />
                    規格清單
                  </div>
                  <div
                    v-for="spec in menu.specs"
                    :key="spec.sno"
                    class="q-mb-xs"
                  >
                    <q-item class="bg-grey-2 q-pa-sm rounded-borders">
                      <q-item-section>
                        <div class="text-subtitle2">{{ spec.name }}</div>
                        <div class="text-caption text-grey">
                          {{ spec.oprice >= 0 ? "+" : "" }}{{ spec.oprice }} 元
                        </div>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>

                <!-- 口味群組清單（僅瀏覽） -->
                <div
                  v-if="menu.taste_groups && menu.taste_groups.length"
                  class="q-mb-md"
                >
                  <div
                    class="row items-center text-subtitle2 text-primary q-pa-sm q-mb-sm rounded-borders bg-orange-1"
                  >
                    <q-icon name="restaurant" class="q-mr-sm" />
                    口味群組
                  </div>
                  <div
                    v-for="taste in menu.taste_groups"
                    :key="taste.taste_code"
                    class="q-mb-xs"
                  >
                    <q-item class="bg-grey-2 q-pa-sm rounded-borders">
                      <q-item-section>
                        <div class="text-subtitle2">
                          {{ taste.taste_title }}
                        </div>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>

                <!-- 套餐對映清單（僅瀏覽） -->
                <div
                  v-if="menu.sets_mappings && menu.sets_mappings.length"
                  class="q-mb-md"
                >
                  <div
                    class="row items-center text-subtitle2 text-primary q-pa-sm q-mb-sm rounded-borders bg-green-1"
                  >
                    <q-icon name="fastfood" class="q-mr-sm" />
                    套餐對映
                  </div>
                  <div
                    v-for="mapping in menu.sets_mappings"
                    :key="mapping.sets_code"
                    class="q-mb-xs"
                  >
                    <q-item class="bg-grey-2 q-pa-sm rounded-borders">
                      <q-item-section>
                        <div class="text-subtitle2">
                          {{ mapping.sname }} | {{ mapping.sets_title }}
                        </div>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>
                <!-- 刪除按鈕 -->
                <div class="row justify-end q-mt-sm">
                  <q-btn
                    flat
                    dense
                    icon="delete"
                    label="刪除商品"
                    color="negative"
                    @click.stop="openDeleteDialog(menu)"
                    class="q-px-sm"
                  />
                </div>
              </q-card-section>
            </q-expansion-item>
          </div>
        </q-list>
      </q-card-section>
    </q-card>

    <!-- 編輯菜單 Dialog -->
    <q-dialog v-model="menuDialog.show" persistent>
      <q-card
        style="
          width: 100%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
        "
      >
        <!-- 固定標題 -->
        <q-card-section
          class="row items-center justify-between q-pb-none"
          style="position: sticky; top: 0; z-index: 2; background: #fff"
        >
          <div class="text-h6 text-primary">
            {{ menuDialog.isEdit ? "編輯商品" : "新增商品" }}
          </div>
          <q-btn
            icon="close"
            flat
            round
            dense
            @click="menuDialog.show = false"
          />
        </q-card-section>
        <!-- 其餘內容正常滾動 -->
        <q-form @submit.prevent="saveMenu">
          <!-- 基本資訊 -->
          <q-card-section class="q-pt-md q-gutter-md">
            <div class="text-subtitle2 text-primary q-mb-sm">基本資訊</div>
            <div class="row q-col-gutter-sm">
              <div class="col-4">
                <q-select
                  v-model="menuDialog.data.omenu"
                  :options="statusOptions"
                  label="狀態"
                  dense
                  emit-value
                  map-options
                  @update:model-value="onOmenuChange"
                />
              </div>
              <div class="col-8">
                <q-select
                  v-model="menuDialog.data.cclass"
                  :options="cclassOptions"
                  label="類部"
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-4">
                <q-input
                  v-model="menuDialog.data.code"
                  label="編號"
                  dense
                  :disable="menuDialog.isEdit"
                />
              </div>
              <div class="col-8">
                <q-input v-model="menuDialog.data.name" label="名稱" dense />
              </div>
              <div class="col-4">
                <q-input v-model="menuDialog.data.code2" label="編號2" dense />
              </div>
              <div class="col-8">
                <q-input v-model="menuDialog.data.simname" label="簡稱" dense />
              </div>
            </div>
          </q-card-section>
          <q-separator class="q-my-md" />
          <!-- 分類設定 -->
          <q-card-section class="q-gutter-md">
            <div class="text-subtitle2 text-primary q-mb-sm">分類設定</div>
            <div class="row q-col-gutter-sm">
              <div class="col-8">
                <q-select
                  v-model="menuDialog.data.c1"
                  :options="kitchenOptions"
                  label="廚房部門"
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-4">
                <q-input
                  v-model="menuDialog.data.barcode"
                  label="出據順序"
                  dense
                  type="number"
                  min="0"
                />
              </div>
              <div class="col-4">
                <q-select
                  v-model="menuDialog.data.b1"
                  :options="orderTypeOptions"
                  label="點餐類型"
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-4">
                <q-select
                  v-model="menuDialog.data.unit"
                  :options="unitOptions"
                  label="單位"
                  dense
                  emit-value
                  map-options
                />
              </div>
              <div class="col-4">
                <q-select
                  v-model="menuDialog.data.mcolor"
                  :options="colorOptions"
                  label="顏色"
                  dense
                  emit-value
                  map-options
                >
                  <template #option="{ opt, toggleOption }">
                    <q-item clickable @click="toggleOption(opt)">
                      <q-item-section avatar>
                        <div
                          :style="{
                            width: '20px',
                            height: '20px',
                            backgroundColor: colorMap[opt.value],
                            border: '1px solid #ccc',
                            borderRadius: '4px',
                          }"
                        />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ opt.label }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  <template #selected>
                    <div v-if="menuDialog.data.mcolor" class="row items-center">
                      <div
                        :style="{
                          width: '16px',
                          height: '16px',
                          backgroundColor: colorMap[menuDialog.data.mcolor],
                          border: '1px solid #ccc',
                          borderRadius: '4px',
                          marginRight: '8px',
                        }"
                      />
                      <span>{{ getColorLabel(menuDialog.data.mcolor) }}</span>
                    </div>
                  </template>
                </q-select>
              </div>
              <div class="col-4">
                <q-input
                  v-model.number="menuDialog.data.ct1"
                  label="限制"
                  dense
                  type="number"
                  min="0"
                />
              </div>

              <div class="col-3">
                <q-input
                  v-model.number="menuDialog.data.k1"
                  :label="
                    menuDialog.data.omenu === '1' || menuDialog.data.omenu === 1
                      ? '頁數 *'
                      : '頁數'
                  "
                  dense
                  disable
                  :class="
                    (menuDialog.data.omenu === '1' ||
                      menuDialog.data.omenu === 1) &&
                    !menuDialog.data.k1
                      ? 'text-negative'
                      : ''
                  "
                />
              </div>
              <div class="col-3">
                <q-input
                  v-model.number="menuDialog.data.k2"
                  :label="
                    menuDialog.data.omenu === '1' || menuDialog.data.omenu === 1
                      ? '個數 *'
                      : '個數'
                  "
                  dense
                  disable
                  :class="
                    (menuDialog.data.omenu === '1' ||
                      menuDialog.data.omenu === 1) &&
                    !menuDialog.data.k2
                      ? 'text-negative'
                      : ''
                  "
                />
              </div>
              <div class="col-2 flex flex-center">
                <q-btn
                  color="primary"
                  icon="location_on"
                  @click="openPositionDialog"
                  round
                  flat
                  :disable="
                    menuDialog.data.omenu === '3' || menuDialog.data.omenu === 3
                  "
                />
              </div>
            </div>
          </q-card-section>
          <q-separator class="q-my-md" />

          <!-- 金額設定 -->
          <q-card-section class="q-gutter-md">
            <div class="text-subtitle2 text-primary q-mb-sm">金額設定</div>
            <div class="row q-col-gutter-sm">
              <div class="col-12">
                <q-select
                  v-model="menuDialog.data.rcode"
                  :options="filteredRmnameOptions"
                  label="實際編號"
                  dense
                  clearable
                  emit-value
                  map-options
                  option-label="label"
                  option-value="value"
                  @filter="filterRmnameOptions"
                />
              </div>
            </div>
          </q-card-section>
          <q-separator class="q-my-md" />
          <!-- 規格管理區塊 -->
          <q-card-section class="q-gutter-md">
            <div class="text-subtitle2 text-primary q-mb-sm">規格管理</div>
            <div class="row items-center justify-between q-mb-sm">
              <div class="row items-center q-gutter-sm">
                <q-btn
                  dense
                  flat
                  icon="add"
                  color="primary"
                  label="新增規格"
                  @click="addSpec"
                />
                <q-btn
                  dense
                  flat
                  icon="settings"
                  color="deep-orange"
                  round
                  size="sm"
                  @click="openSpecDialog"
                >
                  <q-tooltip>管理規格清單</q-tooltip>
                </q-btn>
              </div>
            </div>
            <div
              v-if="menuDialog.data.specs.length === 0"
              class="text-center q-pa-md text-grey"
            >
              請新增至少一個規格
            </div>
            <VueDraggable
              v-else
              v-model="menuDialog.data.specs"
              item-key="sno"
              handle=".drag-handle"
              tag="div"
              ghost-class="bg-grey-3"
              @end="onSpecSortEnd"
            >
              <div
                v-for="(spec, idx) in menuDialog.data.specs"
                :key="spec.sno"
                class="q-mb-sm"
              >
                <q-item class="bg-grey-2 q-pa-sm rounded-borders">
                  <q-item-section
                    avatar
                    class="drag-handle q-pa-none"
                    style="min-width: 30px; width: 30px"
                  >
                    <q-icon name="drag_indicator" color="grey-7" />
                  </q-item-section>
                  <q-item-section class="q-pa-none">
                    <div class="row q-col-gutter-sm items-center">
                      <div class="col-5">
                        <q-select
                          v-model="spec.name"
                          :options="specOptions"
                          label="規格名稱"
                          dense
                          emit-value
                          map-options
                        />
                      </div>
                      <div class="col-4">
                        <q-input
                          v-model.number="spec.oprice"
                          label="價格調整"
                          dense
                          type="number"
                          step="0.01"
                        />
                      </div>
                      <div class="col-1 flex justify-center">
                        <q-btn
                          flat
                          round
                          dense
                          icon="fastfood"
                          color="green"
                          @click="openSetsMappingDialog(spec)"
                        />
                      </div>
                      <div class="col-2 flex justify-center">
                        <q-btn
                          flat
                          round
                          dense
                          icon="close"
                          color="negative"
                          @click="removeSpec(idx)"
                        />
                      </div>
                    </div>
                  </q-item-section>
                </q-item>
              </div>
            </VueDraggable>
          </q-card-section>
          <q-separator class="q-my-md" />
          <!-- 口味群組管理區塊 -->
          <q-card-section class="q-gutter-md">
            <div class="text-subtitle2 text-primary q-mb-sm">口味群組</div>
            <div class="row items-center justify-between q-mb-sm">
              <q-btn
                dense
                flat
                icon="add"
                color="orange"
                label="新增口味群組"
                @click="addTasteGroup"
              />
            </div>
            <div
              v-if="menuDialog.data.taste_groups.length === 0"
              class="text-center q-pa-md text-grey"
            >
              可選擇性新增口味群組（非必填）
            </div>
            <div v-else class="q-mb-sm">
              <div
                v-for="(taste, idx) in menuDialog.data.taste_groups"
                :key="idx"
                class="q-mb-sm"
              >
                <q-item class="bg-orange-1 q-pa-sm rounded-borders">
                  <q-item-section>
                    <q-select
                      v-model="taste.taste_code"
                      :options="getFilteredTasteGroupOptions(idx)"
                      label="口味群組"
                      dense
                      outlined
                      emit-value
                      map-options
                      @update:model-value="
                        (val) => onTasteGroupChange(taste, val)
                      "
                    />
                  </q-item-section>
                  <q-item-section side>
                    <q-btn
                      flat
                      round
                      dense
                      icon="close"
                      color="negative"
                      @click="removeTasteGroup(idx)"
                    />
                  </q-item-section>
                </q-item>
              </div>
            </div>
          </q-card-section>
          <q-separator class="q-my-md" />
          <!-- 套餐對映顯示區塊 -->
          <q-card-section class="q-gutter-md">
            <div class="text-subtitle2 text-green q-mb-sm">套餐對映</div>
            <div
              v-if="
                menuDialog.data.sets_mappings &&
                menuDialog.data.sets_mappings.length
              "
              class="q-mb-sm"
            >
              <div
                v-for="mapping in menuDialog.data.sets_mappings"
                :key="mapping.sets_code"
                class="q-mb-xs"
              >
                <q-item class="bg-green-1 q-pa-sm rounded-borders">
                  <q-item-section>
                    <div class="text-subtitle2">
                      {{ mapping.sets_title }}
                    </div>
                    <div class="text-caption text-grey" v-if="mapping.sname">
                      規格：{{ mapping.sname }}
                    </div>
                  </q-item-section>
                </q-item>
              </div>
            </div>
            <div v-else class="text-center q-pa-md text-grey">
              尚未設定套餐對映
            </div>
          </q-card-section>
          <q-card-actions
            align="right"
            class="q-pt-md"
            style="
              position: sticky;
              bottom: 0;
              background: white;
              z-index: 10;
              border-top: 1px solid #e0e0e0;
            "
          >
            <q-btn
              class="full-width"
              unelevated
              outline
              icon="save"
              label="儲存"
              type="submit"
              color="secondary"
              :disable="
                !menuDialog.data.name ||
                !menuDialog.data.code ||
                !menuDialog.data.specs ||
                menuDialog.data.specs.length === 0 ||
                ((menuDialog.data.omenu === '1' ||
                  menuDialog.data.omenu === 1) &&
                  (!menuDialog.data.k1 || !menuDialog.data.k2))
              "
            />
          </q-card-actions>
        </q-form>
      </q-card>
    </q-dialog>

    <!-- 刪除確認 Dialog -->
    <q-dialog v-model="deleteDialog.show">
      <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
        <q-card-section class="text-center">
          <q-icon
            name="delete_forever"
            color="negative"
            size="xl"
            class="q-mb-md"
          />
          <div class="text-h6 text-negative">確定刪除？</div>
          <div class="text-body2 text-grey-8 q-mt-sm">
            商品刪除後無法復原，是否繼續？
          </div>
        </q-card-section>
        <q-separator class="q-mt-md" />
        <q-card-actions class="q-mt-sm row justify-between">
          <q-btn
            label="取消"
            color="grey"
            flat
            class="full-width q-mr-xs"
            v-close-popup
          />
          <q-btn
            label="刪除"
            color="negative"
            unelevated
            class="full-width q-ml-xs"
            @click="deleteMenuConfirmed"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 選擇頁數與位置 Dialog -->
    <q-dialog v-model="positionDialog" persistent>
      <q-card style="min-width: 350px; max-width: 95vw">
        <q-card-section class="text-h6 text-primary"
          >選擇頁數與位置</q-card-section
        >
        <q-card-section>
          <q-select
            v-model="positionSelect.page"
            :options="pageOptions"
            label="頁數"
            dense
            emit-value
            map-options
            style="width: 160px; margin: 0 auto; display: block"
            @update:model-value="() => (positionSelect.pos = null)"
          />
          <div class="q-mt-md flex flex-center column items-center">
            <div
              v-for="row in 6"
              :key="row"
              class="row q-gutter-sm q-mb-xs justify-center"
            >
              <div
                v-for="col in 5"
                :key="col"
                style="display: inline-block; position: relative"
                @mouseenter="hoveredBtn = (row - 1) * 5 + col"
                @mouseleave="hoveredBtn = null"
              >
                <q-btn
                  outline
                  :color="
                    isOccupied(positionSelect.page, (row - 1) * 5 + col)
                      ? 'grey-5'
                      : 'primary'
                  "
                  :disable="
                    isOccupied(positionSelect.page, (row - 1) * 5 + col)
                  "
                  @click="selectPosition((row - 1) * 5 + col)"
                  style="
                    width: 50px;
                    height: 50px;
                    font-size: 9px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;
                  "
                  class="q-mr-xs q-mb-xs"
                >
                  <span
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      display: block;
                      width: 100%;
                      text-align: center;
                    "
                  >
                    {{
                      isOccupied(positionSelect.page, (row - 1) * 5 + col)
                        ? getOccupiedName(
                            positionSelect.page,
                            (row - 1) * 5 + col
                          )
                        : (row - 1) * 5 + col
                    }}
                  </span>
                </q-btn>
                <q-tooltip
                  v-if="
                    isOccupied(positionSelect.page, (row - 1) * 5 + col) &&
                    hoveredBtn === (row - 1) * 5 + col
                  "
                  anchor="bottom middle"
                  self="top middle"
                >
                  {{
                    getOccupiedName(positionSelect.page, (row - 1) * 5 + col)
                  }}
                </q-tooltip>
              </div>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn
            flat
            label="取消"
            color="grey"
            @click="positionDialog = false"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 管理規格清單 Dialog -->
    <q-dialog v-model="specDialog.visible" persistent>
      <q-card style="width: 100%; max-width: 420px" class="q-pa-md">
        <!-- 🔹 標題列 -->
        <div class="row items-center justify-between q-mb-sm">
          <div class="text-h6 text-deep-orange">管理規格清單</div>
          <q-btn icon="close" flat round dense v-close-popup />
        </div>

        <!-- 🔸 新增區 -->
        <div class="q-gutter-sm q-mb-md">
          <div class="text-subtitle2 text-grey-8">新增規格</div>
          <div class="row q-gutter-sm items-center">
            <q-input
              v-model="specDialog.name"
              label="規格名稱"
              dense
              outlined
              class="col"
              @keyup.enter="confirmAddSpec"
            />
            <q-btn
              icon="add"
              color="primary"
              dense
              round
              unelevated
              :disable="!specDialog.name"
              @click="confirmAddSpec"
            >
              <q-tooltip>新增規格</q-tooltip>
            </q-btn>
          </div>
        </div>

        <!-- 🔸 規格清單 -->
        <div class="q-mb-md">
          <div class="text-subtitle2 text-grey-8 q-mb-xs">現有規格清單</div>
          <q-list bordered separator dense>
            <q-item
              v-for="item in allSpecList"
              :key="item.value"
              class="q-px-sm"
            >
              <q-item-section>{{ item.label }}</q-item-section>
              <q-item-section side>
                <q-btn
                  icon="delete"
                  size="sm"
                  flat
                  round
                  color="negative"
                  @click="deleteSpec(item)"
                >
                  <q-tooltip>刪除此規格</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- 🔸 關閉按鈕 -->
        <div class="row justify-end">
          <q-btn flat label="關閉" @click="specDialog.visible = false" />
        </div>
      </q-card>
    </q-dialog>

    <!-- 套餐對映 Dialog -->
    <q-dialog v-model="setsMappingDialog.show" persistent>
      <q-card style="width: 100%; max-width: 400px" class="q-pa-md">
        <!-- 標題列 -->
        <div class="row items-center justify-between q-mb-md">
          <div class="text-h6 text-green">套餐對映</div>
          <q-btn
            icon="close"
            flat
            round
            dense
            @click="setsMappingDialog.show = false"
          />
        </div>

        <!-- 商品資訊 -->
        <div class="text-body2 text-grey-7 q-mb-md">
          商品：{{ setsMappingDialog.menuName }}
          <span class="text-weight-bold"
            >規格：{{ setsMappingDialog.specName }}</span
          >
        </div>

        <!-- 新增對映 -->
        <div class="row q-gutter-sm items-center q-mb-md">
          <q-select
            v-model="setsMappingDialog.selectedSets"
            :options="setsMappingDialog.setsOptions"
            label="選擇套餐"
            dense
            outlined
            class="col"
            emit-value
            map-options
            option-label="label"
            option-value="value"
          />
          <q-btn
            icon="add"
            color="green"
            dense
            flat
            round
            :disable="!setsMappingDialog.selectedSets"
            @click="addSetsMapping"
          />
        </div>

        <!-- 對映清單 -->
        <div v-if="setsMappingDialog.mappings.length > 0">
          <div class="text-subtitle2 text-grey-8 q-mb-sm">
            {{ setsMappingDialog.specName }} 已對映套餐
          </div>
          <q-list dense>
            <q-item
              v-for="mapping in setsMappingDialog.mappings"
              :key="`${mapping.sname || ''}_${mapping.sets_code}`"
              class="q-pa-xs"
            >
              <q-item-section>
                <div>{{ mapping.sets_title }}</div>
                <div class="text-caption text-grey-7">
                  規格：{{ mapping.sname || "無" }}
                </div>
              </q-item-section>
              <q-item-section side>
                <q-btn
                  icon="delete"
                  size="sm"
                  flat
                  round
                  color="negative"
                  @click="deleteSetsMapping(mapping)"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </div>
        <div v-else class="text-center q-pa-md text-grey">
          此規格尚未對映任何套餐
        </div>

        <!-- 關閉按鈕 -->
        <div class="row justify-end q-mt-md">
          <q-btn flat label="關閉" @click="setsMappingDialog.show = false" />
        </div>
      </q-card>
    </q-dialog>
  </q-page>
</template>
<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { VueDraggable } from "vue-draggable-plus";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
const props = defineProps({ Branch: String });

const $q = useQuasar();
const loading = ref(false);
const selectedCclass = ref("");
const searchKeyword = ref("");
const menuList = ref([]);
const cclassList = ref([]);
const kitchenList = ref([]);
const unitList = ref([]);
const rmnameList = ref([]);
const specList = ref([]);
const tasteGroupList = ref([]);
const hoveredBtn = ref(null);

const typeMap = { 1: "定價", 2: "時價", 3: "稱重" };
const statusMap = { 1: "啟用", 3: "停用", 4: "隱藏" };
const orderTypeMap = { 1: "合菜", 2: "單點", 3: "附餐" };

const typeOptions = [
  { label: "定價", value: "1" },
  { label: "時價", value: "2" },
  { label: "稱重", value: "3" },
];
const statusOptions = [
  { label: "啟用", value: "1" },
  { label: "停用", value: "3" },
  { label: "隱藏", value: "4" },
];
const orderTypeOptions = [
  { label: "合菜", value: "1" },
  { label: "單點", value: "2" },
  { label: "附餐", value: "3" },
];

const cclassOptions = computed(() =>
  cclassList.value.map((item) => ({ label: item.name, value: item.code }))
);
const kitchenOptions = computed(() =>
  kitchenList.value.map((item) => ({
    label: item.name + "(" + item.code + ")",
    value: item.code,
  }))
);
const unitOptions = computed(() =>
  unitList.value.map((item) => ({ label: item.unit, value: item.unit }))
);

const rmnameOptions = computed(() => {
  const options = rmnameList.value.map((item) => ({
    label: item.name,
    value: item.code,
  }));
  //console.log("rmnameOptions:", options);
  return options;
});

const filteredRmnameOptions = ref([]);

watch(rmnameOptions, (newVal) => {
  filteredRmnameOptions.value = newVal;
  //console.log("filteredRmnameOptions updated:", newVal);
});

function filterRmnameOptions(val, update) {
  if (!val) {
    update(() => {
      filteredRmnameOptions.value = rmnameOptions.value;
    });
    return;
  }
  const keyword = val.toLowerCase();
  update(() => {
    filteredRmnameOptions.value = rmnameOptions.value.filter((opt) =>
      opt.label.toLowerCase().includes(keyword)
    );
  });
}

const specDialog = ref({
  visible: false,
  name: "",
});

const allSpecList = ref([]);

const specOptions = computed(() =>
  specList.value.map((item) => ({
    label: item.name,
    value: item.name,
  }))
);

const tasteGroupOptions = computed(() =>
  tasteGroupList.value.map((item) => ({
    label: item.taste_title,
    value: item.taste_code,
  }))
);

// 套餐對映相關變數
const setsMappingDialog = ref({
  show: false,
  menuCode: "",
  menuName: "",
  specName: "",
  selectedSets: "",
  setsOptions: [],
  mappings: [],
});

// 套餐群組列表
const setsGroupsList = ref([]);

// 為每個口味群組下拉選單提供篩選後的選項（排除已選取的）
function getFilteredTasteGroupOptions(currentIndex) {
  const selectedCodes = menuDialog.value.data.taste_groups
    .map((group, index) => (index !== currentIndex ? group.taste_code : null))
    .filter((code) => code && code.trim() !== "");

  return tasteGroupOptions.value.filter(
    (option) => !selectedCodes.includes(option.value)
  );
}

const filteredMenus = computed(() => {
  let filtered = menuList.value;
  if (selectedCclass.value) {
    filtered = filtered.filter(
      (menu) => String(menu.cclass) === String(selectedCclass.value)
    );
  }
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      (menu) =>
        menu.name.toLowerCase().includes(keyword) ||
        menu.simname?.toLowerCase().includes(keyword) ||
        menu.code.toLowerCase().includes(keyword)
    );
  }
  return filtered;
});

function getCclassName(code) {
  const cclass = cclassList.value.find((item) => item.code === code);
  return cclass ? cclass.name : code;
}

function getKitchenName(code) {
  const kitchen = kitchenList.value.find((item) => item.code === code);
  return kitchen ? kitchen.name : code;
}

function onCclassChange() {
  fetchMenus();
}
function onSearch() {}

// Dialog 狀態
const menuDialog = ref({
  show: false,
  isEdit: false,
  data: {
    code: "",
    simname: "",
    name: "",
    type: "1",
    cclass: "",
    omenu: "1",
    k1: null,
    k2: null,
    b1: "2",
    c1: "",
    unit: "",
    code2: "",
    ct1: null,
    rcode: "",
    mcolor: 1,
    specs: [],
    taste_groups: [],
    barcode: "",
    sets_mappings: [],
  },
});
const deleteDialog = ref({
  show: false,
  menu: null,
});

const colorOptions = [
  { label: "綠色", value: 1 },
  { label: "黃色", value: 2 },
  { label: "橘色", value: 3 },
  { label: "灰色", value: 4 },
  { label: "粉色", value: 5 },
  { label: "深藍", value: 6 },
  { label: "淺藍", value: 7 },
];

const colorMap = {
  1: "#32CD32", // 綠色
  2: "#FFFF00", // 黃色
  3: "#FF8C00", // 橘色
  4: "#696969", // 灰色
  5: "#FF69B4", // 粉色
  6: "#4169E1", // 深藍
  7: "#00BFFF", // 淺藍
};

async function getNextCode() {
  // 取得現有 code 陣列（字串），只取數字部分
  const codes = menuList.value
    .map((m) => parseInt(m.code, 10))
    .filter((n) => !isNaN(n));
  let next = codes.length ? Math.max(...codes) + 1 : 1;
  let nextCode = next.toString().padStart(6, "0");
  let isUnique = false;
  while (!isUnique) {
    // 檢查前端
    if (menuList.value.some((m) => m.code === nextCode)) {
      next++;
      nextCode = next.toString().padStart(6, "0");
      continue;
    }
    // 檢查後端
    try {
      const res = await apiClient.get("/omenum/check_code_unique", {
        params: { branch, code: nextCode },
      });
      isUnique = res.data?.unique;
    } catch {
      isUnique = false;
    }
    if (!isUnique) {
      next++;
      nextCode = next.toString().padStart(6, "0");
    }
  }
  return nextCode;
}

async function openMenuDialog(menu = null) {
  if (menu) {
    //console.log("編輯菜單資料:", menu);
    //console.log("rcode 值:", menu.rcode);
    menuDialog.value.isEdit = true;
    menuDialog.value.data = {
      code: menu.code,
      simname: menu.simname,
      name: menu.name,
      type: menu.type !== undefined ? String(menu.type) : "1",
      cclass: menu.cclass,
      omenu: menu.omenu !== undefined ? String(menu.omenu) : "1",
      k1: menu.k1,
      k2: menu.k2,
      b1: menu.b1,
      c1: menu.c1,
      unit: menu.unit,
      code2: menu.code2,
      ct1: menu.ct1,
      barcode: menu.barcode,
      rcode: menu.rcode ? String(menu.rcode) : "",
      mcolor: menu.mcolor || 1,
      specs: menu.specs ? [...menu.specs] : [],
      taste_groups: menu.taste_groups ? [...menu.taste_groups] : [],
      sets_mappings: menu.sets_mappings ? [...menu.sets_mappings] : [],
    };

    // 主動載入套餐對映資料
    if (menu.code) {
      await loadMenuSetsMappings();
    }

    //console.log("設定後的 rcode:", menuDialog.value.data.rcode);
  } else {
    const nextCode = await getNextCode();
    menuDialog.value.isEdit = false;
    menuDialog.value.data = {
      code: nextCode,
      code2: nextCode,
      simname: "",
      name: "",
      type: "1",
      cclass: selectedCclass.value || "",
      omenu: "1",
      k1: null,
      k2: null,
      b1: "2",
      c1: "",
      unit: "",
      ct1: null,
      rcode: "",
      mcolor: 1,
      specs: [],
      taste_groups: [],
      sets_mappings: [],
    };
  }
  menuDialog.value.show = true;
}

function openDeleteDialog(menu) {
  deleteDialog.value.menu = menu;
  deleteDialog.value.show = true;
}

function getColorLabel(colorValue) {
  const color = colorOptions.find((opt) => opt.value === colorValue);
  return color ? color.label : colorValue;
}

function addSpec() {
  const newSpec = {
    sno: menuDialog.value.data.specs.length + 1,
    name: "",
    oprice: 0,
  };
  menuDialog.value.data.specs.push(newSpec);
}

function removeSpec(index) {
  menuDialog.value.data.specs.splice(index, 1);
  // 重新編號
  menuDialog.value.data.specs.forEach((spec, idx) => {
    spec.sno = idx + 1;
  });
}

function addTasteGroup() {
  const newTasteGroup = {
    taste_code: "",
    taste_title: "",
  };
  menuDialog.value.data.taste_groups.push(newTasteGroup);
}

function removeTasteGroup(index) {
  menuDialog.value.data.taste_groups.splice(index, 1);
}

// 監聽口味群組選擇變化，自動填入群組名稱
function onTasteGroupChange(taste, tasteCode) {
  const selectedGroup = tasteGroupList.value.find(
    (group) => group.taste_code === tasteCode
  );
  if (selectedGroup) {
    taste.taste_title = selectedGroup.taste_title;
  }
}

async function saveMenu() {
  const menuData = { ...menuDialog.value.data };

  // 檢查是否有規格
  if (!menuData.specs || menuData.specs.length === 0) {
    $q.notify({ type: "negative", message: "請至少新增一個規格" });
    return;
  }

  // 檢查規格名稱是否都有填寫
  const invalidSpecs = menuData.specs.filter(
    (spec) => !spec.name || spec.name.trim() === ""
  );
  if (invalidSpecs.length > 0) {
    $q.notify({ type: "negative", message: "請填寫所有規格名稱" });
    return;
  }

  // 檢查啟用狀態時 K1 和 K2 是否必填
  if (
    (menuData.omenu === "1" || menuData.omenu === 1) &&
    (!menuData.k1 || !menuData.k2)
  ) {
    $q.notify({ type: "negative", message: "啟用狀態時頁數和個數為必填項目" });
    return;
  }

  // 明確組裝 omenum 物件
  const omenum = {
    code: menuData.code,
    simname: menuData.simname,
    name: menuData.name,
    type: menuData.type,
    cclass: menuData.cclass,
    omenu: menuData.omenu,
    k1: menuData.k1 ? String(menuData.k1) : null,
    k2: menuData.k2 ? String(menuData.k2) : null,
    b1: menuData.b1,
    c1: menuData.c1,
    unit: menuData.unit,
    code2: menuData.code2,
    ct1: menuData.ct1,
    barcode: menuData.barcode,
  };

  // omenumdetail
  const omenumdetail = menuData.rcode
    ? { code: menuData.code, rcode: menuData.rcode, mcolor: menuData.mcolor }
    : null;

  // omenus（規格）
  const omenus = menuData.specs.map((spec) => ({
    ...spec,
    code: menuData.code,
  }));

  // tasteMaps（口味群組）
  const tasteMaps = menuData.taste_groups
    .filter((taste) => taste.taste_code)
    .map((taste) => ({ code: menuData.code, taste_code: taste.taste_code }));

  try {
    const res = await apiClient.post(
      `/omenum/save_Omenum?branch=${encodeURIComponent(branch)}`,
      {
        omenum,
        omenumdetail,
        omenus,
        tasteMaps,
      }
    );
    if (res.data.success) {
      $q.notify({ type: "positive", message: "儲存成功" });
      menuDialog.value.show = false;
      await fetchMenus();
    } else {
      $q.notify({ type: "negative", message: res.data.message || "儲存失敗" });
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
}

async function onSpecSortEnd() {
  // 直接使用 menuDialog.data.specs，因為 VueDraggable 已經更新了 v-model
  if (
    !menuDialog.value.data.specs ||
    !Array.isArray(menuDialog.value.data.specs)
  ) {
    console.warn("onSpecSortEnd: specs is undefined");
    return;
  }

  menuDialog.value.data.specs.forEach((spec, index) => {
    spec.sno = index + 1;
  });
  // 可加API同步儲存排序
}
async function deleteMenuConfirmed() {
  const menu = deleteDialog.value.menu;
  try {
    const res = await apiClient.delete(
      `/omenum/delete_Omenum/${menu.code}?branch=${encodeURIComponent(branch)}`
    );
    if (res.data.success) {
      const index = menuList.value.findIndex((m) => m.code === menu.code);
      if (index > -1) menuList.value.splice(index, 1);
      $q.notify({ type: "positive", message: "刪除成功" });
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  } finally {
    deleteDialog.value.show = false;
    deleteDialog.value.menu = null;
  }
}

// 取得分店代碼（直接用props.Branch）
const branch = props.Branch;

// 管理規格清單相關函數
function openSpecDialog() {
  specDialog.value.name = "";
  specDialog.value.visible = true;
}

async function confirmAddSpec() {
  const name = specDialog.value.name.trim();
  if (!name) return;

  // 檢查是否重複
  const alreadyExists = allSpecList.value.some((item) => item.label === name);
  if (alreadyExists) {
    $q.notify({ type: "warning", message: "此規格已存在" });
    return;
  }

  try {
    // 送後端寫入資料
    await apiClient.post("/omenum/insert_Spec", {
      branch: branch,
      name,
    });

    // 更新 local 清單
    const newItem = { label: name, value: name };
    allSpecList.value.push(newItem);
    specList.value.push({ name }); // 同步到規格選項
    specDialog.value.name = "";
    $q.notify({ type: "positive", message: "新增成功" });
  } catch (err) {
    console.error("❌ 新增規格失敗", err);
    $q.notify({ type: "negative", message: "新增失敗" });
  }
}

async function deleteSpec(item) {
  try {
    const res = await apiClient.post("/omenum/delete_Spec", {
      branch: branch,
      name: item.value,
    });

    if (res.data.success) {
      // 從本地移除
      allSpecList.value = allSpecList.value.filter(
        (i) => i.value !== item.value
      );
      specList.value = specList.value.filter((i) => i.name !== item.value);

      $q.notify({ type: "positive", message: "刪除成功" });
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    console.error("❌ 刪除規格失敗", err);
    // 顯示後端回傳的錯誤訊息
    const errorMessage = err.response?.data?.message || "刪除失敗";
    $q.notify({ type: "negative", message: errorMessage });
  }
}

// 載入下拉資料
async function fetchCclasses() {
  try {
    const res = await apiClient.get("/omenum/get_Cclasses", {
      params: { branch },
    });
    if (res.data?.success) {
      cclassList.value = res.data.data || [];
      if (cclassList.value.length > 0 && !selectedCclass.value)
        selectedCclass.value = cclassList.value[0].code;
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "載入類部失敗" });
  }
}
async function fetchKitchens() {
  try {
    const res = await apiClient.get("/omenum/get_Kitchens", {
      params: { branch },
    });
    if (res.data?.success) kitchenList.value = res.data.data || [];
    //console.log(kitchenList.value);
  } catch (err) {
    $q.notify({ type: "negative", message: "載入廚房部門失敗" });
  }
}

async function fetchUnits() {
  try {
    const res = await apiClient.get("/omenum/get_Units", {
      params: { branch },
    });
    if (res.data?.success) unitList.value = res.data.data || [];
  } catch (err) {
    $q.notify({ type: "negative", message: "載入單位失敗" });
  }
}

async function fetchRmnames() {
  try {
    const res = await apiClient.get("/omenum/get_Rmnames", {
      params: { branch },
    });
    if (res.data?.success) rmnameList.value = res.data.data || [];
  } catch (err) {
    $q.notify({ type: "negative", message: "載入實際金額編號失敗" });
  }
}

async function fetchSpecs() {
  try {
    const res = await apiClient.get("/omenum/get_Specs", {
      params: { branch },
    });
    if (res.data?.success) {
      specList.value = res.data.data || [];
      // 同步更新 allSpecList
      allSpecList.value = specList.value.map((item) => ({
        label: item.name,
        value: item.name,
      }));
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "載入規格選單失敗" });
  }
}

async function fetchTasteGroups() {
  try {
    const res = await apiClient.get("/omenum/get_TasteGroups", {
      params: { branch },
    });
    if (res.data?.success) tasteGroupList.value = res.data.data || [];
  } catch (err) {
    $q.notify({ type: "negative", message: "載入口味群組失敗" });
  }
}

// 載入套餐群組列表
async function fetchSetsGroups() {
  try {
    const res = await apiClient.get("/osets/get_SetsGroupsForMapping", {
      params: { branch },
    });
    if (res.data?.success) {
      setsGroupsList.value = res.data.data || [];
      // 更新套餐對映 Dialog 的選項
      setsMappingDialog.value.setsOptions = setsGroupsList.value.map(
        (item) => ({
          label: item.sets_title,
          value: item.sets_code,
        })
      );
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "載入套餐群組失敗" });
  }
}

// 開啟套餐對映 Dialog
async function openSetsMappingDialog(spec) {
  if (!menuDialog.value.data.code) {
    $q.notify({ type: "negative", message: "請先儲存商品" });
    return;
  }

  setsMappingDialog.value.menuCode = menuDialog.value.data.code;
  setsMappingDialog.value.menuName = menuDialog.value.data.name;
  setsMappingDialog.value.specName = spec.name?.trim() || ""; // 確保規格名稱格式一致
  setsMappingDialog.value.selectedSets = "";

  // 載入現有對映
  await loadSetsMappings();

  setsMappingDialog.value.show = true;
}

// 載入套餐對映
async function loadSetsMappings() {
  try {
    const res = await apiClient.get("/osets/get_MenuSetsMappings", {
      params: {
        branch,
        code: setsMappingDialog.value.menuCode,
      },
    });
    if (res.data?.success) {
      // 調試: 檢查所有對映的規格名稱
      console.log("當前規格:", setsMappingDialog.value.specName);
      console.log("所有對映:", res.data.data);

      // 使用寬鬆比較，忽略空白和大小寫
      setsMappingDialog.value.mappings = (res.data.data || []).filter(
        (mapping) => {
          const currentSpec =
            setsMappingDialog.value.specName?.trim().toLowerCase() || "";
          const mappingSpec = mapping.sname?.trim().toLowerCase() || "";
          return currentSpec === mappingSpec;
        }
      );

      console.log("過濾後對映:", setsMappingDialog.value.mappings);
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "載入套餐對映失敗" });
  }
}

// 新增套餐對映
async function addSetsMapping() {
  if (!setsMappingDialog.value.selectedSets) {
    $q.notify({ type: "negative", message: "請選擇套餐" });
    return;
  }

  try {
    const res = await apiClient.post("/osets/add_SetsMapping", {
      branch,
      code: setsMappingDialog.value.menuCode,
      sname: setsMappingDialog.value.specName?.trim() || "", // 確保規格名稱格式一致
      sets_code: setsMappingDialog.value.selectedSets,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "套餐對映已新增" });
      setsMappingDialog.value.selectedSets = "";
      await loadSetsMappings();

      // 更新編輯 Dialog 中的套餐對映顯示
      if (
        menuDialog.value.show &&
        menuDialog.value.data.code === setsMappingDialog.value.menuCode
      ) {
        await loadMenuSetsMappings();
      }
    } else {
      $q.notify({ type: "negative", message: res.data.message || "新增失敗" });
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "新增套餐對映失敗" });
  }
}

// 刪除套餐對映
async function deleteSetsMapping(mapping) {
  try {
    const res = await apiClient.post("/osets/delete_SetsMapping", {
      branch,
      code: setsMappingDialog.value.menuCode,
      sname: mapping.sname?.trim() || "", // 確保規格名稱格式一致
      sets_code: mapping.sets_code,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "套餐對映已刪除" });
      await loadSetsMappings();

      // 更新編輯 Dialog 中的套餐對映顯示
      if (
        menuDialog.value.show &&
        menuDialog.value.data.code === setsMappingDialog.value.menuCode
      ) {
        await loadMenuSetsMappings();
      }
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "刪除套餐對映失敗" });
  }
}

// 載入編輯 Dialog 中的套餐對映
async function loadMenuSetsMappings() {
  try {
    const res = await apiClient.get("/osets/get_MenuSetsMappings", {
      params: {
        branch,
        code: menuDialog.value.data.code,
      },
    });
    if (res.data?.success) {
      menuDialog.value.data.sets_mappings = res.data.data || [];
    }
  } catch (err) {
    console.error("載入編輯 Dialog 套餐對映失敗:", err);
  }
}

async function fetchMenus() {
  loading.value = true;
  try {
    const params = { branch };
    if (selectedCclass.value && String(selectedCclass.value).trim() !== "") {
      params.cclass = String(selectedCclass.value).trim();
    }

    const res = await apiClient.get("/omenum/get_Menus", { params });
    if (res.data?.success) {
      menuList.value = res.data.data || [];
    } else
      $q.notify({
        type: "negative",
        message: res.data.message || "載入商品失敗",
      });
    //console.log(menuList.value);
  } catch (err) {
    $q.notify({ type: "negative", message: "載入商品時發生錯誤" });
  } finally {
    loading.value = false;
    //console.log(menuList.value);
  }
}

const positionDialog = ref(false);
// 初始化
const positionSelect = ref({ page: 1, pos: null });

const maxPage = 6; // 可依需求調整
const pageOptions = Array.from({ length: maxPage }, (_, i) => ({
  label: `第${i + 1}頁`,
  value: i + 1,
}));

function openPositionDialog() {
  // 如果狀態為停用，不允許開啟位置選擇
  if (
    menuDialog.value.data.omenu === "3" ||
    menuDialog.value.data.omenu === 3
  ) {
    return;
  }

  if (!positionSelect.value) {
    positionSelect.value = { page: 1, pos: null };
  }
  positionSelect.value.page = menuDialog.value.data.k1
    ? parseInt(menuDialog.value.data.k1)
    : 1;
  positionSelect.value.pos = menuDialog.value.data.k2
    ? parseInt(menuDialog.value.data.k2)
    : null;
  positionDialog.value = true;
}
function selectPosition(pos) {
  if (!positionSelect.value) {
    positionSelect.value = { page: 1, pos: null };
  }
  positionSelect.value.pos = pos;
  // 轉成字串格式
  menuDialog.value.data.k1 = String(positionSelect.value.page);
  menuDialog.value.data.k2 = String(pos).padStart(2, "0");
  positionDialog.value = false;
}

function isOccupied(page, pos) {
  const editingCode = menuDialog.value.data.code;
  const posStr = String(pos).padStart(2, "0");
  return menuList.value.some(
    (item) =>
      String(item.k1 || "") === String(page) &&
      String(item.k2 || "").padStart(2, "0") === posStr &&
      item.code !== editingCode
  );
}

function getOccupiedName(page, pos) {
  const editingCode = menuDialog.value.data.code;
  const posStr = String(pos).padStart(2, "0");
  const found = menuList.value.find(
    (item) =>
      String(item.k1 || "") === String(page) &&
      String(item.k2 || "").padStart(2, "0") === posStr &&
      item.code !== editingCode
  );
  return found ? found.simname || found.name || found.code : "";
}

function onOmenuChange(val) {
  if (val === "1" || val === 1) {
    openPositionDialog();
  } else if (val === "3" || val === 3) {
    // 停用時清空位置資訊
    menuDialog.value.data.k1 = null;
    menuDialog.value.data.k2 = null;
  }
}

watch(positionDialog, (val, oldVal) => {
  if (!val && oldVal) {
    // Dialog 關閉時
    if (!menuDialog.value.data.k1 || !menuDialog.value.data.k2) {
      menuDialog.value.data.omenu = "3";
    }
  }
});

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await fetchCclasses(),
    await Promise.all([
      fetchKitchens(),
      fetchUnits(),
      fetchRmnames(),
      fetchSpecs(),
      fetchTasteGroups(),
      fetchSetsGroups(), // 新增套餐群組載入
    ]); //
  await fetchMenus();
  $q.loading.hide();
});
</script>
