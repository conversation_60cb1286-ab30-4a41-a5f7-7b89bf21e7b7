<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 頁面標題卡片 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">部門管理</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="green"
              @click="openAddDeptDialog"
            >
              <map-pin-plus :stroke-width="1" :size="26" />
              <q-tooltip>新增部門</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- 搜尋欄 -->
      <q-card-section class="q-pt-none q-pb-xs">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-8">
            <q-input
              v-model="searchQuery"
              placeholder="搜尋部門..."
              dense
              outlined
              clearable
              @update:model-value="filterDepartments"
            >
              <template v-slot:prepend>
                <search :stroke-width="1.5" :size="20" class="text-grey-7" />
              </template>
            </q-input>
          </div>
          <div class="col-12 col-sm-4">
            <q-select
              v-model="typeFilter"
              :options="typeOptions"
              label="部門類型"
              dense
              outlined
              emit-value
              map-options
              clearable
              @update:model-value="filterDepartments"
            >
              <template v-slot:prepend>
                <building :stroke-width="1.5" :size="20" class="text-grey-7" />
              </template>
            </q-select>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 部門列表 -->
    <div class="row q-col-gutter-md q-mt-md">
      <template v-for="dept in filteredDepartments" :key="dept.Id">
        <div class="col-12 col-sm-6 col-md-4">
          <q-card class="department-card">
            <q-card-section class="q-pb-none">
              <div class="row items-center no-wrap">
                <div class="col">
                  <div class="text-subtitle1 text-weight-medium text-primary">
                    {{ dept.Name }}
                    <q-badge
                      :color="dept.Type === 0 ? 'blue' : 'orange'"
                      class="q-ml-sm"
                      outline
                    >
                      {{ dept.Type === 0 ? "總部" : "營業區域" }}
                    </q-badge>
                  </div>
                  <div class="text-caption text-grey-7">
                    部門代碼: {{ dept.Id }}
                  </div>
                </div>
                <div class="col-auto">
                  <q-btn
                    flat
                    round
                    size="sm"
                    color="primary"
                    @click="openEditDeptDialog(dept)"
                  >
                    <edit2 :stroke-width="1.5" :size="18" />
                  </q-btn>
                  <q-btn
                    flat
                    round
                    size="sm"
                    color="negative"
                    @click="confirmDelete(dept.Id)"
                  >
                    <trash2 :stroke-width="1.5" :size="18" />
                  </q-btn>
                </div>
              </div>
            </q-card-section>

            <q-card-section class="q-pt-sm">
              <div class="row q-col-gutter-y-sm">
                <!-- 上層部門 -->
                <div class="col-12" v-if="dept.Uname">
                  <div class="row items-center q-gutter-x-sm">
                    <gitBranch
                      :stroke-width="1.5"
                      :size="18"
                      class="text-blue-7"
                    />
                    <div class="text-caption text-blue-7">上層部門</div>
                  </div>
                  <div class="text-body2 q-mt-xs q-pl-lg">{{ dept.Uname }}</div>
                </div>

                <!-- 部門主管 -->
                <div class="col-12">
                  <div class="row items-center q-gutter-x-sm">
                    <userCircle
                      :stroke-width="1.5"
                      :size="18"
                      class="text-green-7"
                    />
                    <div class="text-caption text-green-7">部門主管</div>
                  </div>
                  <div class="text-body2 q-mt-xs q-pl-lg">
                    {{ dept.ManagerName || "未指派" }}
                  </div>
                </div>

                <!-- 下屬部門 -->
                <div
                  class="col-12"
                  v-if="getSubDepartments(dept.Id).length > 0"
                >
                  <div class="row items-center q-gutter-x-sm">
                    <network
                      :stroke-width="1.5"
                      :size="18"
                      class="text-purple-7"
                    />
                    <div class="text-caption text-purple-7">下屬部門</div>
                  </div>
                  <div class="text-body2 q-mt-xs q-pl-lg">
                    <q-chip
                      v-for="subDept in getSubDepartments(dept.Id)"
                      :key="subDept.Id"
                      dense
                      size="sm"
                      class="q-mr-xs"
                    >
                      {{ subDept.Name }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </template>

      <!-- 無資料顯示 -->
      <div v-if="filteredDepartments.length === 0" class="col-12">
        <q-card class="text-center q-pa-lg">
          <x :stroke-width="1.5" :size="48" class="text-grey-5 q-mb-md" />
          <div class="text-h6 text-grey-7">
            {{
              searchQuery || typeFilter !== null
                ? "找不到符合的部門"
                : "尚無部門資料"
            }}
          </div>
          <div class="text-body2 text-grey-6 q-mt-sm">
            {{
              searchQuery || typeFilter !== null
                ? "請調整搜尋條件"
                : "點擊右上角按鈕新增部門"
            }}
          </div>
        </q-card>
      </div>
    </div>

    <!-- 新增/編輯部門對話框 -->
    <q-dialog v-model="deptDialog" persistent>
      <q-card style="width: 100%; max-width: 500px; border-radius: 10px">
        <q-card-section class="row items-center">
          <div class="text-h6 text-primary">
            {{ isEditing ? "編輯部門" : "新增部門" }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section class="q-pt-md">
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="selectedDept.Id"
                label="部門代碼"
                outlined
                dense
                :readonly="isEditing"
                :rules="[(val) => !!val || '部門代碼為必填']"
              >
                <template v-slot:prepend>
                  <hash :stroke-width="1.5" :size="20" class="text-grey-7" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-6">
              <q-input
                v-model="selectedDept.Name"
                label="部門名稱"
                outlined
                dense
                :rules="[(val) => !!val || '部門名稱為必填']"
              >
                <template v-slot:prepend>
                  <type :stroke-width="1.5" :size="20" class="text-grey-7" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-6">
              <q-select
                v-model="selectedDept.Type"
                :options="typeOptions"
                label="部門類型"
                outlined
                dense
                emit-value
                map-options
              >
                <template v-slot:prepend>
                  <building
                    :stroke-width="1.5"
                    :size="20"
                    class="text-grey-7"
                  />
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6">
              <q-select
                v-model="selectedDept.Ulevel"
                :options="filteredDepartmentOptions"
                label="上層部門"
                outlined
                dense
                clearable
                emit-value
                map-options
              >
                <template v-slot:prepend>
                  <gitBranch
                    :stroke-width="1.5"
                    :size="20"
                    class="text-grey-7"
                  />
                </template>
              </q-select>
            </div>

            <div class="col-12">
              <q-select
                v-model="selectedDept.ManagerId"
                :options="filteredManagers"
                label="部門主管"
                option-value="id"
                option-label="name"
                outlined
                dense
                clearable
                use-input
                hide-selected
                fill-input
                input-debounce="300"
                behavior="menu"
                emit-value
                map-options
                @filter="filterManagers"
              >
                <template v-slot:prepend>
                  <userCircle
                    :stroke-width="1.5"
                    :size="20"
                    class="text-grey-7"
                  />
                </template>
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey">
                      無符合的人員
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            :label="isEditing ? '更新' : '新增'"
            color="primary"
            @click="saveDept"
            :loading="isSaving"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 刪除確認對話框 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card style="width: 350px; border-radius: 10px">
        <q-card-section class="column items-center q-pt-lg">
          <div class="delete-icon-wrapper">
            <alertTriangle
              :stroke-width="1.5"
              :size="35"
              class="text-negative"
            />
          </div>
          <div class="text-h6 text-weight-bold q-mt-md">確認刪除部門</div>
          <div class="text-body2 text-grey-7 text-center q-mt-sm">
            此操作將永久刪除該部門，且無法復原。<br />
            確定要繼續嗎？
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            label="刪除"
            color="negative"
            @click="deleteDeptConfirmed"
            :loading="isDeleting"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";
import {
  MapPinPlus as mapPinPlus,
  Search as search,
  Edit2 as edit2,
  Trash2 as trash2,
  Building as building,
  X as x,
  GitBranch as gitBranch,
  UserCircle as userCircle,
  Network as network,
  Hash as hash,
  Type as type,
  AlertTriangle as alertTriangle,
} from "lucide-vue-next";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 部門數據
const departments = ref([]);
const searchQuery = ref("");
const typeFilter = ref(null);
const isSaving = ref(false);
const isDeleting = ref(false);

// 部門類型選項
const typeOptions = [
  { label: "總部", value: 0 },
  { label: "營業區域", value: 1 },
];

// 主管數據
const managerOptions = ref([]);
const filteredManagers = ref([]);

// 過濾部門列表
const filterDepartments = () => {
  // 不需要實作此方法，因為我們使用 computed 屬性 filteredDepartments
  // 當 searchQuery 或 typeFilter 改變時，computed 屬性會自動重新計算
};

// 過濾後的部門列表
const filteredDepartments = computed(() => {
  let result = [...departments.value];

  // 搜尋過濾
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (dept) =>
        dept.Name.toLowerCase().includes(query) ||
        dept.Id.toLowerCase().includes(query) ||
        (dept.ManagerName && dept.ManagerName.toLowerCase().includes(query))
    );
  }

  // 類型過濾
  if (typeFilter.value !== null) {
    result = result.filter((dept) => dept.Type === typeFilter.value);
  }

  return result;
});

// 獲取部門清單
const fetchDepartments = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/deps/get_departments`);
    departments.value = response.data.map((deps) => ({
      Id: deps.Id.trim(),
      Ulevel: deps.Ulevel ? deps.Ulevel.trim() : null,
      Uname: deps.Uname ? deps.Uname.trim() : null,
      Name: deps.Name.trim(),
      ManagerId: deps.ManagerId ? deps.ManagerId.trim() : null,
      ManagerName: deps.ManagerName ? deps.ManagerName.trim() : null,
      Type: deps.Type || 0,
    }));
  } catch (error) {
    console.error("載入部門失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入部門資料失敗",
      position: "top",
    });
  }
};

// 獲取下屬部門
const getSubDepartments = (deptId) => {
  return departments.value.filter((dept) => dept.Ulevel === deptId);
};

// 過濾掉自己的部門和子部門選項
const filteredDepartmentOptions = computed(() => {
  const childDepts = new Set();

  // 遞迴查找所有子部門
  const findChildren = (parentId) => {
    departments.value.forEach((dept) => {
      if (dept.Ulevel === parentId) {
        childDepts.add(dept.Id);
        findChildren(dept.Id);
      }
    });
  };

  if (selectedDept.value.Id) {
    findChildren(selectedDept.value.Id);
  }

  return departments.value
    .filter(
      (dept) =>
        dept.Id !== selectedDept.value.Id && // 排除自己
        !childDepts.has(dept.Id) // 排除子部門
    )
    .map((dept) => ({
      value: dept.Id,
      label: dept.Name,
    }));
});

// 獲取主管清單
const fetchUsers = async () => {
  try {
    const response = await apiClient.post(
      `${apiBaseUrl}/users/get_enable_users`
    );
    managerOptions.value = response.data.map((user) => ({
      id: user.ID.trim(),
      name: user.Name.trim(),
    }));
    filteredManagers.value = [...managerOptions.value];
  } catch (error) {
    console.error("查詢人員失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入人員資料失敗",
      position: "top",
    });
  }
};

// 主管篩選函數
const filterManagers = (val, update) => {
  if (!val) {
    update(() => {
      filteredManagers.value = [...managerOptions.value];
    });
    return;
  }

  update(() => {
    filteredManagers.value = managerOptions.value.filter(
      (manager) =>
        manager.name && manager.name.toLowerCase().includes(val.toLowerCase())
    );
  });
};

// Dialog 控制
const deptDialog = ref(false);
const deleteDialog = ref(false);

// 被選擇的部門
const selectedDept = ref({
  Id: "",
  Name: "",
  Ulevel: null,
  ManagerId: null,
  Type: 0,
});
const isEditing = ref(false);

// 開啟新增部門 Dialog
const openAddDeptDialog = () => {
  selectedDept.value = {
    Id: "",
    Name: "",
    Ulevel: null,
    ManagerId: null,
    Type: 0,
  };
  isEditing.value = false;
  deptDialog.value = true;
};

// 開啟編輯部門 Dialog
const openEditDeptDialog = (dept) => {
  selectedDept.value = { ...dept };
  isEditing.value = true;
  deptDialog.value = true;
};

// 儲存部門
const saveDept = async () => {
  if (!selectedDept.value.Id || !selectedDept.value.Name) {
    $q.notify({
      type: "warning",
      message: "請填寫必要欄位",
      position: "top",
    });
    return;
  }

  isSaving.value = true;

  try {
    if (isEditing.value) {
      await apiClient.put(
        `${apiBaseUrl}/deps/update_department/${selectedDept.value.Id}`,
        {
          Name: selectedDept.value.Name,
          Ulevel: selectedDept.value.Ulevel || null,
          Manager: selectedDept.value.ManagerId || null,
          Type: selectedDept.value.Type,
        }
      );
      $q.notify({
        type: "positive",
        message: "部門已更新",
        position: "top",
      });
    } else {
      await apiClient.post(`${apiBaseUrl}/deps/add_department`, {
        Id: selectedDept.value.Id.trim(),
        Name: selectedDept.value.Name.trim(),
        Ulevel: selectedDept.value.Ulevel || null,
        Manager: selectedDept.value.ManagerId || null,
        Type: selectedDept.value.Type,
      });
      $q.notify({
        type: "positive",
        message: "部門已新增",
        position: "top",
      });
    }

    await fetchDepartments();
    deptDialog.value = false;
  } catch (error) {
    console.error("儲存部門失敗:", error);
    $q.notify({
      type: "negative",
      message: "儲存失敗",
      position: "top",
    });
  } finally {
    isSaving.value = false;
  }
};

// 確認刪除部門
const confirmDelete = (id) => {
  // 檢查是否有子部門
  const hasChildren = departments.value.some((dept) => dept.Ulevel === id);
  if (hasChildren) {
    $q.notify({
      type: "warning",
      message: "此部門下還有子部門，無法刪除",
      position: "top",
    });
    return;
  }

  selectedDept.value.Id = id;
  deleteDialog.value = true;
};

// 刪除部門
const deleteDeptConfirmed = async () => {
  if (!selectedDept.value.Id) return;

  isDeleting.value = true;

  try {
    await apiClient.delete(
      `${apiBaseUrl}/deps/delete_department/${selectedDept.value.Id}`
    );
    await fetchDepartments();
    deleteDialog.value = false;
    $q.notify({
      type: "info",
      message: "部門已刪除",
      position: "top",
    });
  } catch (error) {
    console.error("刪除部門失敗:", error);
    if (error.response?.status === 400) {
      $q.notify({
        type: "warning",
        message: error.response.data.error,
        position: "top",
      });
    } else {
      $q.notify({
        type: "negative",
        message: "刪除失敗",
        position: "top",
      });
    }
  } finally {
    isDeleting.value = false;
  }
};

// 初始載入
onMounted(async () => {
  await fetchDepartments();
  await fetchUsers();
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.custom-separator {
  height: 0.5px;
  margin-bottom: 16px;
}

.department-card {
  border-radius: 10px;
  transition: all 0.2s ease;
  border-left: 3px solid var(--q-primary);
}

.department-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.delete-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(255, 59, 48, 0.1);
}
</style>
