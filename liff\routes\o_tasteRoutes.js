const express = require("express");
const {
  getonlineTaste,
  saveTasteGroups,
  deleteTasteGroup,
  saveGroupSort,
  saveTasteItemSort,
  getTasteOptions,
  addTasteItems,
  deleteTasteItem,
} = require("../controllers/o_tasteController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_onlineTaste", authMiddleware, getonlineTaste); // ✅ 讀取 `/api/order`
router.post("/save_TasteGroups", authMiddleware, saveTasteGroups);
router.post("/delete_TasteGroup", authMiddleware, deleteTasteGroup);
router.post("/save_GroupSort", authMiddleware, saveGroupSort);
router.post("/save_TasteItemSort", authMiddleware, saveTasteItemSort);
router.get("/get_TasteOptions", authMiddleware, getTasteOptions); // ✅ 讀取 `/api/order`
router.post("/add_TasteItems", authMiddleware, addTasteItems);
router.post("/delete_TasteItem", authMiddleware, deleteTasteItem);
module.exports = router;
