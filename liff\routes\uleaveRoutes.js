const express = require("express");
const router = express.Router();
const uleaveController = require("../controllers/uleaveController");
const authMiddleware = require("../middleware/authMiddleware");

// Get employee leave information
router.get("/info", authMiddleware, uleaveController.getLeaveInfo);

// Get employee leave usage
router.get("/usage", authMiddleware, uleaveController.getLeaveUsage);

// Get department employees leave stats
router.get("/department-stats", authMiddleware, uleaveController.getDepartmentLeaveStats);

// Check if user is a department manager
router.get("/check-manager", authMiddleware, require("../controllers/userController").checkIsManager);

// Get department members
router.get("/department-members", authMiddleware, require("../controllers/userController").getDepartmentMembers);

module.exports = router;
