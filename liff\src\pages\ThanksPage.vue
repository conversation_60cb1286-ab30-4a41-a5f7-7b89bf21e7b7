<template>
  <q-layout view="hHh Lpr lFf">
    <q-page-container>
      <q-page class="flex flex-center">
        <div class="text-center">
          <q-icon name="send" size="64px" color="orange" />
          <div class="text-h5 q-mt-md">你的表單已成功送出</div>
          <div class="text-grey-6">已成功收到你的資訊，{{ message }}</div>
          <q-btn
            label="回到首頁"
            color="primary"
            class="q-mt-lg"
            @click="goHome"
          />
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";
import { ref, computed } from "vue";

const router = useRouter();
const route = useRoute();
const username = ref("");

// 🔹 改用 computed，確保 `formId` 變動時會自動更新
const message = computed(() => `送出單號：${route.query.formId || "未知"}`);

function goHome() {
  username.value = route.query.username || "未知使用者";
  console.log(username.value);
  router.push({ path: "/home", query: { username: username.value } });
}
</script>

<style scoped>
.text-h5 {
  font-size: 24px;
  font-weight: bold;
}
.text-grey {
  color: grey;
}
</style>
