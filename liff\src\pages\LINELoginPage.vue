<template>
  <div class="line-login-container">
    <q-card
      class="login-card q-pa-lg shadow-8"
      style="width: 420px; text-align: center"
    >
      <q-card-section class="q-pb-none">
        <div class="line-logo-container">
          <q-icon name="chat" size="4rem" color="green-6" />
        </div>
        <div class="text-h5 q-mt-md text-weight-medium text-grey-8">
          LINE 登入中
        </div>
        <div class="text-body2 q-mt-sm text-grey-6">
          正在連接到 LINE 服務...
        </div>
      </q-card-section>

      <!-- 載入動畫 -->
      <q-card-section class="q-pt-md">
        <q-linear-progress
          :indeterminate="isLoading"
          color="green-6"
          class="q-mb-md"
        />
        <div v-if="isLoading" class="text-caption text-grey-6">
          {{ loadingMessage }}
        </div>
      </q-card-section>

      <!-- 錯誤訊息 -->
      <q-card-section v-if="errorMessage" class="q-pt-none">
        <q-banner class="bg-red-1 text-red-8 q-mb-md">
          <template v-slot:avatar>
            <q-icon name="error" color="red-6" />
          </template>
          {{ errorMessage }}
        </q-banner>
        <q-btn
          color="green-6"
          label="重新嘗試"
          @click="retryLogin"
          :loading="isLoading"
          unelevated
        />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import axios from "axios";
import liff from "@line/liff";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const router = useRouter();
const $q = useQuasar();

// 響應式狀態
const isLoading = ref(true);
const loadingMessage = ref("初始化 LIFF...");
const errorMessage = ref("");

// LIFF 配置
const LIFF_ID = "2006882861-kq5G7DjN";

// 更新載入訊息
const updateLoadingMessage = (message) => {
  loadingMessage.value = message;
};

// 顯示錯誤訊息
const showError = (message) => {
  errorMessage.value = message;
  isLoading.value = false;
  $q.notify({
    type: "negative",
    message: message,
    position: "top",
    timeout: 5000,
  });
};

// 初始化 LIFF
const initLiff = async () => {
  try {
    updateLoadingMessage("正在初始化 LIFF...");

    await liff.init({
      liffId: LIFF_ID,
      withLoginOnExternalBrowser: true,
    });

    console.log("✅ LIFF 初始化成功");
    return true;
  } catch (error) {
    console.error("❌ LIFF 初始化失敗:", error);
    showError("LINE 服務初始化失敗，請檢查網路連線後重試");
    return false;
  }
};

// 處理 LINE 登入
const handleLineLogin = async () => {
  try {
    if (!liff.isLoggedIn()) {
      updateLoadingMessage("跳轉到 LINE 登入頁面...");
      console.log("🔄 跳轉到 LINE 登入...");
      liff.login();
      return;
    }

    updateLoadingMessage("正在取得用戶資料...");
    console.log("✅ 已登入 LINE，取得 Profile...");

    const profile = await liff.getProfile();
    console.log("📌 Profile:", profile);

    return profile;
  } catch (error) {
    console.error("❌ LINE 登入失敗:", error);
    showError("LINE 登入失敗，請重新嘗試");
    return null;
  }
};

// 後端驗證
const authenticateWithBackend = async (profile) => {
  try {
    updateLoadingMessage("正在驗證用戶身份...");

    const response = await axios.post(
      `${apiBaseUrl}/auth/line-login`,
      {
        userId: profile.userId,
        displayName: profile.displayName,
        pictureUrl: profile.pictureUrl,
      },
      {
        timeout: 10000, // 10秒超時
      }
    );

    if (response.data.success) {
      console.log("✅ JWT 取得成功:", response.data.token);
      localStorage.setItem("jwt_token", response.data.token);

      // 顯示成功通知
      $q.notify({
        type: "positive",
        message: "登入成功！",
        position: "top",
        timeout: 2000,
      });

      updateLoadingMessage("登入成功，正在跳轉...");

      // 延遲跳轉以顯示成功訊息
      setTimeout(() => {
        router.push("/home");
      }, 1000);

      return true;
    } else {
      console.error("❌ JWT 取得失敗:", response.data.message);
      showError(response.data.message || "驗證失敗，請重新嘗試");
      return false;
    }
  } catch (error) {
    console.error("❌ 後端驗證失敗:", error);

    if (error.code === "ECONNABORTED") {
      showError("連線超時，請檢查網路連線後重試");
    } else if (error.response?.status === 401) {
      showError("用戶驗證失敗，請重新登入");
    } else if (error.response?.status >= 500) {
      showError("伺服器錯誤，請稍後再試");
    } else {
      showError("驗證失敗，請重新嘗試");
    }

    return false;
  }
};

// 主要登入流程
const performLogin = async () => {
  try {
    isLoading.value = true;
    errorMessage.value = "";

    // 步驟 1: 初始化 LIFF
    const liffInitialized = await initLiff();
    if (!liffInitialized) return;

    // 步驟 2: 處理 LINE 登入
    const profile = await handleLineLogin();
    if (!profile) return;

    // 步驟 3: 後端驗證
    const authSuccess = await authenticateWithBackend(profile);
    if (!authSuccess) {
      // 驗證失敗時跳轉到首頁
      setTimeout(() => {
        router.push("/");
      }, 2000);
    }
  } catch (error) {
    console.error("❌ 登入流程發生未預期錯誤:", error);
    showError("登入過程發生錯誤，請重新嘗試");

    setTimeout(() => {
      router.push("/");
    }, 3000);
  } finally {
    isLoading.value = false;
  }
};

// 重新嘗試登入
const retryLogin = () => {
  errorMessage.value = "";
  performLogin();
};

// 頁面載入時執行登入
onMounted(() => {
  performLogin();
});
</script>

<style scoped>
.line-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.login-card {
  border-radius: 16px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.line-logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

/* 響應式設計 */
@media (max-width: 480px) {
  .login-card {
    width: 90% !important;
    margin: 0 10px;
  }

  .line-login-container {
    padding: 10px;
  }
}

/* 載入動畫效果 */
.q-linear-progress {
  border-radius: 8px;
}

/* 卡片懸停效果 */
.login-card:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}
</style>
