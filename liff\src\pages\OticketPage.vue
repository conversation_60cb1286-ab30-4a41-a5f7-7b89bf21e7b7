<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="width: 100%; max-width: 500px; border-radius: 12px"
    >
      <!-- 🔷 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        票券管理
      </q-card-section>

      <!-- 🎫 票券清單 -->
      <q-list separator>
        <div
          v-for="ticket in ticketList"
          :key="ticket.productid"
          class="bg-white q-mb-sm"
          style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
        >
          <q-expansion-item
            expand-separator
            class="q-pa-none"
            header-class="text-indigo"
          >
            <!-- 🔹 票券主列 -->
            <template #header>
              <q-item
                dense
                class="q-pa-none q-gutter-none items-center"
                style="width: 100%; position: relative"
              >
                <!-- 複製按鈕（左側） -->
                <q-item-section side style="min-width: 32px">
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="content_copy"
                    color="primary"
                    @click.stop="copyTicket(ticket)"
                    title="複製此票券"
                  />
                </q-item-section>
                <q-item-section>
                  <div class="text-indigo-10">{{ ticket.ticket_name }}</div>
                  <div class="text-caption text-grey-8">
                    <span class="text-brown-10">{{ ticket.productid }}</span> |
                    商品：{{ ticket.product_name || ticket.mcode }}
                  </div>
                </q-item-section>
                <!-- 最右側展開箭頭 -->
                <q-item-section side />
                <!-- 編輯按鈕 -->
                <q-btn
                  flat
                  dense
                  size="sm"
                  icon="edit"
                  color="primary"
                  @click.stop="openEditDialog(ticket)"
                  style="
                    position: absolute;
                    right: 14px;
                    top: 50%;
                    transform: translateY(-50%);
                    z-index: 1;
                  "
                />
              </q-item>
            </template>

            <!-- 🔽 詳細內容 -->
            <q-card-section class="q-pa-sm q-pt-none bg-grey-1 text-caption">
              <div class="row q-col-gutter-sm q-mb-xs q-pl-md">
                <!-- ✅ 票券編號 -->
                <div class="col-12">
                  <q-icon
                    name="confirmation_number"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  票券編號：
                  <span class="text-weight-medium">{{ ticket.productid }}</span>
                </div>

                <!-- ✅ 門市代號 -->
                <div class="col-6">
                  <q-icon
                    name="store"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  門市代號：
                  <span class="text-weight-medium">{{ ticket.bcode }}</span>
                </div>

                <!-- ✅ 商品欄位 -->
                <div class="col-6">
                  <q-icon
                    name="inventory"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  商品：
                  <span class="text-weight-medium">{{
                    ticket.product_name || ticket.mcode
                  }}</span>
                </div>

                <!-- ✅ 過期商品欄位 -->
                <div class="col-6">
                  <q-icon
                    name="schedule"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  過期商品：
                  <span class="text-weight-medium">{{
                    ticket.expired_product_name || ticket.eprmcode || "無"
                  }}</span>
                </div>

                <!-- ✅ 其他付款 -->
                <div class="col-6">
                  <q-icon
                    name="payment"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  其他付款：
                  <span class="text-weight-medium">{{
                    ticket.otherpay_code || "無"
                  }}</span>
                </div>

                <!-- ✅ 付款金額 -->
                <div class="col-6" v-if="ticket.otherpay_amt">
                  <q-icon
                    name="attach_money"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  付款金額：
                  <span class="text-weight-medium">{{
                    ticket.otherpay_amt
                  }}</span>
                </div>
              </div>

              <!-- 🔻 刪除 -->
              <div class="row justify-end q-mt-sm q-pr-sm">
                <q-btn
                  flat
                  dense
                  icon="delete"
                  label="刪除"
                  color="negative"
                  @click.stop="deleteTicket(ticket)"
                  class="q-px-sm"
                />
              </div>
            </q-card-section>
          </q-expansion-item>
        </div>
      </q-list>

      <!-- ➕ 新增票券按鈕 -->
      <div class="q-pa-none q-mt-sm flex justify-center">
        <q-btn
          color="primary"
          icon="add"
          label="新增票券"
          unelevated
          class="full-width"
          @click="openEditDialog()"
        />
      </div>
    </q-card>
  </q-page>

  <!-- 編輯/新增對話框 -->
  <q-dialog v-model="editDialog.visible" persistent>
    <q-card
      class="dialog-card"
      :class="$q.screen.lt.md ? 'mobile-dialog' : 'desktop-dialog'"
    >
      <!-- 標題列 -->
      <q-card-section class="dialog-header">
        <div class="text-h6 text-weight-medium text-primary">
          <q-icon name="confirmation_number" class="q-mr-sm" />
          {{ editDialog.isEdit ? "編輯票券" : "新增票券" }}
        </div>
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          class="text-grey-6"
        />
      </q-card-section>

      <q-separator />

      <!-- 表單內容 -->
      <q-card-section class="dialog-body">
        <div class="form-grid">
          <!-- 基本資訊 -->
          <q-input
            v-model="editDialog.form.productid"
            label="票券編號"
            dense
            outlined
            :disable="editDialog.isEdit"
          >
            <template v-slot:prepend>
              <q-icon name="confirmation_number" color="primary" />
            </template>
          </q-input>

          <q-input
            v-model="editDialog.form.ticket_name"
            label="票券名稱"
            dense
            outlined
          >
            <template v-slot:prepend>
              <q-icon name="label" color="primary" />
            </template>
          </q-input>

          <!-- 商品設定 -->
          <q-select
            v-model="editDialog.form.mcode"
            label="商品編號"
            :options="productOptions"
            dense
            outlined
            emit-value
            map-options
            clearable
            use-input
            input-debounce="300"
            @filter="onProductFilter"
          >
            <template v-slot:prepend>
              <q-icon name="inventory" color="indigo" />
            </template>
          </q-select>

          <q-select
            v-model="editDialog.form.eprmcode"
            label="過期商品編號"
            :options="productOptions"
            dense
            outlined
            emit-value
            map-options
            clearable
            use-input
            input-debounce="300"
            @filter="onProductFilter"
          >
            <template v-slot:prepend>
              <q-icon name="schedule" color="indigo" />
            </template>
          </q-select>

          <!-- 其他付款設定 -->
          <q-select
            v-model="editDialog.form.otherpay_code"
            label="其他付款編號"
            :options="otherpayOptions"
            dense
            outlined
            emit-value
            map-options
            clearable
            @update:model-value="onOtherpayChange"
          >
            <template v-slot:prepend>
              <q-icon name="payment" color="teal" />
            </template>
          </q-select>

          <q-input
            v-model="editDialog.form.otherpay_amt"
            label="付款金額"
            dense
            outlined
            type="number"
            step="0.01"
            min="0"
          >
            <template v-slot:prepend>
              <q-icon name="attach_money" color="teal" />
            </template>
          </q-input>
        </div>
      </q-card-section>

      <!-- 操作按鈕 -->
      <q-card-actions class="dialog-actions">
        <q-btn
          color="primary"
          :label="editDialog.isEdit ? '更新' : '儲存'"
          @click="saveTicket"
          unelevated
          class="action-btn"
          :loading="false"
        >
          <template v-slot:loading>
            <q-spinner-facebook />
          </template>
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 刪除確認對話框 -->
  <q-dialog v-model="deleteDialog" persistent>
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          票券「{{ ticketToDelete?.ticket_name }}」刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteTicketConfirmed"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";

const $q = useQuasar();
const props = defineProps({ Branch: String });

const editDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    productid: "",
    ticket_name: "",
    bcode: "",
    mcode: "",
    eprmcode: "",
    otherpay_code: "",
    otherpay_paykind: "",
    otherpay_amt: "",
  },
});

const ticketList = ref([]);
const productOptions = ref([]);
const otherpayOptions = ref([]);

// 載入票券列表
const fetchTickets = async () => {
  try {
    const res = await apiClient.get(`/oticket/get_ticketList`, {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      ticketList.value = res.data.data;
    }
    //console.log(ticketList.value);
    $q.loading.hide();
  } catch (err) {
    console.error("❌ 載入票券失敗", err);
    $q.notify({ type: "negative", message: "票券載入失敗" });
    $q.loading.hide();
  }
};

// 載入商品選項
const fetchProducts = async () => {
  try {
    const res = await apiClient.get(`/omenum/get_products_with_rcode`, {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      productOptions.value = res.data.data.map((item) => ({
        label: item.name,
        value: item.code,
      }));
    }
  } catch (err) {
    console.error("❌ 載入商品失敗", err);
  }
};

// 載入其他付款選項
const fetchOtherpay = async () => {
  try {
    const res = await apiClient.get(`/otherpay/get_otherpayList`, {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      otherpayOptions.value = res.data.data.map((item) => ({
        label: `${item.code} - ${item.name}`,
        value: item.code,
        paykind: item.paykind,
      }));
    }
  } catch (err) {
    console.error("❌ 載入其他付款失敗", err);
  }
};

const openEditDialog = (ticket = null) => {
  if (ticket) {
    editDialog.value.form = { ...ticket };
    editDialog.value.isEdit = true;
  } else {
    editDialog.value.form = {
      productid: "",
      ticket_name: "",
      bcode: "", // 這行可保留但不顯示，或直接移除
      mcode: "",
      eprmcode: "",
      otherpay_code: "",
      otherpay_paykind: "",
      otherpay_amt: "",
    };
    editDialog.value.isEdit = false;
  }
  editDialog.value.visible = true;
};

const onOtherpayChange = (value) => {
  if (value) {
    const selectedOtherpay = otherpayOptions.value.find(
      (item) => item.value === value
    );
    if (selectedOtherpay) {
      editDialog.value.form.otherpay_paykind = selectedOtherpay.paykind;
    }
  } else {
    editDialog.value.form.otherpay_paykind = "";
  }
};

const editTicket = (ticket) => {
  openEditDialog(ticket);
};

const saveTicket = async () => {
  // 驗證必填欄位
  if (!editDialog.value.form.productid) {
    $q.notify({
      type: "negative",
      message: "請輸入票券編號",
      position: "top",
    });
    return;
  }

  if (!editDialog.value.form.ticket_name) {
    $q.notify({
      type: "negative",
      message: "請輸入票券名稱",
      position: "top",
    });
    return;
  }

  if (!editDialog.value.form.mcode) {
    $q.notify({
      type: "negative",
      message: "請選擇商品",
      position: "top",
    });
    return;
  }

  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "儲存中...",
    });

    // 將 ticket_name 改為 mname 傳給後端
    const formData = {
      ...editDialog.value.form,
      mname: editDialog.value.form.ticket_name, // 新增這行
      branch: props.Branch,
    };
    // 移除 ticket_name 欄位，避免多餘
    delete formData.ticket_name;

    let res;
    if (editDialog.value.isEdit) {
      // 編輯現有票券
      res = await apiClient.put(`/oticket/update_ticket`, formData, {
        params: { branch: props.Branch },
      });
    } else {
      // 新增票券
      res = await apiClient.post(`/oticket/create_ticket`, formData, {
        params: { branch: props.Branch },
      });
    }

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: editDialog.value.isEdit ? "票券更新成功" : "票券新增成功",
      });
      editDialog.value.visible = false;
      await fetchTickets(); // 重新載入資料
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "操作失敗",
      });
    }
  } catch (err) {
    console.error("❌ 儲存票券失敗", err);
    $q.notify({
      type: "negative",
      message: "儲存失敗：" + (err.response?.data?.message || err.message),
    });
  } finally {
    $q.loading.hide();
  }
};

const deleteDialog = ref(false);
const ticketToDelete = ref(null);

const deleteTicket = async (ticket) => {
  ticketToDelete.value = ticket;
  deleteDialog.value = true;
};

const deleteTicketConfirmed = async () => {
  if (!ticketToDelete.value) return;

  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "刪除中...",
    });

    const res = await apiClient.delete(
      `/oticket/delete_ticket/${ticketToDelete.value.productid}`,
      {
        params: {
          branch: props.Branch,
        },
      }
    );

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: "票券刪除成功",
        icon: "check_circle",
        position: "top",
      });
      await fetchTickets(); // 重新載入資料
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "刪除失敗",
        icon: "error",
        position: "top",
      });
    }
  } catch (err) {
    console.error("❌ 刪除票券失敗", err);
    $q.notify({
      type: "negative",
      message: "刪除失敗：" + (err.response?.data?.message || err.message),
      icon: "error",
      position: "top",
    });
  } finally {
    $q.loading.hide();
    deleteDialog.value = false;
    ticketToDelete.value = null;
  }
};

const onProductFilter = (val, update) => {
  update(() => {
    if (!val) {
      // 無輸入時，顯示全部
      return productOptions.value;
    } else {
      // 有輸入時，前端過濾 label 或 value，顯示全部符合的
      return productOptions.value.filter(
        (opt) =>
          (opt.label && opt.label.includes(val)) ||
          (opt.value && opt.value.includes(val))
      );
    }
  });
};

const copyTicket = (ticket) => {
  // 複製除 productid 外的所有欄位
  const { productid, ...rest } = ticket;
  editDialog.value.form = {
    productid: "", // 票券編號清空，需手動輸入
    ...rest,
  };
  editDialog.value.isEdit = false;
  editDialog.value.visible = true;
};

onMounted(() => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  fetchTickets();
  fetchProducts();
  fetchOtherpay();
});
</script>

<style scoped>
/* 對話框樣式 */
.dialog-card {
  border-radius: 12px;
  overflow: hidden;
}

.desktop-dialog {
  width: 100%;
  max-width: 600px;
  margin: 20px auto;
}

.mobile-dialog {
  width: 95%;
  max-width: 95%;
  margin: 10px auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.dialog-body {
  padding: 20px;
}

.dialog-actions {
  padding: 16px 20px;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

/* 表單網格佈局 */
.form-grid {
  display: grid;
  gap: 16px;
}

/* 桌面版：2列佈局 */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 移除 Quasar 元件的最小寬度限制 */
:deep(.q-field) {
  min-width: 0 !important;
}

:deep(.q-field__control) {
  min-width: 0 !important;
}

:deep(.q-field__native) {
  min-width: 0 !important;
}

:deep(.q-select) {
  min-width: 0 !important;
}

:deep(.q-input) {
  min-width: 0 !important;
}
</style>
