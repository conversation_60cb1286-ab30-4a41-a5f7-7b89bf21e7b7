const sql = require("mssql");
const dbConfig = require("../config/db");

const getTables = async (req, res) => {
  try {
    await sql.connect(dbConfig);
    const result = await sql.query(
      "SELECT  Tcode, Tname FROM Tables where class='M' "
    );

    if (result.recordset.length > 0) {
      res.json(result.recordset);
    } else {
      res.status(404).json({ message: "未找到頁面資料" });
    }
  } catch (err) {
    console.error("SQL 錯誤:", err);
    res.status(500).json({ message: "伺服器錯誤" });
  }
};

const syncTables = async (req, res) => {
  const { className, branchCode, branchIp } = req.body;

  // 遠端資料庫連線設定
  const remoteConfig = {
    user: "sa",
    password: "86740736",
    server: branchIp,
    database: "restq",
    options: {
      encrypt: false,
      trustServerCertificate: true,
    },
  };

  // 本地連線
  const localPool = await sql.connect(dbConfig);
  try {
    // 1. 查詢 Tcode 清單
    const tcodeResult = await localPool
      .request()
      .input("class", sql.VarChar, className)
      .query("SELECT Tcode FROM Tables WHERE Class = @class");

    const tcodes = tcodeResult.recordset.map((r) => r.Tcode.trim());
    //console.log(tcodes);

    // 2. 遍歷每個 Tcode
    for (const tableName of tcodes) {
      const remotePool = new sql.ConnectionPool(remoteConfig);
      await remotePool.connect();

      console.log(tableName);
      // 2-1. 查遠端欄位清單，排除 TB_ID
      const colResult = await remotePool.request().query(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '${tableName}'
        AND COLUMN_NAME NOT IN ('TB_ID', 'CK_ID', 'ISComp')
      `);
      //console.log(colResult);
      const columns = colResult.recordset
        .map((col) => `[${col.COLUMN_NAME}]`)
        .join(", ");
      //console.log(columns);
      if (!columns) continue; // 沒有欄位就跳過

      // 2-2. 清空本地表
      await localPool
        .request()
        .query(`TRUNCATE TABLE  [${branchCode}].[dbo].[${tableName}]`);

      // 2-3. 抓遠端資料
      const dataResult = await remotePool
        .request()
        .query(`SELECT ${columns} FROM [${tableName}]`);

      // 2-4. 寫入本地（分批 Insert）
      for (const row of dataResult.recordset) {
        const values = Object.values(row)
          .map((val) =>
            typeof val === "string"
              ? `'${val.replace(/'/g, "''")}'`
              : val === null
              ? "NULL"
              : val
          )
          .join(", ");

        const insertSql = `INSERT INTO [${branchCode}].[dbo].[${tableName}] (${columns}) VALUES (${values})`;

        await localPool.request().query(insertSql);
      }

      await remotePool.close();
    }
    await localPool.request().input("cod_cust", sql.VarChar, branchCode).query(`
    UPDATE Branch
    SET Rtime = GETUTCDATE()
    WHERE Cod_cust = @cod_cust
  `);

    res.json({ status: "success", message: "資料回收完成" });
  } catch (err) {
    console.error("回收錯誤：", err);
    res.status(500).json({ status: "error", message: err.message });
  } finally {
  }
};

module.exports = {
  getTables,
  syncTables,
};
