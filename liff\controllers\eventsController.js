const sql = require("mssql");
const dbConfig = require("../config/db");

// 獲取所有事件
const getEvents = async (req, res) => {
  try {
    const { userId, userDep, start, end } = req.query;

    const pool = await sql.connect(dbConfig);

    let query = `
      SELECT
        e.id,
        e.title,
        CAST(e.description AS NVARCHAR(MAX)) as description,
        e.start_datetime,
        e.end_datetime,
        e.all_day,
        e.event_type,
        e.source_type,
        e.source_id,
        e.background_color,
        e.active,
        e.dep,
        e.created_by,
        e.updated_by,
        e.created_at,
        e.updated_at,
        d.Name as dep_name,
        d.Color as dep_color,
        u.Name as created_by_name
      FROM Events e
      LEFT JOIN dep d ON e.dep = d.id
      LEFT JOIN Users u ON e.created_by = u.ID
      WHERE e.active = 1
    `;

    const params = [];

    // 如果有日期範圍，添加日期篩選
    if (start) {
      query += ` AND e.end_datetime >= @start`;
      params.push({
        name: "start",
        type: sql.DateTime,
        value: new Date(start),
      });
    }

    if (end) {
      query += ` AND e.start_datetime <= @end`;
      params.push({ name: "end", type: sql.DateTime, value: new Date(end) });
    }

    // 根據用戶權限篩選事件
    if (userDep && userId) {
      query += ` AND (
        e.dep = @userDep
        OR EXISTS (
          SELECT 1 FROM Events_targets et
          WHERE et.id = e.id
          AND (
            et.target_type = 'all'
            OR (et.target_type = 'department' AND et.target_id = @userDep)
            OR (et.target_type = 'user' AND et.target_id = @userId)
          )
        )
      )`;
      params.push({ name: "userDep", type: sql.NChar, value: userDep });
      params.push({ name: "userId", type: sql.NChar, value: userId });
    }

    query += ` ORDER BY e.start_datetime ASC`;

    const request = pool.request();

    params.forEach((param) => {
      request.input(param.name, param.type, param.value);
    });

    const result = await request.query(query);

    // 獲取所有事件的目標對象
    const eventIds = result.recordset.map((event) => event.id);
    let targetsMap = {};

    if (eventIds.length > 0) {
      const targetsResult = await pool.request().query(`
        SELECT id, target_type, target_id
        FROM Events_targets
        WHERE id IN ('${eventIds.join("','")}')
      `);

      // 將目標對象按事件ID分組
      targetsResult.recordset.forEach((target) => {
        if (!targetsMap[target.id]) {
          targetsMap[target.id] = [];
        }
        targetsMap[target.id].push({
          target_type: target.target_type,
          target_id: target.target_id,
        });
      });
    }

    // 轉換事件格式以符合 FullCalendar 需求
    const events = result.recordset.map((event) => {
      let endDateTime = event.end_datetime;

      // 對於全天事件，如果結束時間是 00:00:00，需要加一天以符合 FullCalendar 的排他性結束時間
      if (event.all_day && endDateTime) {
        const endDate = new Date(endDateTime);
        const startDate = new Date(event.start_datetime);

        // 如果結束時間是當天的 00:00:00，表示這是跨天的全天事件
        if (
          endDate.getHours() === 0 &&
          endDate.getMinutes() === 0 &&
          endDate.getSeconds() === 0
        ) {
          // 檢查是否真的是跨天事件（結束日期 > 開始日期）
          if (endDate.getTime() > startDate.getTime()) {
            // 已經是正確的排他性結束時間，不需要修改
            endDateTime = event.end_datetime;
          }
        }
      }

      return {
        id: event.id,
        title: event.title,
        description: event.description,
        start: event.start_datetime,
        end: endDateTime,
        allDay: event.all_day,
        backgroundColor: event.background_color,
        eventType: event.event_type,
        sourceType: event.source_type,
        sourceId: event.source_id,
        dep: event.dep,
        depName: event.dep_name,
        depColor: event.dep_color,
        createdBy: event.created_by,
        createdByName: event.created_by_name,
        createdAt: event.created_at,
        updatedAt: event.updated_at,
        targets: targetsMap[event.id] || [],
      };
    });

    res.json({ success: true, data: events });
  } catch (error) {
    console.error("獲取事件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取事件" });
  }
};

// 獲取單一事件詳情
const getEventById = async (req, res) => {
  try {
    const { id } = req.params;

    const pool = await sql.connect(dbConfig);

    const result = await pool.request().input("id", sql.NChar, id).query(`
        SELECT
          e.id,
          e.title,
          CAST(e.description AS NVARCHAR(MAX)) as description,
          e.start_datetime,
          e.end_datetime,
          e.all_day,
          e.event_type,
          e.source_type,
          e.source_id,
          e.background_color,
          e.active,
          e.dep,
          e.created_by,
          e.updated_by,
          e.created_at,
          e.updated_at,
          d.Name as dep_name,
          d.Color as dep_color,
          u.Name as created_by_name
        FROM Events e
        LEFT JOIN dep d ON e.dep = d.id
        LEFT JOIN Users u ON e.created_by = u.ID
        WHERE e.id = @id AND e.active = 1
      `);

    if (result.recordset.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "找不到指定的事件" });
    }

    const event = result.recordset[0];

    // 獲取事件的目標對象
    const targetsResult = await pool.request().input("eventId", sql.NChar, id)
      .query(`
      SELECT target_type, target_id
      FROM Events_targets
      WHERE id = @eventId
    `);

    const targets = targetsResult.recordset.map((target) => ({
      target_type: target.target_type,
      target_id: target.target_id,
    }));

    res.json({
      success: true,
      data: {
        id: event.id,
        title: event.title,
        description: event.description,
        start: event.start_datetime,
        end: event.end_datetime,
        allDay: event.all_day,
        backgroundColor: event.background_color,
        eventType: event.event_type,
        sourceType: event.source_type,
        sourceId: event.source_id,
        dep: event.dep,
        depName: event.dep_name,
        depColor: event.dep_color,
        createdBy: event.created_by,
        createdByName: event.created_by_name,
        createdAt: event.created_at,
        updatedAt: event.updated_at,
        targets: targets,
      },
    });
  } catch (error) {
    console.error("獲取事件詳情失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取事件詳情" });
  }
};

// 新增事件
const createEvent = async (req, res) => {
  try {
    const {
      title,
      description,
      start_datetime,
      end_datetime,
      all_day,
      background_color,
      event_type,
      source_type,
      source_id,
      dep,
      targets,
      created_by,
    } = req.body;

    if (!title || !start_datetime || !dep || !created_by) {
      return res.status(400).json({
        success: false,
        message: "標題、開始時間、部門和建立者為必填項",
      });
    }

    const pool = await sql.connect(dbConfig);

    // 生成新的 ID：日期(YYYYMMDD) + 兩位流水號 (符合 NCHAR(10) 限制)
    const today = new Date();
    const dateStr =
      today.getFullYear().toString() +
      (today.getMonth() + 1).toString().padStart(2, "0") +
      today.getDate().toString().padStart(2, "0");

    // 查詢當天最大 ID
    const maxIdResult = await pool.request().query(`
      SELECT MAX(id) as maxId FROM Events
      WHERE id LIKE '${dateStr}%'
    `);

    let newId;
    if (maxIdResult.recordset[0].maxId) {
      const maxId = maxIdResult.recordset[0].maxId.trim();
      const sequence = parseInt(maxId.substring(8)) + 1;
      newId = dateStr + sequence.toString().padStart(2, "0");
    } else {
      newId = dateStr + "01";
    }

    // 插入新事件
    await pool
      .request()
      .input("id", sql.NChar(10), newId)
      .input("title", sql.NVarChar(200), title.substring(0, 200))
      .input("description", sql.NText, description || "")
      .input("start_datetime", sql.DateTime, new Date(start_datetime))
      .input(
        "end_datetime",
        sql.DateTime,
        end_datetime ? new Date(end_datetime) : new Date(start_datetime)
      )
      .input("all_day", sql.Bit, all_day !== undefined ? all_day : true)
      .input(
        "background_color",
        sql.NVarChar(7),
        (background_color || "#e3f2fd").substring(0, 7)
      )
      .input(
        "event_type",
        sql.NChar(20),
        (event_type || "department").substring(0, 20)
      )
      .input(
        "source_type",
        sql.NChar(20),
        (source_type || "manual").substring(0, 20)
      )
      .input("source_id", sql.NVarChar(50), (source_id || "").substring(0, 50))
      .input("dep", sql.NChar(10), dep.substring(0, 10))
      .input("active", sql.Bit, 1)
      .input("created_by", sql.NChar(6), created_by.substring(0, 6))
      .input("created_at", sql.DateTime, new Date()).query(`
        INSERT INTO Events (
          id, title, description, start_datetime, end_datetime, all_day,
          background_color, event_type, source_type, source_id,
          dep, active, created_by, created_at
        )
        VALUES (
          @id, @title, @description, @start_datetime, @end_datetime, @all_day,
          @background_color, @event_type, @source_type, @source_id,
          @dep, @active, @created_by, @created_at
        )
      `);

    // 如果有目標對象，插入到 Events_targets 表
    if (targets && targets.length > 0) {
      for (const target of targets) {
        // 檢查目標對象的必要屬性
        if (!target.target_type || !target.target_id) {
          console.warn("跳過無效的目標對象:", target);
          continue;
        }

        await pool
          .request()
          .input("id", sql.NChar(10), newId)
          .input(
            "target_type",
            sql.NChar(20),
            target.target_type.substring(0, 20)
          )
          .input(
            "target_id",
            sql.NVarChar(50),
            target.target_id.substring(0, 50)
          ).query(`
            INSERT INTO Events_targets (id, target_type, target_id)
            VALUES (@id, @target_type, @target_id)
          `);
      }
    }

    res.json({
      success: true,
      message: "事件新增成功",
      data: { id: newId },
    });
  } catch (error) {
    console.error("新增事件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法新增事件" });
  }
};

// 更新事件
const updateEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      start_datetime,
      end_datetime,
      all_day,
      background_color,
      event_type,
      source_type,
      source_id,
      dep,
      targets,
      updated_by,
    } = req.body;

    if (!title || !start_datetime || !dep || !updated_by) {
      return res.status(400).json({
        success: false,
        message: "標題、開始時間、部門和更新者為必填項",
      });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查事件是否存在
    const existingEvent = await pool
      .request()
      .input("id", sql.NChar, id)
      .query("SELECT id FROM Events WHERE id = @id AND active = 1");

    if (existingEvent.recordset.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "找不到指定的事件" });
    }

    // 更新事件
    await pool
      .request()
      .input("id", sql.NChar(10), id)
      .input("title", sql.NVarChar(200), title.substring(0, 200))
      .input("description", sql.NText, description || "")
      .input("start_datetime", sql.DateTime, new Date(start_datetime))
      .input(
        "end_datetime",
        sql.DateTime,
        end_datetime ? new Date(end_datetime) : new Date(start_datetime)
      )
      .input("all_day", sql.Bit, all_day !== undefined ? all_day : true)
      .input(
        "background_color",
        sql.NVarChar(7),
        (background_color || "#e3f2fd").substring(0, 7)
      )
      .input(
        "event_type",
        sql.NChar(20),
        (event_type || "department").substring(0, 20)
      )
      .input(
        "source_type",
        sql.NChar(20),
        (source_type || "manual").substring(0, 20)
      )
      .input("source_id", sql.NVarChar(50), (source_id || "").substring(0, 50))
      .input("dep", sql.NChar(10), dep.substring(0, 10))
      .input("updated_by", sql.NChar(6), updated_by.substring(0, 6))
      .input("updated_at", sql.DateTime, new Date()).query(`
        UPDATE Events SET
          title = @title,
          description = @description,
          start_datetime = @start_datetime,
          end_datetime = @end_datetime,
          all_day = @all_day,
          background_color = @background_color,
          event_type = @event_type,
          source_type = @source_type,
          source_id = @source_id,
          dep = @dep,
          updated_by = @updated_by,
          updated_at = @updated_at
        WHERE id = @id
      `);

    // 如果有目標對象，先刪除舊的，再插入新的
    if (targets && targets.length > 0) {
      // 刪除舊的目標對象
      await pool
        .request()
        .input("id", sql.NChar(10), id)
        .query("DELETE FROM Events_targets WHERE id = @id");

      // 插入新的目標對象
      for (const target of targets) {
        await pool
          .request()
          .input("id", sql.NChar(10), id)
          .input(
            "target_type",
            sql.NChar(20),
            target.target_type.substring(0, 20)
          )
          .input(
            "target_id",
            sql.NVarChar(50),
            target.target_id.substring(0, 50)
          ).query(`
            INSERT INTO Events_targets (id, target_type, target_id)
            VALUES (@id, @target_type, @target_id)
          `);
      }
    }

    res.json({ success: true, message: "事件更新成功" });
  } catch (error) {
    console.error("更新事件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法更新事件" });
  }
};

// 刪除事件（軟刪除）
const deleteEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const { deleted_by } = req.body;

    const pool = await sql.connect(dbConfig);

    // 檢查事件是否存在
    const existingEvent = await pool
      .request()
      .input("id", sql.NVarChar, id)
      .query("SELECT id FROM Events WHERE id = @id AND active = 1");

    if (existingEvent.recordset.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "找不到指定的事件" });
    }

    // 軟刪除事件
    await pool
      .request()
      .input("id", sql.NVarChar, id)
      .input("deleted_by", sql.NVarChar, deleted_by || "")
      .input("deleted_at", sql.DateTime, new Date()).query(`
        UPDATE Events SET
          active = 0,
          deleted_by = @deleted_by,
          deleted_at = @deleted_at
        WHERE id = @id
      `);

    res.json({ success: true, message: "事件刪除成功" });
  } catch (error) {
    console.error("刪除事件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法刪除事件" });
  }
};

// 搜尋事件
const searchEvents = async (req, res) => {
  try {
    const { keyword, dep, start, end, userId, userDep } = req.query;

    const pool = await sql.connect(dbConfig);

    let query = `
      SELECT
        e.id,
        e.title,
        CAST(e.description AS NVARCHAR(MAX)) as description,
        e.start_datetime,
        e.end_datetime,
        e.all_day,
        e.event_type,
        e.source_type,
        e.source_id,
        e.background_color,
        e.active,
        e.dep,
        e.created_by,
        e.updated_by,
        e.created_at,
        e.updated_at,
        d.Name as dep_name,
        d.Color as dep_color,
        u.Name as created_by_name
      FROM Events e
      LEFT JOIN dep d ON e.dep = d.id
      LEFT JOIN Users u ON e.created_by = u.ID
      WHERE e.active = 1
    `;

    const params = [];

    // 關鍵字搜尋
    if (keyword) {
      query += ` AND (e.title LIKE @keyword OR CAST(e.description AS NVARCHAR(MAX)) LIKE @keyword)`;
      params.push({
        name: "keyword",
        type: sql.NVarChar,
        value: `%${keyword}%`,
      });
    }

    // 部門篩選
    if (dep) {
      query += ` AND e.dep = @dep`;
      params.push({ name: "dep", type: sql.NVarChar, value: dep });
    }

    // 日期範圍篩選
    if (start) {
      query += ` AND e.end_date >= @start`;
      params.push({
        name: "start",
        type: sql.DateTime,
        value: new Date(start),
      });
    }

    if (end) {
      query += ` AND e.start_date <= @end`;
      params.push({ name: "end", type: sql.DateTime, value: new Date(end) });
    }

    // 根據用戶權限篩選事件
    if (userDep && userId) {
      query += ` AND (
        e.dep = @userDep
        OR EXISTS (
          SELECT 1 FROM Events_targets et
          WHERE et.id = e.id
          AND (
            et.target_type = 'all'
            OR (et.target_type = 'department' AND et.target_id = @userDep)
            OR (et.target_type = 'user' AND et.target_id = @userId)
          )
        )
      )`;
      params.push({ name: "userDep", type: sql.NChar, value: userDep });
      params.push({ name: "userId", type: sql.NChar, value: userId });
    }

    query += ` ORDER BY e.start_datetime ASC`;

    const request = pool.request();

    params.forEach((param) => {
      request.input(param.name, param.type, param.value);
    });

    const result = await request.query(query);

    // 轉換事件格式
    const events = result.recordset.map((event) => {
      let endDateTime = event.end_datetime;

      // 對於全天事件，如果結束時間是 00:00:00，需要加一天以符合 FullCalendar 的排他性結束時間
      if (event.all_day && endDateTime) {
        const endDate = new Date(endDateTime);
        const startDate = new Date(event.start_datetime);

        // 如果結束時間是當天的 00:00:00，表示這是跨天的全天事件
        if (
          endDate.getHours() === 0 &&
          endDate.getMinutes() === 0 &&
          endDate.getSeconds() === 0
        ) {
          // 檢查是否真的是跨天事件（結束日期 > 開始日期）
          if (endDate.getTime() > startDate.getTime()) {
            // 已經是正確的排他性結束時間，不需要修改
            endDateTime = event.end_datetime;
          }
        }
      }

      return {
        id: event.id,
        title: event.title,
        description: event.description,
        start: event.start_datetime,
        end: endDateTime,
        allDay: event.all_day,
        backgroundColor: event.background_color,
        eventType: event.event_type,
        sourceType: event.source_type,
        sourceId: event.source_id,
        dep: event.dep,
        depName: event.dep_name,
        depColor: event.dep_color,
        createdBy: event.created_by,
        createdByName: event.created_by_name,
        createdAt: event.created_at,
        updatedAt: event.updated_at,
      };
    });

    res.json({ success: true, data: events });
  } catch (error) {
    console.error("搜尋事件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法搜尋事件" });
  }
};

// 獲取部門列表和顏色
const getDepartments = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool.request().query(`
     SELECT 
        id,
        Name,
        Color,
		type
      FROM dep
      ORDER BY type ASC
    `);

    const departments = result.recordset.map((dept) => ({
      id: dept.id ? dept.id.trim() : dept.id, // 去除部門ID的空格
      name: dept.Name,
      color: dept.Color || "info", // 預設色系
    }));

    res.json({ success: true, data: departments });
  } catch (error) {
    console.error("獲取部門列表失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取部門列表" });
  }
};

// 隱藏事件（更新 active 狀態）
const hideEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ success: false, message: "未授權" });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查事件是否存在且是否為發布者
    const checkResult = await pool
      .request()
      .input("id", sql.NChar, id)
      .input("userId", sql.NChar, userId).query(`
        SELECT id, created_by
        FROM Events
        WHERE id = @id AND created_by = @userId
      `);

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: "事件不存在或您沒有權限隱藏此事件",
      });
    }

    // 更新事件的 active 狀態為 0（隱藏）
    const result = await pool.request().input("id", sql.NChar, id).query(`
        UPDATE Events
        SET active = 0, updated_at = GETDATE()
        WHERE id = @id
      `);

    if (result.rowsAffected[0] > 0) {
      res.json({ success: true, message: "事件已隱藏" });
    } else {
      res.status(500).json({ success: false, message: "隱藏事件失敗" });
    }
  } catch (error) {
    console.error("隱藏事件失敗:", error);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  searchEvents,
  getDepartments,
  hideEvent,
};
