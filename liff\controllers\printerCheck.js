const net = require("net");
const {
  PrinterStatus,
  OfflineCauseStatus,
  ErrorCauseStatus,
  RollPaperSensorStatus,
} = require("./DeviceStatus");

const DLE_EOT = (n) => Buffer.from([0x10, 0x04, n]);

const statusMap = [
  { n: 1, label: "PrinterStatus", parser: PrinterStatus },
  { n: 2, label: "OfflineCauseStatus", parser: OfflineCauseStatus },
  { n: 3, label: "ErrorCauseStatus", parser: ErrorCauseStatus },
  { n: 4, label: "RollPaperSensorStatus", parser: RollPaperSensorStatus },
];

const queryStatusByte = (ip, port, n, parser) => {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const query = DLE_EOT(n);

    socket.connect(port, ip, () => {
      socket.write(query);
    });

    socket.on("data", (data) => {
      const byte = data[0];
      const statusObj = new parser(byte);
      socket.destroy();
      resolve(statusObj.toJSON());
    });

    socket.on("error", () => {
      socket.destroy();
      resolve(null); // 無法取得
    });

    socket.setTimeout(1000, () => {
      socket.destroy();
      resolve(null); // 逾時
    });
  });
};

// ✅ 主函式：回傳解析結果
const checkPrinterStatusSummary = async (ip, port = 9100) => {
  const statusResults = [];

  for (const { n, label, parser } of statusMap) {
    const result = await queryStatusByte(ip, port, n, parser);
    if (result) {
      statusResults.push({ label, ...result });
    } else {
      // 若其中一個查詢無法回傳 → 可能為設備未連線
      return {
        isOk: false,
        errors: [`無法連線到出單機 ${ip}（未開機或連線失敗）`],
        raw: [],
      };
    }
  }

  const errors = [];
  for (const status of statusResults) {
    if (!status.statuses) continue;
    for (const s of status.statuses) {
      if (s.status === "error") {
        errors.push(`[${status.label}] ${s.label}`);
      }
    }
  }

  return {
    isOk: errors.length === 0,
    errors,
    raw: statusResults,
  };
};

module.exports = { checkPrinterStatusSummary };
