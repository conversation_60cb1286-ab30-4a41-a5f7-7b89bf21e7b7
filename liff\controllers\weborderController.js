const sql = require("mssql");
require("dotenv").config();
const dbConfig = require("../config/db");
const { sendToPrinter } = require("./printController");
const { withPrinterLock } = require("./PrinterLockManager");
const { checkPrinterStatusSummary } = require("./printerCheck");

const getDbConfig = (IP) => {
  if (!IP) throw new Error("缺少 IP");

  return {
    user: process.env.BRANCH_USER,
    password: process.env.BRANCH_PASSWORD,
    server: IP.trim(),
    database: process.env.BRANCH_DB_NAME,
    port: Number(process.env.BRANCH_PORT),
    options: {
      encrypt: false,
      trustServerCertificate: true,
      connectTimeout: 1000,
      retryAttempts: 3,
    },
  };
};

const getPrintConfig = (IP, Hostname) => {
  if (!IP) throw new Error("缺少 IP");
  return {
    user: process.env.BRANCH_USER,
    password: process.env.BRANCH_PASSWORD,
    server: IP.trim(),
    database: Hostname + process.env.BRANCH_PRINT_NAME,
    port: Number(process.env.BRANCH_PORT),
    options: {
      encrypt: false,
      trustServerCertificate: true,
      connectTimeout: 1000,
      retryAttempts: 3,
    },
  };
};

const insertOrder = async (req, res) => {
  // 🆕 套餐處理流程:
  // 1. 獲取當前訂單的最大 tablesql 值
  // 2. 對於包含套餐的項目，增加 tablesql 計數器並分配一個唯一的 tablesql 值
  // 3. 將主項目寫入 orderMainN 和 orderMainNMinS 表，包含 tablesql 欄位
  // 4. 解析套餐資訊，格式為陣列 [{ sets_code, sets_title, type, item, qty }, ...]
  // 5. 為每個套餐項目創建一個唯一的 scode (原 scode + 3位數 tablesql)
  // 6. 查詢套餐項目的詳細資訊 (code, price, unit)
  // 7. 寫入 OrderSubN 表，如有相同項目則合併數量
  // 8. 寫入 OrderSubNMinS 表，不做合併

  const { ip, scode, room, roomname, person, APIPrint, hostname, items } =
    req.body;
  const printList = [];
  const printposList = [];

  if (!scode || !room || !Array.isArray(items)) {
    return res.status(400).json({ success: false, message: "❌ 缺少必要資料" });
  }
  const config = getDbConfig(ip);
  try {
    const pool = await new sql.ConnectionPool(config).connect();

    // 🔢 取得 sno 起始值（從明細表）
    const maxResult = await pool.request().input("scode", sql.VarChar, scode)
      .query(`
    SELECT
      MAX(CAST(sno AS INT)) AS maxSno,
      MAX(CAST(oround AS INT)) AS maxOround
    FROM orderMainNMinS
    WHERE Scode = @scode
  `);

    // 🔢 取得 tablesql 最大值
    const maxTablesqlResult = await pool
      .request()
      .input("scode", sql.VarChar, scode).query(`
    SELECT
      ISNULL(MAX(CAST(tablesql AS INT)), 0) AS maxTablesql
    FROM orderMainN
    WHERE Scode = @scode
  `);

    let tablesqlCounter = maxTablesqlResult.recordset[0].maxTablesql || 0;
    //console.log(`🔢 當前訂單最大 tablesql: ${tablesqlCounter}`);

    const openmodeResult = await pool
      .request()
      .input("scode", sql.VarChar, scode).query(`
    SELECT ISNULL(omenum.name, '未對應消費模式') AS name
    FROM ordernow
    LEFT JOIN omenum ON ordernow.HideGroup = omenum.code
    WHERE ordernow.code = @scode
  `);

    const openmode = openmodeResult.recordset?.[0]?.name || "未對應消費模式";

    let snoCounter = maxResult.recordset[0].maxSno || 0;
    let oroundCounter = maxResult.recordset[0].maxOround || 0;

    oroundCounter++; // 本次送單用這個 oround
    const oroundStr = oroundCounter.toString();

    for (const item of items) {
      const { code, name, price, spec, tastes, unit, qty, sets } = item;

      const sname = spec || "";
      const Oremname = tastes || ""; // ✅ 不要再 .join()
      const simname = name;
      const oprice = price;
      const cost = 0;

      // 🔍 查 omenum + cclass 取得折扣與服務費
      const dsResult = await pool.request().input("code", sql.VarChar, code)
        .query(`
          SELECT ISNULL(cclass.discount, '0') AS discount,
                 ISNULL(cclass.service, '0') AS service
          FROM omenum
          LEFT JOIN cclass ON omenum.cclass = cclass.code
          WHERE omenum.code = @code
        `);

      const discount = dsResult.recordset[0]?.discount || "0";
      const service = dsResult.recordset[0]?.service || "0";

      // 🆕 如果有套餐資料，每筆主檔都單獨寫入，不合併
      let tablesql = null;
      if (sets && Array.isArray(sets) && sets.length > 0) {
        // 增加 tablesql 計數
        tablesqlCounter++;
        tablesql = tablesqlCounter;
        //console.log(`🍱 項目 ${code} 包含套餐，分配 tablesql: ${tablesql}`);
      }

      // 🆕 查主檔最大 sno，+1 後寫入主檔
      const mainSnoResult = await pool
        .request()
        .input("scode", sql.VarChar, scode)
        .query(
          `SELECT MAX(CAST(sno AS INT)) AS maxSno FROM orderMainN WHERE Scode = @scode`
        );
      const maxSnoMain = mainSnoResult.recordset[0].maxSno || 0;
      const snoMain = (maxSnoMain + 1).toString().padStart(2, "0");

      // 🆕 如果有套餐，不合併，直接寫入
      if (sets && Array.isArray(sets) && sets.length > 0) {
        await pool
          .request()
          .input("Scode", sql.VarChar, scode)
          .input("code", sql.VarChar, code)
          .input("oprice", sql.Int, oprice)
          .input("cost", sql.Int, cost)
          .input("name", sql.NVarChar, name)
          .input("qty", sql.Int, qty)
          .input("sno", sql.VarChar, snoMain)
          .input("unit", sql.NVarChar, unit)
          .input("OYN", sql.VarChar, "1") // 有套餐時 OYN='1'
          .input("Oremname", sql.NVarChar, Oremname)
          .input("prn", sql.VarChar, "1")
          .input("EMPLC", sql.VarChar, "9998")
          .input("empln", sql.NVarChar, "Quasar")
          .input("discount", sql.NVarChar, discount)
          .input("service", sql.NVarChar, service)
          .input("sname", sql.NVarChar, sname)
          .input("taste", sql.VarChar, snoMain)
          .input("room", sql.VarChar, room)
          .input("simname", sql.NVarChar, simname)
          .input("ono", sql.VarChar, "01")
          .input("damt", sql.VarChar, "100")
          .input("Oround", sql.VarChar, "1")
          .input("tablesql", tablesql === null ? sql.Int : sql.Int, tablesql)
          .query(`
            INSERT INTO orderMainN
            (Scode, code, oprice, cost, name, qty, sno, unit, OYN, Oremname, prn, EMPLC, empln, discount, service, sname, taste, room, simname, ono, damt, Oround, tablesql)
            VALUES
            (@Scode, @code, @oprice, @cost, @name, @qty, @sno, @unit, @OYN, @Oremname, @prn, @EMPLC, @empln, @discount, @service, @sname, @taste, @room, @simname, @ono, @damt, @Oround, @tablesql)
          `);
      } else {
        // ✅ 查主檔是否已存在此項（合併判斷）
        const existsResult = await pool
          .request()
          .input("scode", sql.VarChar, scode)
          .input("code", sql.VarChar, code)
          .input("sname", sql.NVarChar, sname)
          .input("Oremname", sql.NVarChar, Oremname).query(`
          SELECT qty FROM orderMainN
          WHERE scode = @scode AND code = @code AND sname = @sname AND Oremname = @Oremname
        `);

        if (existsResult.recordset.length > 0) {
          // ✅ 有相同項目，更新數量
          await pool
            .request()
            .input("addQty", sql.Int, qty)
            .input("scode", sql.VarChar, scode)
            .input("code", sql.VarChar, code)
            .input("sname", sql.NVarChar, sname)
            .input("Oremname", sql.NVarChar, Oremname).query(`
            UPDATE orderMainN
            SET qty = qty + @addQty
            WHERE scode = @scode AND code = @code AND sname = @sname AND Oremname = @Oremname
          `);
        } else {
          // 🆕 沒有重複，寫入主檔
          await pool
            .request()
            .input("Scode", sql.VarChar, scode)
            .input("code", sql.VarChar, code)
            .input("oprice", sql.Int, oprice)
            .input("cost", sql.Int, cost)
            .input("name", sql.NVarChar, name)
            .input("qty", sql.Int, qty)
            .input("sno", sql.VarChar, snoMain)
            .input("unit", sql.NVarChar, unit)
            .input("OYN", sql.VarChar, "2") // 沒有套餐時 OYN='2'
            .input("Oremname", sql.NVarChar, Oremname)
            .input("prn", sql.VarChar, "1")
            .input("EMPLC", sql.VarChar, "9998")
            .input("empln", sql.NVarChar, "Quasar")
            .input("discount", sql.NVarChar, discount)
            .input("service", sql.NVarChar, service)
            .input("sname", sql.NVarChar, sname)
            .input("taste", sql.VarChar, snoMain)
            .input("room", sql.VarChar, room)
            .input("simname", sql.NVarChar, simname)
            .input("ono", sql.VarChar, "01")
            .input("damt", sql.VarChar, "100")
            .input("Oround", sql.VarChar, "1")
            .input("tablesql", sql.Int, null).query(`
            INSERT INTO orderMainN
            (Scode, code, oprice, cost, name, qty, sno, unit, OYN, Oremname, prn, EMPLC, empln, discount, service, sname, taste, room, simname, ono, damt, Oround, tablesql)
            VALUES
            (@Scode, @code, @oprice, @cost, @name, @qty, @sno, @unit, @OYN, @Oremname, @prn, @EMPLC, @empln, @discount, @service, @sname, @taste, @room, @simname, @ono, @damt, @Oround, @tablesql)
          `);
        }
      }

      // ✅ 每筆都寫入明細表
      snoCounter++;
      const snoMinS = snoCounter.toString().padStart(2, "0");

      await pool
        .request()
        .input("Scode", sql.VarChar, scode)
        .input("code", sql.VarChar, code)
        .input("oprice", sql.Int, oprice)
        .input("cost", sql.Int, cost)
        .input("name", sql.NVarChar, name)
        .input("qty", sql.Int, qty)
        .input("sno", sql.VarChar, snoMinS)
        .input("unit", sql.NVarChar, unit)
        .input(
          "OYN",
          sql.VarChar,
          sets && Array.isArray(sets) && sets.length > 0 ? "1" : "2"
        ) // 有套餐時 OYN='1'，沒有套餐時 OYN='2'
        .input("Oremname", sql.NVarChar, Oremname)
        .input("prn", sql.VarChar, "1")
        .input("EMPLC", sql.VarChar, "9998")
        .input("empln", sql.NVarChar, "Quasar")
        .input("discount", sql.NVarChar, discount)
        .input("service", sql.NVarChar, service)
        .input("sname", sql.NVarChar, sname)
        .input("taste", sql.VarChar, snoMinS)
        .input("room", sql.VarChar, room)
        .input("simname", sql.NVarChar, simname)
        .input("ono", sql.VarChar, "01")
        .input("damt", sql.VarChar, "100")
        .input("R1", sql.VarChar, "+")
        .input("oround", sql.VarChar, oroundStr) // 加入 oround
        .input(
          "tablesql",
          sql.Int,
          sets && Array.isArray(sets) && sets.length > 0 ? tablesql : null
        ) // 只有有套餐時才寫入 tablesql，無套餐時為 null
        .query(`
          INSERT INTO orderMainNMinS
          (Scode, code, oprice, cost, name, qty, sno, unit, OYN, Oremname, prn, EMPLC, empln, discount, service, sname, taste, room, simname, ono, damt, R1, oround, tablesql)
          VALUES
          (@Scode, @code, @oprice, @cost, @name, @qty, @sno, @unit, @OYN, @Oremname, @prn, @EMPLC, @empln, @discount, @service, @sname, @taste, @room, @simname, @ono, @damt, @R1, @oround, @tablesql)
        `);

      // 🆕 處理套餐資料
      if (sets && Array.isArray(sets) && sets.length > 0 && tablesql !== null) {
        // 建立 OrderSubN 的 scode
        const subScode = `${scode}${tablesql.toString().padStart(3, "0")}`;
        //console.log(`🔑 套餐訂單 scode: ${subScode}`);

        try {
          //console.log(`🍱 處理套餐資料:`, sets);

          // 只有當套餐陣列不為空時才處理
          if (sets.length > 0) {
            for (let i = 0; i < sets.length; i++) {
              const setItem = sets[i];
              //console.log(`🔍 處理套餐項目 ${i + 1}:`, setItem);

              // 從陣列物件中獲取套餐資訊
              const setTitle = setItem.sets_title;
              const itemName = setItem.item.name;
              const itemSname = setItem.item.sname || "";
              const itemQty = setItem.qty || 1;

              //console.log(
              //  `📋 解析結果 - 套餐: ${setTitle}, 品項: ${itemName}, 規格: ${
              //    itemSname || "無"
              //  }, 數量: ${itemQty}`
              //);

              // 查詢套餐項目資訊
              const itemInfoResult = await pool
                .request()
                .input("name", sql.NVarChar, itemName).query(`
                  SELECT TOP 1 code, price, ISNULL(unit, '份') as unit
                  FROM SetsItems
                  WHERE name = @name
                `);

              if (itemInfoResult.recordset.length === 0) {
                console.error(`❌ 找不到套餐項目: ${itemName}`);
                continue;
              }

              const itemInfo = itemInfoResult.recordset[0];
              const itemCode = itemInfo.code;
              const itemPrice = itemInfo.price || 0;
              const itemUnit = itemInfo.unit || "份";

              //console.log(
              //  `🔢 套餐項目資訊 - 代碼: ${itemCode}, 價格: ${itemPrice}, 單位: ${itemUnit}`
              //);

              // 查詢 OrderSubN 是否已存在相同項目
              const subExistsResult = await pool
                .request()
                .input("scode", sql.VarChar, subScode)
                .input("code", sql.VarChar, itemCode)
                .input("sname", sql.NVarChar, itemSname).query(`
                  SELECT qty FROM OrderSubN
                  WHERE scode = @scode AND code = @code AND sname = @sname
                `);

              // 查詢 OrderSubN 的最大 sno
              const subSnoResult = await pool
                .request()
                .input("scode", sql.VarChar, subScode).query(`
                  SELECT MAX(CAST(sno AS INT)) AS maxSno FROM OrderSubN WHERE Scode = @scode
                `);
              const maxSubSno = subSnoResult.recordset[0].maxSno || 0;
              const subSno = (maxSubSno + 1).toString().padStart(2, "0");

              if (subExistsResult.recordset.length > 0) {
                // 更新數量
                //console.log(
                //  `🔄 更新套餐項目數量: ${itemName}, 原數量: ${subExistsResult.recordset[0].qty}, 新增: ${itemQty}`
                //);
                await pool
                  .request()
                  .input("addQty", sql.Int, itemQty)
                  .input("scode", sql.VarChar, subScode)
                  .input("code", sql.VarChar, itemCode)
                  .input("sname", sql.NVarChar, itemSname).query(`
                    UPDATE OrderSubN
                    SET qty = qty + @addQty
                    WHERE scode = @scode AND code = @code AND sname = @sname
                  `);
              } else {
                // 寫入 OrderSubN
                //(`➕ 新增套餐項目: ${itemName}, sno: ${subSno}`);
                await pool
                  .request()
                  .input("Scode", sql.VarChar, subScode)
                  .input("code", sql.VarChar, itemCode)
                  .input("oprice", sql.Int, itemPrice)
                  .input("cost", sql.Int, 0)
                  .input("name", sql.NVarChar, itemName)
                  .input("qty", sql.Int, itemQty)
                  .input("sno", sql.VarChar, subSno)
                  .input("unit", sql.NVarChar, itemUnit)
                  .input("prn", sql.VarChar, "1")
                  .input("emplc", sql.VarChar, "9998")
                  .input("empln", sql.NVarChar, "Quasar")
                  .input("discount", sql.VarChar, "0")
                  .input("service", sql.VarChar, "0")
                  .input("sname", sql.NVarChar, itemSname)
                  .input("taste", sql.VarChar, subSno)
                  .input("room", sql.VarChar, room)
                  .input("simname", sql.NVarChar, itemName)
                  .input("ono", sql.VarChar, "01")
                  .input("damt", sql.VarChar, "100")
                  .input("OYN", sql.VarChar, "2") // 加入 OYN
                  .query(`
                    INSERT INTO OrderSubN
                    (Scode, code, oprice, cost, name, qty, sno, unit, prn, EMPLC, empln, discount, service, sname, taste, room, simname, ono, damt, OYN)
                    VALUES
                    (@Scode, @code, @oprice, @cost, @name, @qty, @sno, @unit, @prn, @EMPLC, @empln, @discount, @service, @sname, @taste, @room, @simname, @ono, @damt, @OYN)
                  `);
              }

              // 寫入 OrderSubNMinS (不做合併)
              // 查詢 OrderSubNMinS 的最大 sno
              const subMinsSnoResult = await pool
                .request()
                .input("scode", sql.VarChar, subScode).query(`
                  SELECT MAX(CAST(sno AS INT)) AS maxSno FROM OrderSubNMinS WHERE Scode = @scode
                `);
              const maxSubMinsSno = subMinsSnoResult.recordset[0].maxSno || 0;
              const subMinsSno = (maxSubMinsSno + 1)
                .toString()
                .padStart(2, "0");

              //console.log(`📝 寫入套餐明細: ${itemName}, sno: ${subMinsSno}`);
              await pool
                .request()
                .input("Scode", sql.VarChar, subScode)
                .input("code", sql.VarChar, itemCode)
                .input("oprice", sql.Int, itemPrice)
                .input("cost", sql.Int, 0)
                .input("name", sql.NVarChar, itemName)
                .input("qty", sql.Int, itemQty)
                .input("sno", sql.VarChar, subMinsSno)
                .input("unit", sql.NVarChar, itemUnit)
                .input("prn", sql.VarChar, "1")
                .input("emplc", sql.VarChar, "9998")
                .input("empln", sql.NVarChar, "Quasar")
                .input("discount", sql.VarChar, "0")
                .input("service", sql.VarChar, "0")
                .input("sname", sql.NVarChar, itemSname)
                .input("taste", sql.VarChar, subMinsSno)
                .input("room", sql.VarChar, room)
                .input("simname", sql.NVarChar, itemName)
                .input("ono", sql.VarChar, "01")
                .input("damt", sql.VarChar, "100")
                .input("R1", sql.VarChar, "+")
                .input("OYN", sql.VarChar, "2") // 加入 OYN
                .query(`
                  INSERT INTO OrderSubNMinS
                  (Scode, code, oprice, cost, name, qty, sno, unit, prn, EMPLC, empln, discount, service, sname, taste, room, simname, ono, damt, R1, OYN)
                  VALUES
                  (@Scode, @code, @oprice, @cost, @name, @qty, @sno, @unit, @prn, @EMPLC, @empln, @discount, @service, @sname, @taste, @room, @simname, @ono, @damt, @R1, @OYN)
                `);
            }
          } else {
            //console.log(`📝 套餐陣列為空，不處理套餐項目`);
          }

          //console.log(`✅ 套餐處理完成: ${subScode}`);
        } catch (setErr) {
          console.error("❌ 處理套餐資料錯誤:", setErr);
        }
      }

      // 🔍 查列印設定
      // 檢查主項目是否需要出單
      const printRes = await pool.request().input("code", sql.VarChar, code)
        .query(`
      SELECT 
        kclass.NAME AS kname,
        vclass.prn,
        vclass.ptype,
        vclass_net.ip,
        vclass.pseq
      FROM omenum
      LEFT JOIN kclass ON kclass.CODE = omenum.C1
      LEFT JOIN vclass ON vclass.code = kclass.VCODE1
      LEFT JOIN vclass_net ON vclass_net.code = vclass.code
      WHERE omenum.code = @code and (prn='1' or prn='3')
    `);
      //console.log(printRes);
      const p = printRes.recordset[0] || {};

      // 🆕 檢查套餐內品項是否需要出單
      let setNeedsPrinting = false;
      let setsPrintInfo = [];

      if (sets && Array.isArray(sets) && sets.length > 0) {
        // 檢查套餐內的每個品項是否需要出單
        for (const setItem of sets) {
          if (!setItem.item || !setItem.item.name) continue;

          // 查詢套餐項目的編碼
          const itemInfoResult = await pool
            .request()
            .input("name", sql.NVarChar, setItem.item.name).query(`
              SELECT TOP 1 code FROM SetsItems WHERE name = @name
            `);

          if (itemInfoResult.recordset.length === 0) continue;
          const itemCode = itemInfoResult.recordset[0].code;

          // 檢查該品項是否需要出單
          const setPrintRes = await pool
            .request()
            .input("code", sql.VarChar, itemCode).query(`
              SELECT 
                kclass.NAME AS kname,
                vclass.prn,
                vclass.ptype,
                vclass_net.ip,
                vclass.pseq
              FROM omenum
              LEFT JOIN kclass ON kclass.CODE = omenum.C1
              LEFT JOIN vclass ON vclass.code = kclass.VCODE1
              LEFT JOIN vclass_net ON vclass_net.code = vclass.code
              WHERE omenum.code = @code and (prn='1' or prn='3')
            `);

          if (setPrintRes.recordset.length > 0) {
            setNeedsPrinting = true;
            setsPrintInfo.push({
              itemCode,
              itemName: setItem.item.name,
              itemSname: setItem.item.sname || "",
              itemQty: setItem.qty || 1,
              printInfo: setPrintRes.recordset[0],
            });
          }
        }
      }

      // ✅ 建立列印項目（含 scode 末4碼）
      if (printRes.recordset.length === 0 && !setNeedsPrinting) {
        //console.log(`⏭️ ${code} 及其套餐品項均無需列印`);
      } else {
        if (APIPrint) {
          // ✅ 強制全部都走 POS 廠商出單，不管有沒有 ip
          const printItem = {
            scode: scode.slice(-4),
            person,
            roomname: roomname || room,
            name,
            openmode,
            spec: sname,
            tastes: Oremname,
            qty,
            unit,
            kname: p.kname || "",
            prn: printRes.recordset.length > 0 ? p.prn || "1" : "0", // 如果主項目不出單但套餐要出單，標記prn為0
            ptype: p.ptype || "1",
            rseq: p.pseq || 1, // Dprint 用的欄位
            oround: oroundStr,
            isSetHeader: setNeedsPrinting && printRes.recordset.length === 0, // 標記是否為只有套餐內品項需要出單的套餐抬頭
            sets: setsPrintInfo.length > 0 ? setsPrintInfo : undefined,
          };

          printposList.push(printItem);
        } else if (p.ip || setNeedsPrinting) {
          // ✅ 只有當 APIPrint === false 且有IP或套餐需要出單時才走印表機
          const printItem = {
            scode: scode.slice(-4),
            person,
            roomname: roomname || room,
            name,
            openmode,
            spec: sname,
            tastes: Oremname,
            qty,
            unit,
            kname: p.kname || "",
            prn: printRes.recordset.length > 0 ? p.prn || "1" : "0", // 如果主項目不出單但套餐要出單，標記prn為0
            ptype: p.ptype || "1",
            ip: p.ip ? p.ip.trim() : "",
            oround: oroundStr,
            isSetHeader: setNeedsPrinting && printRes.recordset.length === 0, // 標記是否為只有套餐內品項需要出單的套餐抬頭
            sets: setsPrintInfo.length > 0 ? setsPrintInfo : undefined,
          };

          printList.push(printItem);
          //console.log(printItem);
        } else {
          console.log(`⏭️ ${code} 無 IP，APIPrint 為 false，跳過`);
        }
      }
    }
    //跑完迴圈 在送出printList
    if (printList.length > 0 && !APIPrint) {
      //console.log(printList);
      await handlePrintJobs(printList);
    }
    if (printposList.length > 0 && APIPrint) {
      await handlePOSPrintJobs(ip, hostname, printposList);
    }

    res.json({ success: true, message: "點餐資料寫入完成" });
  } catch (err) {
    console.error("❌ insertOrderItems Error:", err);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤", error: err.message });
  }
};

const handlePrintJobs = async (printList) => {
  const grouped = {
    solo: [], // 一品一單
    grouped: {}, // 多品合併（ptype = '2'）
  };

  for (const item of printList) {
    if (item.prn !== "1" && !item.isSetHeader) continue;

    const cleanIp = (item.ip || "").trim();
    const cleanKname = (item.kname || "").trim();

    // 🆕 處理套餐項目
    if (item.sets && Array.isArray(item.sets) && item.sets.length > 0) {
      // 如果是套餐，需要為每個子項目創建獨立的出單

      if (item.ptype === "1") {
        // 單張出單：每個套餐子項目連同標題一起出單
        for (const setItem of item.sets) {
          // 為每個套餐項目創建一個獨立的出單，包含套餐抬頭和當前子項目
          const setOrderItems = [
            // 套餐抬頭
            {
              name: item.name,
              spec: item.spec,
              tastes: item.tastes,
              qty: item.qty,
              unit: item.unit,
              isSetHeader: true, // 標記為套餐抬頭
            },
            // 當前子項目
            {
              name: setItem.itemName,
              spec: setItem.itemSname || "",
              tastes: "", // 套餐子項目通常沒有口味
              qty: setItem.itemQty,
              unit: "份", // 默認單位
              isSetItem: true, // 標記為套餐子項目
            },
          ];

          // ✅ 將套餐轉為 group 格式（items 包起來）
          grouped.solo.push({
            scode: item.scode,
            person: item.person,
            openmode: item.openmode,
            roomname: item.roomname,
            kname: cleanKname,
            prn: item.prn,
            ptype: item.ptype,
            ip: cleanIp,
            oround: item.oround,
            items: setOrderItems,
            hasSetItems: true, // 標記包含套餐項目
          });
        }
      } else {
        // ✅ 多品合併列印（套餐）
        const key = `${cleanIp}|${cleanKname}|${item.scode}|${item.roomname}|${item.person}`;

        if (!grouped.grouped[key]) {
          grouped.grouped[key] = {
            scode: item.scode,
            person: item.person,
            openmode: item.openmode,
            roomname: item.roomname,
            kname: cleanKname,
            prn: item.prn,
            ptype: item.ptype,
            ip: cleanIp,
            oround: item.oround,
            items: [],
            hasSetItems: true, // 標記包含套餐項目
          };
        }

        // 先加入套餐主項目（作為抬頭）
        grouped.grouped[key].items.push({
          name: item.name,
          spec: item.spec,
          tastes: item.tastes,
          qty: item.qty,
          unit: item.unit,
          isSetHeader: true, // 標記為套餐抬頭
        });

        // 加入套餐子項目
        for (const setItem of item.sets) {
          grouped.grouped[key].items.push({
            name: setItem.itemName,
            spec: setItem.itemSname || "",
            tastes: "", // 套餐子項目通常沒有口味
            qty: setItem.itemQty,
            unit: "份", // 默認單位
            isSetItem: true, // 標記為套餐子項目
          });
        }
      }
    } else {
      // 🔄 原有的非套餐項目處理邏輯
      // 如果是只有套餐內品項需要出單的套餐抬頭，但主項目不出單，則跳過
      if (item.isSetHeader && item.prn === "0") {
        //console.log(
        //  `⏭️ 套餐抬頭 ${item.name} 不需要單獨出單，將與套餐項目一起出單`
        //);
        continue;
      }

      const formattedItem = {
        name: item.name,
        spec: item.spec,
        tastes: item.tastes,
        qty: item.qty,
        unit: item.unit,
        isSetHeader: item.isSetHeader || false, // 保留可能的套餐抬頭標記
      };

      if (item.ptype === "1") {
        // ✅ 將單品轉為 group 格式（items 包起來）
        grouped.solo.push({
          scode: item.scode,
          person: item.person,
          openmode: item.openmode,
          roomname: item.roomname,
          kname: cleanKname,
          prn: item.prn,
          ptype: item.ptype,
          ip: cleanIp,
          oround: item.oround,
          items: [formattedItem],
        });
      } else {
        // ✅ 多品合併列印
        const key = `${cleanIp}|${cleanKname}|${item.scode}|${item.roomname}|${item.person}`;

        if (!grouped.grouped[key]) {
          grouped.grouped[key] = {
            scode: item.scode,
            person: item.person,
            openmode: item.openmode,
            roomname: item.roomname,
            kname: cleanKname,
            prn: item.prn,
            ptype: item.ptype,
            ip: cleanIp,
            oround: item.oround,
            items: [],
          };
        }

        grouped.grouped[key].items.push(formattedItem);
      }
    }
  }

  // ✅ 出單：一品一張（逐筆送）
  for (const solo of grouped.solo) {
    // 檢查是否為套餐項目，如果是，則每個子項目單獨出單
    if (solo.hasSetItems && solo.items.length > 2) {
      //console.log(`🍱 套餐項目拆分印表機出單: ${solo.items[0].name}`);

      // 獲取套餐抬頭
      const header = solo.items.find((item) => item.isSetHeader);

      // 獲取所有套餐子項目
      const setItems = solo.items.filter((item) => item.isSetItem);

      // 為每個子項目創建單獨的出單
      for (const setItem of setItems) {
        const singleSetJob = {
          ...solo,
          items: [header, setItem], // 只包含套餐抬頭和當前子項目
          isSingleSetItem: true, // 標記為單獨的套餐子項目出單
        };
        await printToKitchen(solo.ip, [singleSetJob]);
      }
    } else {
      // 普通項目或已經拆分好的套餐項目
      await printToKitchen(solo.ip, [solo]);
    }
  }

  // ✅ 出單：多品同張（按群組送）
  for (const group of Object.values(grouped.grouped)) {
    await printToKitchen(group.ip, [group]);
  }
};

const printToKitchen = async (ip, items) => {
  await withPrinterLock(ip, 9100, async () => {
    const result = await checkPrinterStatusSummary(ip);

    if (!result.isOk) {
      console.warn(`❌ 出單機 ${ip} 異常：${result.errors.join("; ")}`, items);

      try {
        const pool = await sql.connect(dbConfig);

        await pool
          .request()
          .input("ip", sql.VarChar, ip) // ✅ 改成實際的 ip 參數
          .input("reason", sql.NVarChar, result.errors.join("; ")) // ✅ 使用錯誤訊息
          .input("items", sql.NVarChar, JSON.stringify(items)) // ✅ 實際印表資料
          .query(`
            INSERT INTO PrintError (ip, reason, items)
            VALUES (@ip, @reason, @items)
          `);

        console.log("異常資料已寫入 PrintError");
      } catch (err) {
        console.error("❌ 寫入 PrintError 失敗:", err.message);
      }

      return;
    }

    await sendToPrinter(ip, items); // ✅ 印表正常
  });
};

const handlePOSPrintJobs = async (ip, hostname, printposList) => {
  const grouped = {
    solo: [],
    grouped: {},
  };
  //console.log(printposList);
  for (const item of printposList) {
    const cleanKname = (item.kname || "").trim();

    // ✅ 加入 rseq 作為 group key 的一部分
    const key = `${cleanKname}|${item.roomname}|${item.person}|${item.rseq}`;

    // 🆕 處理套餐項目
    if (item.sets && Array.isArray(item.sets) && item.sets.length > 0) {
      // 如果是套餐，需要為每個子項目創建獨立的出單

      // 根據 ptype 決定是否合併出單
      if (item.ptype === "1") {
        // 單張出單：每個套餐子項目連同標題一起出單
        for (const setItem of item.sets) {
          // 為每個套餐項目創建一個獨立的出單，包含套餐抬頭和當前子項目
          const setOrderItems = [
            // 套餐抬頭
            {
              name: item.name,
              spec: item.spec,
              tastes: item.tastes,
              qty: item.qty,
              unit: item.unit,
              isSetHeader: true, // 標記為套餐抬頭
            },
            // 當前子項目
            {
              name: setItem.itemName,
              spec: setItem.itemSname || "",
              tastes: "", // 套餐子項目通常沒有口味
              qty: setItem.itemQty,
              unit: "份", // 默認單位
              isSetItem: true, // 標記為套餐子項目
            },
          ];

          grouped.solo.push({
            scode: item.scode,
            person: item.person,
            openmode: item.openmode,
            roomname: item.roomname,
            kname: cleanKname,
            prn: item.prn,
            ptype: item.ptype,
            rseq: item.rseq,
            oround: item.oround,
            items: setOrderItems,
            hasSetItems: true, // 標記包含套餐項目
          });
        }
      } else {
        // 合併出單：所有套餐項目一起出單
        if (!grouped.grouped[key]) {
          grouped.grouped[key] = {
            scode: item.scode,
            person: item.person,
            openmode: item.openmode,
            roomname: item.roomname,
            kname: cleanKname,
            prn: item.prn,
            ptype: item.ptype,
            rseq: item.rseq,
            oround: item.oround,
            items: [],
            hasSetItems: true, // 標記包含套餐項目
          };
        }

        // 先加入套餐主項目（作為抬頭）
        grouped.grouped[key].items.push({
          name: item.name,
          spec: item.spec,
          tastes: item.tastes,
          qty: item.qty,
          unit: item.unit,
          isSetHeader: true, // 標記為套餐抬頭
        });

        // 加入套餐子項目
        for (const setItem of item.sets) {
          grouped.grouped[key].items.push({
            name: setItem.itemName,
            spec: setItem.itemSname || "",
            tastes: "", // 套餐子項目通常沒有口味
            qty: setItem.itemQty,
            unit: "份", // 默認單位
            isSetItem: true, // 標記為套餐子項目
          });
        }
      }
    } else {
      // 🔄 原有的非套餐項目處理邏輯
      const formattedItem = {
        name: item.name,
        spec: item.spec,
        tastes: item.tastes,
        qty: item.qty,
        unit: item.unit,
        isSetHeader: item.isSetHeader || false, // 保留可能的套餐抬頭標記
      };

      // 如果是只有套餐內品項需要出單的套餐抬頭，但主項目不出單，則跳過
      if (item.isSetHeader && item.prn === "0") {
        console.log(
          `⏭️ 套餐抬頭 ${item.name} 不需要單獨出單，將與套餐項目一起出單`
        );
        continue;
      }

      if (item.ptype === "1") {
        grouped.solo.push({
          scode: item.scode,
          person: item.person,
          openmode: item.openmode,
          roomname: item.roomname,
          kname: cleanKname,
          prn: item.prn,
          ptype: item.ptype,
          rseq: item.rseq,
          oround: item.oround,
          items: [formattedItem],
        });
      } else {
        if (!grouped.grouped[key]) {
          grouped.grouped[key] = {
            scode: item.scode,
            person: item.person,
            openmode: item.openmode,
            roomname: item.roomname,
            kname: cleanKname,
            prn: item.prn,
            ptype: item.ptype,
            rseq: item.rseq,
            oround: item.oround,
            items: [],
          };
        }
        grouped.grouped[key].items.push(formattedItem);
      }
    }
  }

  // ✅ 單品出單：逐筆送到 POSAPI
  for (const job of grouped.solo) {
    // 檢查是否為套餐項目，如果是，則每個子項目單獨出單
    if (job.hasSetItems && job.items.length > 2) {
      console.log(`🍱 套餐項目拆分出單: ${job.items[0].name}`);

      // 獲取套餐抬頭
      const header = job.items.find((item) => item.isSetHeader);

      // 獲取所有套餐子項目
      const setItems = job.items.filter((item) => item.isSetItem);

      // 為每個子項目創建單獨的出單
      for (const setItem of setItems) {
        const singleSetJob = {
          ...job,
          items: [header, setItem], // 只包含套餐抬頭和當前子項目
          isSingleSetItem: true, // 標記為單獨的套餐子項目出單
        };
        await POSAPI(ip, hostname, singleSetJob);
      }
    } else {
      // 普通項目或已經拆分好的套餐項目
      await POSAPI(ip, hostname, job);
    }
  }

  // ✅ 合併出單：每組送一次 POSAPI
  for (const job of Object.values(grouped.grouped)) {
    await POSAPI(ip, hostname, job);
  }
};

const POSAPI = async (ip, hostnam, job) => {
  const {
    rseq,
    kname,
    person,
    roomname,
    scode,
    items,
    oround,
    openmode,
    hasSetItems,
    isSingleSetItem,
  } = job;

  const config = getPrintConfig(ip, hostnam);
  const pool = await new sql.ConnectionPool(config).connect();

  // 📆 時間格式化
  const now = new Date();
  const pad = (n) => n.toString().padStart(2, "0");
  const year = now.getFullYear() - 1911;
  const dateStr = `${year}/${pad(now.getMonth() + 1)}/${pad(now.getDate())}`;
  const timeStr = `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(
    now.getSeconds()
  )}`;

  // 🧾 準備出單內容
  const lines = [
    `#${kname}`,
    `@大人 ${person}`,
    `@小孩 0`,
    `桌號: ${roomname}`,
    `廚房:${kname}`,
    `編號:${scode}`,
    `服務生:主管`,
    `日期:${dateStr}`,
    `時間:${timeStr}`,
    `=====`,
  ];
  lines.push(`+0.第${oround}輪`);
  lines.push(`規格:標準: +*`);
  if (oround === "1") {
    lines.push(`+0.消費方式:${openmode}`);
    lines.push(`規格:標準: +*`);
  }

  // 🆕 處理套餐和普通項目
  // 檢查是否是單獨的套餐子項目出單
  if (
    isSingleSetItem &&
    items.length === 2 &&
    items[0].isSetHeader &&
    items[1].isSetItem
  ) {
    // 單獨的套餐子項目出單，只包含套餐抬頭和一個子項目
    const header = items[0];
    const setItem = items[1];

    // 添加套餐抬頭
    lines.push(`+0.${header.name}`);
    if (header.tastes?.trim()) lines.push(`口味:${header.tastes.trim()}`);
    if (header.spec?.trim())
      lines.push(`規格:${header.spec.trim()}: +${header.qty}`);
    else lines.push(`規格:標準: +${header.qty}`);

    // 添加套餐子項目（不使用點號前綴，因為是獨立出單）
    lines.push(`+0.${setItem.name}`);
    if (setItem.tastes?.trim()) lines.push(`口味:${setItem.tastes.trim()}`);
    if (setItem.spec?.trim())
      lines.push(`規格:標準: +${setItem.qty}`); // 不再使用點號前綴
    else lines.push(`規格:標準: +${setItem.qty}`);
  } else if (
    hasSetItems &&
    items.length === 2 &&
    items[0].isSetHeader &&
    items[1].isSetItem
  ) {
    // 單張出單的套餐項目，只有一個套餐抬頭和一個子項目
    const header = items[0];
    const setItem = items[1];

    // 添加套餐抬頭
    lines.push(`+0.${header.name}`);
    if (header.tastes?.trim()) lines.push(`口味:${header.tastes.trim()}`);
    if (header.spec?.trim())
      lines.push(`規格:${header.spec.trim()}: +${header.qty}`);
    else lines.push(`規格:標準: +${header.qty}`);

    // 添加套餐子項目
    lines.push(`+0.${setItem.name}`);
    if (setItem.tastes?.trim()) lines.push(`口味:${setItem.tastes.trim()}`);
    if (setItem.spec?.trim())
      lines.push(`規格:.${setItem.spec.trim()}: +${setItem.qty}`);
    else lines.push(`規格:.標準: +${setItem.qty}`);
  } else {
    // 普通項目或合併出單的套餐項目
    let currentSetHeader = null;

    for (const item of items) {
      if (item.isSetHeader) {
        // 套餐抬頭
        lines.push(`+0.${item.name}`);
        if (item.tastes?.trim()) lines.push(`口味:${item.tastes.trim()}`);
        if (item.spec?.trim())
          lines.push(`規格:${item.spec.trim()}: +${item.qty}`);
        else lines.push(`規格:標準: +${item.qty}`);

        // 記錄當前套餐抬頭，用於後續子項目
        currentSetHeader = item.name;
      } else if (item.isSetItem) {
        // 套餐子項目，使用特殊格式（規格前加點）
        lines.push(`+0.${item.name}`);
        if (item.tastes?.trim()) lines.push(`口味:${item.tastes.trim()}`);
        if (item.spec?.trim())
          lines.push(`規格:.${item.spec.trim()}: +${item.qty}`);
        else lines.push(`規格:.標準: +${item.qty}`);
      } else {
        // 普通項目
        lines.push(`+0.${item.name}`);
        if (item.tastes?.trim()) lines.push(`口味:${item.tastes.trim()}`);
        if (item.spec?.trim())
          lines.push(`規格:${item.spec.trim()}: +${item.qty}`);
        else lines.push(`+${item.qty}`);

        // 重置套餐抬頭
        currentSetHeader = null;
      }
    }
  }
  lines.push("xxx"); // ✅ 加上結尾行
  // 🔍 決定表格前綴
  const rseqNum = Number(rseq);
  const prefix = rseqNum === 1 ? "CRN" : `C${rseqNum}RN`;

  for (let i = 1; i <= 30; i++) {
    const tableName = `${prefix}${i}`;

    try {
      const check = await pool.request().query(`
        SELECT COUNT(*) AS count FROM [dbo].[${tableName}]
      `);

      if (check.recordset[0].count === 0) {
        const valuesSQL = lines
          .map((line) => `N'${line.replace(/'/g, "''")}'`)
          .map((v) => `(${v})`)
          .join(", ");

        await pool.request().query(`
          INSERT INTO [dbo].[${tableName}] (name)
          VALUES ${valuesSQL}
        `);

        //console.log(`✅ POS出單：已寫入 ${tableName}`);
        return;
      }
    } catch (err) {
      console.warn(`⚠️ 寫入 ${tableName} 發生錯誤:`, err.message);
    }
  }

  console.warn(`❌ POS出單失敗:${prefix}1~30 無空位`);
};

module.exports = { insertOrder };
