const sql = require("mssql");
require("dotenv").config();

const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");

  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

const getMinvos = async (req, res) => {
  const { date = "", invo = "", ino = "", branch } = req.query;

  try {
    if (!branch) {
      return res
        .status(400)
        .json({ success: false, message: "缺少 branch 參數" });
    }

    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    const request = pool.request();
    request.input("ndate", sql.NVarChar, date);
    request.input("invo", sql.NVarChar, invo);
    request.input("ino", sql.NVarChar, ino);

    const result = await request.query(`
      SELECT 
        minvo.code,
        minvo.ino,
        minvo.amt,
        minvo.discount,
        ordernow.ndate AS sdate,
        minvo.ndate,
        minvo.ntime,
        minvo.chk,
        minvo.invo,
        minvo.C3,
        minvo.imno
      FROM minvo
      INNER JOIN ordernow ON minvo.code = ordernow.code
      WHERE 
        (@ndate = '' OR minvo.ndate = @ndate)
        AND (@invo = '' OR minvo.invo = @invo)
        AND (@ino = '' OR minvo.ino = @ino)
      ORDER BY minvo.ndate DESC, minvo.ntime DESC
    `);

    res.json(result.recordset);
  } catch (error) {
    console.error("getMinvos error:", error);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const updateStatus = async (req, res) => {
  const { code, status, group, branch } = req.body;

  if (!code || !branch) {
    return res.status(400).json({ message: "缺少必要參數" });
  }

  try {
    // 權限驗證
    const allowedGroups = ["admin", "manager"];

    // 檢查用戶是否擁有任何允許的群組權限
    let hasPermission = false;
    if (Array.isArray(group)) {
      // 如果 group 是數組，檢查是否有任何一個群組在允許列表中
      hasPermission = group.some((g) =>
        allowedGroups.includes(g.trim().toLowerCase())
      );
    } else if (typeof group === "string") {
      // 向後兼容：如果 group 是字符串，當作單一群組處理
      hasPermission = allowedGroups.includes(group.trim().toLowerCase());
    }

    if (!hasPermission) {
      return res.status(403).json({ message: "無此操作權限" });
    }

    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    const request = pool.request();
    request.input("code", sql.NVarChar, code);
    request.input("chk", sql.NVarChar, status); // "0" or "1"

    await request.query(`
      UPDATE minvo
      SET chk = @chk
      WHERE code = @code
    `);

    res.json({ success: true, message: "更新成功" });
  } catch (err) {
    console.error("update_Status error:", err);
    res.status(500).json({ message: "更新狀態失敗" });
  }
};

module.exports = { getMinvos, updateStatus };
