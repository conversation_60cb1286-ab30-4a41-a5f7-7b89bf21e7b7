const { getPoolByBranch, sql } = require("../services/dbPoolManager");

// 取得 omenum 主檔清單
const getOmenum = async (req, res) => {
  const { branch } = req.query;
  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().query(`
      SELECT * FROM omenum ORDER BY code
    `);
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 新增 omenum 主檔
const createOmenum = async (req, res) => {
  const { branch } = req.query;
  const {
    code,
    simname,
    name,
    type,
    cclass,
    omenu,
    K1,
    K2,
    B1,
    C1,
    unit,
    code2,
    ct1,
  } = req.body;
  if (!branch || !code || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("simname", sql.VarChar, simname)
      .input("name", sql.NVarChar, name)
      .input("type", sql.Int, type)
      .input("cclass", sql.VarChar, cclass)
      .input("omenu", sql.Int, omenu)
      .input("K1", sql.NVarChar, K1)
      .input("K2", sql.VarChar, K2)
      .input("B1", sql.NVarChar, B1)
      .input("C1", sql.VarChar, C1)
      .input("unit", sql.VarChar, unit)
      .input("code2", sql.VarChar, code2)
      .input("ct1", sql.NVarChar, ct1)
      .query(`INSERT INTO omenum (code, simname, name, type, cclass, omenu, K1, K2, B1, C1, unit, code2, ct1)
        VALUES (@code, @simname, @name, @type, @cclass, @omenu, @K1, @K2, @B1, @C1, @unit, @code2, @ct1)`);
    return res.json({ success: true, message: "新增成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 更新 omenum 主檔
const updateOmenum = async (req, res) => {
  const { branch } = req.query;
  const {
    code,
    simname,
    name,
    type,
    cclass,
    omenu,
    K1,
    K2,
    B1,
    C1,
    unit,
    code2,
    ct1,
  } = req.body;
  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("simname", sql.VarChar, simname)
      .input("name", sql.NVarChar, name)
      .input("type", sql.Int, type)
      .input("cclass", sql.VarChar, cclass)
      .input("omenu", sql.Int, omenu)
      .input("K1", sql.NVarChar, K1)
      .input("K2", sql.VarChar, K2)
      .input("B1", sql.NVarChar, B1)
      .input("C1", sql.VarChar, C1)
      .input("unit", sql.VarChar, unit)
      .input("code2", sql.VarChar, code2)
      .input("ct1", sql.NVarChar, ct1)
      .query(
        `UPDATE omenum SET simname=@simname, name=@name, type=@type, cclass=@cclass, omenu=@omenu, K1=@K1, K2=@K2, B1=@B1, C1=@C1, unit=@unit, code2=@code2, ct1=@ct1 WHERE code=@code`
      );
    return res.json({ success: true, message: "更新成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 刪除 omenum 主檔
const deleteOmenum = async (req, res) => {
  const { branch } = req.query;
  const { code } = req.params;
  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .query(`DELETE FROM omenum WHERE code=@code`);
    return res.json({ success: true, message: "刪除成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得 omenus 規格清單
const getOmenus = async (req, res) => {
  const { branch, code } = req.query;
  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query(`SELECT * FROM omenus WHERE code=@code ORDER BY sno`);
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得所有類部
const getCclasses = async (req, res) => {
  const { branch } = req.query;
  if (!branch)
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool
      .request()
      .query("SELECT code, name FROM cclass order by sno");
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得所有廚房部門
const getKitchens = async (req, res) => {
  const { branch } = req.query;
  if (!branch)
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().query("SELECT code, name FROM kclass");
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得所有單位
const getUnits = async (req, res) => {
  try {
    const pool = await getPoolByBranch(req.query.branch);
    const result = await pool.request().query("SELECT unit FROM unit");
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得所有rmname
const getRmnames = async (req, res) => {
  try {
    const pool = await getPoolByBranch(req.query.branch);
    const result = await pool
      .request()
      .query("SELECT code, name, n1 FROM rmname");
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得所有規格選單
const getSpecs = async (req, res) => {
  try {
    const pool = await getPoolByBranch(req.query.branch);
    const result = await pool.request().query("SELECT name FROM spec");
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 新增規格
const insertSpec = async (req, res) => {
  const { branch, name } = req.body;
  if (!branch || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);

    // 檢查是否已存在
    const checkResult = await pool
      .request()
      .input("name", sql.VarChar, name)
      .query("SELECT COUNT(*) as cnt FROM spec WHERE name = @name");

    if (checkResult.recordset[0].cnt > 0) {
      return res.status(400).json({ success: false, message: "此規格已存在" });
    }

    // 新增規格
    await pool
      .request()
      .input("name", sql.VarChar, name)
      .query("INSERT INTO spec (name) VALUES (@name)");

    return res.json({ success: true, message: "新增規格成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 刪除規格
const deleteSpec = async (req, res) => {
  const { branch, name } = req.body;
  if (!branch || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);

    // 檢查是否有商品使用此規格
    const checkResult = await pool
      .request()
      .input("name", sql.VarChar, name)
      .query("SELECT COUNT(*) as cnt FROM omenus WHERE name = @name");

    if (checkResult.recordset[0].cnt > 0) {
      return res
        .status(400)
        .json({ success: false, message: "此規格正在使用中，無法刪除" });
    }

    // 刪除規格
    await pool
      .request()
      .input("name", sql.VarChar, name)
      .query("DELETE FROM spec WHERE name = @name");

    return res.json({ success: true, message: "刪除規格成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得所有口味群組
const getTasteGroups = async (req, res) => {
  try {
    const pool = await getPoolByBranch(req.query.branch);
    const result = await pool
      .request()
      .query("SELECT taste_code, taste_title FROM tastegroup");
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得多筆菜單（含規格、明細）
const getMenus = async (req, res) => {
  const { branch, cclass } = req.query;
  if (!branch)
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });

  try {
    const pool = await getPoolByBranch(branch);

    // 1️⃣ 查主檔 + detail
    let whereClause = "";
    const request = pool.request();
    if (cclass) {
      whereClause = "WHERE omenum.cclass = @cclass";
      request.input("cclass", sql.VarChar, cclass);
    }

    const omenumSql = `
        SELECT
          omenum.code, omenum.simname, omenum.name, omenum.type,
          omenum.cclass, omenum.omenu, omenum.k1, omenum.k2,
          omenum.b1, omenum.c1, omenum.unit, omenum.code2,
          omenum.barcode, cclass.name as cname,
          vclass.name as vname, isnull(omenumdetail.mcolor, '1') as mcolor,
          rmname.name as rname, rmname.n1 as rm_n1, omenumdetail.rcode,
          tastemap.taste_code, tastegroup.taste_title
        FROM omenum
        LEFT JOIN cclass ON cclass.code = omenum.cclass
        LEFT JOIN kclass ON kclass.code = omenum.c1
        LEFT JOIN vclass ON kclass.vcode1 = vclass.code
        LEFT JOIN omenumdetail ON omenumdetail.code = omenum.code
        LEFT JOIN rmname ON omenumdetail.rcode = rmname.code
        LEFT JOIN tastemap ON tastemap.code = omenum.code
        LEFT JOIN tastegroup ON tastegroup.taste_code = tastemap.taste_code
        ${whereClause}
        ORDER BY omenum.code`;

    const result = await request.query(omenumSql);
    const menus = result.recordset;

    // 建立 map
    const menuMap = {};
    menus.forEach((row) => {
      if (!menuMap[row.code]) {
        menuMap[row.code] = {
          ...row,
          specs: [],
          taste_groups: [],
        };
      }

      // 如果有口味群組資料，加入陣列中
      if (row.taste_code && row.taste_title) {
        const existingTaste = menuMap[row.code].taste_groups.find(
          (t) => t.taste_code === row.taste_code
        );
        if (!existingTaste) {
          menuMap[row.code].taste_groups.push({
            taste_code: row.taste_code,
            taste_title: row.taste_title,
          });
        }
      }
    });

    // 2️⃣ 查規格
    const codes = Object.keys(menuMap);
    if (codes.length) {
      const request2 = pool.request();
      codes.forEach((c, idx) => request2.input(`code${idx}`, sql.VarChar, c));
      const inClause = codes.map((_, idx) => `@code${idx}`).join(",");

      const specResult = await request2.query(`
          SELECT code, name, oprice, sno
          FROM omenus
          WHERE code IN (${inClause})
          ORDER BY code, sno
        `);

      specResult.recordset.forEach((spec) => {
        if (menuMap[spec.code]) {
          menuMap[spec.code].specs.push(spec);
        }
      });
    }

    // 3️⃣ 查套餐對映
    if (codes.length) {
      const request3 = pool.request();
      codes.forEach((c, idx) => request3.input(`code${idx}`, sql.VarChar, c));
      const inClause = codes.map((_, idx) => `@code${idx}`).join(",");

      const setsMappingResult = await request3.query(`
          SELECT 
            sm.code, sm.sets_code, sm.sname,
            sg.sets_title
          FROM SetsMap sm
          LEFT JOIN setsgroup sg ON sm.sets_code = sg.sets_code
          WHERE sm.code IN (${inClause})
          ORDER BY sm.code, sm.sets_code
        `);

      setsMappingResult.recordset.forEach((mapping) => {
        // 去除欄位中的空格
        mapping.code = mapping.code?.trim();
        mapping.sets_code = mapping.sets_code?.trim();
        mapping.sname = mapping.sname?.trim();
        mapping.sets_title = mapping.sets_title?.trim();

        if (menuMap[mapping.code]) {
          if (!menuMap[mapping.code].sets_mappings) {
            menuMap[mapping.code].sets_mappings = [];
          }
          menuMap[mapping.code].sets_mappings.push(mapping);
        }
      });
    }

    return res.json({ success: true, data: Object.values(menuMap) });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 其餘 omenus/omenumdetail CRUD 及關聯查詢略，後續可補齊

// 新增或更新口味群組對應
const upsertTasteMap = async (req, res) => {
  const { branch } = req.query;
  const { code, taste_codes } = req.body;
  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);

    // 先刪除舊的對應
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM tastemap WHERE code = @code");

    // 如果有口味群組，則新增新的對應
    if (taste_codes && taste_codes.length > 0) {
      for (const taste_code of taste_codes) {
        await pool
          .request()
          .input("code", sql.VarChar, code)
          .input("taste_code", sql.VarChar, taste_code)
          .query(
            "INSERT INTO tastemap (code, taste_code) VALUES (@code, @taste_code)"
          );
      }
    }

    return res.json({ success: true, message: "口味群組設定成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得指定菜單的口味群組對應
const getTasteMap = async (req, res) => {
  const { branch, code } = req.query;
  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().input("code", sql.VarChar, code).query(`
        SELECT tm.taste_code, tg.taste_title
        FROM tastemap tm
        LEFT JOIN tastegroup tg ON tm.taste_code = tg.taste_code
        WHERE tm.code = @code
        ORDER BY tm.taste_code
      `);
    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 檢查 code 是否唯一
const checkCodeUnique = async (req, res) => {
  const { branch, code } = req.query;
  if (!code) return res.json({ unique: false });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("SELECT COUNT(*) as cnt FROM omenum WHERE code = @code");
    return res.json({ unique: result.recordset[0].cnt === 0 });
  } catch (err) {
    return res.status(500).json({ unique: false });
  }
};

const saveOmenum = async (req, res) => {
  const { branch } = req.query;
  const { omenum, omenumdetail, omenus, tasteMaps } = req.body;
  if (!omenum || !omenum.code) {
    return res.json({ success: false, message: "缺少商品編號" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    // 檢查 code 是否唯一（新增時）
    const checkResult = await pool
      .request()
      .input("code", sql.VarChar, omenum.code)
      .query("SELECT COUNT(*) as cnt FROM omenum WHERE code = @code");
    if (checkResult.recordset[0].cnt > 0) {
      // 已存在則 update
      await pool
        .request()
        .input("code", sql.VarChar, omenum.code)
        .input("simname", sql.VarChar, omenum.simname)
        .input("name", sql.NVarChar, omenum.name)
        .input("type", sql.Int, omenum.type)
        .input("cclass", sql.VarChar, omenum.cclass)
        .input("omenu", sql.Int, omenum.omenu)
        .input("k1", sql.NVarChar, omenum.k1 || null)
        .input("k2", sql.NVarChar, omenum.k2 || null)
        .input("b1", sql.NVarChar, omenum.b1)
        .input("c1", sql.NVarChar, omenum.c1)
        .input("unit", sql.NVarChar, omenum.unit)
        .input("code2", sql.NVarChar, omenum.code2)
        .input("ct1", sql.NVarChar, omenum.ct1)
        .input("barcode", sql.NVarChar, omenum.barcode)
        .query(
          `UPDATE omenum SET simname=@simname, name=@name, type=@type, cclass=@cclass, omenu=@omenu, k1=@k1, k2=@k2, b1=@b1, c1=@c1, unit=@unit, code2=@code2, ct1=@ct1, barcode=@barcode WHERE code=@code`
        );
    } else {
      // 不存在則 insert
      await pool
        .request()
        .input("code", sql.VarChar, omenum.code)
        .input("simname", sql.VarChar, omenum.simname)
        .input("name", sql.NVarChar, omenum.name)
        .input("type", sql.Int, omenum.type)
        .input("cclass", sql.VarChar, omenum.cclass)
        .input("omenu", sql.Int, omenum.omenu)
        .input("k1", sql.NVarChar, omenum.k1 || null)
        .input("k2", sql.NVarChar, omenum.k2 || null)
        .input("b1", sql.NVarChar, omenum.b1)
        .input("c1", sql.NVarChar, omenum.c1)
        .input("unit", sql.NVarChar, omenum.unit)
        .input("code2", sql.NVarChar, omenum.code2)
        .input("ct1", sql.NVarChar, omenum.ct1)
        .input("barcode", sql.NVarChar, omenum.barcode)
        .query(
          `INSERT INTO omenum (code, simname, name, type, cclass, omenu, k1, k2, b1, c1, unit, code2, ct1, barcode) VALUES (@code, @simname, @name, @type, @cclass, @omenu, @k1, @k2, @b1, @c1, @unit, @code2, @ct1, @barcode)`
        );
    }
    // omenumdetail
    await pool
      .request()
      .input("code", sql.VarChar, omenum.code)
      .query("DELETE FROM omenumdetail WHERE code=@code");
    if (omenumdetail && omenumdetail.rcode) {
      await pool
        .request()
        .input("code", sql.VarChar, omenumdetail.code)
        .input("rcode", sql.VarChar, omenumdetail.rcode)
        .input("mcolor", sql.Int, omenumdetail.mcolor)
        .query(
          "INSERT INTO omenumdetail (code, rcode, mcolor) VALUES (@code, @rcode, @mcolor)"
        );
    }
    // omenus（規格）
    await pool
      .request()
      .input("code", sql.VarChar, omenum.code)
      .query("DELETE FROM omenus WHERE code=@code");
    if (omenus && omenus.length > 0) {
      for (const spec of omenus) {
        await pool
          .request()
          .input("code", sql.VarChar, spec.code)
          .input("name", sql.NVarChar, spec.name)
          .input("oprice", sql.Decimal(18, 2), spec.oprice)
          .input("sno", sql.NVarChar, String(spec.sno))

          .query(
            "INSERT INTO omenus (code, name, oprice, sno) VALUES (@code, @name, @oprice, @sno)"
          );
      }
    }
    // tastemap（口味群組）
    await pool
      .request()
      .input("code", sql.VarChar, omenum.code)
      .query("DELETE FROM tastemap WHERE code=@code");
    if (tasteMaps && tasteMaps.length > 0) {
      for (const taste of tasteMaps) {
        await pool
          .request()
          .input("code", sql.VarChar, taste.code)
          .input("taste_code", sql.VarChar, taste.taste_code)
          .query(
            "INSERT INTO tastemap (code, taste_code) VALUES (@code, @taste_code)"
          );
      }
    }
    return res.json({ success: true, message: "儲存成功" });
  } catch (err) {
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得有 RCODE 的商品清單
const getProductsWithRcode = async (req, res) => {
  const { branch } = req.query;
  if (!branch)
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().query(`
      SELECT o.code, o.name
      FROM omenum o
      JOIN OMenuMDetail d ON o.code = d.code
      WHERE d.RCODE IS NOT NULL AND d.RCODE <> ''
      GROUP BY o.code, o.name
      ORDER BY o.code
    `);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 關鍵字查詢有 RCODE 的商品
const searchProductsWithRcode = async (req, res) => {
  const { branch, keyword } = req.query;
  if (!branch)
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool
      .request()
      .input("kw", sql.NVarChar, `%${keyword || ""}%`).query(`
        SELECT TOP 30 o.code, o.name
        FROM omenum o
        JOIN OMenuMDetail d ON o.code = d.code
        WHERE d.RCODE IS NOT NULL AND d.RCODE <> ''
          AND (o.name LIKE @kw OR o.code LIKE @kw)
        GROUP BY o.code, o.name
        ORDER BY o.code
      `);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 取得指定 rcode 的商品清單
const getProductsByRcode = async (req, res) => {
  const { branch, rcode } = req.query;
  if (!branch || !rcode)
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().input("rcode", sql.VarChar, rcode)
      .query(`
        SELECT o.code, o.name
        FROM omenum o
        JOIN OMenuMDetail d ON o.code = d.code
        WHERE d.RCODE = @rcode
        GROUP BY o.code, o.name
        ORDER BY o.code
      `);
    res.json({ success: true, data: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 新增 rmname
const insertRmname = async (req, res) => {
  const { branch, code, name, n1 } = req.body;
  if (!branch || !code || !name || typeof n1 !== "number") {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    // 檢查 code 是否重複
    const check = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("SELECT COUNT(*) as cnt FROM rmname WHERE code = @code");
    if (check.recordset[0].cnt > 0) {
      return res
        .status(400)
        .json({ success: false, message: "實際編號已存在" });
    }
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.NVarChar, name)
      .input("n1", sql.Decimal(18, 2), n1)
      .query("INSERT INTO rmname (code, name, n1) VALUES (@code, @name, @n1)");
    res.json({ success: true, message: "新增成功" });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

// 刪除 rmname（僅未被使用時允許）
const deleteRmname = async (req, res) => {
  const { branch, code } = req.body;
  if (!branch || !code) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }
  try {
    const pool = await getPoolByBranch(branch);
    // 檢查是否被 OMenuMDetail 使用
    const check = await pool
      .request()
      .input("rcode", sql.VarChar, code)
      .query("SELECT COUNT(*) as cnt FROM OMenuMDetail WHERE rcode = @rcode");
    if (check.recordset[0].cnt > 0) {
      return res
        .status(400)
        .json({ success: false, message: "此實際編號已被商品使用，無法刪除" });
    }
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM rmname WHERE code = @code");
    res.json({ success: true, message: "刪除成功" });
  } catch (err) {
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getOmenum,
  createOmenum,
  updateOmenum,
  deleteOmenum,
  getOmenus,
  getCclasses,
  getKitchens,
  getUnits,
  getRmnames,
  getSpecs,
  insertSpec,
  deleteSpec,
  getTasteGroups,
  getMenus,
  upsertTasteMap,
  getTasteMap,
  checkCodeUnique,
  saveOmenum,
  getProductsWithRcode,
  searchProductsWithRcode,
  getProductsByRcode,
  insertRmname,
  deleteRmname,
};
