const sql = require("mssql");
const dbConfig = require("../config/db");
const fs = require("fs");
const path = require("path");
const { sendPendingBulletinNotifications } = require("./sendEmail");
const jwt = require("jsonwebtoken");

// 獲取所有公告
const getBulletins = async (req, res) => {
  try {
    // 從查詢參數獲取用戶ID和權限
    const { userId, isAdmin } = req.query;

    const pool = await sql.connect(dbConfig);

    let query = `
      SELECT b.*, d.Name as dep_name
      FROM Bulletin b
      LEFT JOIN dep d ON b.dep = d.id
    `;

    // 非管理員只能看到自己發布的公告
    if (isAdmin !== "true" && userId) {
      query += ` WHERE b.created_by = @userId`;
    }

    query += ` ORDER BY b.priority ASC, b.publish_at DESC`;

    const request = pool.request();

    // 如果有userId參數，添加到請求中
    if (isAdmin !== "true" && userId) {
      request.input("userId", sql.NVarChar, userId);
    }

    const result = await request.query(query);

    res.json({ success: true, data: result.recordset });
  } catch (error) {
    console.error("獲取公告列表失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取公告列表" });
  }
};

// 獲取公告詳情
const getBulletinById = async (req, res) => {
  try {
    const { id } = req.params;
    const pool = await sql.connect(dbConfig);

    // 獲取公告基本信息
    const bulletinResult = await pool.request().input("id", sql.NChar(10), id)
      .query(`
        SELECT * FROM Bulletin WHERE id = @id
      `);

    if (bulletinResult.recordset.length === 0) {
      return res.status(404).json({ success: false, message: "公告不存在" });
    }

    const bulletin = bulletinResult.recordset[0];

    // 獲取公告目標對象
    const targetsResult = await pool.request().input("id", sql.NChar(10), id)
      .query(`
        SELECT * FROM Bulletin_targets WHERE id = @id
      `);

    // 獲取公告附件
    const attachmentsResult = await pool
      .request()
      .input("id", sql.NChar(10), id).query(`
        SELECT * FROM Bulletin_attachments WHERE id = @id
      `);

    bulletin.targets = targetsResult.recordset;
    bulletin.attachments = attachmentsResult.recordset;

    res.json({ success: true, data: bulletin });
  } catch (error) {
    console.error("獲取公告詳情失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取公告詳情" });
  }
};

// 搜尋公告
const searchBulletins = async (req, res) => {
  try {
    const {
      title,
      contents,
      dep,
      priority,
      required,
      startDate,
      endDate,
      active,
      userId,
      isAdmin,
    } = req.query;

    const pool = await sql.connect(dbConfig);

    let query = `
      SELECT b.*, d.Name as dep_name
      FROM Bulletin b
      LEFT JOIN dep d ON b.dep = d.id
      WHERE 1=1
    `;

    const params = [];

    if (title) {
      query += ` AND b.title LIKE @title`;
      params.push({ name: "title", type: sql.NVarChar, value: `%${title}%` });
    }

    if (contents) {
      query += ` AND b.contents LIKE @contents`;
      params.push({
        name: "contents",
        type: sql.NVarChar,
        value: `%${contents}%`,
      });
    }

    if (dep) {
      query += ` AND b.dep = @dep`;
      params.push({ name: "dep", type: sql.NChar(10), value: dep });
    }

    if (priority) {
      query += ` AND b.priority = @priority`;
      params.push({ name: "priority", type: sql.Int, value: priority });
    }

    if (required) {
      query += ` AND b.required = @required`;
      params.push({ name: "required", type: sql.Int, value: required });
    }

    if (startDate) {
      query += ` AND b.publish_at >= @startDate`;
      params.push({
        name: "startDate",
        type: sql.DateTime,
        value: new Date(startDate),
      });
    }

    if (endDate) {
      query += ` AND b.publish_at <= @endDate`;
      params.push({
        name: "endDate",
        type: sql.DateTime,
        value: new Date(endDate),
      });
    }

    if (active !== undefined) {
      query += ` AND b.active = @active`;
      params.push({ name: "active", type: sql.Int, value: active });
    }

    // 非管理員只能看到自己發布的公告
    if (isAdmin !== "true" && userId) {
      query += ` AND b.created_by = @userId`;
      params.push({ name: "userId", type: sql.NVarChar, value: userId });
    }

    query += ` ORDER BY b.priority ASC, b.publish_at DESC`;

    const request = pool.request();

    params.forEach((param) => {
      request.input(param.name, param.type, param.value);
    });

    const result = await request.query(query);

    res.json({ success: true, data: result.recordset });
  } catch (error) {
    console.error("搜尋公告失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法搜尋公告" });
  }
};

// 新增公告
const createBulletin = async (req, res) => {
  try {
    const {
      title,
      contents,
      dep,
      priority,
      required,
      notify,
      publish_at,
      expires_at,
      active,
      created_by,
      targets,
    } = req.body;

    if (!title || !contents || !dep) {
      return res
        .status(400)
        .json({ success: false, message: "標題、內容和發布單位為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 生成新的 ID：日期(YYYYMMDD) + 兩位流水號
    const today = new Date();
    const dateStr =
      today.getFullYear().toString() +
      (today.getMonth() + 1).toString().padStart(2, "0") +
      today.getDate().toString().padStart(2, "0");

    // 查詢當天最大 ID
    const maxIdResult = await pool.request().query(`
      SELECT MAX(id) as maxId FROM Bulletin 
      WHERE id LIKE '${dateStr}%'
    `);

    let newId;
    if (maxIdResult.recordset[0].maxId) {
      // 如果當天已有公告，取最大 ID 的後兩位並加 1
      const lastId = maxIdResult.recordset[0].maxId;
      const lastSeq = parseInt(lastId.substring(8), 10);
      newId = dateStr + (lastSeq + 1).toString().padStart(2, "0");
    } else {
      // 如果當天沒有公告，從 01 開始
      newId = dateStr + "01";
    }

    // 開始事務
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // 插入公告
      await transaction
        .request()
        .input("id", sql.NChar(10), newId)
        .input("title", sql.Text, title)
        .input("contents", sql.Text, contents)
        .input("dep", sql.NChar(10), dep)
        .input("priority", sql.Int, priority || 3)
        .input("required", sql.Int, required || 0)
        .input("notify", sql.Int, notify || 0)
        .input("publish_at", sql.DateTime, publish_at)
        .input(
          "expires_at",
          sql.DateTime,
          expires_at ? new Date(expires_at) : null
        )
        .input("active", sql.Int, active !== undefined ? active : 1)
        .input("created_by", sql.NChar(10), created_by)
        .input("created_at", sql.DateTime, new Date()).query(`
          INSERT INTO Bulletin (id, title, contents, dep, priority, required, notify, publish_at, expires_at, active, created_by, created_at ,update_at )
          VALUES (@id, @title, @contents, @dep, @priority, @required, @notify, @publish_at, @expires_at, @active, @created_by, GETUTCDATE(), GETUTCDATE());
        `);

      // 用於跟踪已處理的用戶ID，避免重複創建通知
      const processedUserIds = new Set();

      // 插入目標對象
      if (targets && targets.length > 0) {
        for (const target of targets) {
          // 將逗號分隔的 target_id 拆分為單獨的 ID
          const targetIds = target.target_id
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id);

          for (const targetId of targetIds) {
            // 直接使用 target_id 作為唯一標識，不再使用 sno
            await transaction
              .request()
              .input("id", sql.NChar(10), newId)
              .input("target_type", sql.NChar(20), target.target_type)
              .input("target_id", sql.NVarChar(50), targetId)
              .input("permission_type", sql.NChar(10), target.permission_type)
              .query(`
                INSERT INTO Bulletin_targets (id, target_type, target_id, permission_type)
                VALUES (@id, @target_type, @target_id, @permission_type)
              `);

            // 如果啟用了郵件通知，為用戶創建通知記錄
            if (
              notify === 1 &&
              target.target_type === "user" &&
              !processedUserIds.has(targetId)
            ) {
              await transaction
                .request()
                .input("id", sql.NChar(10), newId)
                .input("target_id", sql.NChar(6), targetId)
                .input("status", sql.NChar(10), "pending")
                .input("created", sql.DateTime, new Date()).query(`
                  INSERT INTO Bulletin_notify (id, target_id, status, Created)
                  VALUES (@id, @target_id, @status, @created)
                `);
              processedUserIds.add(targetId); // 記錄已處理的用戶ID
            }
          }

          // 處理群組、部門、門市的目標用戶
          if (
            notify === 1 &&
            ["group", "department", "branch"].includes(target.target_type)
          ) {
            // 根據目標類型查詢對應的用戶
            let userQuery = "";

            // 針對每個目標 ID 單獨處理
            for (const targetId of targetIds) {
              switch (target.target_type) {
                case "group":
                  userQuery = `
                    SELECT u.ID FROM Users u
                    JOIN Users_Access ua ON u.ID = ua.id
                    WHERE ua.access = @target_id
                  `;
                  break;
                case "department":
                  userQuery = `
                    SELECT ID FROM Users 
                    WHERE Dep = @target_id
                  `;
                  break;
                case "branch":
                  userQuery = `
                    SELECT u.ID FROM Users u
                    JOIN users_branch ub ON u.ID = ub.id
                    WHERE ub.cod_cust = @target_id
                  `;
                  break;
              }

              if (userQuery) {
                const usersResult = await transaction
                  .request()
                  .input("target_id", sql.NVarChar(50), targetId)
                  .query(userQuery);

                // 為每個找到的用戶創建通知記錄
                for (const user of usersResult.recordset) {
                  const userId = user.ID.trim();
                  // 檢查是否已經處理過該用戶
                  if (!processedUserIds.has(userId)) {
                    await transaction
                      .request()
                      .input("id", sql.NChar(10), newId)
                      .input("target_id", sql.NChar(6), userId)
                      .input("status", sql.NChar(10), "pending")
                      .input("created", sql.DateTime, new Date()).query(`
                        INSERT INTO Bulletin_notify (id, target_id, status, Created)
                        VALUES (@id, @target_id, @status, @created)
                      `);
                    processedUserIds.add(userId); // 記錄已處理的用戶ID
                  }
                }
              }
            }
          }
        }
      }

      // 提交事務
      await transaction.commit();

      res.json({
        success: true,
        message: "公告新增成功",
        data: { id: newId },
      });
    } catch (error) {
      // 回滾事務
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error("新增公告失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法新增公告" });
  }
};

// 更新公告
const updateBulletin = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      contents,
      dep,
      priority,
      required,
      notify,
      publish_at,
      expires_at,
      active,
      targets,
      update_by,
    } = req.body;

    if (!title || !contents || !dep) {
      return res
        .status(400)
        .json({ success: false, message: "標題、內容和發布單位為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 開始事務
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // 更新公告
      await transaction
        .request()
        .input("id", sql.NChar(10), id)
        .input("title", sql.Text, title)
        .input("contents", sql.Text, contents)
        .input("dep", sql.NChar(10), dep)
        .input("priority", sql.Int, priority)
        .input("required", sql.Int, required)
        .input("notify", sql.Int, notify)
        .input("update_by", sql.NChar(10), update_by)
        .input(
          "publish_at",
          sql.DateTime,
          publish_at ? new Date(publish_at) : new Date()
        )
        .input(
          "expires_at",
          sql.DateTime,
          expires_at ? new Date(expires_at) : null
        )
        .input("active", sql.Int, active).query(`
          UPDATE Bulletin
          SET title = @title, 
              contents = @contents, 
              dep = @dep,
              priority = @priority,
              required = @required,
              notify = @notify,
              publish_at = @publish_at,
              expires_at = @expires_at,
              update_at = GETUTCDATE(),
              active = @active,
              update_by = @update_by
          WHERE id = @id
        `);

      // 獲取當前已有的目標對象，用於後續比較
      const existingTargetsResult = await transaction
        .request()
        .input("id", sql.NChar(10), id)
        .query(
          `SELECT target_type, target_id FROM Bulletin_targets WHERE id = @id`
        );

      // 將現有的目標ID存入Set中，方便後續快速查找
      const existingTargetIds = new Set();
      for (const row of existingTargetsResult.recordset) {
        if (row.target_type === "user") {
          existingTargetIds.add(row.target_id.trim());
        }
      }

      // 收集新增的用戶ID
      const newUserIds = new Set();

      // 先刪除所有現有的目標對象
      await transaction
        .request()
        .input("id", sql.NChar(10), id)
        .query(`DELETE FROM Bulletin_targets WHERE id = @id`);

      // 插入新的目標對象
      if (targets && targets.length > 0) {
        for (const target of targets) {
          // 將逗號分隔的 target_id 拆分為單獨的 ID
          const targetIds = target.target_id
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id);

          for (const targetId of targetIds) {
            // 直接使用 target_id 作為唯一標識，不再使用 sno
            await transaction
              .request()
              .input("id", sql.NChar(10), id)
              .input("target_type", sql.NChar(20), target.target_type)
              .input("target_id", sql.NVarChar(50), targetId)
              .input("permission_type", sql.NChar(10), target.permission_type)
              .query(`
                INSERT INTO Bulletin_targets (id, target_type, target_id, permission_type)
                VALUES (@id, @target_type, @target_id, @permission_type)
              `);

            // 如果是用戶類型且啟用了通知，檢查是否為新增的用戶
            if (
              notify === 1 &&
              target.target_type === "user" &&
              !existingTargetIds.has(targetId)
            ) {
              newUserIds.add(targetId);
            }
          }
        }
      }

      // 處理通知記錄
      if (notify === 1) {
        // 獲取已有通知的用戶ID
        const existingNotifyResult = await transaction
          .request()
          .input("id", sql.NChar(10), id)
          .query(`SELECT target_id FROM Bulletin_notify WHERE id = @id`);

        const existingNotifyIds = new Set();
        for (const row of existingNotifyResult.recordset) {
          existingNotifyIds.add(row.target_id.trim());
        }

        // 為新增的用戶創建通知記錄（先檢查是否已有通知記錄）
        if (newUserIds.size > 0) {
          for (const userId of newUserIds) {
            // 檢查該用戶是否已經有通知記錄
            if (!existingNotifyIds.has(userId)) {
              await transaction
                .request()
                .input("id", sql.NChar(10), id)
                .input("target_id", sql.NChar(6), userId)
                .input("status", sql.NChar(10), "pending")
                .input("created", sql.DateTime, new Date()).query(`
                  INSERT INTO Bulletin_notify (id, target_id, status, Created)
                  VALUES (@id, @target_id, @status, @created)
                `);
              existingNotifyIds.add(userId); // 添加到已處理集合中
            }
          }
        }

        // 處理群組、部門、門市目標
        for (const target of targets) {
          if (["group", "department", "branch"].includes(target.target_type)) {
            // 將逗號分隔的 target_id 拆分為單獨的 ID
            const targetIds = target.target_id
              .split(",")
              .map((id) => id.trim())
              .filter((id) => id);

            // 針對每個目標 ID 單獨處理
            for (const targetId of targetIds) {
              // 根據目標類型查詢對應的用戶
              let userQuery = "";
              switch (target.target_type) {
                case "group":
                  userQuery = `
                    SELECT u.ID FROM Users u
                    JOIN Users_Access ua ON u.ID = ua.id
                    WHERE ua.access = @target_id
                  `;
                  break;
                case "department":
                  userQuery = `
                    SELECT ID FROM Users 
                    WHERE Dep = @target_id
                  `;
                  break;
                case "branch":
                  userQuery = `
                    SELECT u.ID FROM Users u
                    JOIN users_branch ub ON u.ID = ub.id
                    WHERE ub.cod_cust = @target_id
                  `;
                  break;
              }

              if (userQuery) {
                const usersResult = await transaction
                  .request()
                  .input("target_id", sql.NVarChar(50), targetId)
                  .query(userQuery);

                // 為每個找到的用戶創建通知記錄（如果尚未存在）
                for (const user of usersResult.recordset) {
                  const userId = user.ID.trim();
                  // 檢查該用戶是否已經有通知記錄
                  if (!existingNotifyIds.has(userId)) {
                    await transaction
                      .request()
                      .input("id", sql.NChar(10), id)
                      .input("target_id", sql.NChar(6), userId)
                      .input("status", sql.NChar(10), "pending")
                      .input("created", sql.DateTime, new Date()).query(`
                        INSERT INTO Bulletin_notify (id, target_id, status, Created)
                        VALUES (@id, @target_id, @status, @created)
                      `);
                    existingNotifyIds.add(userId); // 添加到已處理集合中
                  }
                }
              }
            }
          }
        }
      }

      // 提交事務
      await transaction.commit();

      // 返回結果，包含是否有新增用戶
      res.json({
        success: true,
        message: "公告更新成功",
        hasNewUsers: newUserIds.size > 0,
      });
    } catch (error) {
      // 回滾事務
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error("更新公告失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法更新公告" });
  }
};

// 刪除公告
const deleteBulletin = async (req, res) => {
  try {
    const { id } = req.params;

    const pool = await sql.connect(dbConfig);

    // 開始事務
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // 刪除目標對象
      await transaction.request().input("id", sql.NChar(10), id).query(`
          DELETE FROM Bulletin_targets WHERE id = @id
        `);

      // 刪除附件
      const attachmentsResult = await transaction
        .request()
        .input("id", sql.NChar(10), id).query(`
          SELECT file_path FROM Bulletin_attachments WHERE id = @id
        `);

      // 從文件系統中刪除附件
      for (const attachment of attachmentsResult.recordset) {
        try {
          const filePath = path.join(
            __dirname,
            "../uploads",
            attachment.file_path
          );
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        } catch (err) {
          console.error("刪除附件文件失敗:", err, "路徑:", filePath);
        }
      }

      // 從數據庫中刪除附件記錄
      await transaction.request().input("id", sql.NChar(10), id).query(`
          DELETE FROM Bulletin_attachments WHERE id = @id
        `);

      // 刪除閱讀記錄
      await transaction.request().input("id", sql.NChar(10), id).query(`
          DELETE FROM Bulletin_reads WHERE id = @id
        `);

      // 刪除公告
      await transaction.request().input("id", sql.NChar(10), id).query(`
          DELETE FROM Bulletin WHERE id = @id
        `);

      // 提交事務
      await transaction.commit();

      res.json({ success: true, message: "公告刪除成功" });
    } catch (error) {
      // 回滾事務
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error("刪除公告失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法刪除公告" });
  }
};

// 複製公告

// 上傳附件
const uploadAttachment = async (req, res) => {
  try {
    const { id } = req.params;
    const { sno } = req.body;

    if (!req.file) {
      return res.status(400).json({ success: false, message: "未選擇文件" });
    }

    // 額外的安全檢查 - 驗證檔案類型
    const allowedMimeTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "text/plain",
      "image/jpeg",
      "image/png",
      "image/gif",
    ];

    if (!allowedMimeTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: `不支援的檔案類型: ${req.file.mimetype}`,
      });
    }

    // 檢查檔案大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (req.file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: `檔案過大，大小限制為 10MB`,
      });
    }

    // 如果沒有提供序號，自動生成一個
    let attachmentSno = sno;
    if (!attachmentSno) {
      // 查詢當前公告的最大附件序號
      const pool = await sql.connect(dbConfig);
      const maxSnoResult = await pool.request().input("id", sql.NChar(10), id)
        .query(`
          SELECT MAX(sno) as maxSno FROM Bulletin_attachments WHERE id = @id
        `);

      const maxSno = maxSnoResult.recordset[0].maxSno || 0;
      attachmentSno = maxSno + 1;
    }

    const pool = await sql.connect(dbConfig);

    // 檢查公告是否存在
    const bulletinResult = await pool.request().input("id", sql.NChar(10), id)
      .query(`
        SELECT * FROM Bulletin WHERE id = @id
      `);

    if (bulletinResult.recordset.length === 0) {
      return res.status(404).json({ success: false, message: "公告不存在" });
    }

    // 生成唯一的文件名
    const fileName = req.file.originalname;
    const fileExtension = path.extname(fileName).toLowerCase();

    // 檢查檔案擴展名
    const allowedExtensions = [
      ".pdf",
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".ppt",
      ".pptx",
      ".txt",
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
    ];
    if (!allowedExtensions.includes(fileExtension)) {
      return res.status(400).json({
        success: false,
        message: `不支援的檔案擴展名: ${fileExtension}`,
      });
    }

    const uniqueFileName = `${Date.now()}${fileExtension}`;
    const fileType = fileExtension.substring(1); // 去除點號獲取檔案類型

    // 創建年月資料夾路徑
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0"); // 確保月份是兩位數
    const yearMonth = `${year}${month}`;

    // 構建完整的資料夾路徑
    const uploadDir = path.join(__dirname, "../uploads/btin", yearMonth);

    // 確保資料夾存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 保存文件到年月資料夾
    const filePath = path.join(uploadDir, uniqueFileName);
    try {
      fs.writeFileSync(filePath, req.file.buffer);
    } catch (err) {
      console.error("保存文件失敗:", err, "路徑:", filePath);
      return res.status(500).json({ success: false, message: "保存文件失敗" });
    }

    // 保存附件記錄 - 檔案路徑包含年月資料夾
    const relativeFilePath = `btin/${yearMonth}/${uniqueFileName}`;
    try {
      await pool
        .request()
        .input("id", sql.NChar(10), id)
        .input("sno", sql.Int, attachmentSno)
        .input("file_name", sql.VarChar(255), fileName)
        .input("file_path", sql.VarChar(255), relativeFilePath)
        .input("file_size", sql.Int, req.file.size)
        .input("file_type", sql.NChar(20), fileType)
        .input("uploaded_at", sql.DateTime, new Date()).query(`
          INSERT INTO Bulletin_attachments (id, sno, file_name, file_path, file_size, file_type, uploaded_at)
          VALUES (@id, @sno, @file_name, @file_path, @file_size, @file_type, @uploaded_at);
        `);
    } catch (err) {
      console.error("保存附件記錄失敗:", err);
      return res
        .status(500)
        .json({ success: false, message: "保存附件記錄失敗" });
    }

    res.json({ success: true, message: "附件上傳成功" });
  } catch (error) {
    console.error("上傳附件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法上傳附件" });
  }
};

// 刪除附件
const deleteAttachment = async (req, res) => {
  try {
    const { id, sno } = req.params;
    const snoInt = parseInt(sno, 10); // 確保轉換為整數

    const pool = await sql.connect(dbConfig);

    // 獲取附件信息
    const attachmentResult = await pool
      .request()
      .input("id", sql.NChar(10), id)
      .input("sno", sql.Int, snoInt).query(`
        SELECT * FROM Bulletin_attachments WHERE id = @id AND sno = @sno
      `);

    if (attachmentResult.recordset.length === 0) {
      return res.status(404).json({ success: false, message: "附件不存在" });
    }

    const attachment = attachmentResult.recordset[0];

    // 刪除文件 - 修正路徑以包含年月資料夾
    try {
      const filePath = path.join(__dirname, "../uploads", attachment.file_path);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (err) {
      console.error("刪除附件文件失敗:", err, "路徑:", filePath);
    }

    // 刪除附件記錄
    await pool
      .request()
      .input("id", sql.NChar(10), id)
      .input("sno", sql.Int, snoInt).query(`
        DELETE FROM Bulletin_attachments WHERE id = @id AND sno = @sno
      `);

    res.json({ success: true, message: "附件刪除成功" });
  } catch (error) {
    console.error("刪除附件失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法刪除附件" });
  }
};

// 獲取閱讀統計
const getBulletinStats = async (req, res) => {
  try {
    const { id } = req.params;

    const pool = await sql.connect(dbConfig);

    // 獲取公告
    const bulletinResult = await pool.request().input("id", sql.NChar(10), id)
      .query(`
        SELECT * FROM Bulletin WHERE id = @id
      `);

    if (bulletinResult.recordset.length === 0) {
      return res.status(404).json({ success: false, message: "公告不存在" });
    }

    const bulletin = bulletinResult.recordset[0];

    // 獲取所有目標用戶
    const targetUsersResult = await pool
      .request()
      .input("id", sql.NChar(10), id).query(`
        SELECT DISTINCT u.ID, u.Name
        FROM Users u
        CROSS JOIN Bulletin_targets bt
        WHERE bt.id = @id AND (
          (bt.target_type = 'user' AND (
            CHARINDEX(',' + LTRIM(RTRIM(u.ID)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(u.ID))
          )) OR
          (bt.target_type = 'group' AND EXISTS (
            SELECT 1
            FROM Users_Access ua
            WHERE ua.id = u.ID AND (
              CHARINDEX(',' + LTRIM(RTRIM(ua.access)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ua.access))
            )
          )) OR
          (bt.target_type = 'department' AND (
            CHARINDEX(',' + LTRIM(RTRIM(u.Dep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(u.Dep))
          )) OR
          (bt.target_type = 'branch' AND EXISTS (
            SELECT 1
            FROM users_branch ub
            WHERE ub.id = u.ID AND (
              CHARINDEX(',' + LTRIM(RTRIM(ub.cod_cust)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ub.cod_cust))
            )
          ))
        )
      `);

    // 獲取已讀用戶
    const readUsersResult = await pool.request().input("id", sql.NChar(10), id)
      .query(`
        SELECT br.*, u.Name
        FROM Bulletin_reads br
        LEFT JOIN Users u ON br.user_id = u.ID
        WHERE br.id = @id
      `);

    const targetUsers = targetUsersResult.recordset;
    const readUsers = readUsersResult.recordset;

    // 創建已讀用戶映射
    const readUsersMap = new Map();
    readUsers.forEach((user) => {
      readUsersMap.set(user.user_id.trim(), user);
    });

    // 合併目標用戶和閱讀狀態
    const allUsers = targetUsers.map((user) => {
      const readInfo = readUsersMap.get(user.ID.trim());
      return {
        user_id: user.ID,
        name: user.Name, // 修改為小寫的name，與前端保持一致
        read_status: readInfo ? readInfo.acknowledged : null,
        read_at: readInfo ? readInfo.read_at : null,
      };
    });

    const targetUserCount = allUsers.length;
    const readUserCount = readUsers.length;
    const readPercentage =
      targetUserCount > 0
        ? Math.round((readUserCount / targetUserCount) * 100)
        : 0;

    res.json({
      success: true,
      data: {
        bulletin,
        targetUserCount,
        readUserCount,
        readPercentage,
        allUsers: allUsers,
      },
    });
  } catch (error) {
    console.error("獲取閱讀統計失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取閱讀統計" });
  }
};

// 標記公告為已讀
const markBulletinAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, acknowledged } = req.body;

    if (!user_id) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查用戶是否在閱讀統計中（使用與getBulletinStats函數中相同的邏輯）
    const targetUsersResult = await pool
      .request()
      .input("id", sql.NChar(10), id)
      .input("userId", sql.NVarChar, user_id)
      .input("userDep", sql.NVarChar, req.body.userDep || "").query(`
        SELECT COUNT(*) as userCount
        FROM Users u
        CROSS JOIN Bulletin_targets bt
        WHERE bt.id = @id AND (
          (bt.target_type = 'user' AND (
            CHARINDEX(',' + LTRIM(RTRIM(u.ID)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(u.ID))
          )) OR
          (bt.target_type = 'group' AND EXISTS (
            SELECT 1
            FROM Users_Access ua
            WHERE ua.id = u.ID AND (
              CHARINDEX(',' + LTRIM(RTRIM(ua.access)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ua.access))
            )
          )) OR
          (bt.target_type = 'department' AND (
            CHARINDEX(',' + LTRIM(RTRIM(u.Dep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(u.Dep))
          )) OR
          (bt.target_type = 'branch' AND EXISTS (
            SELECT 1
            FROM users_branch ub
            WHERE ub.id = u.ID AND (
              CHARINDEX(',' + LTRIM(RTRIM(ub.cod_cust)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ub.cod_cust))
            )
          ))
        ) AND u.ID = @userId
      `);

    if (targetUsersResult.recordset[0].userCount === 0) {
      // 用戶不在閱讀統計中，不需寫入已讀
      return res.json({ success: true, message: "不需寫入已讀" });
    }

    // 檢查是否已經標記為已讀
    const existingResult = await pool
      .request()
      .input("id", sql.NVarChar, id)
      .input("user_id", sql.NVarChar, user_id).query(`
        SELECT * FROM Bulletin_reads WHERE id = @id AND user_id = @user_id
      `);

    if (existingResult.recordset.length > 0) {
      // 如果已經存在記錄且需要更新確認狀態
      if (acknowledged === 1) {
        await pool
          .request()
          .input("id", sql.NVarChar, id)
          .input("user_id", sql.NVarChar, user_id)
          .input("acknowledged", sql.Int, 1).query(`
            UPDATE Bulletin_reads 
            SET acknowledged = @acknowledged
            WHERE id = @id AND user_id = @user_id
          `);
      }
      return res.json({ success: true, message: "公告已經標記為已讀" });
    }

    // 標記為已讀
    await pool
      .request()
      .input("id", sql.NVarChar, id)
      .input("user_id", sql.NVarChar, user_id)
      .input("read_at", sql.DateTime, new Date())
      .input("acknowledged", sql.Int, acknowledged || 0).query(`
        INSERT INTO Bulletin_reads (id, user_id, read_at, acknowledged)
        VALUES (@id, @user_id, @read_at, @acknowledged)
      `);

    res.json({ success: true, message: "公告已標記為已讀" });
  } catch (error) {
    console.error("標記公告為已讀失敗:", error);
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法標記公告為已讀" });
  }
};

// 確認閱讀公告
const confirmBulletinRead = async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.body;

    if (!user_id) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查用戶是否在閱讀統計中（使用與getBulletinStats函數中相同的邏輯）
    const targetUsersResult = await pool
      .request()
      .input("id", sql.NChar(10), id)
      .input("userId", sql.NVarChar, user_id)
      .input("userDep", sql.NVarChar, req.body.userDep || "").query(`
        SELECT COUNT(*) as userCount
        FROM Users u
        CROSS JOIN Bulletin_targets bt
        WHERE bt.id = @id AND (
          (bt.target_type = 'user' AND (
            CHARINDEX(',' + LTRIM(RTRIM(u.ID)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(u.ID))
          )) OR
          (bt.target_type = 'group' AND EXISTS (
            SELECT 1
            FROM Users_Access ua
            WHERE ua.id = u.ID AND (
              CHARINDEX(',' + LTRIM(RTRIM(ua.access)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ua.access))
            )
          )) OR
          (bt.target_type = 'department' AND (
            CHARINDEX(',' + LTRIM(RTRIM(u.Dep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(u.Dep))
          )) OR
          (bt.target_type = 'branch' AND EXISTS (
            SELECT 1
            FROM users_branch ub
            WHERE ub.id = u.ID AND (
              CHARINDEX(',' + LTRIM(RTRIM(ub.cod_cust)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ub.cod_cust))
            )
          ))
        ) AND u.ID = @userId
      `);

    if (targetUsersResult.recordset[0].userCount === 0) {
      // 用戶不在閱讀統計中，不需確認閱讀
      return res.json({ success: true, message: "不需確認閱讀" });
    }

    // 檢查是否已經有閱讀記錄
    const existingResult = await pool
      .request()
      .input("id", sql.NVarChar, id)
      .input("user_id", sql.NVarChar, user_id).query(`
        SELECT * FROM Bulletin_reads WHERE id = @id AND user_id = @user_id
      `);

    if (existingResult.recordset.length === 0) {
      // 如果沒有閱讀記錄，先創建一個
      await pool
        .request()
        .input("id", sql.NVarChar, id)
        .input("user_id", sql.NVarChar, user_id)
        .input("read_at", sql.DateTime, new Date())
        .input("acknowledged", sql.Int, 1).query(`
          INSERT INTO Bulletin_reads (id, user_id, read_at, acknowledged)
          VALUES (@id, @user_id, @read_at, @acknowledged)
        `);
    } else {
      // 更新確認狀態
      await pool
        .request()
        .input("id", sql.NVarChar, id)
        .input("user_id", sql.NVarChar, user_id).query(`
          UPDATE Bulletin_reads 
          SET acknowledged = 1
          WHERE id = @id AND user_id = @user_id
        `);
    }

    res.json({ success: true, message: "公告已確認閱讀" });
  } catch (error) {
    console.error("確認閱讀公告失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法確認閱讀公告" });
  }
};

// 獲取用戶公告列表
const getUserBulletins = async (req, res) => {
  try {
    const { userId } = req.params;
    const { userDep, userGroup, userBranches } = req.query; // 從前端接收用戶部門、群組和門市資訊

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 準備查詢參數
    let userDepValue = userDep || "";
    let userGroupsArray = [];
    let userBranchesArray = [];

    // 處理權限群組參數 - 可能是單個字符串或 JSON 數組
    if (userGroup) {
      try {
        // 嘗試解析為數組
        userGroupsArray =
          typeof userGroup === "string" && userGroup.startsWith("[")
            ? JSON.parse(userGroup)
            : [userGroup];
      } catch (e) {
        console.error("解析 userGroup 失敗:", e);
        userGroupsArray = userGroup ? [userGroup] : [];
      }
    }

    // 處理門市參數
    if (userBranches) {
      try {
        userBranchesArray = JSON.parse(userBranches);
      } catch (e) {
        console.error("解析 userBranches 失敗:", e);
        userBranchesArray = [];
      }
    }

    // 先獲取符合條件的公告ID
    let idQuery = `
      SELECT DISTINCT bt.id
      FROM Bulletin_targets bt
      INNER JOIN Bulletin b ON bt.id = b.id
      WHERE b.active = 1 
    `;

    // 檢查是否有 Admin 權限
    const hasAdminPermission = userGroupsArray.some(
      (g) => typeof g === "string" && g.toLowerCase() === "admin"
    );

    // 如果是 Admin，可以看到所有公告
    if (hasAdminPermission) {
      idQuery += `
        AND (b.expires_at IS NULL OR b.expires_at >= GETDATE())
      `;
    } else {
      // 一般使用者，根據權限過濾
      idQuery += `
        AND (
          (bt.target_type = 'user' AND (
            CHARINDEX(',' + LTRIM(RTRIM(@userId)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userId))
          ))
      `;

      // 如果有權限群組，添加群組條件
      if (userGroupsArray.length > 0) {
        // 構建 OR 條件查詢每個群組
        const groupConditions = userGroupsArray
          .filter((group) => group) // 過濾掉空值
          .map(
            (group) => `
            (bt.target_type = 'group' AND EXISTS (
              SELECT 1 FROM Users_Access ua 
              WHERE ua.id = @userId AND (
                CHARINDEX(',' + LTRIM(RTRIM('${group}')) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
                LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM('${group}'))
              )
            ))
          `
          )
          .join(" OR ");

        if (groupConditions) {
          idQuery += ` OR (${groupConditions})`;
        }
      }

      // 部門條件
      idQuery += ` OR
          (bt.target_type = 'department' AND (
            CHARINDEX(',' + LTRIM(RTRIM(@userDep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userDep))
          ))
      `;

      // 如果用戶有門市，添加門市條件
      if (userBranchesArray.length > 0) {
        const branchConditions = userBranchesArray
          .map(
            (branch) => `
            (bt.target_type = 'branch' AND (
              CHARINDEX(',' + LTRIM(RTRIM('${branch}')) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM('${branch}'))
            ))
          `
          )
          .join(" OR ");

        idQuery += ` OR (${branchConditions})`;
      }

      idQuery += `)
        AND (b.expires_at IS NULL OR b.expires_at >= GETDATE())
      `;
    }

    const request = pool.request();
    request.input("userId", sql.NVarChar, userId);
    request.input("userDep", sql.NVarChar, userDepValue);

    const idResult = await request.query(idQuery);

    // 如果沒有找到符合條件的公告ID，返回空數組
    if (idResult.recordset.length === 0) {
      return res.json({ success: true, data: [] });
    }

    // 獲取公告ID列表
    const bulletinIds = idResult.recordset
      .map((item) => `'${item.id.trim()}'`)
      .join(",");

    // 根據ID列表獲取完整的公告信息
    const bulletinsQuery = `
      SELECT b.*, d.Name as dep_name
      FROM Bulletin b
      LEFT JOIN dep d ON b.dep = d.id
      WHERE b.id IN (${bulletinIds})
      ORDER BY b.priority ASC, b.publish_at DESC
    `;

    const bulletinsResult = await pool.request().query(bulletinsQuery);

    // 獲取公告附件
    const bulletins = bulletinsResult.recordset;
    for (const bulletin of bulletins) {
      const attachmentsResult = await pool
        .request()
        .input("id", sql.NVarChar, bulletin.id).query(`
          SELECT * FROM Bulletin_attachments WHERE id = @id
        `);
      bulletin.attachments = attachmentsResult.recordset;
    }

    res.json({ success: true, data: bulletins });
  } catch (error) {
    console.error("獲取用戶公告列表失敗:", error);
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取用戶公告列表" });
  }
};

// 搜尋用戶公告
const searchUserBulletins = async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      title,
      contents,
      dep,
      priority,
      required,
      startDate,
      endDate,
      userDep,
      userGroup,
      userBranches,
    } = req.query;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 準備查詢參數
    let userDepValue = userDep || "";
    let userGroupsArray = [];
    let userBranchesArray = [];

    // 處理權限群組參數 - 可能是單個字符串或 JSON 數組
    if (userGroup) {
      try {
        // 嘗試解析為數組
        userGroupsArray =
          typeof userGroup === "string" && userGroup.startsWith("[")
            ? JSON.parse(userGroup)
            : [userGroup];
      } catch (e) {
        console.error("解析 userGroup 失敗:", e);
        userGroupsArray = userGroup ? [userGroup] : [];
      }
    }

    // 嘗試解析 userBranches，如果失敗則設為空陣列
    if (userBranches) {
      try {
        userBranchesArray = JSON.parse(userBranches);
      } catch (e) {
        console.error("解析 userBranches 失敗:", e);
        userBranchesArray = [];
      }
    }

    // 先獲取符合條件的公告ID
    let idQuery = `
      SELECT DISTINCT bt.id
      FROM Bulletin_targets bt
      INNER JOIN Bulletin b ON bt.id = b.id
      WHERE b.active = 1 
    `;

    // 添加搜尋條件
    if (title) {
      idQuery += ` AND b.title LIKE '%${title}%'`;
    }

    if (contents) {
      idQuery += ` AND b.contents LIKE '%${contents}%'`;
    }

    if (dep) {
      idQuery += ` AND b.dep = '${dep}'`;
    }

    if (priority) {
      idQuery += ` AND b.priority = ${priority}`;
    }

    if (required) {
      idQuery += ` AND b.required = ${required}`;
    }

    if (startDate) {
      idQuery += ` AND b.publish_at >= '${startDate}'`;
    }

    if (endDate) {
      idQuery += ` AND b.publish_at <= '${endDate}'`;
    }

    // 檢查是否有 Admin 權限
    const hasAdminPermission = userGroupsArray.some(
      (g) => typeof g === "string" && g.toLowerCase() === "admin"
    );

    // 如果是 Admin，可以看到所有公告
    if (hasAdminPermission) {
      idQuery += `
        AND (b.expires_at IS NULL OR b.expires_at >= GETDATE())
      `;
    } else {
      // 一般使用者，根據權限過濾
      idQuery += `
        AND (
          (bt.target_type = 'user' AND (
            CHARINDEX(',' + LTRIM(RTRIM(@userId)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userId))
          ))
      `;

      // 如果有權限群組，添加群組條件
      if (userGroupsArray.length > 0) {
        // 構建 OR 條件查詢每個群組
        const groupConditions = userGroupsArray
          .filter((group) => group) // 過濾掉空值
          .map(
            (group) => `
            (bt.target_type = 'group' AND EXISTS (
              SELECT 1 FROM Users_Access ua 
              WHERE ua.id = @userId AND (
                CHARINDEX(',' + LTRIM(RTRIM('${group}')) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
                LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM('${group}'))
              )
            ))
          `
          )
          .join(" OR ");

        if (groupConditions) {
          idQuery += ` OR (${groupConditions})`;
        }
      }

      // 部門條件
      idQuery += ` OR
          (bt.target_type = 'department' AND (
            CHARINDEX(',' + LTRIM(RTRIM(@userDep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userDep))
          ))
      `;

      // 如果用戶有門市，添加門市條件
      if (userBranchesArray.length > 0) {
        const branchConditions = userBranchesArray
          .map(
            (branch) => `
            (bt.target_type = 'branch' AND (
              CHARINDEX(',' + LTRIM(RTRIM('${branch}')) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM('${branch}'))
            ))
          `
          )
          .join(" OR ");

        idQuery += ` OR (${branchConditions})`;
      }

      idQuery += `)
        AND (b.expires_at IS NULL OR b.expires_at >= GETDATE())
      `;
    }

    const request = pool.request();
    request.input("userId", sql.NVarChar, userId);
    request.input("userDep", sql.NVarChar, userDepValue);

    const idResult = await request.query(idQuery);

    // 如果沒有找到符合條件的公告ID，返回空數組
    if (idResult.recordset.length === 0) {
      return res.json({ success: true, data: [] });
    }

    // 獲取公告ID列表
    const bulletinIds = idResult.recordset
      .map((item) => `'${item.id.trim()}'`)
      .join(",");

    // 根據ID列表獲取完整的公告信息
    const bulletinsQuery = `
      SELECT b.*, d.Name as dep_name
      FROM Bulletin b
      LEFT JOIN dep d ON b.dep = d.id
      WHERE b.id IN (${bulletinIds})
      ORDER BY b.priority ASC, b.publish_at DESC
    `;

    const bulletinsResult = await pool.request().query(bulletinsQuery);

    // 獲取公告附件
    const bulletins = bulletinsResult.recordset;
    for (const bulletin of bulletins) {
      const attachmentsResult = await pool
        .request()
        .input("id", sql.NVarChar, bulletin.id).query(`
          SELECT * FROM Bulletin_attachments WHERE id = @id
        `);
      bulletin.attachments = attachmentsResult.recordset;
    }

    res.json({ success: true, data: bulletins });
  } catch (error) {
    console.error("搜尋用戶公告失敗:", error);
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法搜尋用戶公告" });
  }
};

// 獲取用戶閱讀狀態
const getUserReadStatus = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    const result = await pool.request().input("userId", sql.NVarChar, userId)
      .query(`
        SELECT * FROM Bulletin_reads WHERE user_id = @userId
      `);

    res.json({ success: true, data: result.recordset });
  } catch (error) {
    console.error("獲取用戶閱讀狀態失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取用戶閱讀狀態" });
  }
};

// 獲取未讀公告數量
const getUnreadCount = async (req, res) => {
  try {
    const { userId } = req.params;
    const { userDep, userGroup, userBranches } = req.query;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查用戶是否存在
    const userCheckResult = await pool
      .request()
      .input("userId", sql.NVarChar, userId).query(`
        SELECT COUNT(*) as userCount FROM Users WHERE ID = @userId
      `);

    if (userCheckResult.recordset[0].userCount === 0) {
      return res.json({ success: true, count: 0 });
    }

    // 獲取用戶可見的所有公告
    const userBulletinsResult = await getUserVisibleBulletins(
      pool,
      userId,
      false,
      userDep,
      userGroup,
      userBranches
    );

    // 如果獲取失敗，返回0
    if (!userBulletinsResult || !userBulletinsResult.recordset) {
      return res.json({ success: true, count: 0 });
    }

    const userBulletins = userBulletinsResult.recordset;

    // 獲取用戶已讀公告
    const readResult = await pool
      .request()
      .input("userId", sql.NVarChar, userId).query(`
        SELECT id FROM Bulletin_reads WHERE user_id = @userId
      `);

    const readBulletinIds = new Set(
      readResult.recordset.map((item) => item.id.trim())
    );

    // 計算未讀公告數量
    const unreadCount = userBulletins.filter(
      (bulletin) => !readBulletinIds.has(bulletin.id.trim())
    ).length;

    res.json({ success: true, count: unreadCount });
  } catch (error) {
    console.error("獲取未讀公告數量失敗:", error);
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法獲取未讀公告數量" });
  }
};

// 獲取必讀未讀公告數量
const getRequiredUnreadCount = async (req, res) => {
  try {
    const { userId } = req.params;
    const { userDep, userGroup, userBranches } = req.query;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查用戶是否存在
    const userCheckResult = await pool
      .request()
      .input("userId", sql.NVarChar, userId).query(`
        SELECT COUNT(*) as userCount FROM Users WHERE ID = @userId
      `);

    if (userCheckResult.recordset[0].userCount === 0) {
      return res.json({ success: true, count: 0 });
    }

    // 獲取用戶可見的必讀公告
    const userBulletinsResult = await getUserVisibleBulletins(
      pool,
      userId,
      true,
      userDep,
      userGroup,
      userBranches
    );

    // 如果獲取失敗，返回0
    if (!userBulletinsResult || !userBulletinsResult.recordset) {
      return res.json({ success: true, count: 0 });
    }

    const userBulletins = userBulletinsResult.recordset;

    // 獲取用戶已讀公告
    const readResult = await pool
      .request()
      .input("userId", sql.NVarChar, userId).query(`
        SELECT id FROM Bulletin_reads WHERE user_id = @userId
      `);

    const readBulletinIds = new Set(
      readResult.recordset.map((item) => item.id.trim())
    );

    // 計算未讀必讀公告數量
    const unreadCount = userBulletins.filter(
      (bulletin) => !readBulletinIds.has(bulletin.id.trim())
    ).length;

    res.json({ success: true, count: unreadCount });
  } catch (error) {
    console.error("獲取必讀未讀公告數量失敗:", error);
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    res.status(500).json({
      success: false,
      message: "伺服器錯誤，無法獲取必讀未讀公告數量",
    });
  }
};

// 輔助函數：獲取用戶可見的公告
const getUserVisibleBulletins = async (
  pool,
  userId,
  onlyRequired = false,
  userDepParam = null,
  userGroupParam = null,
  userBranchesParam = null
) => {
  try {
    // 準備查詢參數
    let userDep = userDepParam || "";
    let userGroupsArray = [];
    let userBranches = [];

    // 處理權限群組參數 - 可能是單個字符串或數組
    if (userGroupParam) {
      try {
        // 嘗試將 userGroupParam 轉換為數組
        if (Array.isArray(userGroupParam)) {
          userGroupsArray = userGroupParam;
        } else if (typeof userGroupParam === "string") {
          // 嘗試解析 JSON 字符串
          if (userGroupParam.startsWith("[")) {
            userGroupsArray = JSON.parse(userGroupParam);
          } else {
            // 單個權限值
            userGroupsArray = [userGroupParam];
          }
        }
      } catch (e) {
        console.error("解析 userGroupParam 失敗:", e);
        // 如果解析失敗且 userGroupParam 是字符串，則使用它作為單個值
        userGroupsArray =
          typeof userGroupParam === "string" ? [userGroupParam] : [];
      }
    }

    // 嘗試解析 userBranches，如果失敗則設為空陣列
    if (userBranchesParam) {
      try {
        userBranches = Array.isArray(userBranchesParam)
          ? userBranchesParam
          : JSON.parse(userBranchesParam);
      } catch (e) {
        console.error("解析 userBranches 失敗:", e);
        userBranches = [];
      }
    }

    // 構建查詢公告的SQL，與getBulletinStats中的邏輯一致
    let query = `
      SELECT b.id
      FROM Bulletin b
      INNER JOIN Bulletin_targets bt ON b.id = bt.id
      WHERE b.active = 1 
    `;

    // 如果只查詢必讀公告
    if (onlyRequired) {
      query += ` AND b.required = 1`;
    }

    // 使用與getBulletinStats相同的邏輯來確定用戶可見的公告
    query += ` AND (
      (bt.target_type = 'user' AND (
        CHARINDEX(',' + LTRIM(RTRIM(@userId)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
        LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userId))
      )) OR
      (bt.target_type = 'group' AND EXISTS (
        SELECT 1
        FROM Users_Access ua
        WHERE ua.id = @userId AND (
          CHARINDEX(',' + LTRIM(RTRIM(ua.access)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
          LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ua.access))
        )
      )) OR
      (bt.target_type = 'department' AND (
        CHARINDEX(',' + LTRIM(RTRIM(@userDep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
        LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userDep))
      ))
    `;

    // 如果用戶有門市，添加門市條件
    if (userBranches.length > 0) {
      query += ` OR
        (bt.target_type = 'branch' AND EXISTS (
          SELECT 1
          FROM users_branch ub
          WHERE ub.id = @userId AND (
            CHARINDEX(',' + LTRIM(RTRIM(ub.cod_cust)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ub.cod_cust))
          )
        ))
      `;
    }

    query += `)
      AND (b.expires_at IS NULL OR b.expires_at >= GETDATE())
      GROUP BY b.id
    `;

    const request = pool.request();
    request.input("userId", sql.NVarChar, userId);
    request.input("userDep", sql.NVarChar, userDep);

    return await request.query(query);
  } catch (error) {
    console.error("獲取用戶可見公告錯誤:", error);
    throw error;
  }
};

// 刪除公告中的圖片
const deleteImage = async (req, res) => {
  try {
    // 使用 req.params[0通配符匹配的路徑
    const relativePath = req.params[0];

    console.log("刪除圖片請求:", {
      relativePath,
      fullUrl: req.originalUrl,
      method: req.method,
      params: req.params,
    });

    if (!relativePath) {
      return res.status(400).json({ success: false, message: "缺少檔案路徑" });
    }

    // 構建檔案路徑 - 現在 relativePath 包含相對路徑（如 btin/22507xxx.jpg）
    const filePath = path.join(__dirname, "../uploads", relativePath);

    console.log("嘗試刪除檔案:", filePath);

    // 檢查檔案是否存在
    if (fs.existsSync(filePath)) {
      // 刪除檔案
      fs.unlinkSync(filePath);
      console.log("檔案刪除成功:", filePath);
      res.json({ success: true, message: "圖片刪除成功" });
    } else {
      console.log("檔案不存在:", filePath);
      res.status(404).json({ success: false, message: "找不到指定的圖片檔案" });
    }
  } catch (error) {
    console.error("刪除圖片失敗:", error);
    res
      .status(500)
      .json({ success: false, message: "伺服器錯誤，無法刪除圖片" });
  }
};

// 手動觸發發送公告通知
const sendNotifications = async (req, res) => {
  try {
    // 從請求中獲取公告ID，如果有的話
    const { bulletinId } = req.body;

    // 調用發送函數，如果有指定公告ID，則只處理該公告的通知
    await sendPendingBulletinNotifications(bulletinId);

    res.json({
      success: true,
      message: bulletinId
        ? `公告 ${bulletinId} 的通知發送程序已啟動`
        : "所有待發送的公告通知發送程序已啟動",
    });
  } catch (error) {
    console.error("手動觸發發送公告通知失敗:", error);
    res.status(500).json({ success: false, message: "發送公告通知失敗" });
  }
};

// 獲取所有公告通知
const getNotifications = async (req, res) => {
  try {
    const { keyword, date_from, date_to, status } = req.query;

    const pool = await sql.connect(dbConfig);

    let query = `
      SELECT 
        bn.id, 
        bn.target_id, 
        bn.status, 
        bn.Created,
        bn.Sent,
        u.Name AS target_name,
        b.title AS bulletin_title,
        b.priority,
        b.required,
        d.Name AS dep_name
      FROM Bulletin_notify bn
      JOIN Users u ON bn.target_id = u.ID
      JOIN Bulletin b ON bn.id = b.id
      JOIN dep d ON b.dep = d.id
      WHERE 1=1
    `;

    const params = [];

    if (keyword) {
      query += ` AND (bn.id LIKE @keyword OR bn.target_id LIKE @keyword OR u.Name LIKE @keyword OR b.title LIKE @keyword)`;
      params.push({
        name: "keyword",
        type: sql.NVarChar,
        value: `%${keyword}%`,
      });
    }

    if (date_from) {
      query += ` AND bn.Created >= @date_from`;
      params.push({
        name: "date_from",
        type: sql.DateTime,
        value: new Date(date_from),
      });
    }

    if (date_to) {
      query += ` AND bn.Created <= @date_to`;
      params.push({
        name: "date_to",
        type: sql.DateTime,
        value: new Date(date_to),
      });
    }

    if (status) {
      query += ` AND bn.status = @status`;
      params.push({ name: "status", type: sql.NVarChar, value: status });
    }

    query += ` ORDER BY bn.Created DESC`;

    const request = pool.request();

    params.forEach((param) => {
      request.input(param.name, param.type, param.value);
    });

    const result = await request.query(query);

    res.json(result.recordset);
  } catch (error) {
    console.error("獲取公告通知列表失敗:", error);
    res.status(500).json({ success: false, message: "獲取公告通知列表失敗" });
  }
};

// 重新發送特定通知
const resendNotification = async (req, res) => {
  try {
    const { id, target_id } = req.body;

    if (!id || !target_id) {
      return res.status(400).json({ success: false, message: "缺少必要參數" });
    }

    const pool = await sql.connect(dbConfig);

    // 更新通知狀態為待發送
    await pool
      .request()
      .input("id", sql.NChar(10), id)
      .input("target_id", sql.NChar(6), target_id).query(`
        UPDATE Bulletin_notify 
        SET status = 'pending', Sent = NULL 
        WHERE id = @id AND target_id = @target_id
      `);

    // 立即觸發發送
    await sendPendingBulletinNotifications();

    res.json({ success: true, message: "通知已重新排入發送佇列" });
  } catch (error) {
    console.error("重新發送通知失敗:", error);
    res.status(500).json({ success: false, message: "重新發送通知失敗" });
  }
};

// 刪除歷史通知記錄
const deleteNotificationsHistory = async (req, res) => {
  try {
    const { date_before } = req.body;

    if (!date_before) {
      return res.status(400).json({ success: false, message: "請提供日期" });
    }

    const pool = await sql.connect(dbConfig);

    // 查詢將被刪除的記錄數量
    const countResult = await pool
      .request()
      .input("date_before", sql.DateTime, new Date(date_before)).query(`
        SELECT COUNT(*) as count 
        FROM Bulletin_notify 
        WHERE Created < @date_before
      `);

    const count = countResult.recordset[0].count;

    // 刪除記錄
    await pool
      .request()
      .input("date_before", sql.DateTime, new Date(date_before)).query(`
        DELETE FROM Bulletin_notify 
        WHERE Created < @date_before
      `);

    res.json({
      success: true,
      message: `成功刪除 ${count} 筆歷史通知記錄`,
      deletedCount: count,
    });
  } catch (error) {
    console.error("刪除歷史通知記錄失敗:", error);
    res.status(500).json({ success: false, message: "刪除歷史通知記錄失敗" });
  }
};

// 獲取用戶真正需要閱讀的公告列表
const getUnreadBulletins = async (req, res) => {
  try {
    const { userId } = req.params;
    const { userDep, userGroup, userBranches } = req.query;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "用戶ID為必填項" });
    }

    const pool = await sql.connect(dbConfig);

    // 檢查用戶是否存在
    const userCheckResult = await pool
      .request()
      .input("userId", sql.NVarChar, userId).query(`
        SELECT COUNT(*) as userCount FROM Users WHERE ID = @userId
      `);

    if (userCheckResult.recordset[0].userCount === 0) {
      return res.json({ success: true, data: [] });
    }

    // 獲取用戶真正需要閱讀的公告列表（使用與getBulletinStats相同的邏輯）
    const userBulletinsResult = await pool
      .request()
      .input("userId", sql.NVarChar, userId)
      .input("userDep", sql.NVarChar, userDep || "").query(`
        SELECT b.id, MAX(b.required) as required
        FROM Bulletin b
        INNER JOIN Bulletin_targets bt ON b.id = bt.id
        WHERE b.active = 1 
        AND (b.expires_at IS NULL OR b.expires_at >= GETDATE())
        AND (
          (bt.target_type = 'user' AND (
            CHARINDEX(',' + LTRIM(RTRIM(@userId)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userId))
          )) OR
          (bt.target_type = 'group' AND EXISTS (
            SELECT 1
            FROM Users_Access ua
            WHERE ua.id = @userId AND (
              CHARINDEX(',' + LTRIM(RTRIM(ua.access)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ua.access))
            )
          )) OR
          (bt.target_type = 'department' AND (
            CHARINDEX(',' + LTRIM(RTRIM(@userDep)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
            LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(@userDep))
          )) OR
          (bt.target_type = 'branch' AND EXISTS (
            SELECT 1
            FROM users_branch ub
            WHERE ub.id = @userId AND (
              CHARINDEX(',' + LTRIM(RTRIM(ub.cod_cust)) + ',', ',' + LTRIM(RTRIM(bt.target_id)) + ',') > 0 OR
              LTRIM(RTRIM(bt.target_id)) = LTRIM(RTRIM(ub.cod_cust))
            )
          ))
        )
        GROUP BY b.id
      `);

    res.json({ success: true, data: userBulletinsResult.recordset });
  } catch (error) {
    console.error("獲取用戶需要閱讀的公告列表失敗:", error);
    res.status(500).json({
      success: false,
      message: "伺服器錯誤，無法獲取用戶需要閱讀的公告列表",
    });
  }
};

module.exports = {
  getBulletins,
  getBulletinById,
  searchBulletins,
  createBulletin,
  updateBulletin,
  deleteBulletin,
  uploadAttachment,
  deleteAttachment,
  getBulletinStats,
  markBulletinAsRead,
  confirmBulletinRead,
  deleteImage,
  getUserBulletins,
  searchUserBulletins,
  getUserReadStatus,
  getUnreadCount,
  getRequiredUnreadCount,
  getUnreadBulletins,
  // 新增的通知相關函數
  sendNotifications,
  getNotifications,
  resendNotification,
  deleteNotificationsHistory,
};
