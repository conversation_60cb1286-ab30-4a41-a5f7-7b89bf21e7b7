const sql = require("mssql");
require("dotenv").config();

const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");

  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

const getOrders = async (req, res) => {
  const { branch, date, invo, code, ino } = req.query;

  if (!branch) {
    return res.status(400).json({ error: "缺少 branch 參數" });
  }

  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    request.input("ndate", sql.NVarChar, date || "");
    request.input("invo", sql.NVarChar, invo || "");
    request.input("code", sql.NVar<PERSON>har, code || "");
    request.input("ino", sql.NVarChar, ino || "");

    const result = await request.query(`
      SELECT 
        ordernow.code, 
        ordernow.invo,
        ordernow.ndate,
        ordernow.ctime,
        ordernow.rtime,
        minvo.ino,
        minvo.chk,
        (ISNULL(ordernow.person, 0) + ISNULL(ordernow.boy, 0)) AS person,
        room.name,
        ordernow.checkout,
        ordernow.empln,
        FORMAT(ordernow.Amount, 'N0') AS Amount,
        FORMAT(ordernow.cntot, 'N0') AS cntot,
        FORMAT(ISNULL(ordernow.cdtot, 0), 'N0') AS cdtot,
        FORMAT(ordernow.stot, 'N0') AS stot,
        FORMAT(ordernow.vamt, 'N0') AS vamt,
        FORMAT(ISNULL(ordernow.chtot, 0), 'N0') AS chtot,
        FORMAT(ordernow.dtot, 'N0') AS dtot,
        FORMAT(ISNULL(ordernow.damt1, 0), 'N0') AS damt1,
        FORMAT(ISNULL(ordernow.ramt, 0), 'N0') AS ramt,
        FORMAT(ISNULL(ordernow.tamt, 0), 'N0') AS tamt 
      FROM ordernow
      LEFT JOIN minvo ON minvo.code = ordernow.code
      LEFT JOIN orderroomn ON orderroomn.code = ordernow.code
      LEFT JOIN room ON room.code = orderroomn.room
      WHERE (@ndate = '' OR ordernow.ndate = @ndate)
        AND (@invo = '' OR ordernow.invo = @invo)
        AND (@code = '' OR ordernow.code = @code)
        AND (@ino = '' OR minvo.ino = @ino)
      ORDER BY  ordernow.code 
    `);

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 訂單查詢錯誤:", err);
    res.status(500).json({ error: "訂單查詢失敗" });
  }
};

const updateStatus = async (req, res) => {
  const { code, checkout, branch, group } = req.body;

  // ✅ 權限白名單（只有 Admin 可以執行）
  const ALLOWED_GROUPS = ["Admin"];

  // 檢查用戶是否擁有任何允許的群組權限
  let hasPermission = false;
  if (Array.isArray(group)) {
    // 如果 group 是數組，檢查是否有任何一個群組在允許列表中
    hasPermission = group.some((g) => ALLOWED_GROUPS.includes(g.trim()));
  } else if (typeof group === "string") {
    // 向後兼容：如果 group 是字符串，當作單一群組處理
    hasPermission = ALLOWED_GROUPS.includes(group.trim());
  }

  if (!hasPermission) {
    return res
      .status(403)
      .json({ success: false, message: "您沒有權限操作訂單狀態" });
  }

  if (!branch || !code || !["1", "2"].includes(checkout)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    await pool
      .request()
      .input("code", sql.NVarChar, code)
      .input("checkout", sql.NVarChar, checkout)
      .input("chk", sql.NVarChar, checkout === "2" ? "1" : "0").query(`
        UPDATE ordernow SET checkout = @checkout WHERE code = @code;
        UPDATE minvo SET chk = @chk WHERE code = @code;
      `);

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 更新訂單狀態錯誤:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const getOrderotherpay = async (req, res) => {
  const { code, branch } = req.query;

  if (!code || !branch) {
    return res.status(400).json({ error: "缺少 code 或 branch 參數" });
  }

  try {
    const dbConfig = getDbConfig(branch); // ⚠️ 你前面已定義過
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    const result = await pool.request().input("code", sql.NVarChar, code)
      .query(`
        SELECT name, amt
        FROM OrderOtherPay
        WHERE code = @code AND name IS NOT NULL
      `);

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 查詢其他付款明細失敗:", err);
    res.status(500).json({ error: "查詢失敗" });
  }
};

const getOrdermainn = async (req, res) => {
  const { scode, branch } = req.query;

  if (!scode || !branch) {
    return res.status(400).json({ error: "缺少 scode 或 branch 參數" });
  }

  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    const result = await pool.request().input("scode", sql.NVarChar, scode)
      .query(`
        SELECT NAME, OPRICE, QTY, SNAME ,DAMT  ,R1 
        FROM OrderMainN
        WHERE SCODE = @scode
      `);

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 查詢訂單明細失敗:", err);
    res.status(500).json({ error: "查詢失敗" });
  }
};

module.exports = { getOrders, updateStatus, getOrderotherpay, getOrdermainn };
