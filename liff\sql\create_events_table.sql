-- 建立事件表（根據實際資料表結構）
CREATE TABLE Events (
    id NCHAR(10) PRIMARY KEY,                      -- 事件ID
    title NVARCHAR(200) NOT NULL,                  -- 事件標題
    description NTEXT,                             -- 事件描述
    start_datetime DATETIME NOT NULL,              -- 開始時間
    end_datetime DATETIME,                         -- 結束時間
    all_day BIT,                                   -- 是否全天事件
    event_type NCHAR(20) NOT NULL,                 -- 事件類型
    source_type NCHAR(20),                         -- 來源類型
    source_id NVARCHAR(50),                        -- 來源ID
    background_color NVARCHAR(7),                  -- 背景顏色
    active BIT,                                    -- 是否啟用
    dep NCHAR(10) NOT NULL,                        -- 部門ID
    created_by NCHAR(6) NOT NULL,                  -- 建立者
    updated_by NCHAR(6),                           -- 更新者
    created_at DATETIME,                           -- 建立時間
    updated_at DATETIME,                           -- 更新時間

    -- 外鍵約束
    CONSTRAINT FK_Events_Department FOREIGN KEY (dep) REFERENCES dep(id),
    CONSTRAINT FK_Events_CreatedBy FOREIGN KEY (created_by) REFERENCES Users(ID),
    CONSTRAINT FK_Events_UpdatedBy FOREIGN KEY (updated_by) REFERENCES Users(ID)
);

-- 建立事件存取權限表
CREATE TABLE Events_access (
    group_id NCHAR(10) NOT NULL,                   -- 群組ID
    target_type NCHAR(20) NOT NULL,                -- 目標類型

    PRIMARY KEY (group_id, target_type)
);

-- 建立事件目標對象表
CREATE TABLE Events_targets (
    id NCHAR(10) NOT NULL,                         -- 事件ID
    target_type NCHAR(20) NOT NULL,                -- 目標類型
    target_id NVARCHAR(50) NOT NULL,               -- 目標ID

    PRIMARY KEY (id, target_type, target_id),
    CONSTRAINT FK_Events_Targets_Event FOREIGN KEY (id) REFERENCES Events(id)
);

-- 建立索引以提升查詢效能
CREATE INDEX IX_Events_StartDateTime ON Events(start_datetime);
CREATE INDEX IX_Events_EndDateTime ON Events(end_datetime);
CREATE INDEX IX_Events_Department ON Events(dep);
CREATE INDEX IX_Events_Active ON Events(active);
CREATE INDEX IX_Events_CreatedBy ON Events(created_by);
CREATE INDEX IX_Events_DateRange ON Events(start_datetime, end_datetime);
CREATE INDEX IX_Events_EventType ON Events(event_type);
CREATE INDEX IX_Events_SourceType ON Events(source_type);

-- 建立複合索引用於常見查詢
CREATE INDEX IX_Events_Active_Department_DateRange ON Events(active, dep, start_datetime, end_datetime);
CREATE INDEX IX_Events_Targets_Event ON Events_targets(id);
CREATE INDEX IX_Events_Targets_Type ON Events_targets(target_type);
CREATE INDEX IX_Events_Access_Group ON Events_access(group_id);

-- 添加註釋
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'事件管理表，用於存儲月曆事件資訊', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'Events';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'事件ID，格式：YYYYMMDD + 4位流水號', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'Events',
    @level2type = N'COLUMN', @level2name = N'id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'目標類型：department(部門), user(用戶), group(群組), all(全部)', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'Events',
    @level2type = N'COLUMN', @level2name = N'target_type';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'目標ID，多個ID用逗號分隔', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'Events',
    @level2type = N'COLUMN', @level2name = N'target_id';

-- 插入範例資料
INSERT INTO Events (id, title, description, start_datetime, end_datetime, all_day, event_type, source_type, source_id, background_color, active, dep, created_by, created_at)
VALUES
    ('2024080001', '月度會議', '總務部月度工作會議', '2024-08-05 09:00:00', '2024-08-05 11:00:00', 0, 'department', 'manual', '', '#e8f5e9', 1, '1', 'admin', GETDATE()),
    ('2024080002', '系統維護', '資訊部系統定期維護', '2024-08-12 22:00:00', '2024-08-13 02:00:00', 0, 'department', 'manual', '', '#e3f2fd', 1, '2', 'admin', GETDATE()),
    ('2024080003', '行銷策劃會議', '行銷部策劃下季度活動', '2024-08-18 14:00:00', '2024-08-18 17:00:00', 0, 'department', 'manual', '', '#f3e5f5', 1, '3', 'admin', GETDATE()),
    ('2024080004', '員工培訓', '人資部新員工培訓', '2024-08-25 09:00:00', '2024-08-25 17:00:00', 0, 'department', 'manual', '', '#fff8e1', 1, '4', 'admin', GETDATE()),
    ('2024080005', '父親節活動', '小蒙牛父親節活動，全店消費滿2000送精美禮品', '2024-08-01', '2024-08-15', 1, 'brand', 'manual', '', '#f3e5f5', 1, '3', 'admin', GETDATE());

-- 插入事件目標對象範例資料
INSERT INTO Events_targets (id, target_type, target_id)
VALUES
    ('2024080001', 'department', '1'),
    ('2024080002', 'department', '2'),
    ('2024080003', 'department', '3'),
    ('2024080004', 'department', '4'),
    ('2024080005', 'all', 'all');

-- 插入事件存取權限範例資料
INSERT INTO Events_access (group_id, target_type)
VALUES
    ('admin', 'all'),
    ('manager', 'department'),
    ('user', 'user');

PRINT '事件表建立完成，已插入範例資料';
