<template>
  <q-page
    class="bg-white q-pa-sm flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <!-- 標題容器 -->
    <div class="header-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">差假申請單</div>
          </div>
        </q-card-section>

        <!-- Progress Indicator -->
        <q-card-section class="q-pt-none q-pb-sm">
          <div v-if="loadingFlows" class="text-center q-pa-md">
            <q-spinner-dots color="primary" size="2em" />
          </div>
          <div v-else class="progress-indicator">
            <template v-for="(flowStep, index) in flowSteps" :key="index">
              <div
                class="progress-step"
                :class="{ active: step === flowStep.number }"
              >
                <div class="progress-dot">{{ flowStep.number }}</div>
                <div class="progress-label">{{ flowStep.name }}</div>
              </div>
              <div
                v-if="index < flowSteps.length - 1"
                class="progress-line"
              ></div>
            </template>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Main Form -->
    <div class="q-pa-md">
      <div class="form-container">
        <!-- User Info Section -->
        <div class="form-section">
          <div class="section-title">基本資料</div>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="user.department"
                label="部門"
                outlined
                dense
                disable
                bg-color="grey-2"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-select
                v-model="leaveType"
                :options="leaveOptions"
                label="假別"
                outlined
                dense
              />
            </div>
          </div>
        </div>

        <!-- Date & Time Section -->
        <div class="form-section">
          <div class="section-title">請假時間</div>

          <!-- Full Day Toggle -->
          <div class="q-mb-sm">
            <q-banner
              v-if="!startDate"
              class="bg-orange-1 text-orange-10 q-mb-sm"
              dense
            >
              請先選擇開始日期，才能選擇「整天請假」
            </q-banner>

            <!-- 📦 整天請假勾選框 -->
            <q-checkbox
              v-model="isFullDay"
              label="整天請假"
              @update:model-value="handleFullDayToggle"
              :disable="!startDate"
            />
          </div>

          <!-- Start Date/Time -->
          <div class="row q-col-gutter-sm q-mb-md">
            <div class="col-12 col-sm-6">
              <div class="row q-col-gutter-xs">
                <div class="col-7">
                  <q-input
                    v-model="startDate"
                    label="開始日期"
                    type="date"
                    outlined
                    dense
                  />
                </div>
                <div class="col-5">
                  <q-input
                    v-model="startTime"
                    label="時間"
                    type="time"
                    outlined
                    dense
                  />
                </div>
              </div>
            </div>

            <!-- End Date/Time -->
            <div class="col-12 col-sm-6">
              <div class="row q-col-gutter-xs">
                <div class="col-7">
                  <q-input
                    v-model="endDate"
                    label="結束日期"
                    type="date"
                    outlined
                    dense
                  />
                </div>
                <div class="col-5">
                  <q-input
                    v-model="endTime"
                    label="時間"
                    type="time"
                    outlined
                    dense
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Total Time Display -->
          <div class="time-display q-pa-sm q-mb-md">
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <div class="time-label">總天數</div>
                <q-input
                  v-model="totalDays"
                  type="number"
                  min="0"
                  dense
                  outlined
                  @focus="userEditDays = true"
                  @blur="onDaysBlur"
                  :readonly="false"
                  class="q-mb-none"
                />
              </div>
              <div class="col-6">
                <div class="time-label">總時數</div>
                <q-input
                  v-model="totalHours"
                  type="number"
                  min="0"
                  step="0.1"
                  dense
                  outlined
                  @focus="userEditHours = true"
                  @blur="onHoursBlur"
                  :readonly="false"
                  class="q-mb-none"
                />
              </div>
            </div>
            <q-badge v-if="hasTimeError" color="negative" class="q-mt-sm">
              請檢查開始時間必須早於結束時間
            </q-badge>
          </div>
        </div>

        <!-- Reason Section -->
        <div class="form-section">
          <div class="section-title">請假事由</div>
          <q-input
            v-model="reason"
            type="textarea"
            outlined
            autogrow
            class="q-mb-md"
            placeholder="請填寫請假原因..."
          />

          <!-- File Upload -->
          <div class="file-upload">
            <div class="upload-title q-mb-sm">
              <q-icon
                name="attach_file"
                size="sm"
                class="q-mr-xs text-primary"
              />
              <span class="text-subtitle2">附件上傳</span>
              <span class="text-caption text-grey-7 q-ml-sm"
                >(可上傳照片或PDF文件)</span
              >
            </div>

            <div class="upload-area q-mb-md">
              <q-file
                v-model="tempFileUpload"
                outlined
                dense
                multiple
                accept=".jpg,.png,.pdf"
                @update:model-value="handleFileSelection"
                class="upload-input full-width"
                hide-bottom-space
                label="選擇檔案"
                clearable
              >
                <template v-slot:hint> 最多可上傳5個檔案 </template>
              </q-file>
            </div>

            <!-- Selected Files List -->
            <div v-if="selectedFiles.length > 0" class="file-list q-mb-md">
              <div class="text-caption q-mb-xs text-grey-8">
                <q-icon name="info" size="xs" class="q-mr-xs" />
                已選擇 {{ selectedFiles.length }} 個檔案
              </div>

              <q-list bordered separator class="rounded-borders">
                <q-item
                  v-for="(file, index) in selectedFiles"
                  :key="index"
                  dense
                  class="file-item"
                >
                  <q-item-section avatar>
                    <q-icon
                      :name="getFileIconName(file.name)"
                      :color="getFileIconColor(file.name)"
                    />
                  </q-item-section>

                  <q-item-section>
                    <q-tooltip>{{ file.name }}</q-tooltip>
                    <q-item-label lines="1" class="file-name">{{
                      file.name
                    }}</q-item-label>
                    <q-item-label caption>{{
                      formatFileSize(file.size)
                    }}</q-item-label>
                  </q-item-section>

                  <q-item-section side>
                    <q-btn
                      flat
                      round
                      dense
                      color="grey-7"
                      icon="close"
                      @click="removeFile(index)"
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>

        <!-- Submit Actions -->
        <div class="form-actions">
          <q-btn outline unelevated color="secondary" @click="submitLeave">
            <template v-slot:default>
              <send class="q-mr-sm" :stroke-width="1.5" :size="20" />
              送出
            </template>
          </q-btn>
        </div>
      </div>
    </div>

    <!-- Confirm Dialog -->
    <q-dialog v-model="confirmDialog" persistent>
      <q-card class="confirm-dialog">
        <q-card-section class="bg-primary text-white">
          <div class="text-subtitle1">確認提交申請</div>
        </q-card-section>
        <q-card-section>
          <div class="confirm-content">
            <div class="confirm-item">
              <div class="confirm-label">假別</div>
              <div class="confirm-value">{{ leaveType }}</div>
            </div>
            <div class="confirm-item">
              <div class="confirm-label">開始時間</div>
              <div class="confirm-value">{{ startDate }} {{ startTime }}</div>
            </div>
            <div class="confirm-item">
              <div class="confirm-label">結束時間</div>
              <div class="confirm-value">{{ endDate }} {{ endTime }}</div>
            </div>
            <div class="confirm-item">
              <div class="confirm-label">總時數</div>
              <div class="confirm-value">
                {{ totalDays }}天 {{ totalHours }}小時
              </div>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn
            flat
            label="確認送出"
            color="primary"
            @click="confirmSubmitLeave"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import apiClient from "../api";
import { DateTime } from "luxon";
import { useQuasar } from "quasar";
import { usePendingStore } from "../stores/pendingStore";
import { useRouter, useRoute } from "vue-router";
import { useFormFlows } from "../composables/useFormFlows";

const route = useRoute();
const $q = useQuasar();
const router = useRouter();
const pendingStore = usePendingStore();
const props = defineProps({ Dname: String, Userid: String });

// 使用 useFormFlows composable
const { flowSteps, loading: loadingFlows } = useFormFlows("leave");

// UI State
const step = ref(1);
const user = ref({ userId: props.Userid, department: props.Dname });
const isSubmitting = ref(false);
const confirmDialog = ref(false);
const hasTimeError = ref(false);
const userEditDays = ref(false);
const userEditHours = ref(false);

// Form Fields
const leaveType = ref("");
const leaveOptions = ref(["特休假", "事假", "病假", "時數假", "產假"]);
const startDate = ref("");
const endDate = ref("");
const startTime = ref("");
const endTime = ref("");
const totalDays = ref("");
const totalHours = ref("");
const reason = ref("");
const fileAttachments = ref([]); // 這將用於API提交
const selectedFiles = ref([]); // 用於UI顯示
const tempFileUpload = ref(null); // 臨時文件上傳
const attachmentsCount = ref(0);
const isFullDay = ref(false);

// Form Validation
const isFormValid = computed(() => {
  return (
    !!leaveType.value &&
    !!startDate.value &&
    !!startTime.value &&
    !!endDate.value &&
    !!endTime.value &&
    !hasTimeError.value
  );
});

// Helper Functions
const toUTCDateTime = (date, time) => {
  return DateTime.fromFormat(`${date} ${time}`, "yyyy-MM-dd HH:mm", {
    zone: "Asia/Taipei",
  })
    .toUTC()
    .toISO();
};

function getFileIconName(filename) {
  const extension = filename.split(".").pop().toLowerCase();
  if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
    return "image";
  } else if (extension === "pdf") {
    return "picture_as_pdf";
  } else {
    return "insert_drive_file";
  }
}

function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function handleFileSelection(files) {
  if (!files) return;

  // 將新選擇的文件添加到已選擇列表
  if (Array.isArray(files)) {
    selectedFiles.value = [...selectedFiles.value, ...files];
  } else {
    selectedFiles.value = [...selectedFiles.value, files];
  }

  // 限制最多5個檔案
  if (selectedFiles.value.length > 5) {
    selectedFiles.value = selectedFiles.value.slice(0, 5);
    $q.notify({
      color: "warning",
      message: "最多只能上傳5個檔案",
      icon: "warning",
    });
  }

  // 更新fileAttachments以便API提交
  fileAttachments.value = [...selectedFiles.value];

  // 清空臨時上傳，以便下次選擇
  tempFileUpload.value = null;

  // 更新附件計數
  attachmentsCount.value = selectedFiles.value.length;
}

function removeFile(index) {
  selectedFiles.value.splice(index, 1);
  fileAttachments.value = [...selectedFiles.value];
  attachmentsCount.value = selectedFiles.value.length;
}

function clearAllFiles() {
  selectedFiles.value = [];
  fileAttachments.value = [];
  tempFileUpload.value = null;
  attachmentsCount.value = 0;
}

function clearAttachments() {
  clearAllFiles();
}

function handleFiles(files) {
  attachmentsCount.value = files
    ? Array.isArray(files)
      ? files.length
      : 1
    : 0;
}

function handleFullDayToggle() {
  if (isFullDay.value) {
    startTime.value = "09:00";
    endDate.value = startDate.value;
    endTime.value = "18:00";
    calculateLeaveTime();
  }
}

function goBack() {
  router.go(-1);
}

// Watch for date/time changes
watch([startDate, startTime, endDate, endTime], () => {
  userEditDays.value = false;
  userEditHours.value = false;
  calculateLeaveTime();
});

function calculateLeaveTime() {
  if (
    !startDate.value ||
    !startTime.value ||
    !endDate.value ||
    !endTime.value
  ) {
    if (!userEditDays.value) totalDays.value = "";
    if (!userEditHours.value) totalHours.value = "";
    hasTimeError.value = false;
    return;
  }

  // Convert start & end time
  const start = DateTime.fromFormat(
    `${startDate.value} ${startTime.value}`,
    "yyyy-MM-dd HH:mm",
    { zone: "Asia/Taipei" }
  );
  const end = DateTime.fromFormat(
    `${endDate.value} ${endTime.value}`,
    "yyyy-MM-dd HH:mm",
    { zone: "Asia/Taipei" }
  );

  if (!start.isValid || !end.isValid || start >= end) {
    if (!userEditDays.value) totalDays.value = "錯誤";
    if (!userEditHours.value) totalHours.value = "錯誤";
    hasTimeError.value = true;
    return;
  }

  hasTimeError.value = false;
  let workHours = 0;
  let workDays = 0;
  let current = start;

  while (current < end) {
    let workStart = current.set({ hour: 9, minute: 0 });
    let workEnd = current.set({ hour: 18, minute: 0 });
    let lunchStart = current.set({ hour: 12, minute: 0 });
    let lunchEnd = current.set({ hour: 13, minute: 0 });

    // Skip weekends
    while (isWeekend(current)) {
      current = current.plus({ days: 1 }).set({ hour: 9, minute: 0 });
      if (current > end) {
        if (!userEditDays.value) totalDays.value = workDays;
        if (!userEditHours.value) totalHours.value = workHours.toFixed(1);
        return;
      }
    }

    // Handle end time cap
    if (current.hasSame(end, "day") && end > workEnd) {
      workEnd = workEnd;
    } else if (current.hasSame(end, "day")) {
      workEnd = end;
    }

    // Skip before 9:00 & after 18:00
    if (current < workStart) {
      current = workStart;
    }
    if (current >= workEnd) {
      current = current.plus({ days: 1 }).set({ hour: 9, minute: 0 });
      continue;
    }

    let dailyHours = calculateDailyWorkHours(
      current,
      workEnd,
      lunchStart,
      lunchEnd
    );
    workHours += dailyHours;

    // Convert 8 hours to 1 day
    if (workHours >= 8) {
      workDays += Math.floor(workHours / 8);
      workHours = workHours % 8;
    }

    current = current.plus({ days: 1 }).set({ hour: 9, minute: 0 });
  }

  // Set results
  if (!userEditDays.value) totalDays.value = workDays;
  if (!userEditHours.value) totalHours.value = workHours.toFixed(1);
}

// Calculate daily work hours (exclude lunch break & time after 18:00)
function calculateDailyWorkHours(start, end, lunchStart, lunchEnd) {
  let total = end.diff(start, "hours").hours;

  // If start & end time both within lunch break, set to 0
  if (start >= lunchStart && end <= lunchEnd) {
    return 0;
  }

  // If time range includes lunch break, deduct 1 hour
  if (start < lunchStart && end > lunchEnd) {
    total -= 1;
  }

  return Math.max(0, total); // Ensure no negative values
}

// Determine if date is weekend
function isWeekend(date) {
  const weekday = date.weekday;
  return weekday === 6 || weekday === 7; // 6: Saturday, 7: Sunday
}

function submitLeave() {
  confirmDialog.value = true;
}

function onDaysBlur() {
  if (totalDays.value === "" || totalDays.value === null) {
    userEditDays.value = false;
    calculateLeaveTime();
  }
}
function onHoursBlur() {
  if (totalHours.value === "" || totalHours.value === null) {
    userEditHours.value = false;
    calculateLeaveTime();
  }
}

// Submit leave application
const confirmSubmitLeave = async () => {
  isSubmitting.value = true;

  const requestData = {
    user_id: user.value.userId,
    leaveType: leaveType.value,
    stime: toUTCDateTime(startDate.value, startTime.value),
    etime: toUTCDateTime(endDate.value, endTime.value),
    totalDays: totalDays.value.toString(),
    totalHours: totalHours.value,
    reason: reason.value,
    attachmentsCount: attachmentsCount.value,
  };

  console.log("📤 送出請假申請:", requestData);

  try {
    const response = await apiClient.post(
      "/formsend/submit_leave",
      requestData
    );
    const result = response.data;

    if (result.status === "success") {
      console.log(`✅ 請假申請成功！表單 ID: ${result.form_id}`);

      // 更新待辦事項
      await pendingStore.fetchPendingTasks(props.Userid);

      // 上傳附件
      if (fileAttachments.value && fileAttachments.value.length > 0) {
        await uploadAttachments(result.form_id, fileAttachments.value);
      }

      // 成功通知
      $q.notify({
        color: "positive",
        position: "top",
        message: "申請已成功送出",
        icon: "check_circle",
      });

      // 跳轉至感謝頁面
      const username = route.query.username;
      router.push({
        path: "/thanks",
        query: {
          formId: result.form_id,
          username: username,
        },
      });
    } else {
      console.error("❌ 請假申請失敗:", result.error);
      $q.notify({
        color: "negative",
        position: "top",
        message: "申請送出失敗",
        caption: result.error || "請稍後再試",
        icon: "error",
      });
    }
  } catch (error) {
    console.error("❌ API 錯誤:", error);
    $q.notify({
      color: "negative",
      position: "top",
      message: "API 錯誤",
      caption: "請確認網路連線",
      icon: "error",
    });
  } finally {
    isSubmitting.value = false;
  }
};

const uploadAttachments = async (formId, attachments = []) => {
  if (!attachments || attachments.length === 0) {
    console.log("🔍 沒有附件要上傳，跳過上傳流程");
    return;
  }

  // 取得當前年月作為資料夾名稱 (格式: YYYYMM)
  const now = new Date();
  const yearMonth = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(
    2,
    "0"
  )}`;
  const uploadPath = `uploads/form/leave/${yearMonth}/`;

  const formData = new FormData();
  formData.append("form_id", formId);
  formData.append("upload_path", uploadPath);

  if (Array.isArray(attachments)) {
    attachments.forEach((file) => {
      formData.append("attachments", file);
    });
  } else {
    formData.append("attachments", attachments);
  }

  try {
    const response = await apiClient.post("/upload_attachments", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });

    const result = response.data;
    if (result.status === "success") {
      console.log(`✅ 附件上傳成功`);
    } else {
      console.error("❌ 附件上傳失敗:", result.error);
      $q.notify({
        color: "warning",
        position: "top",
        message: "附件上傳失敗",
        caption: result.error || "表單已送出，但附件未能成功上傳",
        icon: "warning",
      });
    }
  } catch (error) {
    console.error("❌ API 錯誤 (附件上傳):", error);
    $q.notify({
      color: "warning",
      position: "top",
      message: "API 錯誤 (附件上傳)",
      caption: "表單已送出，但附件未能成功上傳",
      icon: "warning",
    });
  }
};

function getFileIconColor(filename) {
  const extension = filename.split(".").pop().toLowerCase();
  if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
    return "green";
  } else if (extension === "pdf") {
    return "red";
  } else {
    return "blue";
  }
}
</script>

<style scoped>
.header-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 16px;
  padding: 0;
}

.header-container .q-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

.progress-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-dot {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #424242;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8rem;
  font-weight: 500;
}

.progress-step.active .progress-dot {
  background-color: var(--q-primary);
  color: white;
}

.progress-label {
  margin-top: 4px;
  font-size: 0.75rem;
  color: #616161;
}

.progress-step.active .progress-label {
  color: var(--q-primary);
  font-weight: 500;
}

.progress-line {
  flex: 1;
  height: 1px;
  background-color: #e0e0e0;
  margin: 0 8px;
  position: relative;
  top: -14px;
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-weight: 500;
  font-size: 0.9rem;
  color: #424242;
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e0e0;
}

.time-display {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.time-label {
  font-size: 0.75rem;
  color: #757575;
  margin-bottom: 2px;
}

.time-value {
  font-weight: 500;
  color: #424242;
  font-size: 1.1rem;
}

.file-upload {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
  max-width: 100%; /* 確保不會超出父容器 */
  overflow: hidden; /* 防止內容溢出 */
}

.upload-title {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #424242;
}

.upload-area {
  display: flex;
  align-items: center;
}

.upload-input {
  flex: 1;
}

.full-width {
  width: 100%;
}

.file-list {
  margin-top: 12px;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 8px;
  width: 100%; /* 確保不會超出父容器 */
}

.file-item {
  padding: 4px 8px;
}

.file-name {
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px; /* 限制檔案名稱寬度 */
}

.file-size {
  color: #757575;
  min-width: 50px;
  text-align: right;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.confirm-dialog {
  min-width: 300px;
}

.confirm-content {
  padding: 8px 0;
}

.confirm-item {
  display: flex;
  margin-bottom: 8px;
}

.confirm-label {
  width: 80px;
  font-size: 0.85rem;
  color: #757575;
}

.confirm-value {
  flex: 1;
  font-weight: 500;
}

/* 移動設備上的調整 */
@media (max-width: 599px) {
  .q-mt-xl {
    margin-top: 100px !important;
  }
}
</style>
