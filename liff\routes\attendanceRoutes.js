const express = require("express");
const {
  getAttendanceRecords,
  getUserInfo,
  getBranchs,
} = require("../controllers/attendanceController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/records", authMiddleware, getAttendanceRecords);
router.get("/user-info", authMiddleware, getUserInfo);
router.get("/get_branchs", authMiddleware, getBranchs);

module.exports = router;
