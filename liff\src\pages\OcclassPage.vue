<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="width: 100%; max-width: 500px; border-radius: 12px"
    >
      <!-- 🔶 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        類部口味
      </q-card-section>

      <!-- 🟦 類部清單 -->
      <q-list separator>
        <VueDraggable
          v-model="cclassList"
          item-key="code"
          tag="div"
          handle=".group-drag-handle"
          ghost-class="bg-indigo-1"
          delay="200"
          @end="onGroupSortEnd"
        >
          <div
            v-for="group in cclassList"
            :key="group.code"
            class="bg-white q-mb-sm"
            style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
          >
            <q-expansion-item
              expand-separator
              class="q-pa-none"
              header-class="text-indigo"
            >
              <!-- 🔹 類部主列 -->
              <template #header>
                <q-item
                  dense
                  class="q-pa-none q-gutter-none items-center"
                  style="width: 100%; position: relative"
                >
                  <!-- 拖曳手把 -->
                  <q-item-section avatar class="group-drag-handle">
                    <q-icon name="drag_handle" color="grey-7" />
                  </q-item-section>

                  <!-- 類部資訊 -->
                  <q-item-section>
                    <div class="text-indigo-10">{{ group.name }}</div>
                    <div class="text-caption text-grey-8">
                      <span class="text-brown-10"> {{ group.code }} </span> |
                      服務費:
                      <span
                        :class="
                          group.service === '1'
                            ? 'text-green-8'
                            : 'text-negative'
                        "
                      >
                        {{ group.service === "1" ? "✔" : "✘" }}
                      </span>
                      | 折扣:
                      <span
                        :class="
                          group.discount === '1'
                            ? 'text-green-8'
                            : 'text-negative'
                        "
                      >
                        {{ group.discount === "1" ? "✔" : "✘" }}
                      </span>
                    </div>
                  </q-item-section>

                  <!-- 最右側展開箭頭 -->
                  <q-item-section side />

                  <!-- ✅ 編輯按鈕：浮在右上角（展開箭頭左側） -->
                  <q-btn
                    flat
                    dense
                    size="sm"
                    icon="edit"
                    color="primary"
                    @click.stop="editGroup(group)"
                    style="
                      position: absolute;
                      right: 14px; /* ⬅️ 靠近展開箭頭 */
                      top: 50%;
                      transform: translateY(-50%);
                      z-index: 1;
                    "
                  />
                </q-item>
              </template>

              <!-- 🔽 展開內容 -->
              <q-card-section class="q-pa-sm q-pt-none bg-grey-1">
                <div
                  v-if="group.tastes && group.tastes.length"
                  class="row items-center text-subtitle2 text-primary q-pa-sm q-mb-sm rounded-borders"
                >
                  <q-icon name="restaurant_menu" class="q-mr-sm" />
                  口味
                </div>

                <VueDraggable
                  v-model="group.tastes"
                  item-key="seq"
                  handle=".drag-handle"
                  tag="div"
                  ghost-class="bg-grey-3"
                  @end="onTasteSortEnd(group)"
                >
                  <div
                    v-for="(taste, idx) in group.tastes"
                    :key="taste.seq"
                    class="q-mb-xs"
                  >
                    <q-item class="bg-grey-2 q-pa-sm rounded-borders" clickable>
                      <q-item-section avatar class="drag-handle">
                        <q-icon name="drag_indicator" color="grey-7" />
                      </q-item-section>

                      <q-item-section>
                        <div class="text-subtitle2">{{ taste.name }}</div>
                        <div class="text-caption text-grey" v-if="taste.price">
                          （+{{ taste.price }} 元）
                        </div>
                      </q-item-section>

                      <q-item-section side>
                        <q-btn
                          flat
                          round
                          dense
                          icon="close"
                          color="negative"
                          @click.stop="deleteTaste(group, idx)"
                        />
                      </q-item-section>
                    </q-item>
                  </div>
                </VueDraggable>

                <div class="q-mt-sm">
                  <q-btn
                    dense
                    flat
                    icon="add"
                    color="primary"
                    label="新增口味"
                    @click="openTasteDialog(group)"
                  />
                </div>
              </q-card-section>
            </q-expansion-item>
          </div>
        </VueDraggable>
      </q-list>

      <!-- ➕ 新增類部按鈕 -->

      <q-btn
        color="green"
        icon="add"
        label="新增類部"
        @click="editGroup(null)"
        class="full-width"
      />
    </q-card>
  </q-page>

  <q-dialog v-model="editDialog" no-focus>
    <q-card
      style="max-width: 300px; max-height: 90vh; overflow-y: auto"
      class="q-pa-sm bg-white"
    >
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-primary">編輯類部</div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <div class="q-gutter-md column">
          <div class="row q-col-gutter-none items-center">
            <div class="col-3">
              <q-input
                v-model="form.code"
                label="編號"
                dense
                :readonly="isEditing"
                class="q-mb-none"
              />
            </div>
            <div class="col q-ml-sm">
              <q-input
                v-model="form.name"
                label="名稱"
                dense
                class="q-mb-none"
              />
            </div>
          </div>

          <div class="row q-col-gutter-none items-center">
            <div class="col">
              <q-select
                v-model="form.service"
                label="服務費"
                :options="[
                  { label: '不收', value: '0' },
                  { label: '收', value: '1' },
                ]"
                dense
                emit-value
                map-options
                class="q-mb-none"
              />
            </div>
            <div class="col q-ml-md">
              <q-select
                v-model="form.discount"
                label="可否折扣"
                :options="[
                  { label: '不可折', value: '0' },
                  { label: '可折扣', value: '1' },
                ]"
                dense
                emit-value
                map-options
                class="q-mb-none"
              />
            </div>
          </div>

          <q-select
            v-model="form.mdisp"
            label="平板顯示方式"
            :options="[
              { label: '不顯示', value: '2' },
              { label: '最後加點後隱藏', value: '3' },
              { label: '用餐時間內顯示', value: '9' },
            ]"
            emit-value
            map-options
          />

          <q-input
            v-model="form.defineC2"
            label="平板時間限制（秒）"
            type="number"
          />

          <q-toggle
            v-model="form.showorder"
            label="手機點餐是否顯示"
            true-value="T"
            false-value="F"
          />
        </div>
      </q-card-section>

      <q-card-actions class="q-pt-md q-gutter-sm">
        <!-- 🔴 刪除按鈕（左） -->
        <q-btn
          color="negative"
          label="刪除"
          @click="openDeleteDialog(form)"
          outline
          icon="delete"
          flat
        />

        <q-space />

        <!-- 🟣 儲存按鈕（右） -->
        <q-btn
          color="secondary"
          label="儲存"
          @click="saveGroup(form)"
          outline
          icon="save"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="deleteDialog">
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          群組刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteDeptConfirmed()"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="tasteDialog.visible" persistent>
    <q-card class="q-pa-md" style="width: 100%; max-width: 500px">
      <!-- 🔹 標題 + 管理按鈕 -->
      <div class="row items-center justify-between q-mb-md">
        <div class="text-h6 text-primary">新增口味</div>
        <div class="row items-center q-gutter-xs">
          <q-btn dense flat round icon="settings" @click="openTspaceDialog">
            <q-tooltip>管理口味清單</q-tooltip>
          </q-btn>
          <q-btn icon="close" flat round dense v-close-popup />
        </div>
      </div>

      <!-- 🔸 主要操作列：口味選擇 + 加價金額 -->
      <div class="row q-col-gutter-sm q-mb-md">
        <!-- 左：口味選擇 -->
        <div class="col">
          <q-select
            v-model="tasteDialog.form.name"
            :options="tspaceList"
            label="口味名稱"
            emit-value
            map-options
            dense
            outlined
          />
        </div>

        <!-- 右：加價金額 -->
        <div style="width: 120px">
          <q-input
            v-model.number="tasteDialog.form.price"
            type="number"
            label="加價"
            prefix="$"
            dense
            outlined
            min="0"
            placeholder="0"
          />
        </div>
      </div>

      <!-- 🔸 動作按鈕 -->
      <div class="row justify-end">
        <q-btn
          label="新增"
          color="secondary"
          icon="add"
          outline
          unelevated
          :disable="!tasteDialog.form.name"
          @click="confirmAddTaste"
        />
      </div>
    </q-card>
  </q-dialog>

  <!-- 新增口味清單： -->
  <q-dialog v-model="tspaceDialog.visible" persistent>
    <q-card style="width: 100%; max-width: 420px" class="q-pa-md">
      <!-- 🔹 標題列 -->
      <div class="row items-center justify-between q-mb-sm">
        <div class="text-h6 text-deep-orange">管理口味清單</div>
        <q-btn icon="close" flat round dense v-close-popup />
      </div>

      <!-- 🔸 新增區 -->
      <div class="q-gutter-sm q-mb-md">
        <div class="text-subtitle2 text-grey-8">新增口味</div>
        <div class="row q-gutter-sm items-center">
          <q-input
            v-model="tspaceDialog.name"
            label="口味名稱"
            dense
            outlined
            class="col"
            @keyup.enter="confirmAddTspace"
          />
          <q-btn
            icon="add"
            color="primary"
            dense
            round
            unelevated
            :disable="!tspaceDialog.name"
            @click="confirmAddTspace"
          >
            <q-tooltip>新增口味</q-tooltip>
          </q-btn>
        </div>
      </div>

      <!-- 🔸 口味清單 -->
      <div class="q-mb-md">
        <div class="text-subtitle2 text-grey-8 q-mb-xs">現有口味清單</div>
        <q-list bordered separator dense>
          <q-item
            v-for="item in allTspaceList"
            :key="item.value"
            class="q-px-sm"
          >
            <q-item-section>{{ item.label }}</q-item-section>
            <q-item-section side>
              <q-btn
                icon="delete"
                size="sm"
                flat
                round
                color="negative"
                @click="deleteTspec(item)"
              >
                <q-tooltip>刪除此口味</q-tooltip>
              </q-btn>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- 🔸 關閉按鈕 -->
      <div class="row justify-end">
        <q-btn flat label="關閉" @click="tspaceDialog.visible = false" />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { VueDraggable } from "vue-draggable-plus";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";

const props = defineProps({ Branch: String });
const $q = useQuasar();
const tspaceDialog = ref({
  visible: false,
  name: "",
});

const openTspaceDialog = () => {
  tspaceDialog.value.name = "";
  tspaceDialog.value.visible = true;
};

const confirmAddTspace = async () => {
  const name = tspaceDialog.value.name.trim();
  if (!name) return;

  // 檢查是否重複
  const alreadyExists = allTspaceList.value.some((item) => item.label === name);
  if (alreadyExists) {
    $q.notify({ type: "warning", message: "此口味已存在" });
    return;
  }

  try {
    // 送後端寫入資料
    await apiClient.post("/onlinecclass/insert_Tspace", {
      branch: props.Branch,
      name,
    });

    // 更新 local 清單
    const newItem = { label: name, value: name };
    allTspaceList.value.push(newItem);
    tspaceList.value.push(newItem); // 若需同步
    tspaceDialog.value.name = "";
    $q.notify({ type: "positive", message: "新增成功" });
  } catch (err) {
    console.error("❌ 新增口味失敗", err);
    $q.notify({ type: "negative", message: "新增失敗" });
  }
};

const tasteDialog = ref({
  visible: false,
  targetGroup: null,
  form: {
    name: "",
    price: 0,
  },
});

const tspaceList = ref([]);
const allTspaceList = ref([]);

const openTasteDialog = (group) => {
  // ➤ 把所有已使用的 name 擷取出來
  const usedNames = (group.tastes || []).map((t) => t.name);

  // ➤ 過濾掉已經用過的項目
  tspaceList.value = allTspaceList.value.filter(
    (item) => !usedNames.includes(item.value)
  );

  // ➤ 初始化 Dialog 表單
  tasteDialog.value.form = {
    name: "", // 選擇的口味
    price: 0, // 預設價格
  };
  tasteDialog.value.group = group;
  tasteDialog.value.visible = true;
};

const confirmAddTaste = async () => {
  const { name, price } = tasteDialog.value.form;
  const group = tasteDialog.value.group;

  if (!name || !group) return;

  if (!group.tastes) group.tastes = [];

  // 🔍 先找出目前最大 seq（確保不重複）
  const total = group.tastes.length;
  const newSeqNumber = total + 1;

  // 🚩 編號規則：每頁 30 個
  const newCno = Math.floor((newSeqNumber - 1) / 30) + 1;
  const newSeq = String(((newSeqNumber - 1) % 30) + 1).padStart(2, "0");

  // 🆕 新資料
  const newTaste = {
    code: group.code,
    name,
    price,
    seq: newSeq,
    cno: newCno,
    show: "1",
  };

  try {
    // ✅ 寫入後端資料庫
    const res = await apiClient.post("/onlinecclass/save_Taste", {
      branch: props.Branch,
      taste: newTaste,
    });
    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: `新增成功`,
      });

      // ✅ 前端更新
      group.tastes.push(newTaste);
      tasteDialog.value.visible = false;
    } else {
      throw new Error(res.data.message || "新增失敗");
    }
  } catch (err) {
    console.error("❌ 新增口味失敗:", err);
    $q.notify({
      type: "negative",
      message: "新增口味失敗：" + (err.response?.data?.message || err.message),
    });
  }
};

const deleteDialog = ref(false);
const deleteTarget = ref(null); // ➕ 要刪除的那筆資料

const editDialog = ref(false);

const openDeleteDialog = (group) => {
  deleteTarget.value = group;
  deleteDialog.value = true;
};

const form = ref({});

const editGroup = (group = null) => {
  if (group && group.code) {
    // 👉 編輯模式：填入原始資料
    isEditing.value = true;
    form.value = {
      code: group.code.trim(),
      name: group.name?.trim() || "",
      service: group.service ?? "0",
      discount: group.discount ?? "0",
      mdisp: group.mdisp ?? "2",
      defineC2: group.defineC2 ?? "0",
      showorder: group.showorder ?? "T",
      sno: group.sno ?? "",
    };
    console.log(form.value);
  } else {
    isEditing.value = false;
    // 👉 新增模式：預設空白
    form.value = {
      code: "", // 使用者需輸入
      name: "",
      service: "0",
      discount: "0",
      mdisp: "2",
      defineC2: "0",
      showorder: "T",
      sno: "",
    };
  }

  editDialog.value = true;
};
const isEditing = ref(false);

const saveGroup = async (form) => {
  if (!isEditing.value) {
    const duplicate = cclassList.value.some(
      (item) => item.code.trim() === form.code.trim()
    );

    if (duplicate) {
      $q.notify({
        type: "warning",
        message: `代碼「${form.code}」已存在，請重新輸入`,
      });
      return; // 中止儲存
    }
  }
  try {
    const res = await apiClient.post("/onlinecclass/save_Cclass", {
      branch: props.Branch,
      list: [
        {
          code: form.code.trim(),
          name: form.name?.trim() || "",
          service: form.service ?? "0",
          discount: form.discount ?? "0",
          mdisp: form.mdisp ?? "2",
          defineC2: form.defineC2 ?? "0",
          showorder: form.showorder ?? "T",
          sno:
            form.sno && form.sno !== ""
              ? form.sno
              : String(cclassList.value.length + 1).padStart(2, "0"),
        },
      ],
    });

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: `類部「${form.name}」已儲存`,
      });

      editDialog.value = false;
      await fetchCclass(); // 重新取得資料
    } else {
      throw new Error(res.data.message || "儲存失敗");
    }
  } catch (err) {
    console.error("❌ 儲存類部失敗:", err);
    $q.notify({
      type: "negative",
      message: "儲存失敗，請稍後再試",
    });
  }
};

const cclassList = ref([]);

const onGroupSortEnd = async () => {
  // 🟡 更新排序欄位 (sno)
  cclassList.value.forEach((item, index) => {
    item.sno = index + 1;
  });

  try {
    await apiClient.post("/onlinecclass/save_GroupSort", {
      branch: props.Branch,
      groups: cclassList.value.map((item) => ({
        code: item.code,
        sno: item.sno,
      })),
    });

    $q.notify({ type: "positive", message: "排序已儲存" });
  } catch (err) {
    console.error("❌ 排序儲存錯誤", err);
    $q.notify({ type: "negative", message: "排序儲存失敗" });
  }
};

const onTasteSortEnd = async (group) => {
  try {
    // 重新計算 seq / cno
    group.tastes.forEach((item, index) => {
      item.seq = String((index % 30) + 1).padStart(2, "0");
      item.cno = Math.floor(index / 30) + 1;
    });

    // 呼叫 API 儲存
    await apiClient.post("/onlinecclass/save_TasteSort", {
      branch: props.Branch,
      code: group.code, // 類部代碼
      tastes: group.tastes.map((item) => ({
        name: item.name,
        price: item.price,
        seq: item.seq,
        cno: item.cno,
      })),
    });

    $q.notify({ type: "positive", message: "口味排序已儲存" });
  } catch (err) {
    console.error("❌ 口味排序儲存錯誤", err);
    $q.notify({ type: "negative", message: "口味排序儲存失敗" });
  }
};

const fetchCclass = async () => {
  try {
    const res = await apiClient.get("/onlinecclass/get_onlineCclass", {
      params: {
        branch: props.Branch,
      },
    });

    if (res.data?.success) {
      cclassList.value = res.data.data;
      //console.log(cclassList.value);
    } else {
      console.error("❌ 載入類部失敗:", res.data.message);
    }
  } catch (err) {
    console.error("❌ 載入類部時發生錯誤:", err.message);
  } finally {
    $q.loading.hide();
  }
};

const deleteDeptConfirmed = async () => {
  if (!deleteTarget.value?.code) return;

  try {
    const res = await apiClient.post("/onlinecclass/delete_Cclass", {
      branch: props.Branch,
      code: deleteTarget.value.code,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "類部已刪除" });
      editDialog.value = false;
      deleteDialog.value = false;
      await fetchCclass(); // 重新載入
    } else {
      throw new Error(res.data.message || "刪除失敗");
    }
  } catch (err) {
    console.error("❌ 刪除錯誤:", err);
    $q.notify({ type: "negative", message: "刪除失敗" });
  }
};

const fetchTspec = async () => {
  try {
    const res = await apiClient.get("/onlinecclass/get_TspecList", {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      allTspaceList.value = res.data.data;
    }
  } catch (err) {
    console.error("❌ 載入口味失敗", err);
  }
};

const deleteTaste = async (group, index) => {
  const taste = group.tastes[index];

  try {
    const res = await apiClient.post("/onlinecclass/delete_Taste", {
      branch: props.Branch,
      code: group.code,
      name: taste.name,
      price: taste.price ?? 0,
    });

    if (res.data.success) {
      // 從陣列中移除該項目
      group.tastes.splice(index, 1);

      // 重新排序剩餘項目
      group.tastes.forEach((item, idx) => {
        item.seq = String((idx % 30) + 1).padStart(2, "0");
        item.cno = Math.floor(idx / 30) + 1;
      });

      // 保存更新後的排序
      if (group.tastes.length > 0) {
        await apiClient.post("/onlinecclass/save_TasteSort", {
          branch: props.Branch,
          code: group.code,
          tastes: group.tastes.map((item) => ({
            name: item.name,
            price: item.price,
            seq: item.seq,
            cno: item.cno,
          })),
        });
      }

      $q.notify({ type: "positive", message: "口味已刪除" });
    } else {
      throw new Error(res.data.message || "刪除失敗");
    }
  } catch (err) {
    console.error("❌ 刪除失敗", err);
    $q.notify({ type: "negative", message: "刪除失敗" });
  } finally {
  }
};

const deleteTspec = async (item) => {
  try {
    await apiClient.post("/onlinecclass/delete_Tspec", {
      branch: props.Branch,
      name: item.value,
    });

    // 從本地移除
    allTspaceList.value = allTspaceList.value.filter(
      (i) => i.value !== item.value
    );
    tspaceList.value = tspaceList.value.filter((i) => i.value !== item.value);

    $q.notify({ type: "positive", message: "刪除成功" });
  } catch (err) {
    console.error("❌ 刪除 Tspace 失敗", err);
    $q.notify({ type: "negative", message: "刪除失敗" });
  }
};

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await fetchCclass();
  await fetchTspec();
});
</script>
