const sql = require("mssql");
const dbConfig = require("../config/db");

const getMonthlySales = async (req, res) => {
  try {
    const { cod_cust, month } = req.query;
    if (!month) return res.status(400).json({ error: "缺少 month 參數" });

    await sql.connect(dbConfig);
    const request = new sql.Request();
    request.input("month", sql.NVarChar, month);

    if (cod_cust) {
      request.input("cod_cust", sql.NVarChar, cod_cust);
    }

    let query = `
      SELECT 
        FORMAT(CAST(s.Ndate AS DATE), 'MM/dd') AS date,
        SUM(s.Amount) AS amount
      FROM sales s
    `;

    if (!cod_cust) {
      query += `
        INNER JOIN branch b ON s.cod_cust = b.cod_cust
        WHERE FORMAT(CAST(s.Ndate AS DATE), 'yyyy-MM') = @month
          AND b.invest = 0
      `;
    } else {
      query += `
        WHERE s.cod_cust = @cod_cust
          AND FORMAT(CAST(s.Ndate AS DATE), 'yyyy-MM') = @month
      `;
    }

    query += `
      GROUP BY CAST(s.Ndate AS DATE)
      ORDER BY CAST(s.Ndate AS DATE)
    `;

    const result = await request.query(query);
    res.json(result.recordset);
  } catch (err) {
    console.error("[getMonthlySales] error:", err);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

const getDailySales = async (req, res) => {
  try {
    const { date } = req.query;
    if (!date) {
      return res.status(400).json({ error: "缺少 date 參數" });
    }

    await sql.connect(dbConfig);
    const request = new sql.Request();
    request.input("date", sql.NVarChar, date); // 格式：2025-05-27

    // 查詢每日營收
    const query = `
      SELECT
        s.cod_cust,
        b.cod_name AS name,
        SUM(s.Amount) AS amount,
        b.invest
      FROM sales s
      INNER JOIN branch b ON s.cod_cust = b.cod_cust
      WHERE CONVERT(date, s.Ndate) = @date
      GROUP BY s.cod_cust, b.cod_name, b.cod_group, b.invest
      ORDER BY b.cod_group
    `;

    const result = await request.query(query);

    // 計算直營合計
    const total = result.recordset
      .filter((r) => r.invest?.trim() === "0")
      .reduce((sum, r) => sum + r.amount, 0);

    // 查詢更新時間
    const updateTimeResult = await sql.query(`
      SELECT detail time
      FROM setting
      WHERE class = '00' AND code = 'Update'
    `);
    const updateTime = updateTimeResult.recordset[0]?.time || null;

    // 回傳結果
    res.json({
      data: result.recordset,
      total,
      updateTime, // ⬅️ 新增欄位
    });
  } catch (err) {
    console.error("[getDailySales] error:", err);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

module.exports = { getMonthlySales, getDailySales };
