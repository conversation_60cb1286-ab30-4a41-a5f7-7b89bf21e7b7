import { boot } from "quasar/wrappers";
import { defineComponent } from "vue";
import { use } from "echarts/core";
import VChart from "vue-echarts";
import { CanvasRenderer } from "echarts/renderers";
import { <PERSON><PERSON><PERSON> } from "echarts/charts";
import { LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from "echarts/components";

use([
  <PERSON><PERSON><PERSON>,
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
]);

export default boot(({ app }) => {
  app.component("v-chart", VChart);
});
