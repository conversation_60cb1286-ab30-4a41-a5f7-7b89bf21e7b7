const { getPoolByBranch, sql } = require("../services/dbPoolManager");

const getVclass = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查 vclass 表是否存在
    const checkVclass = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'vclass'
    `);

    if (checkVclass.recordset.length === 0) {
      console.log("🔧 建立資料表 vclass");
      await pool.request().query(`
        CREATE TABLE vclass (
          TB_ID INT IDENTITY(1,1) PRIMARY KEY,
          code NVARCHAR(10) NOT NULL,
          name NVARCHAR(50),
          prn NVARCHAR(2) DEFAULT '1',
          prn1 NVARCHAR(2),
          ptype NVARCHAR(2) DEFAULT '1',
          prncn NVARCHAR(50),
          prnc NVARCHAR(2) DEFAULT '2',
          pno NVARCHAR(2),
          pseq NVARCHAR(2),
          pname2 NVARCHAR(50)
        )
      `);
    }

    // 檢查 vclass_net 表是否存在
    const checkVclassNet = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'vclass_net'
    `);

    if (checkVclassNet.recordset.length === 0) {
      console.log("🔧 建立資料表 vclass_net");
      await pool.request().query(`
        CREATE TABLE vclass_net (
          code NVARCHAR(10) NOT NULL,
          ip NVARCHAR(15),
          PRIMARY KEY (code)
        )
      `);
    }

    const result = await pool.request().query(`
      SELECT 
        v.TB_ID, 
        v.code, 
        v.name, 
        v.prn, 
        v.prn1, 
        v.ptype, 
        v.prncn, 
        v.prnc, 
        v.pno, 
        v.pseq, 
        v.pname2,
        vn.ip
      FROM vclass v
      LEFT JOIN vclass_net vn ON v.code = vn.code
      ORDER BY v.code
    `);

    // 處理資料，確保所有欄位都有值
    const processedData = result.recordset.map((row) => ({
      TB_ID: row.TB_ID || null,
      code: row.code || "",
      name: row.name || "",
      prn: row.prn || "1",
      prn1: row.prn1 || null,
      ptype: row.ptype || "1",
      prncn: row.prncn || "",
      prnc: row.prnc || "2",
      pno: row.pno || "",
      pseq: row.pseq || "",
      pname2: row.pname2 || null,
      ip: row.ip || "",
    }));

    return res.json({ success: true, data: processedData });
  } catch (err) {
    console.error("❌ getVclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const createVclass = async (req, res) => {
  const { branch } = req.query;
  const { code, name, prn, ptype, prncn, prnc, pno, ip } = req.body;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  if (!code || !name || !prn || !ptype || !prncn || !prnc || !pno) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查主機編號是否已存在
    const checkResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("SELECT code FROM vclass WHERE code = @code");

    if (checkResult.recordset.length > 0) {
      return res
        .status(400)
        .json({ success: false, message: "主機編號已存在" });
    }

    // 新增主機資料
    const insertResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.VarChar, name)
      .input("prn", sql.VarChar, prn)
      .input("ptype", sql.VarChar, ptype)
      .input("prncn", sql.VarChar, prncn)
      .input("prnc", sql.VarChar, prnc)
      .input("pno", sql.VarChar, pno).query(`
        INSERT INTO vclass (code, name, prn, ptype, prncn, prnc, pno)
        VALUES (@code, @name, @prn, @ptype, @prncn, @prnc, @pno)
      `);

    // 如果有 IP 資料，新增到 vclass_net 表
    if (ip) {
      await pool
        .request()
        .input("code", sql.VarChar, code)
        .input("ip", sql.VarChar, ip).query(`
          INSERT INTO vclass_net (code, ip)
          VALUES (@code, @ip)
        `);
    }

    return res.json({ success: true, message: "主機新增成功" });
  } catch (err) {
    console.error("❌ createVclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const updateVclass = async (req, res) => {
  const { branch } = req.query;
  const { code, name, prn, ptype, prncn, prnc, pno, ip } = req.body;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  if (!code || !name || !prn || !ptype || !prncn || !prnc || !pno) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 更新主機資料
    const updateResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.VarChar, name)
      .input("prn", sql.VarChar, prn)
      .input("ptype", sql.VarChar, ptype)
      .input("prncn", sql.VarChar, prncn)
      .input("prnc", sql.VarChar, prnc)
      .input("pno", sql.VarChar, pno).query(`
        UPDATE vclass 
        SET name = @name, prn = @prn, ptype = @ptype, prncn = @prncn, prnc = @prnc, pno = @pno
        WHERE code = @code
      `);

    if (updateResult.rowsAffected[0] === 0) {
      return res.status(404).json({ success: false, message: "主機不存在" });
    }

    // 更新或新增 IP 資料
    if (ip) {
      // 先檢查是否已存在 IP 記錄
      const checkIpResult = await pool
        .request()
        .input("code", sql.VarChar, code)
        .query("SELECT code FROM vclass_net WHERE code = @code");

      if (checkIpResult.recordset.length > 0) {
        // 更新現有記錄
        await pool
          .request()
          .input("code", sql.VarChar, code)
          .input("ip", sql.VarChar, ip)
          .query("UPDATE vclass_net SET ip = @ip WHERE code = @code");
      } else {
        // 新增新記錄
        await pool
          .request()
          .input("code", sql.VarChar, code)
          .input("ip", sql.VarChar, ip)
          .query("INSERT INTO vclass_net (code, ip) VALUES (@code, @ip)");
      }
    }

    return res.json({ success: true, message: "主機更新成功" });
  } catch (err) {
    console.error("❌ updateVclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const deleteVclass = async (req, res) => {
  const { branch } = req.query;
  const { code } = req.params;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少 branch 參數" });
  }

  if (!code) {
    return res.status(400).json({ success: false, message: "缺少主機編號" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 先刪除 vclass_net 表中的相關記錄
    await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM vclass_net WHERE code = @code");

    // 再刪除 vclass 表中的記錄
    const deleteResult = await pool
      .request()
      .input("code", sql.VarChar, code)
      .query("DELETE FROM vclass WHERE code = @code");

    if (deleteResult.rowsAffected[0] === 0) {
      return res.status(404).json({ success: false, message: "主機不存在" });
    }

    return res.json({ success: true, message: "主機刪除成功" });
  } catch (err) {
    console.error("❌ deleteVclass error:", err);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getVclass,
  createVclass,
  updateVclass,
  deleteVclass,
};
