<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題與功能按鈕 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">表單相關設定</div>

          <q-btn flat round size="sm" color="green" @click="formDialog = true">
            <file-text :stroke-width="1" :size="26" />
            <q-tooltip>選擇表單</q-tooltip>
          </q-btn>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />
      <!-- 表單選擇按鈕 -->
      <q-card-section v-if="!selectedForm" class="q-pt-none">
        <q-banner class="bg-blue-1 text-grey-8" rounded>
          <template v-slot:avatar>
            <Info class="text-primary" :stroke-width="1" :size="30" />
          </template>
          請點擊右上角按鈕選擇要管理的表單
        </q-banner>
      </q-card-section>
    </q-card>
    <q-card
      class="q-pa-xs no-shadow q-py-sm"
      style="width: 100%; border-radius: 10px"
      v-if="selectedForm"
    >
      <!-- 表單資訊區塊 -->
      <q-card-section class="q-py-none">
        <div class="row items-center q-py-sm">
          <q-chip
            square
            :color="selectedFormColor || 'grey'"
            text-color="white"
            class="q-mr-sm"
            size="md"
          >
            {{ selectedForm.Type }}
          </q-chip>
          <div class="text-h6 text-secondary">
            {{ selectedFormName }}
          </div>
        </div>

        <div class="row q-gutter-sm">
          <q-input
            v-model="selectedForm.Type"
            label="類型"
            outlined
            dense
            class="col-grow"
            style="width: 100px"
            readonly
          />
          <q-input
            v-model="selectedFormColor"
            label="顏色"
            :color="selectedFormColor"
            outlined
            dense
            class="col-auto"
            style="width: 150px"
          />
        </div>
        <div class="row q-mt-sm">
          <q-input
            v-model="selectedForm.Name"
            label="名稱"
            outlined
            dense
            class="col-12"
          />
        </div>

        <q-separator class="q-mt-md" />

        <!-- 流程步驟標題 - 惰性載入 -->
        <q-expansion-item
          label="流程步驟"
          expand-separator
          class="q-mt-md"
          header-class="text-primary"
          v-model="flowExpanded"
          @update:model-value="onFlowExpand"
          @click="
            () => {
              if (!flowExpanded.value && selectedForm.value) {
                flowExpanded.value = true;
                onFlowExpand(true);
              }
            }
          "
        >
          <template v-slot:header>
            <q-item-section avatar>
              <trending-up-down :stroke-width="1.5" :size="22" />
            </q-item-section>
            <q-item-section>流程步驟</q-item-section>
          </template>

          <!-- 新增步驟按鈕 -->
          <div class="row q-mb-md">
            <q-btn flat dense color="deep-orange" @click="addStep">
              <plus :stroke-width="1" :size="22" class="q-mr-xs" />
              新增步驟
            </q-btn>
          </div>

          <!-- 流程步驟列表 -->
          <div
            v-if="approvalSteps.length === 0"
            class="text-center q-pa-lg bg-grey-2 rounded-borders q-mt-md"
          >
            <q-icon name="info" color="grey" size="2rem" />
            <div class="text-grey q-mt-sm">
              尚未設定流程步驟，請點擊「新增步驟」按鈕
            </div>
          </div>

          <!-- 優化列表渲染 -->
          <q-list v-else separator class="q-mt-md">
            <VueDraggable
              v-model="approvalSteps"
              item-key="step_number"
              tag="div"
              handle=".drag-handle"
              ghost-class="bg-indigo-1"
              delay="200"
              @end="onSortEnd"
            >
              <div
                v-for="(step, index) in approvalSteps"
                :key="step.step_number"
                class="bg-white q-mb-sm"
                style="
                  border: 1px solid #ccc;
                  border-radius: 8px;
                  overflow: hidden;
                "
              >
                <q-expansion-item
                  expand-separator
                  class="q-pa-none"
                  header-class="text-indigo"
                >
                  <template #header>
                    <q-item
                      dense
                      class="q-pa-none q-gutter-none items-center"
                      style="width: 100%; position: relative; padding: 8px 10px"
                    >
                      <!-- 拖曳手把 -->
                      <q-item-section avatar class="drag-handle">
                        <q-icon name="drag_indicator" color="grey-7" />
                      </q-item-section>

                      <!-- 步驟資訊 -->
                      <q-item-section>
                        <div class="text-subtitle2 text-brown">
                          {{ step.remark || "未命名步驟" }}
                        </div>
                        <div class="text-caption text-grey-8">
                          {{ getApproverRoleLabel(step.approver_role) }}
                          <q-icon
                            v-if="step.notify"
                            name="notifications_active"
                            color="indigo"
                            size="16px"
                            class="q-ml-sm"
                          />
                        </div>
                      </q-item-section>

                      <!-- 最右側展開箭頭 -->
                      <q-item-section side />

                      <!-- 編輯icon（右上角） -->

                      <q-btn
                        flat
                        dense
                        color="green"
                        @click.stop="editStep(step)"
                      >
                        <square-pen :stroke-width="1" :size="22" />
                      </q-btn>
                    </q-item>
                  </template>

                  <q-card-section
                    class="q-pa-sm q-pt-none bg-grey-1 text-caption"
                  >
                    <div class="row q-col-gutter-sm q-mb-xs q-pl-md">
                      <!-- 流程名稱 -->
                      <div class="col-12">
                        <q-icon
                          name="label"
                          size="16px"
                          class="q-mr-xs text-primary"
                        />
                        流程名稱：
                        <span class="text-weight-medium">{{
                          step.remark
                        }}</span>
                      </div>
                      <!-- 規則 -->
                      <div class="col-6">
                        <q-icon
                          name="rule"
                          size="16px"
                          class="q-mr-xs text-primary"
                        />
                        規則：
                        <span class="text-weight-medium">{{
                          getApproverRoleLabel(step.approver_role)
                        }}</span>
                      </div>
                      <!-- 指定人員 -->
                      <div
                        class="col-6"
                        v-if="step.approver_role === 'personal'"
                      >
                        <q-icon
                          name="person"
                          size="16px"
                          class="q-mr-xs text-primary"
                        />
                        指定人員：
                        <span class="text-weight-medium">{{
                          getUserName(step.approver_id)
                        }}</span>
                      </div>
                      <!-- 通知 -->
                      <div class="col-6">
                        <q-icon
                          name="notifications"
                          size="16px"
                          class="q-mr-xs text-primary"
                        />
                        通知：
                        <span class="text-weight-medium">{{
                          step.notify ? "是" : "否"
                        }}</span>
                      </div>
                    </div>
                    <!-- 刪除按鈕 右下角 -->
                    <div class="row justify-end q-mt-sm q-pr-sm">
                      <q-btn
                        flat
                        dense
                        color="negative"
                        @click.stop="removeStep(index)"
                      >
                        <trash :stroke-width="1" :size="22" class="q-mr-xs" />
                        刪除
                      </q-btn>
                    </div>
                  </q-card-section>
                </q-expansion-item>
              </div>
            </VueDraggable>
          </q-list>
        </q-expansion-item>

        <!-- 表單對應欄位 - 惰性載入 -->
        <q-expansion-item
          label="對應欄位"
          expand-separator
          class="q-mt-md"
          header-class="text-primary"
          v-model="fieldsExpanded"
          @update:model-value="onFieldsExpand"
          @click="
            () => {
              if (!fieldsExpanded.value && selectedForm.value) {
                fieldsExpanded.value = true;
                onFieldsExpand(true);
              }
            }
          "
        >
          <template v-slot:header>
            <q-item-section avatar>
              <brackets :stroke-width="1.5" :size="22" />
            </q-item-section>
            <q-item-section>對應欄位</q-item-section>
          </template>

          <!-- 加載中狀態 -->
          <div v-if="loadingFields" class="text-center q-pa-md">
            <q-spinner color="primary" size="2em" />
            <div class="q-mt-sm text-grey">載入欄位資訊中...</div>
          </div>

          <div v-else class="q-pa-md">
            <!-- 欄位載入中 -->
            <div v-if="loadingFields" class="q-pa-md">
              <div class="row q-col-gutter-md">
                <div v-for="n in 3" :key="n" class="col-12 col-sm-6 col-md-4">
                  <q-skeleton type="rect" height="80px" class="field-card" />
                </div>
              </div>
            </div>

            <!-- 表單欄位列表 -->
            <div
              v-else-if="!formFields.length"
              class="text-center q-pa-lg bg-grey-2 rounded-borders q-mt-md"
            >
              <q-icon name="info" color="grey" size="2rem" />
              <div class="text-grey q-mt-sm">
                尚未設定表單欄位對應，請點擊「新增欄位」按鈕
              </div>
            </div>

            <!-- 優化表單欄位顯示 -->
            <div v-else class="q-mt-md">
              <div class="row q-col-gutter-md">
                <div
                  v-for="field in formFields"
                  :key="field.field_name"
                  class="col-12 col-sm-6 col-md-4"
                >
                  <q-card class="field-card">
                    <q-card-section class="q-py-sm">
                      <div class="row items-center no-wrap">
                        <div class="col">
                          <div class="text-subtitle2 text-weight-bold">
                            {{ field.display_name }}
                          </div>
                          <div class="text-caption text-grey">
                            {{ field.field_name }}
                          </div>
                        </div>
                        <div class="col-auto">
                          <q-btn
                            flat
                            round
                            icon="delete"
                            color="negative"
                            size="sm"
                            @click="removeField(field.field_name)"
                          />
                        </div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>

            <!-- 新增欄位區塊 -->
            <div class="row items-center q-gutter-sm q-mt-md">
              <div class="col">
                <q-select
                  v-model="newFieldName"
                  :options="predefinedFields"
                  label="欄位"
                  emit-value
                  map-options
                  outlined
                  dense
                  :disable="!selectedForm || !predefinedFields.length"
                  use-input
                  input-debounce="300"
                  clearable
                />
              </div>
              <div class="col">
                <q-input
                  v-model="newDisplayName"
                  label="顯示名稱"
                  outlined
                  dense
                  :disable="!selectedForm || !newFieldName"
                />
              </div>
              <q-btn
                flat
                round
                size="sm"
                @click="addField"
                :disable="!selectedForm || !newFieldName || !newDisplayName"
              >
                <Plus class="text-primary" size="20" />
              </q-btn>
            </div>
          </div>
        </q-expansion-item>

        <!-- 表單可檢視人員設定 - 惰性載入 -->
        <q-expansion-item
          label="授權查詢"
          expand-separator
          class="q-mt-md"
          header-class="text-primary"
          v-model="viewersExpanded"
          @update:model-value="onViewersExpand"
          @click="
            () => {
              if (!viewersExpanded.value && selectedForm.value) {
                viewersExpanded.value = true;
                onViewersExpand(true);
              }
            }
          "
        >
          <template v-slot:header>
            <q-item-section avatar>
              <eye :stroke-width="1.5" :size="22" />
            </q-item-section>
            <q-item-section>授權查詢</q-item-section>
          </template>

          <!-- 加載中狀態 -->
          <div v-if="loadingViewers" class="text-center q-pa-md">
            <q-spinner color="primary" size="2em" />
            <div class="q-mt-sm text-grey">載入授權人員中...</div>
          </div>

          <div v-else class="q-pa-md">
            <!-- 人員載入中 -->
            <div v-if="loadingViewers" class="q-pa-md">
              <div class="row q-col-gutter-sm">
                <div v-for="n in 4" :key="n" class="col-6 col-sm-4 col-md-3">
                  <q-skeleton type="rect" height="60px" class="viewer-card" />
                </div>
              </div>
            </div>

            <!-- 人員選擇區塊 -->
            <div v-else class="row q-mb-md">
              <q-select
                v-model="newViewer"
                :options="filteredViewerOptions"
                label="授權人員"
                outlined
                dense
                class="col"
                :disable="!selectedForm"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <q-avatar color="primary" text-color="white">
                        {{ scope.opt.label.charAt(0) }}
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
                <!-- 載入中提示 -->
                <template v-slot:no-option>
                  <q-item v-if="loadingViewerOptions">
                    <q-item-section class="text-grey">
                      載入人員選項中...
                    </q-item-section>
                  </q-item>
                  <q-item v-else>
                    <q-item-section class="text-grey">
                      無符合的人員
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
              <q-btn
                flat
                round
                @click="addViewer"
                class="q-ml-sm"
                :disable="!selectedForm || !newViewer"
              >
                <Plus class="text-primary" size="20" />
              </q-btn>
            </div>

            <!-- 已選擇人員列表 -->
            <div
              v-if="!loadingViewers && !selectedViewers.length"
              class="text-center q-pa-lg bg-grey-2 rounded-borders q-mt-md"
            >
              <q-icon name="info" color="grey" size="2rem" />
              <div class="text-grey q-mt-sm">
                尚未授權可檢視人員，請選擇人員並點擊新增按鈕
              </div>
            </div>

            <!-- 優化可檢視人員顯示 - 虛擬滾動優化 -->
            <div v-else-if="!loadingViewers" class="q-mt-md">
              <q-virtual-scroll
                v-if="selectedViewersData.length > 10"
                :items="selectedViewersData"
                virtual-scroll-item-size="60"
                style="max-height: 400px"
              >
                <template v-slot="{ item: viewer }">
                  <q-item class="q-py-xs">
                    <q-card class="viewer-card full-width">
                      <q-card-section class="text-center q-py-sm">
                        <div
                          class="text-subtitle2 ellipsis row items-center justify-center"
                          style="position: relative"
                        >
                          <UserRound
                            class="text-primary"
                            style="position: absolute; left: 8px"
                          />
                          {{ viewer.name }}
                        </div>

                        <q-btn
                          flat
                          round
                          icon="close"
                          color="yellow"
                          size="sm"
                          class="absolute-top-right q-mt-xs q-mr-xs"
                          @click="removeViewer(viewer.id)"
                        />
                      </q-card-section>
                    </q-card>
                  </q-item>
                </template>
              </q-virtual-scroll>

              <div v-else class="row q-col-gutter-sm">
                <div
                  v-for="viewer in selectedViewersData"
                  :key="viewer.id"
                  class="col-6 col-sm-4 col-md-3"
                >
                  <q-card class="viewer-card">
                    <q-card-section class="text-center q-py-sm">
                      <div
                        class="text-subtitle2 ellipsis row items-center justify-center"
                        style="position: relative"
                      >
                        <UserRound
                          class="text-primary"
                          size="16"
                          style="position: absolute; left: 8px"
                        />
                        {{ viewer.name }}
                      </div>

                      <q-btn
                        flat
                        round
                        icon="close"
                        color="negative"
                        size="sm"
                        class="absolute-top-right q-mt-xs q-mr-xs"
                        @click="removeViewer(viewer.id)"
                      />
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>
          </div>
        </q-expansion-item>
      </q-card-section>

      <!-- 儲存按鈕 -->
      <q-card-actions v-if="selectedForm" align="right" class="q-pa-md">
        <q-btn
          outline
          unelevated
          color="primary"
          class="text-bold q-px-lg q-py-sm"
          style="
            min-width: 120px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44, 62, 80, 0.08);
          "
          :loading="loading"
          @click="saveApprovalFlow"
        >
          <save :stroke-width="1.5" :size="22" class="q-mr-sm" />

          儲存
        </q-btn>
      </q-card-actions>
    </q-card>

    <!-- 跳出表單選擇對話框 - 優化性能 -->
    <q-dialog v-model="formDialog">
      <q-card style="width: 100%; max-width: 400px; border-radius: 10px">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">選擇表單</div>
        </q-card-section>

        <q-input
          v-if="formOptions.length > 5"
          dense
          outlined
          clearable
          class="q-ma-md"
          v-model="formSearch"
          label="搜尋表單"
        />

        <!-- 載入中狀態 -->
        <div
          v-if="loading && formOptions.length === 0"
          class="q-pa-md flex flex-center"
        >
          <q-spinner color="primary" />
          <div class="q-ml-sm text-grey">載入表單中...</div>
        </div>

        <q-list separator style="max-height: 400px; overflow: auto">
          <q-item
            v-for="form in filteredFormOptions"
            :key="form.Type"
            clickable
            v-ripple
            @click="selectForm(form)"
          >
            <q-item-section avatar>
              <q-icon name="description" :color="form.Color" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ form.Name }}</q-item-label>
              <q-item-label caption>類型: {{ form.Type }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <q-card-actions align="right">
          <q-btn label="取消" color="grey" flat v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 使用者選擇對話框 - 優化搜尋 -->
    <q-dialog v-model="userDialog">
      <q-card style="width: 100%; max-width: 400px; border-radius: 10px">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">選擇使用者</div>
        </q-card-section>

        <q-card-section class="q-pa-sm">
          <q-input
            v-model="userSearchText"
            label="搜尋使用者"
            outlined
            dense
            clearable
            debounce="300"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 使用者載入中 -->
        <div v-if="loadingUsers" class="text-center q-pa-md">
          <q-spinner color="primary" />
          <div class="text-grey q-mt-xs">載入使用者中...</div>
        </div>

        <!-- 使用虛擬滾動提高性能 -->
        <q-virtual-scroll
          v-else
          style="height: 300px"
          :items="filteredUsers"
          separator
          v-slot="{ item: user }"
        >
          <q-item clickable v-ripple @click="selectUser(user)">
            <q-item-section avatar>
              <q-avatar color="primary" text-color="white">
                {{ user.Name ? user.Name.charAt(0) : "?" }}
              </q-avatar>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ user.Name }}</q-item-label>
              <q-item-label caption>ID: {{ user.ID }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-virtual-scroll>

        <q-card-actions align="right">
          <q-btn label="取消" color="grey" flat v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 編輯對話框 -->
    <q-dialog v-model="editDialog">
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">編輯步驟</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-card-section class="q-pt-md">
          <q-input
            v-model="editingStep.remark"
            label="流程名稱"
            dense
            outlined
          />
          <q-select
            v-model="editingStep.approver_role"
            :options="[
              { label: '階層部門主管', value: 'Umanager' },
              { label: '指定部門主管', value: 'Smanager' },
              { label: '部門主管', value: 'manager' },
              { label: '指定人員', value: 'personal' },
            ]"
            label="規則"
            dense
            outlined
            class="q-mt-sm"
            emit-value
            map-options
          />
          <q-select
            v-if="editingStep.approver_role === 'personal'"
            v-model="editingStep.approver_id"
            :options="
              userOptions.map((user) => ({ label: user.Name, value: user.ID }))
            "
            label="指定人員"
            dense
            outlined
            class="q-mt-sm"
            emit-value
            map-options
            option-label="label"
            option-value="value"
          />
          <q-checkbox
            v-model="editingStep.notify"
            label="通知"
            class="q-mt-sm"
          />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn flat label="儲存" color="primary" @click="saveStepEdit" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from "vue";
import apiClient from "../api";
import { useQuasar } from "quasar";
import { VueDraggable } from "vue-draggable-plus";
const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// UI state
const formDialog = ref(false);
const userDialog = ref(false);
const loading = ref(false);
const userSearchText = ref("");
const formSearch = ref(""); // Add form search field

// Loading states
const loadingFields = ref(false);
const loadingViewers = ref(false);
const loadingUsers = ref(false);
const loadingViewerOptions = ref(false);

// Main data
const selectedForm = ref(null);
const selectedFormColor = ref("");
const selectedFormName = ref("");
const selectedStepIndex = ref(null);
const approvalSteps = ref([]);

// Form fields data
const formFields = ref([]);
const predefinedFields = ref([]);
const newFieldName = ref("");
const newDisplayName = ref("");

// Viewers data
const selectedViewers = ref([]);
const viewerOptions = ref([]);
const newViewer = ref(null);

// Options data (moved outside reactivity when possible)
const formOptions = ref([]);
const userOptions = ref([]);

// Expansion items state - avoid loading all data at once
const flowExpanded = ref(false);
const fieldsExpanded = ref(false);
const viewersExpanded = ref(false);

const approverOptions = reactive([
  { label: "階層主管", value: "Umanager" },
  { label: "部門主管", value: "manager" },
  { label: "指定人員", value: "personal" },
]);

// Add filtered form options for search
const filteredFormOptions = computed(() => {
  if (!formSearch.value) {
    return formOptions.value;
  }
  const searchText = formSearch.value.toLowerCase();
  return formOptions.value.filter(
    (form) =>
      form.Name.toLowerCase().includes(searchText) ||
      form.Type.toLowerCase().includes(searchText)
  );
});

// Memoized helper function
const getApproverRoleLabel = (value) => {
  const option = approverOptions.find((opt) => opt.value === value);
  return option ? option.label : value;
};

// Optimized computed properties
const selectedViewersData = computed(() => {
  if (selectedViewers.value.length === 0) return [];

  return selectedViewers.value.map((item) => {
    if (typeof item === "object" && item !== null) {
      return {
        id: item.value,
        name: item.label,
        avatar: typeof item.label === "string" ? item.label.charAt(0) : "?",
      };
    } else {
      const idStr = typeof item === "string" ? item.trim() : String(item);
      const user = viewerOptions.value.find((opt) => {
        const optValue =
          typeof opt.value === "string" ? opt.value.trim() : String(opt.value);
        return optValue === idStr;
      });
      const label = user ? user.label : idStr;
      return {
        id: item,
        name: label,
        avatar: typeof label === "string" ? label.charAt(0) : "?",
      };
    }
  });
});

const filteredUsers = computed(() => {
  if (!userSearchText.value) return userOptions.value;

  const searchText = userSearchText.value.toLowerCase();
  return userOptions.value.filter(
    (user) =>
      user.Name.toLowerCase().includes(searchText) ||
      user.ID.toLowerCase().includes(searchText)
  );
});

const filteredViewerOptions = computed(() => {
  if (selectedViewers.value.length === 0) return viewerOptions.value;

  const selectedIds = new Set(
    selectedViewers.value.map((item) =>
      typeof item === "object" && item !== null
        ? item.value.toString().trim()
        : item.toString().trim()
    )
  );

  return viewerOptions.value.filter(
    (option) => !selectedIds.has(option.value.toString().trim())
  );
});

// Load form types (initial load)
const fetchFmain = async () => {
  try {
    loading.value = true;
    const formsResponse = await apiClient.get(
      `${apiBaseUrl}/forms/get_form_types`
    );
    formOptions.value = formsResponse.data.map((form) => ({
      Name: form.Name.trim(),
      Type: form.Type.trim(),
      Color: form.Color.trim(),
    }));
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "無法載入表單類型，請稍後再試",
    });
  } finally {
    loading.value = false;
  }
};

// Optimized to use Promise.all for parallel requests
const selectForm = async (form) => {
  // 清除之前表單的數據
  formFields.value = [];
  predefinedFields.value = [];
  selectedViewers.value = [];
  approvalSteps.value = [];

  // 切換表單時自動收合所有展開區塊
  flowExpanded.value = false;
  fieldsExpanded.value = false;
  viewersExpanded.value = false;

  try {
    loading.value = true;
    selectedForm.value = form;
    selectedFormName.value = form.Name;
    selectedFormColor.value = form.Color;

    // Only load flow data immediately, other data on-demand
    const response = await apiClient.get(`${apiBaseUrl}/forms/get_form_flows`, {
      params: { form_type: form.Type },
    });

    approvalSteps.value = response.data.map((step) => ({
      step_number: step.Step_number,
      remark: step.Remark.trim(),
      approver_role: step.approver_role.trim(),
      approver_id: step.approver_id ? step.approver_id.trim() : "",
      notify: step.Notify === true || step.Notify === "true",
    }));

    // Pre-fetch users for better UX
    fetchUsers();

    // 預加載欄位數據，以便在展開時可以立即顯示
    setTimeout(() => {
      loadFormFields();
    }, 500);

    // 預加載授權查詢數據，以便在展開時可以立即顯示
    setTimeout(() => {
      loadFormViewers();
    }, 800); // 稍微延後，避免同時發起太多請求
  } catch (error) {
    approvalSteps.value = [];
    formFields.value = [];
    predefinedFields.value = [];
    selectedViewers.value = [];
    $q.notify({
      type: "negative",
      message: "無法載入簽核流程，請稍後再試",
    });
  } finally {
    loading.value = false;
    formDialog.value = false;
  }
};

// Cache users data
let usersCache = null;
const fetchUsers = async () => {
  if (usersCache) return;

  try {
    loadingUsers.value = true;
    loadingViewerOptions.value = true;

    const response = await apiClient.post(
      `${apiBaseUrl}/users/get_enable_users`
    );
    userOptions.value = response.data.map((user) => ({
      Name: user.Name,
      ID: user.ID,
    }));

    viewerOptions.value = response.data.map((user) => ({
      label: user.Name,
      value: user.ID,
    }));

    usersCache = response.data;
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "無法載入使用者資訊，請稍後再試",
    });
  } finally {
    loadingUsers.value = false;
    loadingViewerOptions.value = false;
  }
};

// On-demand data loading functions
const onFlowExpand = async (expanded) => {
  // 無論expanded是什麼值，只要flowExpanded為true，就確保數據已載入
  if (flowExpanded.value) {
    if (!selectedForm.value) {
      $q.notify({
        type: "negative",
        message: "請先選擇表單",
      });
      return;
    }

    // 流程步驟數據已在selectForm中載入，這裡不需要額外操作
  }
};

const onFieldsExpand = async (expanded) => {
  // 無論expanded是什麼值，只要fieldsExpanded為true，就嘗試載入欄位
  if (fieldsExpanded.value) {
    if (!selectedForm.value) {
      $q.notify({
        type: "negative",
        message: "請先選擇表單",
      });
      return;
    }

    // 直接使用 selectedForm.value.Type 作為表單類型
    await loadFormFields();
  }
};

const onViewersExpand = async (expanded) => {
  // 無論expanded是什麼值，只要viewersExpanded為true，就嘗試載入授權查詢
  if (viewersExpanded.value) {
    if (!selectedForm.value) {
      $q.notify({
        type: "negative",
        message: "請先選擇表單",
      });
      return;
    }

    // 直接使用 selectedForm.value.Type 作為表單類型
    await loadFormViewers();
  }
};

const addStep = () => {
  const newStepNumber = approvalSteps.value.length;
  approvalSteps.value.push({
    step_number: newStepNumber,
    remark: "",
    approver_role: "manager",
    approver_id: "-",
    notify: false,
  });
};

const removeStep = (index) => {
  $q.dialog({
    title: "確認刪除",
    message: "確定要刪除此簽核步驟嗎？",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    approvalSteps.value.splice(index, 1);
  });
};

const onSortEnd = () => {
  approvalSteps.value.forEach((step, index) => {
    step.step_number = index;
  });
};

const selectUser = (user) => {
  approvalSteps.value[selectedStepIndex.value].approver_id = user.ID;
  userDialog.value = false;
};

// Optimized fields loading
const loadFormFields = async () => {
  if (!selectedForm.value) {
    $q.notify({
      type: "negative",
      message: "請先選擇表單",
    });
    return;
  }

  try {
    loadingFields.value = true;
    if (!selectedForm.value.Type) {
      $q.notify({
        type: "negative",
        message: "表單類型不存在",
      });
      return;
    }

    const formType = selectedForm.value.Type;

    try {
      // Make requests in parallel
      const [fieldsResponse, predefinedResponse] = await Promise.all([
        apiClient.get(`${apiBaseUrl}/page/get_FormsDetail`, {
          params: { form_type: formType },
        }),
        apiClient.get(`${apiBaseUrl}/page/get_FormsField`, {
          params: { table: `form_${formType}` },
        }),
      ]);

      // Process fields data
      formFields.value =
        fieldsResponse.data?.map((field) => ({
          field_name: field.Field_name,
          display_name: field.Display_name,
        })) || [];

      // Create a Set of existing field names for O(1) lookups
      const formFieldNames = new Set(
        formFields.value.map((field) => field.field_name.trim())
      );

      // Process predefined fields
      predefinedFields.value = predefinedResponse.data
        ? predefinedResponse.data
            .map((field) => ({
              label: field.Field_name,
              value: field.Field_name,
            }))
            .filter((field) => !formFieldNames.has(field.value))
        : [];
    } catch (apiError) {
      $q.notify({
        type: "negative",
        message: `API 請求失敗: ${apiError.message || "未知錯誤"}`,
      });
    }
  } catch (error) {
    formFields.value = [];
    predefinedFields.value = [];
    $q.notify({
      type: "negative",
      message: "無法載入表單欄位，請稍後再試",
    });
  } finally {
    loadingFields.value = false;
  }
};

const addField = () => {
  if (newFieldName.value && newDisplayName.value) {
    formFields.value.push({
      field_name: newFieldName.value,
      display_name: newDisplayName.value,
    });

    predefinedFields.value = predefinedFields.value.filter(
      (field) => field.value !== newFieldName.value
    );

    newFieldName.value = null;
    newDisplayName.value = "";
  }
};

const removeField = (fieldName) => {
  formFields.value = formFields.value.filter(
    (field) => field.field_name !== fieldName
  );

  predefinedFields.value.push({
    label: fieldName,
    value: fieldName,
  });
};

// Optimized viewers loading
const loadFormViewers = async () => {
  if (!selectedForm.value) {
    $q.notify({
      type: "negative",
      message: "請先選擇表單",
    });
    return;
  }

  try {
    loadingViewers.value = true;
    if (!selectedForm.value.Type) {
      $q.notify({
        type: "negative",
        message: "表單類型不存在",
      });
      return;
    }

    // Use cached users if available
    if (!usersCache) {
      await fetchUsers();
    }

    try {
      const viewersResponse = await apiClient.get(
        `${apiBaseUrl}/forms/get_form_viewers`,
        {
          params: { form_type: selectedForm.value.Type },
        }
      );

      if (viewersResponse.data && Array.isArray(viewersResponse.data)) {
        selectedViewers.value = viewersResponse.data.map((viewer) => {
          const id =
            typeof viewer.user_id === "string"
              ? viewer.user_id.trim()
              : viewer.user_id;
          return {
            value: id,
            label: viewer.user_name || id,
          };
        });
      } else {
        selectedViewers.value = [];
      }
    } catch (viewerError) {
      selectedViewers.value = [];
      $q.notify({
        type: "negative",
        message: `讀取授權查詢人員失敗: ${viewerError.message || "未知錯誤"}`,
      });
    }
  } catch (error) {
    selectedViewers.value = [];
    $q.notify({
      type: "negative",
      message: "無法載入可檢視人員，請稍後再試",
    });
  } finally {
    loadingViewers.value = false;
  }
};

const addViewer = () => {
  if (newViewer.value) {
    const newViewerValue =
      typeof newViewer.value === "object"
        ? newViewer.value.value
        : newViewer.value;

    const exists = selectedViewers.value.some((viewer) => {
      const viewerValue = typeof viewer === "object" ? viewer.value : viewer;
      return viewerValue === newViewerValue;
    });

    if (!exists) {
      selectedViewers.value.push(newViewer.value);
      newViewer.value = null;
    }
  }
};

const removeViewer = (viewerId) => {
  selectedViewers.value = selectedViewers.value.filter((viewer) =>
    typeof viewer === "object" ? viewer.value !== viewerId : viewer !== viewerId
  );
};

// Optimized save function
const saveApprovalFlow = async () => {
  if (!selectedForm.value) {
    $q.notify({
      type: "warning",
      message: "請先選擇表單",
    });
    return;
  }

  // Validate steps
  for (let i = 0; i < approvalSteps.value.length; i++) {
    const step = approvalSteps.value[i];
    if (!step.remark) {
      $q.notify({
        type: "warning",
        message: `第 ${i + 1} 步驟的流程名稱不能為空`,
      });
      return;
    }
    if (
      step.approver_role === "personal" &&
      (!step.approver_id || step.approver_id === "-")
    ) {
      $q.notify({
        type: "warning",
        message: `第 ${i + 1} 步驟選擇了指定使用者，但未選擇人員`,
      });
      return;
    }
  }

  try {
    loading.value = true;
    const formType = selectedForm.value.Type;

    // Prepare all payloads first
    const fieldMappingPayload = {
      form_type: formType,
      fields: formFields.value.map((field) => ({
        field_name: field.field_name?.trim(),
        display_name: field.display_name?.trim(),
      })),
    };

    const stepsPayload = approvalSteps.value.map((step, index) => ({
      step_number: step.step_number || index,
      approver_role: step.approver_role,
      approver_id: step.approver_role === "personal" ? step.approver_id : null,
      remark: step.remark,
      notify: step.notify,
    }));

    const fullPayload = {
      form_type: formType,
      form_name: selectedForm.value.Name,
      steps: stepsPayload,
      viewers: selectedViewers.value,
    };

    // Send requests in parallel
    const [formMappingRes, formFlowsRes] = await Promise.all([
      formFields.value.length > 0
        ? apiClient.post(
            `${apiBaseUrl}/page/save_FormMapping`,
            fieldMappingPayload
          )
        : Promise.resolve({ data: { success: true } }),
      apiClient.post(`${apiBaseUrl}/forms/save_form_flows`, fullPayload),
    ]);

    if (formFlowsRes.data.success) {
      $q.notify({
        type: "positive",
        message: "表單設定已更新",
        position: "top",
        timeout: 1500,
      });
    } else {
      $q.notify({
        type: "negative",
        message: `儲存失敗: ${formFlowsRes.data.error || "未知錯誤"}`,
      });
    }
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "儲存失敗，請稍後再試",
    });
  } finally {
    loading.value = false;
  }
};

// 編輯對話框相關狀態
const editDialog = ref(false);
const editingStep = reactive({
  remark: "",
  approver_role: "manager",
  approver_id: "-",
  notify: false,
  step_number: null,
});

const editStep = (step) => {
  editingStep.remark = step.remark;
  editingStep.approver_role = step.approver_role;
  editingStep.approver_id = step.approver_id;
  editingStep.notify = step.notify;
  editingStep.step_number = step.step_number;
  editDialog.value = true;
};

const saveStepEdit = () => {
  if (!editingStep.remark) {
    $q.notify({
      type: "warning",
      message: "流程名稱不能為空",
    });
    return;
  }

  if (editingStep.approver_role === "personal" && !editingStep.approver_id) {
    $q.notify({
      type: "warning",
      message: "指定人員不能為空",
    });
    return;
  }

  const stepIndex = approvalSteps.value.findIndex(
    (step) => step.step_number === editingStep.step_number
  );

  if (stepIndex === -1) {
    $q.notify({
      type: "negative",
      message: "無法找到步驟進行編輯",
    });
    return;
  }

  // 更新本地數據
  approvalSteps.value[stepIndex] = {
    ...approvalSteps.value[stepIndex],
    remark: editingStep.remark,
    approver_role: editingStep.approver_role,
    approver_id:
      editingStep.approver_role === "personal" ? editingStep.approver_id : null,
    notify: editingStep.notify,
  };

  editDialog.value = false;
};

const getUserName = (userId) => {
  const user = userOptions.value.find((u) => u.ID === userId);
  return user ? user.Name : userId;
};

onMounted(async () => {
  await fetchFmain();
});
</script>

<style scoped>
/* 移除空的規則集 */
.step-card {
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.step-card:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

/* 自定義分隔線樣式 */
.custom-separator {
  height: 0.5px;
  margin-bottom: 16px;
}

/* 表單欄位卡片樣式 */
.field-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  border-left: 4px solid #1976d2;
  will-change: transform, box-shadow;
}

.field-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 可檢視人員卡片樣式 */
.viewer-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  will-change: transform, box-shadow;
}

.viewer-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 提高渲染效率 */
.q-item,
.q-card {
  contain: content;
}

.q-virtual-scroll__content {
  contain: strict;
}

/* 增加層級獨立性 */
.q-expansion-item,
.q-list {
  contain: layout;
}

/* 移動設備優化 */
@media (max-width: 599px) {
  .q-card-section {
    padding: 12px !important;
  }

  .q-gutter-sm > * {
    margin: 4px !important;
  }

  .q-btn {
    min-height: 32px;
    padding: 0 10px;
  }
}

/* 移除標題底線 */
.title-section {
  border-bottom: none;
}
</style>
