const sql = require("mssql");
const dbConfig = require("../config/db");

let poolPromise;

const getPool = async () => {
  if (!poolPromise) {
    poolPromise = sql.connect(dbConfig); // 全域只建立一次
  }
  return poolPromise;
};

const getBranchIP = async (cod_cust) => {
  if (!cod_cust) throw new Error("❌ 缺少 cod_cust");

  const pool = await getPool(); // 使用共用池

  const result = await pool
    .request()
    .input("cod_cust", sql.VarChar, cod_cust)
    .query(`SELECT ip FROM branch WHERE cod_cust = @cod_cust`);

  if (result.recordset.length === 0) {
    throw new Error(`❌ 找不到門市代號：${cod_cust}`);
  }

  return result.recordset[0].ip;
};

module.exports = { getBranchIP };
