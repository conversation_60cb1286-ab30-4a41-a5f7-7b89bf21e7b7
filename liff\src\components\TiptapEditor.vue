<template>
  <div class="tiptap-editor">
    <div v-if="editor" class="toolbar">
      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="format_bold"
          :color="editor.isActive('bold') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleBold().run()"
        >
          <q-tooltip>粗體</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="format_italic"
          :color="editor.isActive('italic') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleItalic().run()"
        >
          <q-tooltip>斜體</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="format_underline"
          :color="editor.isActive('underline') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleUnderline().run()"
        >
          <q-tooltip>底線</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="strikethrough_s"
          :color="editor.isActive('strike') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleStrike().run()"
        >
          <q-tooltip>刪除線</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <!-- 字體大小選擇器 -->
      <q-btn-group flat>
        <q-btn flat round dense icon="format_size" color="grey-7">
          <q-tooltip>字體大小</q-tooltip>
          <q-menu>
            <q-list style="min-width: 100px">
              <q-item
                v-for="size in fontSizes"
                :key="size.value"
                clickable
                v-close-popup
                @click="setFontSize(size.value)"
              >
                <q-item-section>
                  <div :style="{ fontSize: size.value }">{{ size.label }}</div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <!-- 顏色選擇器 -->
      <q-btn-group flat>
        <q-btn flat round dense icon="format_color_text" color="grey-7">
          <q-tooltip>文字顏色</q-tooltip>
          <q-menu>
            <div class="row no-wrap q-pa-sm">
              <div
                v-for="color in textColors"
                :key="color.value"
                class="color-box cursor-pointer q-ma-xs"
                :style="{ backgroundColor: color.value }"
                @click="setTextColor(color.value)"
              ></div>
            </div>
          </q-menu>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="format_list_bulleted"
          :color="editor.isActive('bulletList') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleBulletList().run()"
        >
          <q-tooltip>項目符號</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="format_list_numbered"
          :color="editor.isActive('orderedList') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleOrderedList().run()"
        >
          <q-tooltip>編號清單</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="format_align_left"
          :color="editor.isActive({ textAlign: 'left' }) ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().setTextAlign('left').run()"
        >
          <q-tooltip>靠左對齊</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="format_align_center"
          :color="
            editor.isActive({ textAlign: 'center' }) ? 'primary' : 'grey-7'
          "
          @click="editor.chain().focus().setTextAlign('center').run()"
        >
          <q-tooltip>置中對齊</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="format_align_right"
          :color="
            editor.isActive({ textAlign: 'right' }) ? 'primary' : 'grey-7'
          "
          @click="editor.chain().focus().setTextAlign('right').run()"
        >
          <q-tooltip>靠右對齊</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="format_quote"
          :color="editor.isActive('blockquote') ? 'primary' : 'grey-7'"
          @click="editor.chain().focus().toggleBlockquote().run()"
        >
          <q-tooltip>引用</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="horizontal_rule"
          color="grey-7"
          @click="editor.chain().focus().setHorizontalRule().run()"
        >
          <q-tooltip>分隔線</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <!-- 圖片上傳按鈕 -->
      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="image"
          color="grey-7"
          @click="openImageDialog"
        >
          <q-tooltip>插入圖片</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="undo"
          color="grey-7"
          @click="editor.chain().focus().undo().run()"
        >
          <q-tooltip>復原</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="redo"
          color="grey-7"
          @click="editor.chain().focus().redo().run()"
        >
          <q-tooltip>重做</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="link"
          :color="editor.isActive('link') ? 'primary' : 'grey-7'"
          @click="setLink"
        >
          <q-tooltip>插入連結</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          dense
          icon="link_off"
          color="grey-7"
          @click="editor.chain().focus().unsetLink().run()"
        >
          <q-tooltip>移除連結</q-tooltip>
        </q-btn>
      </q-btn-group>

      <q-separator vertical />

      <q-btn-group flat>
        <q-btn
          flat
          round
          dense
          icon="format_clear"
          color="grey-7"
          @click="editor.chain().focus().clearNodes().unsetAllMarks().run()"
        >
          <q-tooltip>清除格式</q-tooltip>
        </q-btn>
      </q-btn-group>
    </div>

    <div class="editor-content">
      <editor-content :editor="editor" />
    </div>

    <!-- 連結輸入對話框 -->
    <q-dialog v-model="linkDialog.show">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">插入連結</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="linkDialog.url"
            label="URL"
            outlined
            dense
            placeholder="https://example.com"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            flat
            label="取消"
            color="grey"
            @click="linkDialog.show = false"
          />
          <q-btn unelevated label="確定" color="primary" @click="confirmLink" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 圖片上傳對話框 -->
    <q-dialog v-model="imageDialog.show">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">插入圖片</div>
        </q-card-section>

        <q-card-section>
          <q-tabs
            v-model="imageDialog.tab"
            dense
            class="text-grey"
            active-color="primary"
            indicator-color="primary"
            align="justify"
            narrow-indicator
          >
            <q-tab name="url" label="圖片網址" />
            <q-tab name="upload" label="上傳圖片" />
          </q-tabs>

          <q-tab-panels v-model="imageDialog.tab" animated>
            <q-tab-panel name="url">
              <q-input
                v-model="imageDialog.url"
                label="圖片 URL"
                outlined
                dense
                placeholder="https://example.com/image.jpg"
              />
            </q-tab-panel>

            <q-tab-panel name="upload">
              <q-file
                v-model="imageDialog.file"
                label="選擇圖片"
                outlined
                dense
                accept="image/*"
                @update:model-value="previewImage"
              >
                <template v-slot:prepend>
                  <q-icon name="attach_file" />
                </template>
              </q-file>

              <div v-if="imageDialog.preview" class="q-mt-md">
                <p class="text-caption">預覽：</p>
                <img
                  :src="imageDialog.preview"
                  style="max-width: 100%; max-height: 200px"
                />
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            flat
            label="取消"
            color="grey"
            @click="imageDialog.show = false"
          />
          <q-btn
            unelevated
            label="插入"
            color="primary"
            @click="confirmImage"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import { useEditor, EditorContent } from "@tiptap/vue-3";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import { TextStyle } from "@tiptap/extension-text-style";
import { Color } from "@tiptap/extension-color";
import { Extension } from "@tiptap/core";
import { FontFamily } from "@tiptap/extension-font-family";
import Typography from "@tiptap/extension-typography";
import Image from "@tiptap/extension-image";
import { useQuasar } from "quasar";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "";

// 自定義圖片擴展，添加刪除事件和可調整大小功能
const CustomImage = Image.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      width: {
        default: null,
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }
          return {
            width: attributes.width,
            style: `width: ${attributes.width}px;`,
          };
        },
      },
      height: {
        default: null,
        renderHTML: (attributes) => {
          if (!attributes.height) {
            return {};
          }
          return {
            height: attributes.height,
            style: `height: ${attributes.height}px;`,
          };
        },
      },
    };
  },
  addNodeView() {
    return ({ editor, node, getPos }) => {
      // 創建一個容器
      const dom = document.createElement("div");
      dom.classList.add("resizable-image-wrapper");
      dom.style.position = "relative";
      dom.style.display = "inline-block";
      dom.style.border = "none"; // 無邊框
      dom.style.padding = "0"; // 無內邊距
      dom.style.margin = "10px 0";

      const img = document.createElement("img");
      img.src = node.attrs.src;
      if (node.attrs.alt) {
        img.alt = node.attrs.alt;
      }
      if (node.attrs.width) {
        img.style.width = `${node.attrs.width}px`;
      }
      if (node.attrs.height) {
        img.style.height = `${node.attrs.height}px`;
      }

      dom.appendChild(img);

      // 創建調整大小的控制面板
      const controlPanel = document.createElement("div");
      controlPanel.classList.add("image-control-panel");
      controlPanel.style.position = "absolute";
      controlPanel.style.bottom = "10px";
      controlPanel.style.right = "10px";
      controlPanel.style.backgroundColor = "rgba(52, 152, 219, 0.8)";
      controlPanel.style.padding = "5px";
      controlPanel.style.borderRadius = "20px";
      controlPanel.style.display = "none"; // 初始時隱藏
      controlPanel.style.alignItems = "center";
      controlPanel.style.zIndex = "1000";
      controlPanel.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.3)";

      // 創建刪除按鈕
      const deleteBtn = document.createElement("button");
      deleteBtn.innerHTML = "×";
      deleteBtn.title = "刪除圖片";
      deleteBtn.style.position = "absolute";
      deleteBtn.style.top = "5px";
      deleteBtn.style.right = "5px";
      deleteBtn.style.backgroundColor = "rgba(231, 76, 60, 0.9)";
      deleteBtn.style.border = "none";
      deleteBtn.style.color = "white";
      deleteBtn.style.width = "24px";
      deleteBtn.style.height = "24px";
      deleteBtn.style.borderRadius = "50%";
      deleteBtn.style.cursor = "pointer";
      deleteBtn.style.fontSize = "16px";
      deleteBtn.style.fontWeight = "bold";
      deleteBtn.style.display = "none"; // 初始時隱藏
      deleteBtn.style.zIndex = "1001";
      deleteBtn.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.3)";
      deleteBtn.style.transition = "background-color 0.2s";

      // 添加縮小按鈕
      const decreaseBtn = document.createElement("button");
      decreaseBtn.innerHTML = "−";
      decreaseBtn.title = "縮小圖片";
      decreaseBtn.style.backgroundColor = "transparent";
      decreaseBtn.style.border = "none";
      decreaseBtn.style.color = "white";
      decreaseBtn.style.width = "28px";
      decreaseBtn.style.height = "28px";
      decreaseBtn.style.borderRadius = "50%";
      decreaseBtn.style.margin = "0 3px";
      decreaseBtn.style.cursor = "pointer";
      decreaseBtn.style.fontSize = "18px";
      decreaseBtn.style.fontWeight = "bold";
      decreaseBtn.style.display = "flex";
      decreaseBtn.style.justifyContent = "center";
      decreaseBtn.style.alignItems = "center";
      decreaseBtn.style.transition = "background-color 0.2s";

      // 添加尺寸顯示
      const sizeDisplay = document.createElement("span");
      sizeDisplay.style.color = "white";
      sizeDisplay.style.margin = "0 5px";
      sizeDisplay.style.fontSize = "12px";
      sizeDisplay.style.minWidth = "60px";
      sizeDisplay.style.textAlign = "center";

      // 添加放大按鈕
      const increaseBtn = document.createElement("button");
      increaseBtn.innerHTML = "+";
      increaseBtn.title = "放大圖片";
      increaseBtn.style.backgroundColor = "transparent";
      increaseBtn.style.border = "none";
      increaseBtn.style.color = "white";
      increaseBtn.style.width = "28px";
      increaseBtn.style.height = "28px";
      increaseBtn.style.borderRadius = "50%";
      increaseBtn.style.margin = "0 3px";
      increaseBtn.style.cursor = "pointer";
      increaseBtn.style.fontSize = "18px";
      increaseBtn.style.fontWeight = "bold";
      increaseBtn.style.display = "flex";
      increaseBtn.style.justifyContent = "center";
      increaseBtn.style.alignItems = "center";
      increaseBtn.style.transition = "background-color 0.2s";

      // 添加到控制面板
      controlPanel.appendChild(decreaseBtn);
      controlPanel.appendChild(sizeDisplay);
      controlPanel.appendChild(increaseBtn);

      // 添加控制面板到容器
      dom.appendChild(controlPanel);

      // 添加刪除按鈕到容器
      dom.appendChild(deleteBtn);

      // 更新尺寸顯示
      const updateSizeDisplay = () => {
        const width = img.offsetWidth;
        const height = img.offsetHeight;
        sizeDisplay.textContent = `${width}×${height}`;
      };

      // 初始更新尺寸顯示
      setTimeout(updateSizeDisplay, 0);

      // 鼠標懸停時顯示控制面板
      const showControls = () => {
        controlPanel.style.display = "flex"; // 顯示控制面板
        deleteBtn.style.display = "block"; // 顯示刪除按鈕
        updateSizeDisplay(); // 更新尺寸顯示
      };

      // 鼠標離開時隱藏控制面板
      const hideControls = () => {
        // 檢查是否正在操作控制面板
        if (!isResizing) {
          controlPanel.style.display = "none"; // 隱藏控制面板
          deleteBtn.style.display = "none"; // 隱藏刪除按鈕
        }
      };

      // 用於跟踪是否正在調整大小
      let isResizing = false;

      // 添加鼠標事件
      dom.addEventListener("mouseenter", showControls);
      dom.addEventListener("mouseleave", hideControls);

      // 縮小按鈕鼠標懸停效果
      decreaseBtn.addEventListener("mouseenter", () => {
        decreaseBtn.style.backgroundColor = "rgba(255, 255, 255, 0.2)";
      });
      decreaseBtn.addEventListener("mouseleave", () => {
        decreaseBtn.style.backgroundColor = "transparent";
      });

      // 放大按鈕鼠標懸停效果
      increaseBtn.addEventListener("mouseenter", () => {
        increaseBtn.style.backgroundColor = "rgba(255, 255, 255, 0.2)";
      });
      increaseBtn.addEventListener("mouseleave", () => {
        increaseBtn.style.backgroundColor = "transparent";
      });

      // 縮小按鈕點擊事件
      decreaseBtn.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 設置為正在調整大小狀態
        isResizing = true;

        // 獲取當前尺寸
        const currentWidth = img.offsetWidth;
        const currentHeight = img.offsetHeight;

        // 計算新尺寸 (縮小10%)
        const newWidth = Math.max(50, Math.round(currentWidth * 0.9));
        const aspectRatio = currentWidth / currentHeight;
        const newHeight = Math.round(newWidth / aspectRatio);

        // 更新圖片大小
        img.style.width = `${newWidth}px`;
        img.style.height = `${newHeight}px`;

        // 更新節點屬性
        if (typeof getPos === "function") {
          editor.view.dispatch(
            editor.view.state.tr.setNodeMarkup(getPos(), undefined, {
              ...node.attrs,
              width: newWidth,
              height: newHeight,
            })
          );
        }

        // 更新尺寸顯示
        updateSizeDisplay();

        // 保持控制面板可見
        controlPanel.style.display = "flex";
        deleteBtn.style.display = "block";

        // 重置調整大小狀態
        setTimeout(() => {
          isResizing = false;
        }, 100);
      });

      // 刪除按鈕點擊事件
      deleteBtn.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 確認刪除
        if (confirm("確定要刪除這張圖片嗎？")) {
          // 刪除節點
          if (typeof getPos === "function") {
            editor.view.dispatch(
              editor.view.state.tr.delete(getPos(), getPos() + node.nodeSize)
            );
          }
        }
      });

      // 刪除按鈕鼠標懸停效果
      deleteBtn.addEventListener("mouseenter", () => {
        deleteBtn.style.backgroundColor = "rgba(231, 76, 60, 1)";
      });
      deleteBtn.addEventListener("mouseleave", () => {
        deleteBtn.style.backgroundColor = "rgba(231, 76, 60, 0.9)";
      });

      // 放大按鈕點擊事件
      increaseBtn.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 設置為正在調整大小狀態
        isResizing = true;

        // 獲取當前尺寸
        const currentWidth = img.offsetWidth;
        const currentHeight = img.offsetHeight;

        // 計算新尺寸 (放大10%)
        const newWidth = Math.round(currentWidth * 1.1);
        const aspectRatio = currentWidth / currentHeight;
        const newHeight = Math.round(newWidth / aspectRatio);

        // 更新圖片大小
        img.style.width = `${newWidth}px`;
        img.style.height = `${newHeight}px`;

        // 更新節點屬性
        if (typeof getPos === "function") {
          editor.view.dispatch(
            editor.view.state.tr.setNodeMarkup(getPos(), undefined, {
              ...node.attrs,
              width: newWidth,
              height: newHeight,
            })
          );
        }

        // 更新尺寸顯示
        updateSizeDisplay();

        // 保持控制面板可見
        controlPanel.style.display = "flex";
        deleteBtn.style.display = "block";

        // 重置調整大小狀態
        setTimeout(() => {
          isResizing = false;
        }, 100);
      });

      // 監聽 DOM 元素的刪除
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === "childList" && mutation.removedNodes.length) {
            for (const removedNode of mutation.removedNodes) {
              if (removedNode === dom) {
                // 圖片被刪除，發出事件
                emit("image-deleted", node.attrs.src);
                observer.disconnect();
                // 移除事件監聽
                dom.removeEventListener("mouseenter", showControls);
                dom.removeEventListener("mouseleave", hideControls);
              }
            }
          }
        });
      });

      // 開始觀察 - 確保父節點存在
      setTimeout(() => {
        if (dom && dom.parentNode) {
          observer.observe(dom.parentNode, { childList: true, subtree: true });
        }
      }, 100);

      return {
        dom,
        destroy() {
          observer.disconnect();
          // 移除事件監聽
          dom.removeEventListener("mouseenter", showControls);
          dom.removeEventListener("mouseleave", hideControls);
        },
      };
    };
  },
});

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "開始輸入內容...",
  },
  minHeight: {
    type: String,
    default: "200px",
  },
});

const emit = defineEmits(["update:modelValue", "image-deleted"]);

// 連結對話框
const linkDialog = ref({
  show: false,
  url: "",
});

// 圖片對話框
const imageDialog = ref({
  show: false,
  tab: "url",
  url: "",
  file: null,
  preview: null,
});

// 文字顏色選項
const textColors = [
  { label: "黑色", value: "#000000" },
  { label: "紅色", value: "#ff0000" },
  { label: "綠色", value: "#008000" },
  { label: "藍色", value: "#0000ff" },
  { label: "黃色", value: "#ffff00" },
  { label: "紫色", value: "#800080" },
  { label: "橙色", value: "#ffa500" },
  { label: "粉紅色", value: "#ffc0cb" },
  { label: "灰色", value: "#808080" },
  { label: "棕色", value: "#a52a2a" },
  { label: "青色", value: "#00ffff" },
  { label: "深紅色", value: "#8b0000" },
];

// 字體大小選項
const fontSizes = [
  { label: "極小", value: "0.75em" },
  { label: "較小", value: "0.875em" },
  { label: "一般", value: "1em" },
  { label: "較大", value: "1.25em" },
  { label: "更大", value: "1.5em" },
  { label: "超大", value: "2em" },
  { label: "巨大", value: "3em" },
];

// 自定義字體大小擴展
const FontSize = Extension.create({
  name: "fontSize",

  addOptions() {
    return {
      types: ["textStyle"],
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontSize: {
            default: null,
            parseHTML: (element) => element.style.fontSize.replace(/['"]/g, ""),
            renderHTML: (attributes) => {
              if (!attributes.fontSize) {
                return {};
              }

              return {
                style: `font-size: ${attributes.fontSize}`,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setFontSize:
        (fontSize) =>
        ({ chain }) => {
          return chain().setMark("textStyle", { fontSize }).run();
        },
      unsetFontSize:
        () =>
        ({ chain }) => {
          return chain()
            .setMark("textStyle", { fontSize: null })
            .removeEmptyTextStyle()
            .run();
        },
    };
  },
});

// 根據背景色計算對比色（黑或白）
const getContrastColor = (hexColor) => {
  // 將十六進制顏色轉換為RGB
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);

  // 計算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  // 如果亮度大於 125，返回黑色，否則返回白色
  return brightness > 125 ? "#000000" : "#ffffff";
};

const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit.configure({
      // 配置 StarterKit 中的擴展
      heading: {
        levels: [1, 2, 3, 4, 5, 6],
      },
    }),
    TextAlign.configure({
      types: ["heading", "paragraph"],
    }),
    TextStyle,
    Color,
    FontFamily,
    Typography,
    FontSize,
    CustomImage.configure({
      inline: true,
      allowBase64: true,
    }),
  ],
  editorProps: {
    attributes: {
      class:
        "prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none",
    },
  },
  onUpdate: ({ editor }) => {
    emit("update:modelValue", editor.getHTML());
  },
});

// 監聽 modelValue 變化
watch(
  () => props.modelValue,
  (newValue) => {
    if (editor.value && newValue !== editor.value.getHTML()) {
      editor.value.commands.setContent(newValue, false);
    }
  }
);

// 檢測刪除的圖片
const detectRemovedImages = (oldContent, newContent) => {
  // 提取舊內容中的所有圖片URL
  const oldImgRegex = /<img[^>]+src="([^">]+)"/g;
  const oldUrls = new Set();
  let match;

  while ((match = oldImgRegex.exec(oldContent)) !== null) {
    oldUrls.add(match[1]);
  }

  // 提取新內容中的所有圖片URL
  const newImgRegex = /<img[^>]+src="([^">]+)"/g;
  const newUrls = new Set();

  while ((match = newImgRegex.exec(newContent)) !== null) {
    newUrls.add(match[1]);
  }

  // 找出在舊內容中存在但在新內容中不存在的URL
  const removedUrls = [...oldUrls].filter((url) => !newUrls.has(url));

  // 對每個被刪除的URL發出事件
  removedUrls.forEach((url) => {
    // 處理所有類型的圖片URL
    if (url.startsWith("data:") || url.includes("/uploads/")) {
      emit("image-deleted", url);
    }
  });
};

// 保存上一次的內容
let previousContent = props.modelValue;

// 監聽編輯器內容變化
watch(
  () => editor.value?.getHTML(),
  (newContent) => {
    if (newContent && previousContent) {
      detectRemovedImages(previousContent, newContent);
    }
    previousContent = newContent;
  }
);

// 設置文字顏色
const setTextColor = (color) => {
  if (!editor.value) return;
  editor.value.chain().focus().setColor(color).run();
};

// 設置字體大小
const setFontSize = (size) => {
  if (!editor.value) return;
  editor.value.chain().focus().setFontSize(size).run();
};

// 開啟圖片對話框
const openImageDialog = () => {
  imageDialog.value = {
    show: true,
    tab: "url",
    url: "",
    file: null,
    preview: null,
  };
};

// 圖片預覽
const previewImage = () => {
  const file = imageDialog.value.file;
  if (!file) {
    imageDialog.value.preview = null;
    return;
  }

  const reader = new FileReader();
  reader.onload = (e) => {
    imageDialog.value.preview = e.target.result;
  };
  reader.readAsDataURL(file);
};

// 確認圖片
const confirmImage = async () => {
  if (!editor.value) return;

  try {
    let imageUrl = "";

    if (imageDialog.value.tab === "url") {
      // 使用 URL
      if (!imageDialog.value.url) {
        $q.notify({
          type: "warning",
          message: "請輸入圖片網址",
        });
        return;
      }

      // 直接使用外部 URL
      imageUrl = imageDialog.value.url;
    } else {
      // 上傳圖片
      if (!imageDialog.value.file) {
        $q.notify({
          type: "warning",
          message: "請選擇圖片",
        });
        return;
      }

      // 檢查圖片大小，限制為5MB
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (imageDialog.value.file.size > maxSize) {
        $q.notify({
          type: "negative",
          message: "圖片過大，請選擇小於5MB的圖片或使用圖片網址",
        });
        return;
      }

      // 判斷環境 - 開發環境使用Base64，生產環境上傳到伺服器
      const isDevelopment = process.env.NODE_ENV === "development";
      console.log(`當前環境: ${isDevelopment ? "開發環境" : "生產環境"}`);

      if (isDevelopment) {
        // 開發環境：使用Base64
        console.log("開發環境：使用Base64格式插入圖片");
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            // 獲取Base64數據
            imageUrl = e.target.result;
            console.log("Base64圖片準備完成");

            // 插入圖片
            editor.value.chain().focus().setImage({ src: imageUrl }).run();
            console.log("圖片已插入編輯器");

            // 關閉對話框
            imageDialog.value.show = false;
          } catch (error) {
            console.error("插入Base64圖片錯誤:", error);
            $q.notify({
              type: "negative",
              message: "插入圖片失敗",
            });
          }
        };
        reader.onerror = (error) => {
          console.error("讀取文件錯誤:", error);
          $q.notify({
            type: "negative",
            message: "讀取圖片失敗",
          });
        };
        reader.readAsDataURL(imageDialog.value.file);
        return; // 提前返回，因為FileReader是異步的
      } else {
        // 生產環境：上傳到伺服器
        console.log("生產環境：上傳圖片到伺服器");
        try {
          const formData = new FormData();
          formData.append("file", imageDialog.value.file);

          const apiUrl = "/api/btin/upload-image";
          const response = await fetch(apiUrl, {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            throw new Error(
              `上傳失敗: ${response.status} ${response.statusText}`
            );
          }

          const result = await response.json();
          if (!result.success) {
            throw new Error(result.message || "上傳圖片失敗");
          }

          // 使用相對路徑，這樣會自動匹配當前頁面的協議（http或https）
          imageUrl = result.filePath;
          console.log("圖片已上傳，URL:", imageUrl);
        } catch (error) {
          console.error("上傳圖片錯誤:", error);
          $q.notify({
            type: "negative",
            message: `上傳圖片失敗: ${error.message}`,
          });
          return;
        }
      }
    }

    // 插入圖片（URL模式或生產環境上傳後）
    editor.value.chain().focus().setImage({ src: imageUrl }).run();
    console.log("圖片已插入編輯器");

    // 關閉對話框
    imageDialog.value.show = false;
  } catch (error) {
    console.error("插入圖片錯誤:", error);
    $q.notify({
      type: "negative",
      message: "插入圖片失敗",
    });
  }
};

// 壓縮圖片函數
const compressImage = (file, maxSize) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        const canvas = document.createElement("canvas");
        let width = img.width;
        let height = img.height;

        // 計算壓縮比例
        let quality = 0.8; // 初始壓縮品質
        const maxWidth = 1200; // 最大寬度

        // 如果圖片寬度大於最大寬度，按比例縮小
        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width);
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, width, height);

        // 嘗試壓縮，最多壓縮3次
        const tryCompress = (attemptQuality, attempt = 1) => {
          const dataUrl = canvas.toDataURL("image/jpeg", attemptQuality);

          // 估算base64大小
          const base64Size = Math.round(
            (dataUrl.length - "data:image/jpeg;base64,".length) * 0.75
          );

          if (base64Size > maxSize && attempt < 3) {
            // 如果還是太大，繼續壓縮
            tryCompress(attemptQuality * 0.7, attempt + 1);
          } else {
            if (base64Size > maxSize) {
              reject(new Error("圖片太大，無法壓縮到要求大小"));
            } else {
              resolve(dataUrl);
            }
          }
        };

        // 開始壓縮
        tryCompress(quality);
      };
      img.onerror = () => {
        reject(new Error("圖片載入失敗"));
      };
    };
    reader.onerror = () => {
      reject(new Error("圖片讀取失敗"));
    };
  });
};

// 設置連結
const setLink = () => {
  if (!editor.value) return;
  const previousUrl = editor.value.getAttributes("link").href;
  linkDialog.value.url = previousUrl;
  linkDialog.value.show = true;
};

// 確認連結
const confirmLink = () => {
  if (!editor.value) return;

  if (linkDialog.value.url) {
    editor.value
      .chain()
      .focus()
      .extendMarkRange("link")
      .setLink({ href: linkDialog.value.url })
      .run();
  } else {
    editor.value.chain().focus().extendMarkRange("link").unsetLink().run();
  }
  linkDialog.value.show = false;
  linkDialog.value.url = "";
};

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy();
  }
});
</script>

<style scoped>
.tiptap-editor {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
  gap: 4px;
  flex-wrap: wrap;
}

.editor-content {
  min-height: v-bind(minHeight);
  padding: 16px;
  background-color: white;
}

.editor-content :deep(.ProseMirror) {
  outline: none;
  min-height: v-bind(minHeight);
}

.editor-content :deep(.ProseMirror p) {
  margin: 0.5em 0;
}

.editor-content :deep(.ProseMirror h1),
.editor-content :deep(.ProseMirror h2),
.editor-content :deep(.ProseMirror h3),
.editor-content :deep(.ProseMirror h4),
.editor-content :deep(.ProseMirror h5),
.editor-content :deep(.ProseMirror h6) {
  margin: 1em 0 0.5em 0;
}

.editor-content :deep(.ProseMirror ul),
.editor-content :deep(.ProseMirror ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.editor-content :deep(.ProseMirror blockquote) {
  border-left: 3px solid #ddd;
  margin: 0.5em 0;
  padding-left: 1em;
  color: #666;
}

.editor-content :deep(.ProseMirror hr) {
  border: none;
  border-top: 1px solid #ddd;
  margin: 1em 0;
}

.editor-content :deep(.ProseMirror a) {
  color: #1976d2;
  text-decoration: underline;
}

.editor-content :deep(.ProseMirror a:hover) {
  color: #1565c0;
}

.editor-content :deep(.ProseMirror img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 0.5em 0;
}

.editor-content :deep(.resizable-image-wrapper) {
  display: inline-block;
  position: relative;
  margin: 0.5em 0;
}

.editor-content :deep(.ProseMirror .is-editor-empty:first-child::before) {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* 顏色選擇器樣式 */
.color-box {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #ddd;
  transition: transform 0.2s;
}

.color-box:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 圖片縮放控制點樣式 */
.resizable-image-wrapper {
  position: relative;
  display: inline-block;
  cursor: default;
  user-select: none;
  padding: 0;
  margin: 10px 0;
}

.resizable-image-wrapper img {
  display: block;
  max-width: 100%;
}

.image-control-panel {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(52, 152, 219, 0.8);
  padding: 5px 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: opacity 0.2s;
}

.image-control-panel button {
  background-color: transparent;
  border: none;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin: 0 3px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s;
}

.image-control-panel button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.image-control-panel span {
  color: white;
  margin: 0 5px;
  font-size: 12px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}
</style>
