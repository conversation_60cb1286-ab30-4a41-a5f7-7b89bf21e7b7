<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題卡片 -->
    <q-card class="no-shadow" style="border-radius: 12px; overflow: hidden">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">休假統計表</div>
          <div class="row items-center">
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              @click="showDetailDialog = true"
              class="q-mr-xs"
            >
              <info :stroke-width="1" :size="20" />
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_left"
              @click="adjustMonth(-1)"
              color="primary"
            />
            <q-input
              v-model="selectedMonth"
              readonly
              dense
              outlined
              class="month-selector"
              input-class="text-center"
              style="width: 110px"
            />
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_right"
              @click="adjustMonth(1)"
              color="primary"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 主內容 -->
    <q-card
      class="no-shadow q-mt-sm"
      style="border-radius: 12px; overflow: hidden"
    >
      <!-- 載入中 -->
      <div v-if="loading" class="q-pa-xl">
        <div class="fancy-loader">
          <svg class="loader-svg" viewBox="0 0 100 100">
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">假別資訊載入中...</div>
        </div>
      </div>

      <!-- 查無資料 -->
      <q-card-section v-else-if="error" class="empty-state-container">
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">{{ error || "查無假別資訊" }}</div>
          <div class="empty-subtext">請聯絡人資部門</div>
        </div>
      </q-card-section>

      <!-- 假別內容 -->
      <div v-else>
        <!-- 基本資訊卡片 -->
        <q-card-section class="q-pt-md q-pb-sm">
          <div class="row q-col-gutter-sm info-cards-container">
            <div class="col-6 col-md-3">
              <div class="stats-card person-card">
                <div class="stats-icon">
                  <user-round :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">姓名</div>
                  <div class="person-value">
                    {{ userInfo.name || "載入中..." }}
                  </div>
                </div>
                <!-- 部門主管圖標 -->
                <template v-if="selectedStaffId">
                  <eye :stroke-width="1" :size="16" class="q-mr-xs" />
                </template>
                <q-btn
                  v-if="isManager"
                  flat
                  dense
                  round
                  icon="supervisor_account"
                  color="teal"
                  class="q-ml-xs"
                  @click="showStaffSelector = true"
                  size="sm"
                >
                </q-btn>
              </div>
            </div>
            <div class="col-6 col-md-3">
              <div class="stats-card month-card">
                <div class="stats-icon">
                  <calendar-clock :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">到職日期</div>
                  <div class="month-value">
                    {{ formatDate(userInfo.arriveDate) || "載入中..." }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3">
              <div class="stats-card salary-card">
                <div class="stats-icon">
                  <calendar :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">年資</div>
                  <div class="salary-value">
                    {{ userInfo.yearsOfService || "0" }} 年
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3">
              <div class="stats-card type-card">
                <div class="stats-icon">
                  <briefcase :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">員工類別</div>
                  <div class="type-value">
                    {{ userInfo.employeeType || "載入中..." }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <q-separator inset />

        <!-- 詳細假別統計 -->
        <q-card-section>
          <div class="text-primary q-mb-md flex items-center justify-between">
            <div class="flex items-center">
              <calendar-days :stroke-width="1" :size="20" class="q-mr-sm" />
              詳細假別統計
            </div>
          </div>

          <!-- 特別休假詳細統計 -->
          <div class="leave-stats-card q-mb-lg">
            <div class="leave-stats-header special-leave">
              <div class="leave-stats-title">
                <calendar-plus :stroke-width="1.5" :size="20" class="q-mr-sm" />
                特別休假統計
              </div>
              <q-badge color="primary" class="leave-stats-badge">
                {{ specialLeaveBalance }} 小時
              </q-badge>
            </div>
            <div class="leave-stats-content">
              <div class="leave-stats-grid">
                <div class="leave-stats-item">
                  <div class="leave-stats-label">去年結算剩餘時數</div>
                  <div class="leave-stats-value positive">
                    {{ vacationInfo.lastYearSpecialLeaveHours }} 小時
                  </div>
                </div>
                <div class="leave-stats-item">
                  <div class="leave-stats-label">
                    年度特別休假
                    <q-tooltip v-if="!isAnnualLeaveAvailable">
                      {{ formatDate(nextAnniversaryDate) }} 後可使用
                    </q-tooltip>
                  </div>
                  <div
                    class="leave-stats-value"
                    :class="isAnnualLeaveAvailable ? 'positive' : 'disabled'"
                  >
                    {{ vacationInfo.annualSpecialLeaveHours }} 小時
                  </div>
                </div>
                <div class="leave-stats-item">
                  <div class="leave-stats-label">本年度已休特休時數</div>
                  <div class="leave-stats-value negative">
                    {{ vacationInfo.usedSpecialLeaveHours }} 小時
                  </div>
                </div>
                <div class="leave-stats-item">
                  <div class="leave-stats-label">本月使用特休時數</div>
                  <div class="leave-stats-value warning">
                    {{ vacationInfo.monthlySpecialLeaveHours }} 小時
                  </div>
                </div>
              </div>
              <div class="leave-stats-footer">
                <div class="leave-stats-total-label">特別休假剩餘時數</div>
                <div
                  class="leave-stats-total-value"
                  :class="specialLeaveBalance > 0 ? 'positive' : 'negative'"
                >
                  <span class="text-primary">
                    {{ specialLeaveBalance }} 小時</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 加班補休詳細統計 -->
          <div class="leave-stats-card">
            <div class="leave-stats-header overtime-leave">
              <div class="leave-stats-title">
                <calendar-check
                  :stroke-width="1.5"
                  :size="20"
                  class="q-mr-sm"
                />
                加班補休統計
              </div>
              <q-badge
                :color="overtimeLeaveBalance > 0 ? '#26A69A' : '#80CBC4'"
                class="leave-stats-badge bg-teal"
              >
                {{ overtimeLeaveBalance }} 小時
              </q-badge>
            </div>
            <div class="leave-stats-content">
              <div class="leave-stats-grid">
                <div class="leave-stats-item">
                  <div class="leave-stats-label">去年結算加班補休時數</div>
                  <div class="leave-stats-value teal">
                    {{ vacationInfo.lastYearOvertimeLeaveHours }} 小時
                  </div>
                </div>
                <div class="leave-stats-item">
                  <div class="leave-stats-label">本年度加班補休時數</div>
                  <div class="leave-stats-value teal">
                    {{ vacationInfo.currentYearOvertimeHours }} 小時
                  </div>
                </div>
                <div class="leave-stats-item">
                  <div class="leave-stats-label">本年度使用補休時數</div>
                  <div class="leave-stats-value negative">
                    {{ vacationInfo.usedOvertimeLeaveHours }} 小時
                  </div>
                </div>
                <div class="leave-stats-item">
                  <div class="leave-stats-label">本月使用補休時數</div>
                  <div class="leave-stats-value warning">
                    {{ vacationInfo.monthlyOvertimeLeaveHours }} 小時
                  </div>
                </div>
              </div>
              <div class="leave-stats-footer">
                <div class="leave-stats-total-label">剩餘補休時數</div>
                <div class="leave-stats-total-value teal">
                  {{ overtimeLeaveBalance }} 小時
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </div>
    </q-card>

    <!-- 統一記錄列表 -->
    <div
      v-if="!loading && (leaveUsage.length > 0 || overtimeRecords.length > 0)"
      class="records-section q-mt-md"
    >
      <div class="leave-stats-card">
        <div class="leave-stats-header records-header">
          <div class="leave-stats-title">
            <calendar-days :stroke-width="1.5" :size="20" class="q-mr-sm" />
            請假與加班記錄
          </div>
        </div>
        <div class="leave-stats-content">
          <div class="records-list">
            <!-- 請假記錄 -->
            <div
              v-for="record in leaveUsage.slice(0, 10)"
              :key="`leave-${record.id}`"
              class="record-item"
            >
              <div class="record-date">
                <calendar :stroke-width="1.5" :size="16" class="q-mr-xs" />
                {{ formatDate(record.lv_date) }}
              </div>
              <div class="record-type">
                <q-badge color="primary" outline>{{
                  record.leave_type_name
                }}</q-badge>
              </div>
              <div class="record-hours">
                <span class="text-weight-medium">{{
                  Number(record.eff_hours).toFixed(1)
                }}</span>
                小時
              </div>
            </div>
            <!-- 加班記錄 -->
            <div
              v-for="record in overtimeRecords.slice(0, 10)"
              :key="`overtime-${record.id}`"
              class="record-item"
            >
              <div class="record-date">
                <calendar :stroke-width="1.5" :size="16" class="q-mr-xs" />
                {{ formatDate(record.ot_date) }}
              </div>
              <div class="record-type">
                <q-badge color="teal" outline>
                  {{
                    record.use_type === "leave"
                      ? "加班補休"
                      : record.use_type === "paid" && record.leave_hours > 0
                      ? "加班費+加班補休"
                      : "加班費"
                  }}
                </q-badge>
              </div>
              <div class="record-hours">
                <span class="text-weight-medium">{{
                  Number(record.comp_hours).toFixed(1)
                }}</span>
                小時
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <q-dialog
      v-model="showDetailDialog"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <q-card class="detail-dialog-card">
        <!-- 標題區域 -->
        <q-card-section class="bg-primary text-white detail-header">
          <div class="row items-center no-wrap">
            <div class="col-auto">
              <calendar :stroke-width="1.5" :size="28" />
            </div>
            <div class="col q-ml-md">
              <div class="text-h6">假別明細</div>
              <div class="text-caption">各類型假別與可休天數</div>
            </div>
            <div class="col-auto">
              <q-btn flat round dense icon="close" color="white" v-close-popup>
                <q-tooltip>關閉</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>

        <!-- 搜尋欄位 -->
        <q-card-section class="q-pa-md q-pb-none">
          <q-input
            v-model="leaveTypeSearchTerm"
            outlined
            dense
            placeholder="搜尋假別"
            clearable
            class="search-input"
          >
            <template v-slot:prepend>
              <search :stroke-width="1.5" :size="18" class="text-grey-7" />
            </template>
            <template v-slot:append v-if="leaveTypeSearchTerm">
              <q-icon
                name="close"
                @click.stop="leaveTypeSearchTerm = ''"
                class="cursor-pointer"
              />
            </template>
          </q-input>
        </q-card-section>

        <!-- 內容區域 -->
        <q-card-section class="q-pa-md q-pt-sm detail-content">
          <div
            v-if="filteredLeaveTypesSearch.length === 0"
            class="empty-search-result"
          >
            <search
              :stroke-width="1.5"
              :size="36"
              class="q-mb-sm text-grey-6"
            />
            <div class="text-subtitle1 text-grey-8">找不到相符的假別</div>
            <div class="text-caption text-grey-7">請嘗試其他搜尋關鍵字</div>
          </div>

          <div v-else class="leave-types-list">
            <div
              v-for="(leaveType, index) in filteredLeaveTypesSearch"
              :key="leaveType.typeId"
              class="leave-type-card"
              :class="{
                'leave-type-card-expanded': expandedTypeId === leaveType.typeId,
              }"
            >
              <div
                class="leave-type-header"
                @click="toggleDetails(leaveType.typeId)"
              >
                <div class="leave-type-name">
                  <calendar
                    :stroke-width="1.5"
                    :size="20"
                    class="q-mr-sm"
                    :color="getLeaveTypeColor(index)"
                  />
                  {{ cleanTypeName(leaveType.typeName) }}
                </div>
                <div class="leave-type-days">
                  <template
                    v-if="
                      leaveType.hasSubItems &&
                      Object.keys(leaveType.subItems || {}).length > 0
                    "
                  >
                    <div class="view-details-button">查看明細</div>
                    <chevron-down
                      v-if="expandedTypeId !== leaveType.typeId"
                      :stroke-width="1.5"
                      :size="18"
                      class="q-ml-xs"
                    />
                    <chevron-up
                      v-else
                      :stroke-width="1.5"
                      :size="18"
                      class="q-ml-xs"
                    />
                  </template>
                  <template v-else>
                    <div
                      class="leave-days-value"
                      :class="getDaysBadgeClass(leaveType.maxDays)"
                    >
                      {{ leaveType.maxDays }} 天
                    </div>
                  </template>
                </div>
              </div>

              <!-- 展開的子項目 -->
              <div
                v-if="
                  expandedTypeId === leaveType.typeId && leaveType.hasSubItems
                "
                class="leave-type-details"
              >
                <div
                  v-for="sub in Object.values(leaveType.subItems || {})"
                  :key="sub.key"
                  class="leave-subtype-item"
                >
                  <div class="leave-subtype-row">
                    <div class="leave-subtype-name">
                      <minus
                        :stroke-width="1.5"
                        :size="16"
                        class="q-mr-sm text-grey-6"
                      />
                      {{ cleanTypeName(sub.name) }}
                    </div>
                    <div class="leave-subtype-days">
                      <div
                        class="leave-days-value leave-subtype-days-value"
                        :class="getDaysBadgeClass(sub.days)"
                      >
                        {{ sub.days }} 天
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- 底部按鈕 -->
        <q-separator />
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="關閉" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 部門員工選擇對話框 - 現代化設計 -->
    <q-dialog
      v-model="showStaffSelector"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <q-card style="min-width: 350px; max-width: 600px; border-radius: 12px">
        <!-- 對話框標題列 -->
        <q-card-section class="bg-teal-1" style="padding: 15px 20px">
          <div class="row items-center no-wrap">
            <div class="col-auto">
              <q-avatar
                color="teal"
                text-color="white"
                icon="groups"
                size="md"
              />
            </div>
            <div class="col q-ml-md">
              <div class="text-h6 text-teal-9">選擇成員</div>
              <div class="text-caption text-grey-7">查看部屬員工的休假資訊</div>
            </div>
            <div class="col-auto">
              <q-btn round flat icon="close" color="grey-7" v-close-popup>
                <q-tooltip>關閉</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>

        <!-- 部門選擇器 -->
        <q-card-section v-if="departments.length > 1" class="q-pt-md">
          <q-select
            v-model="selectedDepartment"
            :options="departments"
            option-value="Id"
            option-label="Name"
            label="部門"
            outlined
            dense
            map-options
            emit-value
            popup-content-class="staff-select-popup"
            class="staff-selector"
          >
            <template v-slot:prepend>
              <q-icon name="apartment" color="teal" />
            </template>
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  沒有符合的部門
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </q-card-section>

        <!-- 搜尋欄位 -->
        <q-card-section class="q-py-sm">
          <q-input
            v-model="staffSearchTerm"
            outlined
            dense
            placeholder="搜尋員工"
            clearable
          >
            <template v-slot:prepend>
              <q-icon name="search" color="grey" />
            </template>
            <template v-slot:append v-if="staffSearchTerm">
              <q-icon
                name="close"
                color="grey"
                @click.stop="staffSearchTerm = ''"
                class="cursor-pointer"
              />
            </template>
          </q-input>
        </q-card-section>

        <!-- 員工列表 -->
        <q-card-section
          style="max-height: 40vh; overflow-y: auto"
          class="q-py-none"
        >
          <div class="text-caption text-grey q-my-sm q-ml-sm">
            員工 ({{ filteredStaffList.length }})
          </div>
          <q-list separator padding>
            <!-- 自己的選項 -->
            <q-item
              clickable
              v-ripple
              @click="selectStaff(props.Userid)"
              :active="selectedStaffId === null"
              active-class="staff-active-item"
            >
              <q-item-section avatar>
                <q-avatar color="teal" text-color="white">
                  {{ userInfo.name ? userInfo.name.charAt(0) : "U" }}
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ userInfo.name || "載入中..." }}</q-item-label>
                <q-item-label caption>目前登入的使用者</q-item-label>
              </q-item-section>
              <q-item-section side v-if="selectedStaffId === null">
                <q-icon name="check" color="teal" />
              </q-item-section>
            </q-item>

            <!-- 部門員工列表 -->
            <q-item
              v-for="staff in filteredStaffList"
              :key="staff.ID"
              clickable
              v-ripple
              @click="selectStaff(staff.ID)"
              :active="selectedStaffId === staff.ID"
              active-class="staff-active-item"
            >
              <q-item-section avatar>
                <q-avatar color="grey-5" text-color="white">
                  {{ staff.Name ? staff.Name.charAt(0) : "U" }}
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ staff.Name }}</q-item-label>
                <q-item-label caption v-if="staff.Email">
                  <q-icon name="email" size="xs" class="q-mr-xs" />
                  {{ staff.Email }}
                </q-item-label>
              </q-item-section>
              <q-item-section side v-if="selectedStaffId === staff.ID">
                <q-icon name="check" color="teal" />
              </q-item-section>
            </q-item>

            <!-- 無員工時的題示 -->
            <q-item
              v-if="
                filteredStaffList.length === 0 && departmentStaff.length === 0
              "
            >
              <q-item-section>
                <div class="text-center q-py-md text-grey">
                  <q-icon name="groups_off" size="md" />
                  <div class="q-mt-sm">此部門沒有員工</div>
                </div>
              </q-item-section>
            </q-item>

            <!-- 搜尋無結果 -->
            <q-item
              v-else-if="filteredStaffList.length === 0 && staffSearchTerm"
            >
              <q-item-section>
                <div class="text-center q-py-md text-grey">
                  <q-icon name="search_off" size="md" />
                  <div class="q-mt-sm">
                    沒有符合「{{ staffSearchTerm }}」的員工
                  </div>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <!-- 取消與確認按鈕 -->
        <q-card-actions align="right" class="bg-white">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn
            unelevated
            label="確認選擇"
            color="teal"
            :disable="
              selectedStaffId !== null &&
              !departmentStaff.some((s) => s.ID === selectedStaffId)
            "
            @click="confirmStaffSelection"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useQuasar } from "quasar";

const props = defineProps({
  Userid: {
    type: String,
    required: true,
  },
});

// 檢查當前用戶是否為部門主管
const checkIsManager = async () => {
  try {
    const response = await apiClient.get(
      `/leave/check-manager?userId=${props.Userid}`
    );
    isManager.value = response.data.isManager;
    if (isManager.value) {
      departments.value = response.data.departments;
      if (departments.value.length > 0) {
        selectedDepartment.value = departments.value[0].Id;
        await getDepartmentStaff();
      }
    }
  } catch (err) {
    console.error("檢查部門主管狀態失敗:", err);
  }
};

// 獲取部門員工列表
const getDepartmentStaff = async () => {
  if (!selectedDepartment.value) return;

  try {
    const response = await apiClient.get(
      `/leave/department-members?departmentId=${selectedDepartment.value}`
    );
    departmentStaff.value = response.data;
  } catch (err) {
    console.error("獲取部門員工列表失敗:", err);
    $q.notify({
      type: "negative",
      message: "獲取部門員工列表失敗",
    });
  }
};

// 選擇員工
// 在選擇檔式中只選擇並更新UI，不立即載入資訊
// 由確認選擇按鈕觸發資料載入
const selectStaff = (staffId) => {
  if (staffId === props.Userid) {
    selectedStaffId.value = null; // 表示選擇自己
  } else {
    selectedStaffId.value = staffId; // 記錄選擇的員工ID
  }
};

// 確認選擇並載入所選員工的休假資訊
const confirmStaffSelection = async () => {
  if (selectedStaffId.value === null) {
    // 選擇自己
    await fetchLeaveInfo(props.Userid);
    await fetchLeaveUsage(props.Userid);
  } else {
    // 載入選擇員工的休假資訊
    await fetchLeaveInfo(selectedStaffId.value);
    await fetchLeaveUsage(selectedStaffId.value);
  }

  // 關閉選擇器對話框
  showStaffSelector.value = false;
};

const $q = useQuasar();

import apiClient from "../api";
// 響應式數據
const loading = ref(true);
const error = ref(null);
const userInfo = ref({});
const isManager = ref(false);
const departments = ref([]);
const selectedDepartment = ref(null);
const departmentStaff = ref([]);
const selectedStaffId = ref(null);
const showStaffSelector = ref(false);
const staffSearchTerm = ref("");

// 篩選員工列表
const filteredStaffList = computed(() => {
  if (!staffSearchTerm.value) return departmentStaff.value;

  const searchLower = staffSearchTerm.value.toLowerCase();
  return departmentStaff.value.filter(
    (staff) =>
      staff.Name.toLowerCase().includes(searchLower) ||
      (staff.Email && staff.Email.toLowerCase().includes(searchLower))
  );
});
const leaveUsage = ref([]);
const overtimeRecords = ref([]);
const leaveTypes = ref([]); // 保留原始假別類型資料
const recordsLoading = ref(true); // 新增請假與加班記錄的載入狀態
const vacationInfo = ref({
  lastYearSpecialLeaveHours: 0, // 去年度結餘特別休假時數 (sv)
  lastYearOvertimeLeaveHours: 0, // 去年度結餘補休假時數 (cl)
  annualSpecialLeaveHours: 0, // 年度特別休假時數
  usedSpecialLeaveHours: 0, // 本年度已休特休時數
  monthlySpecialLeaveHours: 0, // 本月使用特休時數
  currentYearOvertimeHours: 0, // 本年度加班補休時數
  usedOvertimeLeaveHours: 0, // 本年度使用補休時數
  monthlyOvertimeLeaveHours: 0, // 本月使用補休時數
  isAnnualLeaveAvailable: false, // 年度特休是否可用
});

const usageColumns = [
  {
    name: "leaveDate",
    required: true,
    label: "請假日期",
    align: "left",
    field: "lv_date",
    sortable: true,
  },
  {
    name: "leaveType",
    required: true,
    label: "假別",
    align: "left",
    field: "leave_type_name",
    sortable: true,
  },
  {
    name: "hours",
    required: true,
    label: "時數",
    align: "right",
    field: "eff_hours",
    sortable: true,
  },
  {
    name: "status",
    required: true,
    label: "狀態",
    align: "left",
    field: "status",
    sortable: true,
  },
];

const overtimeColumns = [
  {
    name: "otDate",
    required: true,
    label: "加班日期",
    align: "left",
    field: "ot_date",
    sortable: true,
  },
  {
    name: "hours",
    required: true,
    label: "補休時數",
    align: "right",
    field: "comp_hours",
    sortable: true,
  },
  {
    name: "type",
    required: true,
    label: "類型",
    align: "left",
    field: "use_type",
    sortable: true,
  },
];

// 假別明細表格列定義
const detailColumns = [
  {
    name: "typeName",
    required: true,
    label: "假別名稱",
    align: "left",
    field: (row) => row.typeName,
    sortable: true,
  },
  {
    name: "maxDays",
    required: true,
    label: "可休天數",
    align: "right",
    field: "maxDays",
    sortable: true,
  },
];

const formatDate = (dateString) => {
  if (!dateString) return "";

  const date = new Date(dateString);
  if (isNaN(date)) return dateString;

  return date.toLocaleDateString("zh-TW", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

// 清理假別名稱，移除 PHP 序列化的殘留部分
const cleanTypeName = (name) => {
  if (!name || typeof name !== "string") return "未命名假別";

  // 處理各種可能的 PHP 序列化格式問題
  let cleanedName = name;

  // 特別處理LEAVE_開頭的命名
  if (name.startsWith("LEAVE_")) {
    // 移除LEAVE_前綴
    cleanedName = name.replace(/^LEAVE_/, "");

    // 移除類型編號（如MARRIAGE_TYPE1中的1）
    cleanedName = cleanedName.replace(/_TYPE\d+$/, "");

    // 將下劃線替換為空格，並轉換為首字母大寫形式
    cleanedName = cleanedName
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

    return cleanedName;
  }

  // 移除開頭的引號和結尾的序列化部分
  cleanedName = cleanedName.replace(/^"/, "");

  // 移除可能的 PHP 序列化殘留部分，如 "假別名稱";i:1;s:
  cleanedName = cleanedName.replace(/";i:\d+;s:.*$/, "");

  // 移除其他可能的序列化格式
  cleanedName = cleanedName.replace(/;i:\d+;.*$/, "");
  cleanedName = cleanedName.replace(/;s:\d+:.*$/, "");

  // 移除可能的數字前綴，如 -3
  cleanedName = cleanedName.replace(/^-\d+\s+/, "");

  // 如果名稱變成空字串，返回"未命名假別"
  if (!cleanedName.trim()) {
    return "未命名假別";
  }

  return cleanedName;
};

// 特別休假剩餘時數 = 去年結算剩餘時數 + 年度特別休假 - 本年度已休特休時數
const specialLeaveBalance = computed(() => {
  // 只有當年度特休可用時，才將其計入剩餘時數
  const annualHours = vacationInfo.value.isAnnualLeaveAvailable
    ? vacationInfo.value.annualSpecialLeaveHours
    : 0;

  return (
    vacationInfo.value.lastYearSpecialLeaveHours +
    annualHours -
    vacationInfo.value.usedSpecialLeaveHours
  );
});

// 剩餘補休時數 = 去年結算加班補休時數 + 本年度加班補休時數 - 本年度使用補休時數
const overtimeLeaveBalance = computed(() => {
  return (
    vacationInfo.value.lastYearOvertimeLeaveHours +
    vacationInfo.value.currentYearOvertimeHours -
    vacationInfo.value.usedOvertimeLeaveHours
  );
});

// 計算下一個周年日期
const nextAnniversaryDate = computed(() => {
  if (!userInfo.value.arriveDate) return null;

  const arriveDate = new Date(userInfo.value.arriveDate);
  // 從選擇的月份獲取年份和月份
  const [year, month] = selectedMonth.value.split("-");
  const selectedYear = parseInt(year);
  const selectedMonthValue = parseInt(month) - 1; // JavaScript月份從0開始

  // 獲取到職日期的月和日
  const arriveMonth = arriveDate.getMonth();
  const arriveDay = arriveDate.getDate();

  // 創建選擇年份的周年日期
  const anniversaryDate = new Date(selectedYear, arriveMonth, arriveDay);

  // 創建一個基於選擇月份的日期對象，設置為該月的1號
  const selectedDate = new Date(selectedYear, selectedMonthValue, 1);

  // 如果選擇的月份已經過了當年的周年日期，則使用下一年度的周年日期
  if (selectedDate >= anniversaryDate) {
    return new Date(selectedYear + 1, arriveMonth, arriveDay);
  }

  return anniversaryDate;
});

// 判斷年度特休是否可用
const isAnnualLeaveAvailable = computed(() => {
  return vacationInfo.value.isAnnualLeaveAvailable;
});

// 過濾顯示有子項目或可休天數大於0的假別
const filteredLeaveTypes = computed(() =>
  leaveTypes.value.filter(
    (type) =>
      (type.hasSubItems &&
        type.subItems &&
        Object.keys(type.subItems).length > 0) ||
      (!type.hasSubItems && type.maxDays > 0)
  )
);

const showDetailDialog = ref(false);
const expandedTypeId = ref(null);
const leaveTypeSearchTerm = ref("");

// 根據搜尋條件篩選假別
const filteredLeaveTypesSearch = computed(() => {
  if (!leaveTypeSearchTerm.value) return filteredLeaveTypes.value;

  const searchLower = leaveTypeSearchTerm.value.toLowerCase();
  return filteredLeaveTypes.value.filter((type) => {
    // 搜尋主假別名稱
    if (cleanTypeName(type.typeName).toLowerCase().includes(searchLower)) {
      return true;
    }

    // 搜尋子假別名稱
    if (type.hasSubItems && type.subItems) {
      return Object.values(type.subItems).some((sub) =>
        cleanTypeName(sub.name).toLowerCase().includes(searchLower)
      );
    }

    return false;
  });
});

// 根據天數返回適當的樣式類別
const getDaysBadgeClass = (days) => {
  if (days <= 0) return "leave-days-zero";
  if (days <= 3) return "leave-days-low";
  if (days <= 7) return "leave-days-medium";
  return "leave-days-high";
};

// 根據索引返回不同的圖標顏色，用於區分不同假別
const getLeaveTypeColor = (index) => {
  const colors = [
    "#1976D2", // 藍色
    "#26A69A", // 蒂爾綠
    "#FF7043", // 橙紅色
    "#7E57C2", // 紫色
    "#66BB6A", // 綠色
    "#FFA000", // 琥珀色
    "#42A5F5", // 淺藍色
    "#EC407A", // 粉紅色
    "#5C6BC0", // 靛藍色
  ];
  return colors[index % colors.length];
};

const toggleDetails = (typeId) => {
  expandedTypeId.value = expandedTypeId.value === typeId ? null : typeId;
};

const fetchLeaveInfo = async (targetUserId = null) => {
  loading.value = true;
  error.value = null;
  const userId = targetUserId || props.Userid;

  try {
    const token = localStorage.getItem("jwt_token");
    if (!token) {
      throw new Error("未登入，請先登入");
    }

    // 獲取年度假期資訊
    const response = await apiClient.get(`leave/info`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        userId: userId,
        month: selectedMonth.value, // 使用選擇的月份
      },
    });

    if (response.data.success) {
      userInfo.value = response.data.userInfo;

      // 處理假期資訊
      if (response.data.vacationInfo) {
        // console.log("收到的假期資訊:", response.data.vacationInfo);
        vacationInfo.value = {
          lastYearSpecialLeaveHours:
            response.data.vacationInfo.lastYearSpecialLeaveHours || 0,
          lastYearOvertimeLeaveHours:
            response.data.vacationInfo.lastYearOvertimeLeaveHours || 0,
          annualSpecialLeaveHours:
            response.data.vacationInfo.annualSpecialLeaveHours || 0,
          usedSpecialLeaveHours:
            response.data.vacationInfo.usedSpecialLeaveHours || 0,
          monthlySpecialLeaveHours:
            response.data.vacationInfo.monthlySpecialLeaveHours || 0,
          currentYearOvertimeHours:
            response.data.vacationInfo.currentYearOvertimeHours || 0,
          usedOvertimeLeaveHours:
            response.data.vacationInfo.usedOvertimeLeaveHours || 0,
          monthlyOvertimeLeaveHours:
            response.data.vacationInfo.monthlyOvertimeLeaveHours || 0,
          isAnnualLeaveAvailable:
            response.data.vacationInfo.isAnnualLeaveAvailable || false,
        };
        // console.log("處理後的假期資訊:", vacationInfo.value);
      }

      // 保存原始假別資料
      if (response.data.leaveInfo && response.data.leaveInfo.leaveTypes) {
        leaveTypes.value = [];
        const leaveTypesObj = response.data.leaveInfo.leaveTypes;

        for (const typeId in leaveTypesObj) {
          if (leaveTypesObj.hasOwnProperty(typeId)) {
            const type = leaveTypesObj[typeId];
            const leaveType = {
              typeId: typeId,
              typeKey: typeId,
              typeName: type.name,
              maxDays: type.maxDays || 0,
              usedDays: type.usedDays || 0,
              remainingDays: type.remainingDays || 0,
              hasSubItems: type.hasSubItems || false,
              showDetails: type.showDetails || false,
              subItems: type.subItems || {},
              subTypes: [],
            };

            // 處理子類型
            if (type.hasSubItems && type.subItems) {
              for (const subTypeKey in type.subItems) {
                if (type.subItems.hasOwnProperty(subTypeKey)) {
                  const subType = type.subItems[subTypeKey];
                  leaveType.subTypes.push({
                    key: subTypeKey,
                    name: subType.name || subType.displayName || subTypeKey,
                    days: subType.days || 0,
                  });
                }
              }
            }

            leaveTypes.value.push(leaveType);
          }
        }
      }

      // 獲取請假記錄
      await fetchLeaveUsage();
    } else {
      throw new Error(response.data.message || "無法獲取假別資訊");
    }
  } catch (err) {
    console.error("獲取假別資訊失敗:", err);
    error.value =
      err.response?.data?.message || err.message || "獲取假別資訊失敗";
    $q.notify({
      type: "negative",
      message: error.value,
      position: "top",
      timeout: 3000,
    });
  } finally {
    loading.value = false;
  }
};

const fetchLeaveUsage = async (targetUserId = null) => {
  recordsLoading.value = true; // 開始載入時設為 true
  const userId = targetUserId || props.Userid;

  try {
    const token = localStorage.getItem("jwt_token");
    if (!token) {
      throw new Error("未登入，請先登入");
    }

    // 從選擇的月份中獲取年和月
    const [year, month] = selectedMonth.value.split("-");

    const response = await apiClient.get(`leave/usage`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        userId: userId,
        year: year,
        month: month,
      },
    });

    if (response.data.success) {
      // 儲存使用記錄
      leaveUsage.value = response.data.leaveUsage || [];

      // 儲存加班記錄
      overtimeRecords.value = response.data.overtimeRecords || [];
    }
  } catch (err) {
    console.error("獲取請假記錄失敗:", err);
    // 不顯示錯誤通知，因為這是次要功能
    leaveUsage.value = [];
    overtimeRecords.value = [];
  } finally {
    recordsLoading.value = false; // 完成載入時設為 false
  }
};

const selectedMonth = ref(new Date().toISOString().slice(0, 7));
const adjustMonth = (delta) => {
  // 從當前選擇的月份解析年和月
  let [year, month] = selectedMonth.value.split("-").map(Number);

  // 調整月份 (JavaScript 月份從 0 開始)
  month = month - 1;

  // 加上 delta (1 或 -1)
  month = month + delta;

  // 處理年份變化
  if (month < 0) {
    month = 11; // 12月 (0-based index)
    year--;
  } else if (month > 11) {
    month = 0; // 1月 (0-based index)
    year++;
  }

  // 格式化為 YYYY-MM
  const formattedMonth = month + 1 < 10 ? `0${month + 1}` : `${month + 1}`;
  const newMonthValue = `${year}-${formattedMonth}`;

  // 更新選擇的月份
  selectedMonth.value = newMonthValue;

  // 重新獲取數據
  fetchLeaveInfo();
  fetchLeaveUsage(); // 同時更新請假與加班記錄
};

// 移除 watch，避免重複觸發
// watch(selectedMonth, () => {
//   fetchLeaveInfo();
// });

onMounted(async () => {
  // 先檢查是否為主管，以便UI能及時更新
  await checkIsManager();

  // 載入休假資訊
  fetchLeaveInfo();
  fetchLeaveUsage();
});

// 監聽選擇的部門變化，獲取該部門的員工列表
watch(selectedDepartment, async () => {
  if (selectedDepartment.value) {
    await getDepartmentStaff();
  }
});
</script>

<style>
/* 載入動畫樣式 */
.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}

.loader-message {
  margin-top: 1rem;
  color: #1976d2;
  font-size: 0.9rem;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #757575;
  margin-bottom: 0.5rem;
}

.empty-subtext {
  font-size: 0.9rem;
  color: #9e9e9e;
}

/* 基本資訊卡片樣式優化 */
.info-cards-container {
  margin: 0 -6px;
  padding: 8px 0;
}

@media (max-width: 599px) {
  .info-cards-container {
    margin: 0 -2px;
    padding: 4px 0;
  }

  .info-cards-container > div {
    padding: 0 2px;
  }

  /* 響應式調整 */
  .stats-card {
    padding: 8px 6px 8px 20px;
  }

  .stats-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  .stats-label {
    font-size: 10px;
  }
}

/* 統計卡片樣式 */
.stats-card {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eaeaea;
  transition: all 0.3s ease;
  margin-bottom: 6px;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #d0d0d0;
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 6px;
}

.stats-content {
  flex: 1;
  min-width: 0; /* 確保文字可以正確縮減 */
}

.stats-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stats-value {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.person-value {
  color: #8e24aa;
  font-weight: 600;
}

.month-value {
  color: #ff8f00;
  font-weight: 600;
}

.salary-value {
  color: #388e3c;
  font-weight: 600;
}

.type-value {
  color: #3949ab;
  font-weight: 600;
}

/* 卡片特定樣式 */
.person-card::before {
  background: linear-gradient(180deg, #ce93d8 0%, #8e24aa 100%);
}

.person-card .stats-icon {
  color: #8e24aa;
  background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
}

.month-card::before {
  background: linear-gradient(180deg, #ffcc80 0%, #ff8f00 100%);
}

.month-card .stats-icon {
  color: #ff8f00;
  background: linear-gradient(135deg, #fff8e1 0%, #ffe082 100%);
}

.salary-card::before {
  background: linear-gradient(180deg, #a5d6a7 0%, #388e3c 100%);
}

.salary-card .stats-icon {
  color: #388e3c;
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}

.type-card::before {
  background: linear-gradient(180deg, #9fa8da 0%, #3949ab 100%);
}

.type-card .stats-icon {
  color: #3949ab;
  background: linear-gradient(135deg, #e8eaf6 0%, #c5cae9 100%);
}

/* 卡片特定樣式 */
.total-card::before {
  background-color: #1976d2;
}

.total-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.remaining-card::before {
  background-color: #ff5722; /* Changed to orange-red */
}

.remaining-card .stats-icon {
  color: #ff5722; /* Changed to orange-red */
  background-color: rgba(
    255,
    87,
    34,
    0.1
  ); /* Changed background color to match */
}

/* 表格樣式 */
.leave-table {
  border-radius: 8px;
  overflow: hidden;
}

.table-row:hover {
  background-color: rgba(25, 118, 210, 0.05);
}

.item-name {
  font-weight: 500;
  color: #333;
}

.amount-cell {
  font-size: 0.9rem;
}

.amount-value {
  font-weight: 600;
}

/* 響應式調整 */
@media (max-width: 599px) {
  .info-card-row {
    gap: 4px;
  }

  .info-card {
    padding: 8px 4px 8px 20px;
    min-height: 36px;
    font-size: 0.9rem;
  }

  .info-icon {
    width: 22px;
    height: 22px;
    margin-right: 8px;
    margin-left: 2px;
    font-size: 14px;
  }

  .info-label {
    font-size: 0.65rem;
  }

  .info-value {
    font-size: 0.78rem;
  }

  .stats-card {
    padding: 8px 6px;
  }

  .stats-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  .stats-value {
    font-size: 14px;
    margin-bottom: 0;
  }

  .stats-label {
    font-size: 10px;
  }
}

.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

/* 子類型對話框樣式 */
.leave-subtype-item {
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.leave-subtype-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.leave-days-badge {
  font-size: 0.8rem;
}

/* New leave statistics styles */
.leave-stats-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: white;
  margin-bottom: 16px;
}

.leave-stats-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.leave-stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.leave-stats-header.special-leave {
  background: linear-gradient(135deg, #1976d2, #64b5f6);
  color: white;
}

.leave-stats-header.overtime-leave {
  background: linear-gradient(135deg, #26a69a, #80cbc4);
  color: white;
}

.leave-stats-header.records-header {
  background: linear-gradient(135deg, #ff6b35, #ff8a65);
  color: white;
}

.leave-stats-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 1rem;
}

.leave-stats-badge {
  font-size: 1.1rem;
  padding: 4px 12px;
}

.leave-stats-content {
  padding: 16px;
}

.leave-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.leave-stats-item {
  padding: 8px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.leave-stats-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 4px;
}

.leave-stats-value {
  font-size: 1.1rem;
  font-weight: 500;
}

.leave-stats-value.positive {
  color: #1976d2;
}

.leave-stats-value.negative {
  color: #f44336;
}

.leave-stats-value.warning {
  color: #ff9800;
}

.leave-stats-value.disabled {
  color: #9e9e9e;
}

.leave-stats-value.teal {
  color: #26a69a;
}

.leave-stats-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.leave-stats-total-label {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.leave-stats-total-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.leave-stats-total-value.positive {
  color: #4caf50;
}

.leave-stats-total-value.negative {
  color: #f44336;
}

.leave-stats-total-value.teal {
  color: #26a69a;
}

@media (max-width: 767px) {
  .leave-stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Records list styles */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.records-section {
  padding: 0 16px;
}

.record-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 16px;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #e9ecef;
  transition: all 0.2s ease;
}

.record-item:hover {
  background-color: #e9ecef;
  transform: translateX(2px);
}

.record-date {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #666;
}

.record-type {
  display: flex;
  justify-content: center;
}

.record-hours {
  text-align: right;
  font-size: 0.9rem;
  color: #333;
}

.record-status {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .record-item {
    grid-template-columns: 1fr auto auto;
    gap: 8px;
    padding: 8px;
    font-size: 0.85rem;
  }

  .record-date,
  .record-hours {
    text-align: left;
  }
}
/* 員工選擇器特別樣式 */
.staff-active-item {
  background-color: #e0f2f1 !important; /* 淡綠色背景 */
}

.staff-selector .q-field__native {
  padding-left: 0;
}

.staff-select-popup {
  max-height: 300px;
}

/* 滾動條樣式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a6a6a6;
}
/* 假別明細對話框樣式 */
.detail-dialog-card {
  width: 100%;
  max-width: 600px;
  border-radius: 12px;
  overflow: hidden;
}

.detail-header {
  padding: 16px 20px;
}

.search-input {
  margin-bottom: 8px;
}

.detail-content {
  max-height: 60vh;
  overflow-y: auto;
}

.leave-types-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.leave-type-card {
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.leave-type-card:hover {
  background-color: #e3f2fd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.leave-type-card-expanded {
  background-color: #e3f2fd;
  border-left-color: #1976d2;
}

.leave-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  cursor: pointer;
}

.leave-type-name {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #333;
}

.leave-type-days {
  display: flex;
  align-items: center;
}

.leave-type-details {
  background-color: #ffffff;
  padding: 8px 16px 16px 48px;
  border-top: 1px solid #e0e0e0;
}

.leave-subtype-item {
  margin-top: 12px;
}

.leave-subtype-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leave-subtype-name {
  display: flex;
  align-items: center;
  color: #555;
}

.leave-subtype-days {
  margin-left: 16px;
}

.leave-days-value {
  padding: 6px 10px;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 6px;
  display: inline-block;
  min-width: 60px;
  text-align: center;
}

.leave-days-zero {
  background-color: #f5f5f5;
  color: #9e9e9e;
  border: 1px solid #e0e0e0;
}

.leave-days-low {
  background-color: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffe0b2;
}

.leave-days-medium {
  background-color: #e0f2f1;
  color: #00897b;
  border: 1px solid #b2dfdb;
}

.leave-days-high {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.leave-subtype-days-value {
  font-size: 0.85rem;
  padding: 4px 8px;
  min-width: 50px;
}

.view-details-button {
  font-size: 0.85rem;
  color: #1976d2;
  border: 1px solid #1976d2;
  border-radius: 4px;
  padding: 2px 8px;
  cursor: pointer;
  display: inline-block;
  transition: all 0.3s ease;
}

.view-details-button:hover {
  background-color: #e3f2fd;
}

.empty-search-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
}

@media (max-width: 599px) {
  .detail-dialog-card {
    max-width: 100%;
    margin: 8px;
  }

  .detail-content {
    max-height: 70vh;
  }
}
</style>
