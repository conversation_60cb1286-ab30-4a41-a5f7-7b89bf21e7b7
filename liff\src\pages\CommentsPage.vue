<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <!-- 標題和月份選擇 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">Google評論</div>
          <div class="row items-center">
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_left"
              @click="adjustMonth(-1)"
              color="primary"
              :disable="!props.Branch"
            />
            <q-input
              v-model="selectedMonth"
              readonly
              dense
              outlined
              class="month-selector"
              input-class="text-center"
              style="width: 110px"
            />
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_right"
              @click="adjustMonth(1)"
              color="primary"
              :disable="!props.Branch"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 標籤頁 -->
    <q-card class="q-mt-sm no-shadow" style="width: 100%; border-radius: 10px">
      <q-tabs
        v-model="activeTab"
        dense
        class="text-grey"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
      >
        <q-tab name="stats"><chart-pie :stroke-width="1" :size="26" /></q-tab>
        <q-tab name="details"
          ><list-collapse :stroke-width="1" :size="26"
        /></q-tab>
      </q-tabs>

      <q-separator class="q-mb-md q-mt-md" />

      <q-tab-panels v-model="activeTab" animated>
        <!-- 統計資訊標籤頁 -->
        <q-tab-panel name="stats" class="q-pa-xs">
          <!-- 門市最新評論統計 -->
          <q-card flat bordered class="statistics-card q-mb-sm">
            <q-card-section class="q-pa-sm">
              <div class="text-subtitle2 text-primary q-mb-xs">
                <q-icon name="bar_chart" class="q-mr-xs" />
                最新評論統計
              </div>

              <!-- 統計數據橫向排列 -->
              <div class="row q-col-gutter-sm justify-center items-stretch">
                <!-- 最新評分 -->
                <div class="col-6 col-md-4 flex flex-center">
                  <div class="flex column items-center">
                    <q-icon name="star_rate" color="orange" size="24px" />
                    <div
                      class="stats-value"
                      :class="
                        getScoreTextClass(
                          branchStats?.Comment_Rating?.toFixed(1) || '0.0'
                        )
                      "
                    >
                      {{ branchStats?.Comment_Rating?.toFixed(1) || "0.0" }}
                    </div>
                    <div class="stats-label">最新評分</div>
                  </div>
                </div>

                <!-- 最新評論數 - 點擊跳轉到明細 -->
                <div class="col-6 col-md-4 flex flex-center">
                  <div
                    class="flex column items-center clickable-stats"
                    @click="activeTab = 'details'"
                  >
                    <q-icon name="forum" color="primary" size="24px" />
                    <div class="stats-value text-primary">
                      {{ branchStats?.Comment_Count || 0 }}
                    </div>
                    <div class="stats-label">最新評論數</div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 當月評論統計 -->
          <q-card flat bordered class="statistics-card q-mb-sm">
            <q-card-section class="q-pa-sm">
              <div class="text-subtitle2 text-primary q-mb-xs">
                <q-icon name="calendar_month" class="q-mr-xs" />
                當月評論統計
              </div>

              <!-- 統計數據橫向排列 -->
              <div class="row q-col-gutter-sm justify-center items-stretch">
                <!-- 平均評分 -->
                <div class="col-6 col-md-4 flex flex-center">
                  <div class="flex column items-center">
                    <q-icon name="star" color="deep-orange" size="24px" />
                    <div
                      class="stats-value"
                      :class="getScoreTextClass(averageRating)"
                    >
                      {{ averageRating }}
                    </div>
                    <div class="stats-label">平均評分</div>
                  </div>
                </div>

                <!-- 評論數量 - 點擊跳轉到明細 -->
                <div class="col-6 col-md-4 flex flex-center">
                  <div
                    class="flex column items-center clickable-stats"
                    @click="activeTab = 'details'"
                  >
                    <q-icon name="chat" color="green" size="24px" />
                    <div class="stats-value text-green">
                      {{ filteredReviews.length }}
                    </div>
                    <div class="stats-label">評論數量</div>
                  </div>
                </div>
              </div>

              <!-- 評分分布圓環圖 -->
              <div
                class="row justify-center q-mt-md"
                v-if="filteredReviews.length > 0"
              >
                <q-circular-progress
                  :value="averageRatingPercentage"
                  size="120px"
                  :thickness="0.22"
                  :color="getScoreColor(averageRating)"
                  class="q-ma-sm"
                  show-value
                  font-size="0px"
                  track-color="grey-2"
                >
                  <div class="text-center progress-content">
                    <div
                      class="text-h5 text-weight-bold"
                      :class="getScoreTextClass(averageRating)"
                    >
                      {{ averageRating }}
                    </div>
                    <div class="text-caption">平均評分</div>
                  </div>
                </q-circular-progress>
              </div>

              <!-- 最後更新時間 -->
              <div class="row justify-end q-mt-xs">
                <div class="text-caption text-grey-7">
                  <q-icon name="update" size="12px" class="q-mr-xs" />
                  最後更新：{{
                    branchStats?.Comment_At
                      ? formatDateTime(branchStats.Comment_At)
                      : "無資料"
                  }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-tab-panel>

        <!-- 評論明細標籤頁 -->
        <q-tab-panel name="details" class="q-pa-xs">
          <div class="details-container">
            <!-- 星級篩選按鈕 -->
            <div class="filter-container q-mb-sm">
              <div class="text-subtitle2 text-primary q-mb-xs">
                <q-icon name="filter_list" class="q-mr-xs" />
                評分篩選
              </div>
              <div class="row justify-between q-gutter-xs">
                <q-btn
                  v-for="opt in starOptions"
                  :key="opt.value"
                  outline
                  unelevated
                  :color="starFilter === opt.value ? 'deep-orange' : 'grey-7'"
                  class="col filter-btn"
                  dense
                  @click="starFilter = opt.value"
                >
                  <div class="row items-center justify-center">
                    <q-icon name="star" size="14px" />
                    <span v-if="opt.value !== 'all'" class="q-ml-xs">{{
                      opt.value
                    }}</span>
                  </div>
                </q-btn>
              </div>
              <!-- 分頁控制器 -->
              <div class="row justify-end q-mt-sm">
                <q-pagination
                  v-if="totalPages > 1"
                  v-model="currentPage"
                  :max="totalPages"
                  max-pages="6"
                  boundary-numbers
                  color="deep-orange"
                  size="sm"
                />
              </div>
            </div>

            <q-list separator class="review-list">
              <q-item
                v-for="review in paginatedReviews"
                :key="review.id"
                class="review-item q-py-sm"
              >
                <q-item-section>
                  <div class="row items-center justify-between q-mb-xs">
                    <div class="text-subtitle2 text-weight-bold text-primary">
                      {{ review.author }}
                    </div>
                    <div
                      class="rating-badge"
                      :class="getScoreOutlineClass(review.stars)"
                    >
                      <q-icon name="star" size="14px" class="q-mr-xs" />
                      <div>{{ review.stars.toFixed(1) }}</div>
                    </div>
                  </div>

                  <div class="review-text q-mb-xs">
                    {{ review.text }}
                  </div>
                  <div class="text-caption text-grey-7">
                    {{ review.raw_time }}
                  </div>
                </q-item-section>
              </q-item>

              <q-item v-if="paginatedReviews.length === 0">
                <q-item-section class="text-center text-grey q-py-sm">
                  暫無評論資料
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import apiClient from "../api";
import { DateTime } from "luxon";
import { useQuasar } from "quasar";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const props = defineProps({ Branch: String });

// 標籤頁控制
const activeTab = ref("stats");

const UTCformatDateTime = (dateString) => {
  if (!dateString) return "無日期"; // 避免 `null` 或 `undefined`
  return DateTime.fromISO(dateString, { zone: "utc" }) // 解析為 UTC
    .setZone("Asia/Taipei") // 轉換為台北時間 (UTC+8)
    .toFormat("yyyy-MM-dd"); // 格式化
};

const formatDateTime = (dateString) => {
  if (!dateString) return "無日期";
  return DateTime.fromISO(dateString)
    .setZone("Asia/Taipei")
    .toFormat("yyyy-MM-dd HH:mm");
};

const selectedMonth = ref(DateTime.now().toFormat("yyyy-MM"));
const branchStats = ref(null);

const adjustMonth = (offset) => {
  const newMonth = DateTime.fromFormat(selectedMonth.value, "yyyy-MM").plus({
    months: offset,
  });

  selectedMonth.value = newMonth.toFormat("yyyy-MM");
};

const starFilter = ref("all");
const starOptions = [
  { value: "5" },
  { value: "4" },
  { value: "3" },
  { value: "2" },
  { value: "1" },
  { value: "all" },
];

const reviews = ref([]);

// 獲取門市評論統計資料
const fetchBranchCommentStats = async () => {
  try {
    if (!props.Branch) {
      console.log("❌ 未提供門市代號，無法獲取門市評論統計資料");
      return;
    }

    //console.log(`⏳ 正在獲取門市 ${props.Branch} 的評論統計資料...`);
    const response = await apiClient.get(
      `${apiBaseUrl}/comments/get_branch_comment_stats`,
      { params: { cod_cust: props.Branch } }
    );

    //console.log("✅ 獲取門市評論統計資料成功:", response.data);
    branchStats.value = response.data;
  } catch (error) {
    console.error("❌ 載入門市評論統計資料失敗:", error);
  }
};

const fetchComments = async () => {
  try {
    const response = await apiClient.get(
      `${apiBaseUrl}/comments/get_comments`,
      { params: { cod_cust: props.Branch, month: selectedMonth.value } }
    );

    if (!Array.isArray(response.data)) {
      throw new Error("API 回傳不是陣列");
    }

    reviews.value = response.data.map((item) => ({
      raw_time: UTCformatDateTime(item.Comment_Date),
      author: item.Reviewer,
      stars: item.Rating,
      text: item.Comment_Text,
    }));
  } catch (error) {
    console.error("❌ 載入評論資料失敗:", error);
  }
};

// 篩選當月
const filteredReviews = computed(() =>
  reviews.value.filter((r) => r.raw_time.startsWith(selectedMonth.value))
);

// 再依星數篩選
const filteredAndStarredReviews = computed(() => {
  if (starFilter.value === "all") return filteredReviews.value;
  return filteredReviews.value.filter(
    (r) => String(r.stars) === starFilter.value
  );
});

// 分頁邏輯
const currentPage = ref(1);
const pageSize = 5;

const totalPages = computed(() =>
  Math.ceil(filteredAndStarredReviews.value.length / pageSize)
);

const paginatedReviews = computed(() => {
  const start = (currentPage.value - 1) * pageSize;
  return filteredAndStarredReviews.value.slice(start, start + pageSize);
});

// 計算平均評分百分比 (用於圓環圖)
const averageRatingPercentage = computed(() => {
  const rating = parseFloat(averageRating.value);
  return rating * 20; // 將 5 分制轉換為百分比 (5 => 100%)
});

// 當篩選或月份變動時，重置頁碼
watch([selectedMonth, starFilter], () => {
  fetchComments(); // 重新從 API 抓資料
  currentPage.value = 1;
});

// 只有當月份變動時，才重新獲取評論統計資料
watch(selectedMonth, () => {
  if (selectedMonth.value) {
    fetchBranchCommentStats();
  }
});

// 顏色邏輯
const getScoreColor = (score) => {
  if (score === "0.0") return "grey";
  const s = parseFloat(score);
  if (s < 3) return "red-7";
  if (s < 4) return "orange-6";
  if (s < 4.5) return "primary";
  return "green-6";
};

const getScoreTextClass = (score) => {
  if (score === "0.0") return "text-grey";
  const s = parseFloat(score);
  if (s < 3) return "text-red-7";
  if (s < 4) return "text-orange-6";
  if (s < 4.5) return "text-primary";
  return "text-green-6";
};

const getScoreOutlineClass = (stars) => {
  if (stars < 3) return "text-red-7 border-red-7";
  if (stars < 4) return "text-orange-6 border-orange-6";
  if (stars < 4.5) return "text-primary border-primary";
  return "text-green-6 border-green-6";
};

const averageRating = computed(() => {
  const list = filteredReviews.value;
  if (list.length === 0) return "0.0";
  const total = list.reduce((sum, r) => sum + r.stars, 0);
  return (total / list.length).toFixed(1);
});

onMounted(async () => {
  if (!props.Branch) {
    $q.notify({
      type: "warning",
      message: "請先選擇門市！",
    });
  } else {
    await fetchBranchCommentStats();
    await fetchComments();
  }
});
</script>

<style scoped>
/* 基本樣式 */
.title-stats-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.month-selector {
  transition: all 0.3s ease;
}

.month-selector:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.statistics-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  overflow: hidden;
}

/* 進度圓環容器 */
.progress-container {
  position: relative;
  padding: 10px;
}

/* 進度圓環內容 */
.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

/* 統計標籤 */
.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 統計數值 */
.stats-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
}

/* 可點擊的統計數據 */
.clickable-stats {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
}

.clickable-stats:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.clickable-stats:hover .stats-value {
  text-decoration: underline;
}

/* 篩選按鈕樣式 */
.filter-container {
  padding: 8px 0;
}

.filter-btn {
  border-radius: 8px;
  font-size: 0.8rem;
  padding: 4px 8px;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 評論項目樣式 */
.review-item {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.review-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.rating-badge {
  display: flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 5px;
  border: 1px solid;
  font-size: 14px;
  font-weight: 500;
}

.review-text {
  white-space: pre-line;
  line-height: 1.5;
}

/* 評論明細容器樣式 */
.details-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
}

.filter-container {
  flex: 0 0 auto;
}

.review-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 0;
}
</style>
