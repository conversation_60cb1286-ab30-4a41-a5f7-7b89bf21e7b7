<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="
        width: 100%;
        max-width: 500px;
        overflow: hidden;
        border-radius: 12px;
      "
    >
      <!-- 🔶 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        口味群組管理
      </q-card-section>

      <!-- 🧂 群組清單 -->
      <q-list separator>
        <VueDraggable
          v-model="tasteGroups"
          item-key="group_id"
          tag="div"
          handle=".group-drag-handle"
          ghost-class="bg-indigo-1"
          delay="200"
          @end="onGroupSortEnd"
        >
          <div
            v-for="group in tasteGroups"
            :key="group.group_id"
            class="bg-white q-mb-sm"
            style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
          >
            <q-expansion-item
              expand-separator
              header-class="text-indigo"
              class="q-pa-none"
            >
              <template #header>
                <q-item
                  dense
                  class="q-pa-none q-gutter-none items-center"
                  style="width: 100%"
                >
                  <!-- 拖曳手把 -->
                  <q-item-section avatar class="group-drag-handle">
                    <q-icon name="drag_handle" color="grey-7" />
                  </q-item-section>

                  <!-- 群組資訊 -->
                  <q-item-section>
                    <q-item-label>{{ group.group_name }}</q-item-label>
                    <q-item-label caption>
                      最少選 {{ group.c_min }}、最多選 {{ group.c_max }}
                    </q-item-label>
                  </q-item-section>

                  <!-- ✅ 這個 section 必須存在，Quasar 才會自動把原生展開箭頭放進來 -->
                  <q-item-section side />
                </q-item>
              </template>

              <!-- ✅ 拖曳項目（你的原寫法保留） -->
              <q-card-section class="q-pa-sm q-pt-none">
                <VueDraggable
                  v-model="group.items"
                  item-key="sno"
                  tag="div"
                  handle=".drag-handle"
                  ghost-class="bg-grey-3"
                  delay="200"
                  @end="() => onTasteItemSortEnd(group)"
                >
                  <div
                    v-for="(item, index) in group.items"
                    :key="item.sno"
                    class="q-mb-xs"
                  >
                    <q-item
                      class="bg-grey-1 q-pa-sm rounded-borders"
                      clickable
                      v-ripple
                      style="align-items: center"
                    >
                      <!-- 拖曳手把 -->
                      <q-item-section avatar class="drag-handle">
                        <q-icon name="drag_indicator" color="grey-7" />
                      </q-item-section>

                      <!-- 名稱 + 價格 -->
                      <q-item-section>
                        <div class="text-subtitle2">{{ item.name }}</div>
                        <div v-if="item.price" class="text-caption text-grey">
                          （+{{ item.price.toLocaleString() }} 元）
                        </div>
                      </q-item-section>

                      <!-- 刪除按鈕 -->
                      <q-item-section side>
                        <q-btn
                          flat
                          round
                          dense
                          icon="close"
                          color="negative"
                          @click.stop="deleteItem(group, index)"
                        />
                      </q-item-section>
                    </q-item>
                  </div>
                </VueDraggable>
              </q-card-section>

              <!-- 編輯按鈕 -->
              <q-card-section class="q-pa-sm q-pt-none">
                <div class="row items-center q-gutter-sm justify-between">
                  <!-- 左邊：刪除 + 編輯 -->
                  <div class="row items-center q-gutter-sm">
                    <q-btn
                      icon="delete"
                      label="刪除"
                      dense
                      flat
                      color="negative"
                      @click="openDeleteDialog(group)"
                    />
                    <q-btn
                      icon="edit"
                      label="編輯"
                      dense
                      flat
                      color="blue"
                      @click="openGroupDialog(group)"
                    />
                  </div>

                  <!-- 右邊：新增口味 -->
                  <q-btn
                    icon="add"
                    label="新增口味"
                    dense
                    flat
                    color="primary"
                    @click="openItemDialog(group)"
                  />
                </div>
              </q-card-section>
            </q-expansion-item>
          </div>
        </VueDraggable>
      </q-list>

      <!-- ➕ 新增群組按鈕 -->
      <div class="q-pa-none">
        <q-btn
          label="新增口味群組"
          icon="add"
          color="green"
          unelevated
          class="full-width"
          @click="openGroupDialog()"
        />
      </div>
    </q-card>
  </q-page>

  <!-- ✏️ 編輯群組 Dialog -->
  <q-dialog v-model="groupDialog.show" persistent>
    <q-card
      class="q-pa-sm bg-white"
      style="width: 100%; max-width: 500px; max-height: 90vh; overflow-y: auto"
    >
      <!-- 標題列 -->
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-primary">編輯口味群組</div>
        <q-btn
          icon="close"
          flat
          round
          dense
          @click="groupDialog.show = false"
        />
      </q-card-section>

      <q-form @submit.prevent="saveGroup">
        <q-card-section class="q-pt-md q-gutter-md">
          <!-- 群組名稱 -->
          <q-input
            v-model="groupDialog.data.group_name"
            label="群組名稱"
            dense
            outlined
          />
        </q-card-section>

        <!-- 最少選 -->
        <q-card-section
          class="row items-center justify-between q-px-md q-mt-none"
        >
          <div>最少選</div>
          <div class="row items-center q-gutter-sm">
            <q-btn
              flat
              round
              icon="remove"
              color="primary"
              @click="groupDialog.data.c_min > 0 && groupDialog.data.c_min--"
            />
            <q-input
              v-model.number="groupDialog.data.c_min"
              readonly
              dense
              outlined
              style="width: 80px"
              input-class="text-center"
              :input-style="{ fontSize: '18px' }"
            />
            <q-btn
              flat
              round
              icon="add"
              color="primary"
              @click="groupDialog.data.c_min++"
            />
          </div>
        </q-card-section>

        <!-- 最多選 -->
        <q-card-section
          class="row items-center justify-between q-px-md q-mt-none"
        >
          <div>最多選</div>
          <div class="row items-center q-gutter-sm">
            <q-btn
              flat
              round
              icon="remove"
              color="primary"
              @click="groupDialog.data.c_max > 0 && groupDialog.data.c_max--"
            />
            <q-input
              v-model.number="groupDialog.data.c_max"
              readonly
              dense
              outlined
              style="width: 80px"
              input-class="text-center"
              :input-style="{ fontSize: '18px' }"
            />
            <q-btn
              flat
              round
              icon="add"
              color="primary"
              @click="groupDialog.data.c_max++"
            />
          </div>
        </q-card-section>

        <!-- 按鈕列 -->
        <q-card-actions align="right" class="q-pt-md">
          <q-btn
            unelevated
            outline
            icon="save"
            label="儲存"
            type="submit"
            color="secondary"
            :disable="!groupDialog.data.group_name"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>

  <!-- 刪除確認 Dialog -->
  <q-dialog v-model="deleteDialog">
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          群組刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteDeptConfirmed"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="itemDialog.show" persistent>
    <q-card style="width: 400px; max-width: 90vw">
      <q-card-section class="row items-center justify-between">
        <div class="text-h6 text-primary">選擇可加入的口味</div>
        <q-btn flat round dense icon="close" @click="itemDialog.show = false" />
      </q-card-section>

      <!-- 搜尋 + 清單 -->
      <q-card-section class="q-pt-none">
        <q-input
          v-model="itemDialog.search"
          dense
          outlined
          label="搜尋口味"
          debounce="300"
          clearable
          class="q-mb-sm"
        >
          <!-- 👉 右側 slot 放「全選」按鈕 -->
          <template #append>
            <q-btn
              flat
              dense
              icon="select_all"
              color="primary"
              size="sm"
              @click="selectAllVisibleTastes"
              :disable="!filteredAvailableTastes.length"
              class="q-mr-xs"
            />
          </template>
        </q-input>
        <q-list bordered style="max-height: 300px; overflow-y: auto">
          <q-item
            v-for="item in filteredAvailableTastes"
            :key="item.name + item.price"
            tag="label"
            clickable
          >
            <q-item-section avatar>
              <q-checkbox
                v-model="itemDialog.selected"
                :val="item"
                color="primary"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ item.name }}</q-item-label>
              <q-item-label caption>+ {{ item.price }} 元</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="!filteredAvailableTastes.length" class="text-grey">
            <q-item-section>無可新增的口味</q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          label="加入"
          color="primary"
          unelevated
          outline
          :disable="!itemDialog.selected.length"
          @click="confirmAddItems"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { VueDraggable } from "vue-draggable-plus";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const props = defineProps({ Branch: String });
const $q = useQuasar();
const expandedGroupId = ref(null);

const selectAllVisibleTastes = () => {
  const visible = filteredAvailableTastes.value;
  const selected = itemDialog.value.selected;

  const selectedNames = new Set(selected.map((i) => i.name));
  const allSelected = visible.every((item) => selectedNames.has(item.name));

  if (allSelected) {
    // ❌ 全部已選 → 移除這些項目
    itemDialog.value.selected = selected.filter(
      (item) => !visible.some((v) => v.name === item.name)
    );
  } else {
    // ✅ 尚未全選 → 補齊所有可見項目
    const newSelected = [...selected];
    const selectedMap = new Set(selected.map((i) => i.name));

    visible.forEach((item) => {
      if (!selectedMap.has(item.name)) {
        newSelected.push(item);
      }
    });

    itemDialog.value.selected = newSelected;
  }
};

const itemDialog = ref({
  show: false,
  search: "",
  selected: [],
  group: null,
});

const allTasteItems = ref([]); // 從資料庫撈出來的完整清單

// 可選項目（排除已經在 group.items 裡的）
const availableTastes = computed(() => {
  const existingNames = new Set(
    itemDialog.value.group?.items?.map((i) => i.name)
  );
  return allTasteItems.value.filter((item) => !existingNames.has(item.name));
});

// 搜尋過濾
const filteredAvailableTastes = computed(() => {
  const keyword = itemDialog.value.search?.toLowerCase() || "";
  return availableTastes.value.filter((item) =>
    item.name.toLowerCase().includes(keyword)
  );
});

// 開啟 Dialog 並撈資料
const openItemDialog = async (group) => {
  itemDialog.value.group = group;
  itemDialog.value.search = "";
  itemDialog.value.selected = [];

  try {
    const res = await apiClient.get("/onlinetaste/get_TasteOptions", {
      params: { branch: props.Branch },
    });
    allTasteItems.value = res.data.data || [];
    itemDialog.value.show = true;
  } catch (err) {
    console.error("❌ 載入口味清單失敗", err);
    $q.notify({ type: "negative", message: "無法載入口味清單" });
  }
};

// 加入選取的口味到 group.items
const confirmAddItems = async () => {
  const group = itemDialog.value.group;
  const newItems = itemDialog.value.selected;

  try {
    const res = await apiClient.post("/onlinetaste/add_TasteItems", {
      branch: props.Branch,
      group_id: group.group_id,
      items: newItems,
    });

    if (res.data.success) {
      // 获取最新的完整口味列表，包括序号
      const updatedGroup = await apiClient.get("/onlinetaste/get_onlineTaste", {
        params: {
          branch: props.Branch,
        },
      });

      if (updatedGroup.data?.success) {
        // 找到当前编辑的群组
        const freshGroup = updatedGroup.data.data.find(
          (g) => g.group_id === group.group_id
        );
        if (freshGroup && freshGroup.items) {
          // 更新本地数据
          group.items = freshGroup.items;
        }
      }

      $q.notify({ type: "positive", message: "新增口味成功" });
      itemDialog.value.show = false;
    } else {
      $q.notify({ type: "negative", message: res.data.message || "新增失敗" });
    }
  } catch (err) {
    console.error("❌ 新增口味失敗:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};
const deleteDialog = ref(false); // 控制 Dialog 開關
const groupToDelete = ref(null); // 暫存欲刪除的群組

const groupDialog = ref({
  show: false,
  data: {
    group_id: null,
    group_name: "",
    c_min: 0,
    c_max: 1,
  },
});

const onTasteItemSortEnd = async (group) => {
  // 确保每个口味的排序序号是连续的，从1开始
  group.items.forEach((item, index) => {
    item.sno = index + 1;
  });

  try {
    await apiClient.post("/onlinetaste/save_TasteItemSort", {
      branch: props.Branch,
      group_id: group.group_id,
      items: group.items.map((item) => ({
        name: item.name,
        sno: item.sno,
        price: item.price,
      })),
    });

    $q.notify({ type: "positive", message: "口味排序已儲存" });
  } catch (err) {
    console.error("❌ 口味排序儲存失敗:", err);
    $q.notify({ type: "negative", message: "口味排序儲存失敗" });
  }
};

const onGroupSortEnd = async () => {
  // 更新 sort 欄位
  tasteGroups.value.forEach((g, index) => {
    g.sort = index + 1;
  });

  try {
    await apiClient.post("/onlinetaste/save_GroupSort", {
      branch: props.Branch,
      groups: tasteGroups.value.map((g) => ({
        group_id: g.group_id,
        sort: g.sort,
      })),
    });

    $q.notify({ type: "positive", message: "群組排序已儲存" });
  } catch (err) {
    console.error("❌ 群組排序錯誤", err);
    $q.notify({ type: "negative", message: "群組排序儲存失敗" });
  }
};

const openGroupDialog = (group = null) => {
  if (group) {
    groupDialog.value.data = { ...group }; // 編輯模式：帶入資料
  } else {
    groupDialog.value.data = {
      group_name: "",
      c_min: 0,
      c_max: 1,
      items: [], // 建議預設空陣列，避免未定義錯誤
      sort: tasteGroups.value.length + 1, // 預估排序
      // 不設 group_id，讓後端自動產生
    };
  }

  groupDialog.value.show = true;
};

const openDeleteDialog = (group) => {
  groupToDelete.value = group;
  deleteDialog.value = true;
};

const deleteDeptConfirmed = async () => {
  if (!groupToDelete.value) return;

  try {
    const res = await apiClient.post("/onlinetaste/delete_TasteGroup", {
      branch: props.Branch,
      group_id: groupToDelete.value.group_id,
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "刪除成功" });
      await fetchTaste(); // 🔁 重新抓群組
    } else {
      $q.notify({ type: "negative", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    console.error("❌ 刪除錯誤:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  } finally {
    deleteDialog.value = false;
    groupToDelete.value = null;
  }
};

const saveGroup = async () => {
  const group = { ...groupDialog.value.data };

  // ✅ 驗證
  if (!group.group_name) {
    $q.notify({ type: "negative", message: "群組名稱不得為空" });
    return;
  }

  if (group.c_max < group.c_min) {
    $q.notify({ type: "negative", message: "最多選不能小於最少選" });
    return;
  }

  // ✅ 新增時補 sort/items（group_id 交給後端產生）
  if (!group.group_id) {
    group.sort = tasteGroups.value.length + 1;
    group.items = [];
  }
  //console.log(group);
  try {
    const res = await apiClient.post("/onlinetaste/save_TasteGroups", {
      branch: props.Branch,
      groups: [group],
    });

    if (res.data.success) {
      $q.notify({ type: "positive", message: "儲存成功" });

      groupDialog.value.show = false;

      // ✅ 重新讀取最新資料
      await fetchTaste(); // ⬅ 你自己的資料讀取函式
    } else {
      $q.notify({ type: "negative", message: res.data.message || "儲存失敗" });
    }
  } catch (err) {
    console.error("❌ 儲存錯誤:", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

const tasteGroups = ref([]);

const deleteItem = async (group, index) => {
  const item = group.items[index];

  try {
    const res = await apiClient.post("/onlinetaste/delete_TasteItem", {
      branch: props.Branch,
      group_id: group.group_id,
      sno: item.sno,
    });

    if (res.data.success) {
      // 直接重新載入所有資料，而不是嘗試在前端更新
      await fetchTaste();
      $q.notify({ type: "positive", message: "口味已刪除" });
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "刪除失敗",
      });
    }
  } catch (err) {
    console.error("❌ 刪除口味項目失敗", err);
    $q.notify({ type: "negative", message: "伺服器錯誤" });
  }
};

const fetchTaste = async () => {
  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "讀取中...",
    });

    const res = await apiClient.get(
      `${apiBaseUrl}/onlinetaste/get_onlineTaste`,
      {
        params: {
          branch: props.Branch,
        },
      }
    );

    if (res.data?.success) {
      // 確保所有群組的項目都是有序的
      const groups = res.data.data;

      // 處理每個群組中的項目
      groups.forEach((group) => {
        if (Array.isArray(group.items)) {
          // 確保按序號排序
          group.items.sort((a, b) => a.sno - b.sno);
        } else {
          // 如果項目不是陣列，初始化為空陣列
          group.items = [];
        }
      });

      // 按sort欄位排序群組
      groups.sort((a, b) => a.sort - b.sort);

      // 更新資料
      tasteGroups.value = groups;

      console.log("✅ 資料載入成功:", groups);
    } else {
      console.error("❌ 載入菜單失敗:", res.data?.message || "未知錯誤");
      $q.notify({
        type: "negative",
        message: "載入菜單失敗: " + (res.data?.message || "未知錯誤"),
      });
    }
  } catch (err) {
    console.error("❌ 載入菜單時發生錯誤:", err.message);
    $q.notify({
      type: "negative",
      message: "載入菜單時發生錯誤: " + err.message,
    });
  } finally {
    $q.loading.hide();
  }
};

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await fetchTaste();
});
</script>
