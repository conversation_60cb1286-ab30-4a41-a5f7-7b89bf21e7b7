import { ref, onMounted } from "vue";
import apiClient from "../api";

export function useFormFlows(formType) {
  const flowSteps = ref([]);
  const loading = ref(false);
  const error = ref(null);

  const fetchFlowSteps = async () => {
    if (!formType) return;
    loading.value = true;
    error.value = null;
    try {
      const response = await apiClient.get("/forms/get_form_flows", {
        params: { form_type: formType },
      });
      flowSteps.value = response.data.map((step, index) => ({
        number: index + 1,
        name: step.Remark?.trim() || "",
        role: step.approver_role?.trim() || "",
        approver: step.approver_id ? step.approver_id.trim() : null,
        notify: step.Notify,
      }));
    } catch (err) {
      error.value = "無法載入簽核流程";
      flowSteps.value = [];
    } finally {
      loading.value = false;
    }
  };

  onMounted(fetchFlowSteps);

  return {
    flowSteps,
    loading,
    error,
    fetchFlowSteps,
  };
}
