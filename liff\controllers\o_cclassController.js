const { getPoolByBranch, sql } = require("../services/dbPoolManager");

const getonlineCclass = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少參數 branch 門市代號" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 一次查詢兩張表：cclass + taste
    const [cclassResult, tasteResult] = await Promise.all([
      pool.request().query(`
        SELECT code, name, service, discount, mdisp, defineC2, showorder, sno
        FROM cclass
        ORDER BY sno
      `),
      pool.request().query(`
        SELECT code, name, cno, seq, price, show
        FROM taste
        ORDER BY code, cno, seq
      `),
    ]);

    // 分組 taste 為 Map<code, taste[]>
    const tasteMap = {};
    for (const t of tasteResult.recordset) {
      if (!tasteMap[t.code]) tasteMap[t.code] = [];
      tasteMap[t.code].push({
        name: t.name,
        cno: t.cno,
        seq: t.seq,
        price: t.price,
        show: t.show,
      });
    }

    // 將 taste 加入對應的 cclass 裡
    const data = cclassResult.recordset.map((row) => ({
      code: row.code,
      name: row.name,
      service: row.service,
      discount: row.discount,
      mdisp: row.mdisp,
      defineC2: row.defineC2,
      showorder: row.showorder,
      sno: row.sno,
      tastes: tasteMap[row.code] || [],
    }));

    res.json({ success: true, data });
  } catch (err) {
    console.error("❌ 載入類部資料失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveGroupSort = async (req, res) => {
  const { branch, groups } = req.body;

  if (!branch || !Array.isArray(groups)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    for (const group of groups) {
      if (!group.code || typeof group.sno !== "number") continue;

      const req = new sql.Request(transaction);

      await req
        .input("code", sql.VarChar, group.code)
        .input("sno", sql.VarChar(2), group.sno.toString().padStart(2, "0")) // ✅ 轉為兩碼字串
        .query(`
          UPDATE cclass
          SET sno = @sno
          WHERE code = @code
        `);
    }

    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存類部排序失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveCclass = async (req, res) => {
  const { branch, list } = req.body;

  if (!branch || !Array.isArray(list) || list.length === 0) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  const item = list[0]; // 只處理第一筆
  const code = item.code?.trim();
  const sno = item.sno?.trim();

  if (!code) {
    return res
      .status(400)
      .json({ success: false, message: "缺少代碼（code）" });
  }

  if (!sno) {
    return res
      .status(400)
      .json({ success: false, message: "缺少排序欄位（sno）" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const checkReq = pool.request();
    const existsResult = await checkReq
      .input("code", sql.VarChar, code)
      .query("SELECT COUNT(*) AS count FROM cclass WHERE code = @code");

    const exists = existsResult.recordset[0].count > 0;

    const request = pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.NVarChar, item.name)
      .input("service", sql.VarChar, item.service)
      .input("discount", sql.VarChar, item.discount)
      .input("mdisp", sql.VarChar, item.mdisp)
      .input("defineC2", sql.VarChar, item.defineC2)
      .input("showorder", sql.VarChar, item.showorder)
      .input("sno", sql.VarChar, sno);

    if (exists) {
      await request.query(`
        UPDATE cclass
        SET name = @name,
            service = @service,
            discount = @discount,
            mdisp = @mdisp,
            defineC2 = @defineC2,
            showorder = @showorder,
            sno = @sno
        WHERE code = @code
      `);
    } else {
      await request.query(`
        INSERT INTO cclass (code, name, service, discount, mdisp, defineC2, showorder, sno)
        VALUES (@code, @name, @service, @discount, @mdisp, @defineC2, @showorder, @sno)
      `);
    }

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存類部失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};
const deleteCclass = async (req, res) => {
  const { branch, code } = req.body;

  if (!branch || !code) {
    return res
      .status(400)
      .json({ success: false, message: "缺少必要參數（branch 或 code）" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    await pool
      .request()
      .input("code", sql.VarChar, code.trim())
      .query("DELETE FROM cclass WHERE code = @code");

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 刪除類部失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const getTspecList = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({ success: false, message: "缺少參數 branch" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const result = await pool.request().query(`
      SELECT name FROM Tspec ORDER BY name
    `);

    const list = result.recordset.map((row) => ({
      label: row.name,
      value: row.name,
    }));

    res.json({ success: true, data: list });
  } catch (err) {
    console.error("❌ 取得 Tspec 清單失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const deleteTaste = async (req, res) => {
  const { branch, code, name, price } = req.body;

  if (!branch || !code || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const result = await pool
      .request()
      .input("code", sql.VarChar, code)
      .input("name", sql.NVarChar, name)
      .input("price", sql.Decimal(10, 2), price || 0) // 預設 0 元也可以刪
      .query(`
        DELETE FROM taste
        WHERE code = @code AND name = @name AND price = @price
      `);

    res.json({ success: true, rowsAffected: result.rowsAffected[0] });
  } catch (err) {
    console.error("❌ 刪除口味失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveTaste = async (req, res) => {
  const { branch, taste } = req.body;

  if (!branch || !taste || !taste.code || !taste.name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // // 檢查是否已存在相同 name + price 的記錄
    // const checkResult = await pool.request()
    //   .input("code", sql.VarChar, taste.code)
    //   .input("name", sql.NVarChar, taste.name)
    //   .input("price", sql.Int, taste.price)
    //   .query(`
    //     SELECT COUNT(*) AS count
    //     FROM taste
    //     WHERE code = @code AND name = @name AND price = @price
    //   `);

    // if (checkResult.recordset[0].count > 0) {
    //   return res.status(400).json({ success: false, message: "口味已存在" });
    // }

    // 寫入 taste 資料表
    await pool
      .request()
      .input("code", sql.VarChar, taste.code)
      .input("name", sql.NVarChar, taste.name)
      .input("cno", sql.Int, taste.cno)
      .input("seq", sql.VarChar, taste.seq)
      .input("price", sql.Int, taste.price)
      .input("show", sql.VarChar, taste.show || "1").query(`
        INSERT INTO taste (code, name, cno, seq, price, show)
        VALUES (@code, @name, @cno, @seq, @price, @show)
      `);

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 新增口味失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveTasteSort = async (req, res) => {
  const { branch, code, tastes } = req.body;

  if (!branch || !code || !Array.isArray(tastes)) {
    return res.status(400).json({ success: false, message: "參數錯誤" });
  }

  try {
    const pool = await getPoolByBranch(branch);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    for (const taste of tastes) {
      await new sql.Request(transaction)
        .input("code", sql.VarChar, code)
        .input("name", sql.NVarChar, taste.name)
        .input("price", sql.Int, taste.price)
        .input("seq", sql.VarChar, taste.seq)
        .input("cno", sql.Int, taste.cno).query(`
          UPDATE taste
          SET seq = @seq, cno = @cno
          WHERE code = @code AND name = @name AND price = @price
        `);
    }

    await transaction.commit();
    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存 taste 排序失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const insertTspace = async (req, res) => {
  const { branch, name } = req.body;

  if (!branch || !name) {
    return res
      .status(400)
      .json({ success: false, message: "缺少參數 branch 或 name" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 檢查是否重複（避免同門市重複）
    const check = await pool
      .request()
      .input("name", sql.NVarChar, name)
      .query("SELECT COUNT(*) as cnt FROM Tspec WHERE name = @name");

    if (check.recordset[0].cnt > 0) {
      return res.status(409).json({ success: false, message: "口味已存在" });
    }

    await pool
      .request()
      .input("name", sql.NVarChar, name)
      .query("INSERT INTO Tspec (name) VALUES (@name)");

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 新增 Tspec 失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const deleteTspec = async (req, res) => {
  const { branch, name } = req.body;

  if (!branch || !name) {
    return res.status(400).json({ success: false, message: "缺少必要參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    await pool
      .request()
      .input("name", sql.NVarChar, name)
      .query("DELETE FROM Tspec WHERE name = @name");

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 刪除 Tspec 失敗", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getonlineCclass,
  saveGroupSort,
  saveCclass,
  deleteCclass,
  getTspecList,
  deleteTaste,
  saveTaste,
  saveTasteSort,
  insertTspace,
  deleteTspec,
};
