<template>
  <q-layout view="lHh Lpr lFf">
    <q-header elevated class="bg-primary text-white">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="選單"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title class="row items-center">
          <img
            src="~assets/quasar-logo-vertical.svg"
            style="height: 30px; margin-right: 10px"
          />
          <span class="text-weight-bold">Quasar 管理系統</span>
        </q-toolbar-title>

        <!-- 右側工具區 -->
        <div class="row items-center">
          <!-- 主題切換 -->
          <q-btn
            flat
            round
            dense
            :icon="darkMode ? 'light_mode' : 'dark_mode'"
            @click="toggleDarkMode"
          >
            <q-tooltip>{{
              darkMode ? "切換亮色模式" : "切換暗色模式"
            }}</q-tooltip>
          </q-btn>

          <!-- 通知 -->
          <q-btn flat round dense icon="notifications">
            <q-badge color="red" floating>2</q-badge>
            <q-tooltip>通知</q-tooltip>
          </q-btn>

          <!-- 使用者 -->
          <q-btn-dropdown flat dense icon="account_circle" class="q-ml-sm">
            <q-list>
              <q-item clickable v-close-popup>
                <q-item-section avatar>
                  <q-icon name="person" />
                </q-item-section>
                <q-item-section>個人資料</q-item-section>
              </q-item>

              <q-item clickable v-close-popup>
                <q-item-section avatar>
                  <q-icon name="settings" />
                </q-item-section>
                <q-item-section>設定</q-item-section>
              </q-item>

              <q-separator />

              <q-item clickable v-close-popup>
                <q-item-section avatar>
                  <q-icon name="logout" color="negative" />
                </q-item-section>
                <q-item-section class="text-negative">登出</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      :width="240"
      :breakpoint="500"
      :class="darkMode ? 'bg-dark text-white' : 'bg-grey-1'"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <q-item-label
            header
            class="text-weight-bold q-pb-md q-pt-md text-primary"
          >
            功能選單
          </q-item-label>

          <q-item
            v-for="link in linksList"
            :key="link.title"
            v-ripple
            clickable
            :to="link.to"
            :href="link.link"
            target="_blank"
            :active="link.active"
            :active-class="
              darkMode ? 'bg-blue-10' : 'bg-primary-1 text-primary'
            "
          >
            <q-item-section avatar>
              <q-icon :name="link.icon" />
            </q-item-section>

            <q-item-section>
              <q-item-label>{{ link.title }}</q-item-label>
              <q-item-label caption>{{ link.caption }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>

    <!-- 頁尾 -->
    <q-footer
      bordered
      class="bg-white text-grey-8 q-py-xs"
      :class="{ 'bg-dark text-white': darkMode }"
    >
      <q-toolbar>
        <q-toolbar-title class="text-subtitle2 text-center">
          © {{ new Date().getFullYear() }} Quasar App · 版本 v{{ $q.version }}
        </q-toolbar-title>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup>
import { ref, watch } from "vue";
import { useQuasar } from "quasar";
import EssentialLink from "components/EssentialLink.vue";

defineOptions({
  name: "MainLayout",
});

const $q = useQuasar();
const darkMode = ref($q.dark.isActive);

// 切換深色模式
function toggleDarkMode() {
  $q.dark.toggle();
  darkMode.value = $q.dark.isActive;
  localStorage.setItem("darkMode", darkMode.value);
}

// 初始化深色模式
const initDarkMode = () => {
  const savedMode = localStorage.getItem("darkMode");
  if (savedMode !== null) {
    const isDark = savedMode === "true";
    $q.dark.set(isDark);
    darkMode.value = isDark;
  }
};

// 監聽系統深色模式變化
watch(
  () => $q.dark.isActive,
  (isDark) => {
    darkMode.value = isDark;
  }
);

// 在組件掛載時初始化
initDarkMode();

const linksList = [
  {
    title: "儀表板",
    caption: "系統概覽",
    icon: "dashboard",
    to: "/",
    active: true,
  },
  {
    title: "文件",
    caption: "quasar.dev",
    icon: "description",
    link: "https://quasar.dev",
  },
  {
    title: "GitHub",
    caption: "github.com/quasarframework",
    icon: "code",
    link: "https://github.com/quasarframework",
  },
  {
    title: "Discord 聊天頻道",
    caption: "chat.quasar.dev",
    icon: "chat",
    link: "https://chat.quasar.dev",
  },
  {
    title: "論壇",
    caption: "forum.quasar.dev",
    icon: "forum",
    link: "https://forum.quasar.dev",
  },
  {
    title: "Twitter",
    caption: "@quasarframework",
    icon: "rss_feed",
    link: "https://twitter.quasar.dev",
  },
  {
    title: "Facebook",
    caption: "@QuasarFramework",
    icon: "public",
    link: "https://facebook.quasar.dev",
  },
  {
    title: "Quasar 精選",
    caption: "社群 Quasar 專案",
    icon: "favorite",
    link: "https://awesome.quasar.dev",
  },
];

const leftDrawerOpen = ref(false);

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;
}
</script>

<style lang="scss">
.q-drawer {
  transition: all 0.3s ease;
}

.q-item {
  border-radius: 8px;
  margin: 0 8px 4px 8px;
  transition: all 0.3s ease;

  &.q-router-link--active {
    font-weight: 600;
  }
}

.q-header {
  transition: background-color 0.3s ease;
}

.q-footer {
  transition: background-color 0.3s ease;
}
</style>
