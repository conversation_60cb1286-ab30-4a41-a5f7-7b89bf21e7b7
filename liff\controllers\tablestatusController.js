const sqlLocal = require("mssql");
const dbConfig = require("../config/db");

const { DateTime } = require("luxon");
const { getPoolByBranch, sql } = require("../services/dbPoolManager");

function getDeviceTime() {
  const localOffset = new Date().getTimezoneOffset(); // 單位為分鐘
  const isUTC = localOffset === 0;

  return isUTC
    ? DateTime.now().plus({ hours: 8 }) // Docker 時補 8 小時
    : DateTime.now(); // 本地直接使用
}

const getPrintErrors = async (req, res) => {
  let ipList = req.query.ipList;

  // ✅ 保證 ipList 一定是陣列格式
  if (!Array.isArray(ipList)) {
    ipList = ipList ? [ipList] : [];
  }

  if (!ipList.length) {
    return res
      .status(400)
      .json({ success: false, error: "參數不足或格式錯誤" });
  }

  // 查詢 PrintError 表
  const pool = await sqlLocal.connect(dbConfig);
  const request = pool.request();
  const ipConditions = ipList.map((_, i) => `ip = @ip${i}`).join(" OR ");
  ipList.forEach((ip, i) => request.input(`ip${i}`, sql.VarChar, ip));

  const result = await request.query(`
    SELECT * FROM PrintError
    WHERE ${ipConditions}
    ORDER BY created_at DESC
  `);

  res.json({ success: true, data: result.recordset });
};

const saveSetting = async (req, res) => {
  const { page, branch, data } = req.body;

  if (!page || !data || typeof data !== "object") {
    return res
      .status(400)
      .json({ success: false, error: "參數不足或格式錯誤" });
  }

  try {
    // ✅ 使用 branch 自動建立/取得連線池
    const pool = await getPoolByBranch(branch);

    // 🔁 刪除舊設定
    await pool
      .request()
      .input("page", sql.NChar, page)
      .query(`DELETE FROM Setting WHERE setting_page = @page`);

    // 🔁 寫入新設定
    for (const [key, value] of Object.entries(data)) {
      await pool
        .request()
        .input("page", sql.NChar, page)
        .input("key", sql.NVarChar(100), key)
        .input(
          "value",
          sql.NVarChar(sql.MAX),
          typeof value === "string" ? value : JSON.stringify(value)
        ).query(`
          INSERT INTO Setting (setting_page, setting_key, setting_value)
          VALUES (@page, @key, @value)
        `);
    }

    res.json({ success: true, message: "✅ 設定已儲存" });
  } catch (err) {
    console.error("❌ 儲存設定失敗:", err);
    res.status(500).json({ success: false, error: "儲存設定錯誤" });
  }
};

const getSetting = async (req, res) => {
  const { page, branch } = req.query;

  if (!page || !branch) {
    return res
      .status(400)
      .json({ success: false, error: "缺少參數 page 或 branch" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // ✅ 查詢設定
    const settingResult = await pool
      .request()
      .input("page", sql.NChar, page.trim()).query(`
        SELECT setting_key, setting_value
        FROM Setting
        WHERE setting_page = @page
      `);

    // ✅ 查詢出單機 IP
    const printerResult = await pool.request().query(`
      SELECT ip FROM Vclass_net
      WHERE ISNULL(ip, '') <> ''
    `);

    const settings = {};
    settingResult.recordset.forEach(({ setting_key, setting_value }) => {
      try {
        settings[setting_key] = JSON.parse(setting_value);
      } catch {
        settings[setting_key] = setting_value;
      }
    });

    const printerIps = printerResult.recordset.map((row) => row.ip.trim());

    res.json({
      success: true,
      data: {
        settings,
        printerIps,
      },
    });
  } catch (err) {
    console.error("❌ 讀取設定錯誤：", err);
    res.status(500).json({ success: false, error: "讀取設定失敗" });
  }
};

const getTablestatus = async (req, res) => {
  const { branch, page, sort } = req.body;

  if (!branch || !page) {
    return res.status(400).json({
      success: false,
      message: "❌ 請提供 branch 和 page 參數",
    });
  }

  const allowedSortFields = {
    name: "Room.name",
    stable: "Room.STABLE",
  };
  const orderByField = allowedSortFields[sort] || "Room.STABLE";

  try {
    const pool = await getPoolByBranch(branch); // ✅ 改用連線池

    const result = await pool.request().input("page", sql.VarChar, page).query(`
        SELECT 
          Room.name, 
          ordernow.code, 
          ISNULL(PERSON, 0) + ISNULL(BOY, 0) AS person,
          ISNULL(AMOUNT, 0) AS amount, 
          Rtime, 
          ordernow.Ndate, 
          Room.code AS roomcode,
          Room.STATE AS state,
          OrderRoomN.appmemo,
          omenum.name AS hidename
        FROM Room WITH (NOLOCK)
        LEFT JOIN OrderRoomN 
          ON OrderRoomN.Room = Room.code 
          AND OrderRoomN.code IN (SELECT code FROM ordernow WHERE checkout = '0')
        LEFT JOIN ordernow 
          ON OrderRoomN.code = ordernow.code
        LEFT JOIN omenum on ordernow.HideGroup=omenum.code
        WHERE Room.MPAGE = @page
        ORDER BY ${orderByField}
      `);

    // ✅ 不需要 pool.close()，連線池會自動管理

    const data = result.recordset.map((row) => ({
      name: row.name,
      code: row.code,
      person: row.person,
      amount: row.amount,
      rtime: row.Rtime,
      ndate: row.Ndate,
      roomcode: row.roomcode,
      state: row.state,
      appmemo: row.appmemo,
      hidename: row.hidename,
    }));

    return res.json({ success: true, data });
  } catch (err) {
    console.error("❌ getTablestatus error:", err.message);
    return res.status(500).json({
      success: false,
      message: "伺服器錯誤",
      error: err.message,
    });
  }
};

const getOpenMode = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "❌ 缺少 branch 參數" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // 查消費方式
    const resultEatway = await pool
      .request()
      .query("SELECT code, name FROM MenuMod ORDER BY no");

    // 查頁面名稱
    const resultPage = await pool.request().query(`
        SELECT N1, N2, N3, N4, N5, N6 
        FROM roompage 
        WHERE CC IS NULL AND code IS NULL
      `);

    const pageRow = resultPage.recordset[0] || {};
    const pagesArray = [];

    for (let i = 1; i <= 6; i++) {
      const name = pageRow[`N${i}`];
      pagesArray.push({
        label: name && name.trim() !== "" ? name : `第${i}頁`,
        value: i,
      });
    }

    res.json({
      success: true,
      eatways: resultEatway.recordset,
      pages: pagesArray,
    });
  } catch (err) {
    console.error("❌ 無法取得資料:", err);
    res.status(500).json({ success: false, message: "無法取得資料" });
  }
};

const getOrderCode = async (branch) => {
  const pool = await getPoolByBranch(branch);

  // 取得目前時段與日期前綴
  const result = await pool
    .request()
    .query("SELECT code, stime, etime, Tdate FROM TimeSeg");

  const now = getDeviceTime(); // Luxon DateTime
  const nowTime = now.toFormat("HH:mm");
  let selectedCode = -1;
  let datePrefix = now.toFormat("yyyyLLdd") + "B";

  for (const row of result.recordset) {
    const { code, stime, etime, Tdate } = row;

    const isWithinSameDay =
      stime < etime && nowTime >= stime && nowTime < etime;
    const isAcrossDay = stime > etime && (nowTime >= stime || nowTime < etime);

    if (isWithinSameDay || isAcrossDay) {
      selectedCode = code;
      datePrefix =
        Tdate === 1
          ? now.minus({ days: 1 }).toFormat("yyyyLLdd") + "B"
          : now.toFormat("yyyyLLdd") + "B";
      break;
    }
  }

  // 查詢目前最大單號（like '20250615B%'）
  const resultMax = await pool
    .request()
    .input("prefix", sql.VarChar, datePrefix + "%")
    .query("SELECT MAX(code) AS maxCode FROM ordernow WHERE code LIKE @prefix");

  const maxCode = resultMax.recordset[0]?.maxCode;
  let suffix = "001";
  if (maxCode) {
    const lastNum = parseInt(maxCode.slice(datePrefix.length));
    suffix = (isNaN(lastNum) ? 1 : lastNum + 1).toString().padStart(3, "0");
  }

  const finalCode = datePrefix + suffix;

  return { ts: selectedCode, orderCode: finalCode };
};

const openTable = async (req, res) => {
  try {
    const payload = req.body;
    const branch = payload.branch;

    if (!branch) {
      return res
        .status(400)
        .json({ success: false, message: "❌ 缺少 branch 參數" });
    }

    const pool = await getPoolByBranch(branch); // ✅ 改為連線池
    const now = getDeviceTime();
    const ndate = now.toFormat("yyyy/LL/dd");
    const rtime = now.toFormat("HH:mm");

    const { ts, orderCode } = await getOrderCode(branch); // ✅ getOrderCode 也要支援 branch
    const baseFields = {
      code: orderCode,
      person: payload.adult,
      boy: payload.child,
      ndate,
      rtime,
      roomcode: payload.roomcode,
      roomname: payload.roomname,
      ts,
      eatwayCode: payload.eatwayCode,
      eatwayName: payload.eatwayName,
    };

    const request = pool.request();

    // ✅ 寫入五張表
    await request.query(`
      INSERT INTO ordernow (
        code, person, boy, ndate, rtime, checkout, Tablesql, ts,
        service, tax, vamt, ramt, emplc, empln, cntot, stot, dtot,
        amount, oclass, tchk, schk, ono, Damt ,hidegroup
      ) VALUES (
        '${baseFields.code}', '${baseFields.person}', '${baseFields.boy}', '${baseFields.ndate}', '${baseFields.rtime}',
        '0', '1', '${baseFields.ts}', '10', '0', '0', '0', 
        '9998', 'Quasar', '0', '0', '0', '0', '2', '0', '0', '1', '100','${baseFields.eatwayCode}'
      );
    `);

    await request.query(`
      INSERT INTO orderroomN (code, ndate, room, qty, ts)
      VALUES ('${baseFields.code}', '${baseFields.ndate}', '${baseFields.roomcode}', '1', '${baseFields.ts}');
    `);

    await request.query(`
      INSERT INTO orderMainN (
        Scode, code, oprice, cost, name, qty, sno, unit, 
        OYN, prn, EMPLC, empln, discount, service, sname, taste, 
        room, simname, ono, damt, oround
      ) VALUES (
        '${baseFields.code}', '${baseFields.eatwayCode}', '0', '0', '${baseFields.eatwayName}', '1', '01', '份',
        '2', '1', '9998', 'Quasar', '0', '0', '模式', '01',
        '${baseFields.roomcode}', '${baseFields.eatwayName}', '01', '100', '00'
      );
    `);

    await request.query(`
      INSERT INTO orderMainNMinS (
        Scode, code, oprice, cost, name, qty, sno, unit,
        OYN, prn, EMPLC, empln, discount, service, sname, taste,
        room, simname, ono, damt, R1
      ) VALUES (
        '${baseFields.code}', '${baseFields.eatwayCode}', '0', '0', '${baseFields.eatwayName}', '1', '01', '份',
        '2', '1', '9998', 'Quasar', '0', '0', '模式', '01',
        '${baseFields.roomcode}', '${baseFields.eatwayName}', '01', '100', '+'
      );
    `);

    await request.query(`
      INSERT INTO Qrappspools (
        code, TableName, HideGroup, Status, Person, Boy
      ) VALUES (
        '${baseFields.code}', '${baseFields.roomname}', '${baseFields.eatwayCode}', '0',
        '${baseFields.person}', '${baseFields.boy}'
      );
    `);

    res.json({ success: true, code: baseFields.code });
  } catch (err) {
    console.error("❌ 開桌錯誤", err);
    res.status(500).json({ success: false, message: err.message });
  }
};

const getOpenBase = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res.status(400).json({
      success: false,
      message: "❌ 缺少 branch 參數",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const result = await pool.request().query(`
      SELECT 
        OMenuM.code,
        OMenuM.name,
        OMenuM.cclass,
        OMenuM.unit,
        OMenuS.name AS spec,
        OMenuS.oprice,
        TasteItem.pname,
        TasteItem.price
      FROM OMenuM
      LEFT JOIN OMenuS ON OMenuS.code = OMenuM.code
      LEFT JOIN TasteItem ON TasteItem.mcode = OMenuM.code
      WHERE OMenuM.omenu = '1' AND OMenuM.cclass IN ('01','02')
    `);

    const raw = result.recordset;
    const grouped = {};

    raw.forEach((row) => {
      const { code, name, cclass, unit, spec, oprice, pname, price } = row;
      if (!grouped[code]) {
        grouped[code] = {
          code,
          name,
          cclass,
          unit,
          specs: [],
          tastes: [],
        };
      }
      if (spec && !grouped[code].specs.find((s) => s.name === spec)) {
        grouped[code].specs.push({ name: spec, price: oprice });
      }
      if (pname && !grouped[code].tastes.find((t) => t.name === pname)) {
        grouped[code].tastes.push({ name: pname, price });
      }
    });

    const soupOptions = Object.values(grouped).filter((x) => x.cclass === "01");
    const baseOptions = Object.values(grouped).filter((x) => x.cclass === "02");

    res.json({ success: true, soupOptions, baseOptions });
  } catch (err) {
    console.error("❌ getOpenBase 載入選單失敗:", err);
    res.status(500).json({ message: "載入選單失敗" });
  }
};

const checkDatabase = async (req, res) => {
  const { branch } = req.body;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "❌ 請提供 branch" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    // ✅ 檢查 Vclass_Net 是否存在
    const checkVclassNet = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Vclass_Net'
    `);
    if (checkVclassNet.recordset.length === 0) {
      console.log("🔧 建立資料表 Vclass_Net");
      await pool.request().query(`
        CREATE TABLE Vclass_Net (
          code NCHAR(2) NOT NULL,
          ip   NCHAR(15)
        )
      `);
    }

    // ✅ 檢查 Setting 是否存在
    const checkSetting = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Setting'
    `);
    if (checkSetting.recordset.length === 0) {
      console.log("🔧 建立資料表 Setting 並寫入預設值");

      await pool.request().query(`
        CREATE TABLE Setting (
          setting_page NCHAR(20) NOT NULL,
          setting_key NVARCHAR(100) NOT NULL,
          setting_value NVARCHAR(MAX)
        )
      `);

      const defaultSettings = [
        { key: "refresh_interval", value: "3" },
        { key: "sort_by", value: `"name"` },
        {
          key: "tableColorRules",
          value: JSON.stringify([
            { min: 90, color: "#4C4C4C" },
            { min: 60, color: "#8B4513" },
            { min: 30, color: "#FF9933" },
            { min: 0, color: "#F48FB1" },
          ]),
        },
      ];

      for (const setting of defaultSettings) {
        await pool
          .request()
          .input("page", sql.NChar(20), "tablestatus")
          .input("key", sql.NVarChar(100), setting.key)
          .input("val", sql.NVarChar(sql.MAX), setting.value).query(`
            INSERT INTO Setting (setting_page, setting_key, setting_value)
            VALUES (@page, @key, @val)
          `);
      }
    }

    // ✅ 檢查 TasteGroup 是否存在
    const checkTasteGroup = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'TasteGroup'
    `);
    if (checkTasteGroup.recordset.length === 0) {
      console.log("🔧 建立資料表 TasteGroup");
      await pool.request().query(`
        CREATE TABLE TasteGroup (
          taste_code NVARCHAR(10) PRIMARY KEY,
          taste_title NVARCHAR(50),
          c_max INT,
          c_min INT,
          sort INT,
        )
      `);
    }

    // ✅ 檢查 TasteItems 是否存在
    const checkTasteItems = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'TasteItems'
    `);
    if (checkTasteItems.recordset.length === 0) {
      console.log("🔧 建立資料表 TasteItems");
      await pool.request().query(`
        CREATE TABLE TasteItems (
          taste NVARCHAR(10),
          sno INT,
          name NVARCHAR(50),
          price INT,
          PRIMARY KEY (taste, sno)
        )
      `);
    }

    // ✅ 檢查 TasteMap 是否存在
    const checkTasteMap = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'TasteMap'
    `);
    if (checkTasteMap.recordset.length === 0) {
      console.log("🔧 建立資料表 TasteMap");
      await pool.request().query(`
        CREATE TABLE TasteMap (
          code NVARCHAR(10),
          taste_code NVARCHAR(10)
          PRIMARY KEY (code, taste_code)
        )
      `);
    }

    // ✅ 檢查 SetsGroup 是否存在
    const checkSetsGroup = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SetsGroup'
    `);
    if (checkSetsGroup.recordset.length === 0) {
      console.log("🔧 建立資料表 SetsGroup");
      await pool.request().query(`
        CREATE TABLE SetsGroup (
          sets_code NCHAR(10) PRIMARY KEY,
          sets_title NCHAR(10),
          c_min INT,
          c_max INT,
          sort INT,
          type NCHAR(1),
          mcq NCHAR(1)
        )
      `);
    }

    // ✅ 檢查 SetsItems 是否存在
    const checkSetsItems = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SetsItems'
    `);
    if (checkSetsItems.recordset.length === 0) {
      console.log("🔧 建立資料表 SetsItems");
      await pool.request().query(`
        CREATE TABLE SetsItems (
          sets NCHAR(10),
          sno INT,
          code NCHAR(10),
          name NVARCHAR(50),
          sname NCHAR(10),
          unit NCHAR(10),
          price INT,
          PRIMARY KEY (sets, sno)
        )
      `);
    }

    // ✅ 檢查 SetsMap 是否存在
    const checkSetsMap = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SetsMap'
    `);
    if (checkSetsMap.recordset.length === 0) {
      console.log("🔧 建立資料表 SetsMap");
      await pool.request().query(`
        CREATE TABLE SetsMap (
          code NCHAR(10),
          sname NCHAR(10),
          sets_code NCHAR(10),
          PRIMARY KEY (code, sname, sets_code)
        )
      `);
    }

    const checkSpecColumn = await pool.request().query(`
      SELECT * FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'MenuModOIn' AND COLUMN_NAME = 'spec'
    `);

    if (checkSpecColumn.recordset.length === 0) {
      console.log("🔧 MenuModOIn 缺少欄位 spec，進行新增...");
      await pool.request().query(`
        ALTER TABLE MenuModOIn ADD spec NCHAR(10) NULL
      `);
    }

    res.json({ success: true, message: "初始化成功" });
  } catch (err) {
    console.error("❌ 初始化失敗：", err);
    res
      .status(500)
      .json({ success: false, message: "初始化失敗", error: err.message });
  }
};

const cancelService = async (req, res) => {
  const { branch, code } = req.body;

  if (!code || !branch) {
    return res.status(400).json({
      success: false,
      message: "❌ 請提供 branch 與 code",
    });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const result = await pool.request().input("code", sql2.VarChar, code)
      .query(`
        UPDATE orderroomN
        SET appmemo = NULL
        WHERE code = @code
      `);

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({
        success: false,
        message: "❌ 找不到該桌",
      });
    }

    return res.json({
      success: true,
      message: "✅ 已取消服務鈴",
    });
  } catch (err) {
    console.error("❌ 取消服務鈴錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "❌ 伺服器錯誤",
    });
  }
};

module.exports = {
  getSetting,
  getTablestatus,
  getOpenMode,
  openTable,
  getOpenBase,
  saveSetting,
  getPrintErrors,
  checkDatabase,
  cancelService,
};
