const { getPoolByBranch, sql } = require("../services/dbPoolManager");
require("dotenv").config();

const getInternalMenu = async (req, res) => {
  const { branch } = req.query;

  if (!branch) {
    return res
      .status(400)
      .json({ success: false, message: "缺少參數 branch 門市代號" });
  }

  try {
    const pool = await getPoolByBranch(branch);

    const [
      menuM,
      menuS,
      tasteGroups,
      tasteItems,
      tasteMap,
      categories,
      setsGroups,
      setsItems,
      setsMap,
    ] = await Promise.all([
      pool.request().query(`
        SELECT code, name, cclass, unit, ISNULL(k1, 99) AS k1, ISNULL(k2, 99) AS k2
        FROM OMenuM WHERE omenu = '1'
      `),
      pool.request().query(`
        SELECT code, name, oprice FROM OMenuS
      `),
      pool.request().query(`
        SELECT taste_code, taste_title, c_min, c_max, sort FROM TasteGroup
      `),
      pool.request().query(`
        SELECT taste, sno, name, price FROM TasteItems
      `),
      pool.request().query(`
        SELECT code, taste_code FROM TasteMap
      `),
      pool.request().query(`
        SELECT code, name, sno FROM cclass
      `),
      pool.request().query(`
        SELECT sets_code, sets_title, c_min, c_max, type, mcq, sort FROM setsgroup
      `),
      pool.request().query(`
        SELECT sets, code, sno, name, price, sname, ISNULL(unit, '份') as unit FROM SetsItems
      `),
      pool.request().query(`
        SELECT code, sets_code, sname FROM SetsMap
      `),
    ]);

    // 處理規格
    const specMap = {};
    menuS.recordset.forEach((s) => {
      if (!specMap[s.code]) specMap[s.code] = [];
      specMap[s.code].push({ name: s.name, price: s.oprice });
    });

    // 處理口味群組 + 選項
    const groupMap = {}; // taste_code => group object
    tasteGroups.recordset.forEach((g) => {
      groupMap[g.taste_code] = {
        taste_code: g.taste_code,
        taste_title: g.taste_title,
        c_min: g.c_min,
        c_max: g.c_max,
        items: [],
        sort: g.sort || 0,
      };
    });

    // 加入子項目進去
    tasteItems.recordset.forEach((ti) => {
      if (groupMap[ti.taste]) {
        groupMap[ti.taste].items.push({
          name: ti.name,
          price: ti.price,
          sno: ti.sno,
        });
      }
    });

    // 建立 code -> taste 群組清單的 Map
    const tasteMapByCode = {};
    tasteMap.recordset.forEach(({ code, taste_code }) => {
      if (!tasteMapByCode[code]) tasteMapByCode[code] = [];
      if (groupMap[taste_code]) {
        // 深拷貝避免多筆共用同一個 array reference
        const clone = JSON.parse(JSON.stringify(groupMap[taste_code]));
        clone.items.sort((a, b) => a.sno - b.sno);
        tasteMapByCode[code].push(clone);
      }
    });
    // 處理套餐群組 + 選項
    const setsGroupMap = {}; // sets_code => group object
    setsGroups.recordset.forEach((g) => {
      const cleanSetsCode = g.sets_code.trim();
      setsGroupMap[cleanSetsCode] = {
        sets_code: cleanSetsCode,
        sets_title: g.sets_title.trim(),
        c_min: g.c_min,
        c_max: g.c_max,
        type: g.type,
        mcq: g.mcq,
        items: [],
        sort: g.sort || 0,
      };
    });

    // 加入套餐子項目
    setsItems.recordset.forEach((si) => {
      const cleanSets = si.sets.trim();
      if (setsGroupMap[cleanSets]) {
        setsGroupMap[cleanSets].items.push({
          code: si.code.trim(),
          name: si.name.trim(),
          price: si.price,
          sno: si.sno,
          sname: si.sname ? si.sname.trim() : null,
          unit: si.unit || "份",
        });
      }
    });

    // 建立 code -> sets 群組清單的 Map
    const setsMapByCode = {};
    setsMap.recordset.forEach(({ code, sets_code, sname }) => {
      // 使用 code 作為主鍵（去除空格）
      const cleanCode = code.trim();
      const cleanSetsCode = sets_code.trim();

      if (!setsMapByCode[cleanCode]) setsMapByCode[cleanCode] = [];
      if (setsGroupMap[cleanSetsCode]) {
        // 深拷貝避免多筆共用同一個 array reference
        const clone = JSON.parse(JSON.stringify(setsGroupMap[cleanSetsCode]));
        clone.items.sort((a, b) => a.sno - b.sno);
        // 將規格信息放入套餐對象
        clone.sname = sname ? sname.trim() : null;
        setsMapByCode[cleanCode].push(clone);
      }
    });

    // 處理分類
    const classMap = {};
    categories.recordset.forEach((c) => {
      classMap[c.code] = {
        name: c.name,
        sno: Number(c.sno) || 999,
      };
    });

    // 組合菜單
    const result = menuM.recordset.map((item) => {
      const itemCode = item.code.trim();
      const itemSets = setsMapByCode[itemCode] || [];

      return {
        code: item.code,
        name: item.name,
        unit: item.unit,
        category: classMap[item.cclass] || { name: "未分類", sno: 999 },
        categorySno: classMap[item.cclass]?.sno ?? 999,
        specs: specMap[item.code] || [],
        tastes: tasteMapByCode[item.code] || [],
        sets: itemSets,
        sortKey: (Number(item.k1) || 99) * 100 + (Number(item.k2) || 99),
      };
    });

    result.sort((a, b) => a.sortKey - b.sortKey);

    return res.json({ success: true, data: result });
  } catch (err) {
    console.error("❌ getInternalMenu 錯誤:", err.message);
    return res.status(500).json({ success: false, message: "內部錯誤" });
  }
};

const getOrderHistory = async (req, res) => {
  const { scode, branch } = req.query;

  if (!scode || !branch) {
    return res
      .status(400)
      .json({ success: false, message: "❌ 缺少參數 scode 或 cod_cust" });
  }
  try {
    // ✅ 自動查 IP → 建連線池
    const pool = await getPoolByBranch(branch);

    const result = await pool.request().input("scode", sql.VarChar, scode)
      .query(`
        SELECT 
          name, 
          SUM(qty) AS qty, 
          sname, 
          oremname, 
          oprice,
          MIN(sno) AS min_sno
        FROM OrderMainN
        WHERE scode = @scode
        GROUP BY name, sname, oremname, oprice
        ORDER BY min_sno
      `);

    res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ 查詢點餐紀錄失敗:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

module.exports = {
  getInternalMenu,
  getOrderHistory,
};
