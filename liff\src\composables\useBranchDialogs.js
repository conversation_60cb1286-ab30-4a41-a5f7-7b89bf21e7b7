import { ref, computed } from "vue";

export function useBranchDialogs() {
  // 對話框狀態
  const branchDialog = ref(false);
  const addPageDialog = ref(false);
  const searchQuery = ref("");
  const filterActive = ref(true); // 預設為 true
  const newBranchId = ref("");

  // 計算屬性：篩選後的門市列表
  const getFilteredBranches = (branches) => {
    let result = branches || [];

    // 搜尋篩選
    if (searchQuery.value) {
      result = result.filter(
        (branch) =>
          (branch?.Cod_name || "").includes(searchQuery.value) ||
          (branch?.Cod_cust || "").includes(searchQuery.value)
      );
    }

    // 狀態篩選
    if (filterActive.value) {
      result = result.filter((branch) => branch.Sts === "1");
    }

    return result;
  };

  // 對話框控制函數
  const openBranchDialog = () => {
    branchDialog.value = true;
  };

  const closeBranchDialog = () => {
    branchDialog.value = false;
    resetSearch();
  };

  const openAddDialog = () => {
    addPageDialog.value = true;
    newBranchId.value = "";
  };

  const closeAddDialog = () => {
    addPageDialog.value = false;
    newBranchId.value = "";
  };

  const resetSearch = () => {
    searchQuery.value = "";
    // 不重置 filterActive，保持其當前值
  };

  // 驗證函數
  const validateBranchId = (branchId) => {
    return branchId && branchId.length === 6;
  };

  return {
    // 響應式狀態
    branchDialog,
    addPageDialog,
    searchQuery,
    filterActive,
    newBranchId,

    // 計算屬性
    getFilteredBranches,

    // 對話框控制
    openBranchDialog,
    closeBranchDialog,
    openAddDialog,
    closeAddDialog,
    resetSearch,

    // 驗證
    validateBranchId,
  };
}
