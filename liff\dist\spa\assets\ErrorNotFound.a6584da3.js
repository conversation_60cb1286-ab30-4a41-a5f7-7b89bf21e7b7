import{ae as o,af as s,ag as t,f as a,ah as l}from"./index.036468ba.js";const r={class:"fullscreen bg-blue text-white text-center q-pa-md flex flex-center"},d=Object.assign({name:"ErrorNotFound"},{__name:"ErrorNotFound",setup(n){return(c,e)=>(o(),s("div",r,[t("div",null,[e[0]||(e[0]=t("div",{style:{"font-size":"30vh"}}," 404 ",-1)),e[1]||(e[1]=t("div",{class:"text-h2",style:{opacity:".4"}}," Oops. Nothing here... ",-1)),a(l,{class:"q-mt-xl",color:"white","text-color":"blue",unelevated:"",to:"/",label:"Go Home","no-caps":""})])]))}});export{d as default};
