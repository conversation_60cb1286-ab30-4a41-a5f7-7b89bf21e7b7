<template>
  <q-page
    class="q-pa-sm flex justify-center items-start"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 主卡片容器 -->
    <q-card
      class="no-shadow"
      style="width: 100%; border-radius: 10px; overflow: hidden"
    >
      <!-- 標題區域 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">業績總覽</div>
          <div>
            <q-btn flat round size="sm" color="red" @click="refreshData">
              <refresh-ccw :stroke-width="1" :size="18" />
              <q-tooltip>重新整理</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <!-- 統計卡片區域 -->
      <q-card-section class="q-pa-sm">
        <div class="row q-col-gutter-sm">
          <!-- 今日營收卡片 -->
          <div class="col-4">
            <div
              class="stats-card today-card"
              v-touch-hold="() => (showDailyDialog = true)"
              @mouseenter="showDailyDialog = true"
              @mouseleave="showDailyDialog = false"
              style="cursor: pointer"
            >
              <div class="stats-icon">
                <calendar-1 :stroke-width="1" :size="22" />
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ dailyTotal ? (dailyTotal / 10000).toFixed(1) : "0.0" }}
                  <span class="text-caption">萬</span>
                </div>
                <div class="stats-label">今日營收</div>
              </div>
              <q-tooltip v-if="!isMobile" v-model="showDailyDialog">
                {{ dailyTotal ? dailyTotal.toLocaleString() : "0" }} 元
              </q-tooltip>
              <q-dialog v-if="isMobile" v-model="showDailyDialog">
                <q-card>
                  <q-card-section>
                    {{ dailyTotal ? dailyTotal.toLocaleString() : "0" }} 元
                  </q-card-section>
                </q-card>
              </q-dialog>
            </div>
          </div>

          <!-- 本月營收卡片 -->
          <div class="col-4">
            <div
              class="stats-card monthly-revenue-card"
              v-touch-hold="() => (showMonthDialog = true)"
              @mouseenter="showMonthDialog = true"
              @mouseleave="showMonthDialog = false"
              style="cursor: pointer"
            >
              <div class="stats-icon">
                <calendar-days :stroke-width="1" :size="22" />
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ (total / 10000).toFixed(1) }}
                  <span class="text-caption">萬</span>
                </div>
                <div class="stats-label">本月營收</div>
              </div>
              <q-tooltip v-if="!isMobile" v-model="showMonthDialog">
                {{ total.toLocaleString() }} 元
              </q-tooltip>
              <q-dialog v-if="isMobile" v-model="showMonthDialog">
                <q-card>
                  <q-card-section>
                    {{ total.toLocaleString() }} 元
                  </q-card-section>
                </q-card>
              </q-dialog>
            </div>
          </div>

          <!-- 平均營收卡片 -->
          <div class="col-4">
            <div class="stats-card avg-card">
              <div class="stats-icon">
                <chart-no-axes-combined :stroke-width="1" :size="22" />
              </div>
              <div class="stats-content">
                <div class="stats-value">
                  {{ calculateAverage() }}
                  <span class="text-caption">萬</span>
                </div>
                <div class="stats-label">日均營收</div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Tab 切換 -->
      <q-tabs
        v-model="activeTab"
        class="text-grey q-mb-sm q-pt-md"
        dense
        active-color="blue"
        indicator-color="blue"
        align="justify"
        narrow-indicator
        no-caps
        inline-label
      >
        <q-tab name="chart" class="q-py-xs"
          ><chart-no-axes-column-increasing
            :stroke-width="1"
            :size="22"
            class="q-mr-xs"
          />圖表</q-tab
        >
        <q-tab name="day" class="q-py-xs"
          ><list :stroke-width="1" :size="22" class="q-mr-xs" />明細</q-tab
        >
        <q-tab name="filter" class="q-py-xs"
          ><funnel :stroke-width="1" :size="22" class="q-mr-xs" />篩選</q-tab
        >
      </q-tabs>

      <q-tab-panels v-model="activeTab" animated>
        <!-- 圖表面板 -->
        <q-tab-panel name="chart" class="q-pa-none">
          <!-- 載入中狀態 -->
          <div v-if="isLoading" class="fancy-loader">
            <q-spinner size="40px" color="grey-5" />
            <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
              載入數據中...
            </div>
          </div>

          <!-- 圖表顯示 -->
          <q-card-section v-else>
            <div class="chart-container">
              <v-chart :option="chartOptions" autoresize v-if="chartOptions" />
            </div>

            <!-- 圖表下方資訊 -->
            <div class="row justify-between items-center q-mt-md">
              <div class="text-caption text-grey">
                {{ storeLabel }} - {{ month }}
              </div>
              <div class="text-weight-medium text-green">
                合計：{{ (total / 10000).toFixed(1) }} 萬元
              </div>
            </div>
          </q-card-section>
        </q-tab-panel>

        <!-- 每日明細面板 -->
        <q-tab-panel name="day" class="q-pa-none">
          <!-- 日期選擇器 -->
          <q-card-section class="row items-center q-gutter-sm q-pb-md">
            <q-btn flat round size="sm" color="primary" @click="adjustDate(-1)">
              <chevron-left :stroke-width="1.5" :size="22" />
            </q-btn>
            <q-input
              v-model="selectedDate"
              dense
              outlined
              square
              mask="####-##-##"
              readonly
              class="col"
            >
              <template v-slot:prepend>
                <calendar-days :stroke-width="1" :size="22" />
              </template>
              <template v-slot:append>
                <calendar-fold :stroke-width="1" :size="20" color="blue" />
                <q-popup-proxy
                  ref="datePopup"
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date
                    v-model="selectedDate"
                    mask="YYYY-MM-DD"
                    @update:model-value="closeDatePopup"
                  >
                  </q-date>
                </q-popup-proxy>
              </template>
            </q-input>
            <q-btn flat round size="sm" color="primary" @click="adjustDate(1)">
              <chevron-right :stroke-width="1.5" :size="22" />
            </q-btn>
          </q-card-section>

          <!-- 載入中狀態 -->

          <div v-if="isLoading" class="fancy-loader">
            <q-spinner size="40px" color="grey-5" />
            <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
              載入中...
            </div>
          </div>

          <!-- 無資料顯示 -->
          <q-card-section
            v-else-if="!dailySales.data || dailySales.data.length === 0"
            class="empty-state-container"
          >
            <div class="empty-state">
              <svg class="empty-icon" viewBox="0 0 24 24">
                <path
                  d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
                />
              </svg>
              <div class="empty-text">查無營收資料</div>
              <div class="empty-subtext">請嘗試選擇不同的日期</div>
            </div>
          </q-card-section>

          <!-- 資料顯示 -->
          <q-card-section v-else class="q-pa-none">
            <!-- 分組資料 -->
            <template v-for="(group, brand) in groupedSales" :key="brand">
              <div
                class="text-white text-subtitle2 q-px-sm q-py-xs"
                :class="brandColorClass(brand)"
              >
                {{ brand }}
              </div>
              <q-list dense separator bordered style="background-color: #fff">
                <q-item
                  v-for="item in group"
                  :key="item.cod_cust"
                  class="q-py-xs"
                >
                  <!-- 門市名稱 -->
                  <q-item-section>
                    <q-item-label class="text-dark">
                      {{ item.name.trim() }}
                    </q-item-label>
                  </q-item-section>

                  <!-- 金額數字 -->
                  <q-item-section side class="items-center">
                    <q-item-label class="text-right text-grey-8">
                      {{ item.amount.toLocaleString() }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </template>

            <!-- 更新時間 -->
            <div
              v-if="updateTime"
              class="text-caption text-center text-grey q-mt-md"
            >
              資料更新時間：{{ updateTime }}
            </div>
          </q-card-section>
        </q-tab-panel>

        <!-- 篩選面板 -->
        <q-tab-panel name="filter" class="q-pa-md">
          <div class="row q-col-gutter-md">
            <!-- 門市選擇 -->
            <div class="col-12 col-md-6">
              <q-select
                v-model="selectedStore"
                :options="branchOptions"
                label="選擇門市"
                outlined
                emit-value
                map-options
                dense
              >
                <template v-slot:prepend>
                  <store :stroke-width="1" :size="22" />
                </template>
              </q-select>
            </div>

            <!-- 月份選擇 -->
            <div class="col-12 col-md-6">
              <div class="row items-center q-gutter-sm">
                <q-btn
                  flat
                  round
                  size="sm"
                  color="primary"
                  @click="adjustMonth(-1)"
                >
                  <chevron-left :stroke-width="1.5" :size="22" />
                </q-btn>

                <q-input
                  v-model="pendingMonth"
                  label="選擇月份"
                  mask="####-##"
                  outlined
                  dense
                  class="col"
                  input-class="text-center"
                >
                  <template v-slot:prepend>
                    <calendar-days :stroke-width="1" :size="22" />
                  </template>
                </q-input>
                <q-btn
                  flat
                  round
                  size="sm"
                  color="primary"
                  @click="adjustMonth(1)"
                >
                  <chevron-right :stroke-width="1.5" :size="22" />
                </q-btn>
              </div>
            </div>

            <!-- 套用按鈕 -->
            <div class="col-12 flex justify-end">
              <q-btn
                outline
                color="deep-orange"
                label="套用篩選"
                @click="applyFilters"
              />
            </div>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, onMounted, watch, computed } from "vue";
import { QSpinner } from "quasar";
import { DateTime } from "luxon";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 狀態變數
const activeTab = ref("day");
const isLoading = ref(false);
const dailySales = ref({ data: [] });
const dailyTotal = ref(0);
const month = ref(new Date().toISOString().slice(0, 7));
const pendingMonth = ref(month.value); // 新增暫存月份
const selectedStore = ref("all");
const branchOptions = ref([{ label: "全部", value: "all" }]);
const chartOptions = ref(null);
const total = ref(0);
const today = new Date().toISOString().slice(0, 10);
const selectedDate = ref(today);
const updateTime = ref(null);
const datePopup = ref(null);
const monthPopup = ref(null);
const isMobile = computed(() =>
  /Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent)
);
const showDailyDialog = ref(false);
const showMonthDialog = ref(false);

// 計算屬性
const storeLabel = computed(() => {
  if (selectedStore.value === "all") return "集團合計";
  const found = branchOptions.value.find(
    (b) => b.value === selectedStore.value
  );
  return found?.label || selectedStore.value;
});

// 群組分法
const groupedSales = computed(() => {
  const groups = {
    小蒙牛: [],
    金大鋤: [],
    燒肉老大: [],
    其他: [],
  };

  for (const item of dailySales.value.data || []) {
    if (item.name.includes("小蒙牛")) {
      groups["小蒙牛"].push(item);
    } else if (item.name.includes("金大鋤")) {
      groups["金大鋤"].push(item);
    } else if (item.name.includes("燒肉老大")) {
      groups["燒肉老大"].push(item);
    } else {
      groups["其他"].push(item);
    }
  }

  // 各群組內按金額排序（大 → 小）
  for (const brand in groups) {
    groups[brand].sort((a, b) => b.amount - a.amount);
  }

  return groups;
});

// 群組顏色 class
const brandColorClass = (brand) => {
  switch (brand) {
    case "小蒙牛":
      return "bg-deep-orange-8";
    case "金大鋤":
      return "bg-blue-10";
    case "燒肉老大":
      return "bg-amber-6";
    default:
      return "bg-grey-5";
  }
};

// 計算日均營收
const calculateAverage = () => {
  if (
    !chartOptions.value ||
    !chartOptions.value.series ||
    !chartOptions.value.series[0].data
  ) {
    return "0.0";
  }

  const data = chartOptions.value.series[0].data;
  const validData = data.filter((val) => val > 0);

  if (validData.length === 0) return "0.0";

  const avg = validData.reduce((sum, val) => sum + val, 0) / validData.length;
  return (avg / 10000).toFixed(1);
};

// 日期調整
const adjustDate = (offset) => {
  const newDate = DateTime.fromISO(selectedDate.value).plus({ days: offset });
  selectedDate.value = newDate.toISODate();

  // 檢查是否跨越月份，如果是則同時更新月份資料
  const currentMonth = month.value;
  const newMonth = newDate.toFormat("yyyy-MM");

  if (newMonth !== currentMonth) {
    month.value = newMonth;
    pendingMonth.value = newMonth;
    loadChart(); // 更新圖表和統計卡片
  }
};

// 月份調整
const adjustMonth = (offset) => {
  const currentDate = DateTime.fromISO(`${pendingMonth.value}-01`);
  const newDate = currentDate.plus({ months: offset });
  pendingMonth.value = newDate.toFormat("yyyy-MM");
};

// 關閉日期選擇器
const closeDatePopup = () => {
  datePopup.value?.hide();
};

// 開啟月份選擇器
const openMonthPicker = () => {
  monthPopup.value?.show();
};

// 關閉月份選擇器
const closeMonthPopup = () => {
  monthPopup.value?.hide();
};

// 載入每日銷售數據
const loadDailySales = async (date) => {
  isLoading.value = true;
  try {
    const res = await apiClient.get(`${apiBaseUrl}/sales/get_DailySales`, {
      params: { date },
    });
    dailySales.value = res.data || { data: [] };
    dailyTotal.value = dailySales.value.total || 0;
    updateTime.value = res.data.updateTime || null;

    // 檢查是否跨越月份，如果是則同時更新月份資料
    const dateMonth = date.substring(0, 7);
    if (dateMonth !== month.value) {
      month.value = dateMonth;
      pendingMonth.value = dateMonth;
      loadChart(); // 更新圖表和統計卡片
    }
  } catch (err) {
    console.error("載入本日業績失敗", err);
    updateTime.value = null;
    dailySales.value = { data: [] };
    dailyTotal.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 載入門市選單
const fetchBranches = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/branch/get_branch`);
    const options = response.data
      .filter((branch) => branch.Sts === "1")
      .map((branch) => ({
        value: branch.Cod_cust.trim(),
        label: branch.Cod_name.trim(),
      }));
    branchOptions.value = [{ label: "全部", value: "all" }, ...options];
  } catch (error) {
    console.error("獲取門市資料失敗:", error);
  }
};

// 載入圖表資料
const loadChart = async () => {
  isLoading.value = true;
  try {
    const params = { month: month.value };
    if (selectedStore.value !== "all") {
      params.cod_cust = selectedStore.value;
    }

    const res = await apiClient.get(`${apiBaseUrl}/sales/get_MonthlySales`, {
      params,
    });
    const data = res.data;
    total.value = data.reduce((sum, d) => sum + d.amount, 0);

    // 設定圖表選項
    chartOptions.value = {
      title: {
        text: `${storeLabel.value} - ${month.value} 營收趨勢`,
        left: "center",
        top: 10,
        textStyle: {
          fontSize: 16,
          fontWeight: "normal",
        },
      },
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${(
            data.value / 10000
          ).toFixed(1)}萬元`;
        },
      },
      grid: {
        top: 60,
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: data.map((d) => d.date.substring(5)), // 只顯示月-日
        axisLabel: {
          interval: "auto",
          rotate: 0,
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: function (value) {
            return (value / 10000).toFixed(0) + "萬";
          },
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
      },
      series: [
        {
          name: "營收",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 6,
          itemStyle: {
            color: "#4caf50",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(76, 175, 80, 0.3)" },
                { offset: 1, color: "rgba(76, 175, 80, 0.05)" },
              ],
            },
          },
          data: data.map((d) => d.amount),
        },
      ],
    };
  } catch (err) {
    console.error("載入圖表資料失敗", err);
    chartOptions.value = null;
    total.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 刷新數據
const refreshData = () => {
  if (activeTab.value === "chart" || activeTab.value === "filter") {
    loadChart();
  } else if (activeTab.value === "day") {
    loadDailySales(selectedDate.value);
  }
};

// 套用篩選
const applyFilters = () => {
  month.value = pendingMonth.value;
  loadChart();
  activeTab.value = "chart";
};

// 監聽器
watch([selectedStore, month], () => {
  if (activeTab.value === "filter") return;
  loadChart();
});

watch(activeTab, (tab) => {
  if (tab === "day") {
    loadDailySales(selectedDate.value);
  } else if (tab === "chart") {
    loadChart();
  }
});

watch(selectedDate, (date) => {
  if (activeTab.value === "day" && date) {
    loadDailySales(date);
  }
});

// 初始載入
onMounted(() => {
  fetchBranches();
  loadChart();
  loadDailySales(selectedDate.value);
});
</script>

<style scoped>
.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

.chart-container {
  width: 100%;
  height: 400px;
  padding-left: 4px;
}

/* 統計卡片樣式 */
.stats-card {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eaeaea;
  transition: all 0.3s ease;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 6px;
}

.stats-content {
  flex: 1;
  min-width: 0; /* 確保文字可以正確縮減 */
}

.stats-value {
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stats-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 卡片特定樣式 */
.today-card::before {
  background: linear-gradient(180deg, #a5d6a7 0%, #4caf50 100%);
}

.today-card .stats-icon {
  color: #4caf50;
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}

.monthly-revenue-card::before {
  background: linear-gradient(180deg, #90caf9 0%, #1976d2 100%);
}

.monthly-revenue-card .stats-icon {
  color: #1976d2;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.avg-card::before {
  background: linear-gradient(180deg, #ffcc80 0%, #ff9800 100%);
}

.avg-card .stats-icon {
  color: #ff9800;
  background: linear-gradient(135deg, #fff8e1 0%, #ffe082 100%);
}

/* 載入動畫樣式 */
.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #4caf50;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 0.5rem;
}

.empty-text {
  font-size: 1rem;
  color: #616161;
  margin-bottom: 0.25rem;
}

.empty-subtext {
  font-size: 0.8rem;
  color: #9e9e9e;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}

/* 響應式調整 */
@media (max-width: 599px) {
  .stats-card {
    padding: 8px 6px;
  }

  .stats-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  .stats-value {
    font-size: 14px;
    margin-bottom: 0;
  }

  .stats-label {
    font-size: 10px;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
