const express = require("express");
const {
  getUserByUID,
  getEnableUsers,
  getAllDepartment,
  getAllUsers,
  updateUser,
  getUsersGroup,
  createUsersgroup,
  updateUsersgroup,
  deleteUsersgroup,
  getUserBranch,
  saveUserBranch,
  getUserAccess,
  saveUserAccess,
} = require("../controllers/userController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.post("/users", authMiddleware, getUserByUID); // ✅ 設定 `/api/users`
router.post("/get_enable_users", authMiddleware, getEnableUsers); // ✅ 設定 `/api/users`
router.get("/get_all_department", authMiddleware, getAllDepartment); // ✅ 設定 `/api/users`
router.get("/get_all_users", authMiddleware, getAllUsers); // ✅ 設定 `/api/users`
router.put("/updateuser", authMiddleware, updateUser); // ✅ 設定 `/api/users`
router.post("/save_user", authMiddleware, updateUser); // 添加新路由，映射到相同的處理函數
router.get("/get_usersgroup", authMiddleware, getUsersGroup);
router.post("/usersgroup", authMiddleware, createUsersgroup);
router.put("/usersgroup/:Code", authMiddleware, updateUsersgroup);
router.delete("/usersgroup/:Code", authMiddleware, deleteUsersgroup);
router.get("/get_userBranch", authMiddleware, getUserBranch);
router.post("/save_UserBranch", authMiddleware, saveUserBranch);
router.get("/get_user_access", authMiddleware, getUserAccess);
router.post("/save_user_access", authMiddleware, saveUserAccess);

module.exports = router;
