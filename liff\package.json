{"name": "liff", "version": "0.0.1", "description": "A Quasar Project", "productName": "Quasar App", "author": "", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@fullcalendar/vue3": "^6.1.18", "@line/liff": "^2.25.1", "@quasar/extras": "^1.16.4", "@tiptap/extension-color": "^3.0.1", "@tiptap/extension-font-family": "^3.0.1", "@tiptap/extension-image": "^3.0.1", "@tiptap/extension-text-align": "^3.0.1", "@tiptap/extension-text-style": "^3.0.1", "@tiptap/extension-typography": "^3.0.1", "@tiptap/extension-underline": "^3.0.1", "@tiptap/starter-kit": "^3.0.1", "@tiptap/vue-3": "^3.0.1", "axios": "^1.2.1", "basic-ftp": "^5.0.5", "bcryptjs": "^3.0.0", "config": "^3.3.12", "cors": "^2.8.5", "dotenv": "^16.4.7", "echarts": "^5.6.0", "escpos": "^3.0.0-alpha.6", "escpos-network": "^3.0.0-alpha.5", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-vue-next": "^0.525.0", "luxon": "^3.5.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "nodemailer": "^6.10.0", "php-unserialize": "^0.0.1", "ping": "^0.4.4", "pinia": "^2.3.1", "process": "^0.11.10", "quasar": "^2.8.0", "vue": "^3.4.18", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "^9.0.0", "vue-router": "^4.0.12", "vuedraggable": "^2.24.3", "vuex": "^4.0.1", "zkteco-js": "^1.7.1"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.8.0", "@stagewise-plugins/vue": "^0.6.0", "@stagewise/toolbar-vue": "^0.6.0", "autoprefixer": "^10.4.2", "eslint": "^8.11.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.14", "prettier": "^2.5.1", "vite-plugin-checker": "^0.6.4"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}