<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="width: 100%; max-width: 500px; border-radius: 12px"
    >
      <!-- 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        實際編號管理
      </q-card-section>

      <!-- 實際編號清單 -->
      <q-list separator>
        <div
          v-for="item in rmnameListSorted"
          :key="item.code"
          class="bg-white q-mb-sm"
          style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
        >
          <q-expansion-item
            expand-separator
            class="q-pa-none"
            header-class="text-indigo"
          >
            <!-- 主列 -->
            <template #header>
              <q-item
                dense
                class="q-pa-none q-gutter-none items-center"
                style="width: 100%; position: relative"
              >
                <q-item-section>
                  <div class="text-indigo-10">{{ item.name }}</div>
                  <div class="text-caption text-grey-8">
                    <span class="text-brown-10">{{ item.code }}</span>
                    | 金額：<span
                      :class="item.n1 >= 0 ? 'text-green-8' : 'text-negative'"
                      >{{ item.n1 }}</span
                    >
                  </div>
                </q-item-section>
                <q-item-section side />
                <!-- 刪除按鈕：僅當無商品使用時可點擊 -->
                <q-btn
                  flat
                  dense
                  size="sm"
                  icon="delete"
                  color="negative"
                  @click.stop="confirmDelete(item)"
                  :disable="getProductsByRcode(item.code).length > 0"
                  :title="
                    getProductsByRcode(item.code).length > 0
                      ? '有商品使用，無法刪除'
                      : '刪除'
                  "
                  style="
                    position: absolute;
                    right: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    z-index: 1;
                  "
                />
              </q-item>
            </template>

            <!-- 展開內容：對應商品 -->
            <q-card-section class="q-pa-sm q-pt-none bg-grey-1">
              <div
                class="text-subtitle2 text-primary q-pa-sm q-mb-sm rounded-borders row items-center"
              >
                <q-icon name="shopping_cart" class="q-mr-sm" />
                商品編號
              </div>
              <q-list bordered separator dense>
                <q-item
                  v-for="prod in getProductsByRcode(item.code)"
                  :key="prod.code"
                >
                  <q-item-section>{{ prod.code }}</q-item-section>
                  <q-item-section>{{ prod.name }}</q-item-section>
                </q-item>
                <div
                  v-if="getProductsByRcode(item.code).length === 0"
                  class="text-grey text-caption q-pa-sm"
                >
                  無對應商品
                </div>
              </q-list>
            </q-card-section>
          </q-expansion-item>
        </div>
        <!-- 新增按鈕放在清單最下方 -->
        <div class="q-mt-md flex flex-center">
          <q-btn
            color="green"
            icon="add"
            label="新增實際編號"
            @click="openAddDialog"
            class="full-width"
          />
        </div>
      </q-list>

      <q-dialog v-model="addDialog" persistent no-focus>
        <q-card
          style="max-width: 320px; max-height: 90vh; overflow-y: auto"
          class="q-pa-sm bg-white"
        >
          <q-card-section class="row items-center justify-between q-pb-none">
            <div class="text-h6 text-primary">新增實際編號</div>
            <q-btn icon="close" flat round dense v-close-popup />
          </q-card-section>
          <q-card-section>
            <div class="q-gutter-md column">
              <q-input
                v-model="newRm.code"
                label="實際編號"
                dense
                outlined
                maxlength="10"
              />
              <q-input
                v-model="newRm.name"
                label="名稱"
                dense
                outlined
                maxlength="30"
              />
              <q-input
                v-model.number="newRm.n1"
                label="金額"
                dense
                outlined
                type="number"
              />
            </div>
          </q-card-section>
          <q-card-actions class="q-pt-md q-gutter-sm">
            <q-space />
            <q-btn
              color="secondary"
              label="儲存"
              icon="save"
              @click="addRmname"
              :disable="!canAddRmname"
              outline
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-dialog v-model="deleteDialog.visible" persistent>
        <q-card class="q-pa-md" style="width: 320px">
          <q-card-section class="text-center">
            <q-icon
              name="delete_forever"
              color="negative"
              size="xl"
              class="q-mb-md"
            />
            <div class="text-h6 text-negative">確定刪除？</div>
            <div class="text-body2 text-grey-8 q-mt-sm">
              刪除後無法復原，是否繼續？
            </div>
          </q-card-section>
          <q-separator class="q-mt-md" />
          <q-card-actions class="q-mt-sm row justify-between">
            <q-btn
              label="取消"
              color="grey"
              flat
              class="full-width q-mr-xs"
              v-close-popup
            />
            <q-btn
              label="刪除"
              color="negative"
              unelevated
              class="full-width q-ml-xs"
              @click="deleteRmnameConfirmed"
            />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";

const props = defineProps({ Branch: String });
const $q = useQuasar();
const rmnameList = ref([]);
const productList = ref([]);

const rmnameListSorted = computed(() => {
  return [...rmnameList.value].sort((a, b) => a.code.localeCompare(b.code));
});

const newRm = ref({ code: "", name: "", n1: 0 });
const canAddRmname = computed(() => {
  return (
    newRm.value.code.trim() &&
    newRm.value.name.trim() &&
    typeof newRm.value.n1 === "number"
  );
});

const addDialog = ref(false);
function openAddDialog() {
  newRm.value = { code: "", name: "", n1: 0 };
  addDialog.value = true;
}

function getProductsByRcode(rcode) {
  return productList.value.filter((p) => p.rcode === rcode);
}

async function fetchRmnames() {
  try {
    const res = await apiClient.get("/omenum/get_Rmnames", {
      params: { branch: props.Branch },
    });
    if (res.data?.success) rmnameList.value = res.data.data || [];
  } catch (err) {
    $q.notify({ type: "negative", message: "載入實際編號失敗" });
  }
}

async function fetchProducts() {
  try {
    // 這裡需要查詢所有商品及其 rcode
    const res = await apiClient.get("/omenum/get_Menus", {
      params: { branch: props.Branch },
    });
    if (res.data?.success) {
      // 只保留有 rcode 的商品
      const all = res.data.data || [];
      productList.value = all
        .map((item) => ({
          code: item.code,
          name: item.name,
          rcode: item.rcode,
        }))
        .filter((item) => item.rcode);
    }
  } catch (err) {
    $q.notify({ type: "negative", message: "載入商品失敗" });
  }
}

async function addRmname() {
  if (!canAddRmname.value) return;
  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "primary",
      message: "新增中...",
    });
    const res = await apiClient.post("/omenum/insert_Rmname", {
      branch: props.Branch,
      code: newRm.value.code.trim(),
      name: newRm.value.name.trim(),
      n1: Number(newRm.value.n1),
    });
    if (res.data?.success) {
      $q.notify({ type: "positive", message: "新增成功" });
      addDialog.value = false;
      newRm.value = { code: "", name: "", n1: 0 };
      await fetchRmnames();
    } else {
      $q.notify({ type: "warning", message: res.data.message || "新增失敗" });
    }
  } catch (err) {
    $q.notify({
      type: "negative",
      message: err.response?.data?.message || "新增失敗",
    });
  } finally {
    $q.loading.hide();
  }
}

const deleteDialog = ref({ visible: false, item: null });
function confirmDelete(item) {
  deleteDialog.value = { visible: true, item };
}
async function deleteRmnameConfirmed() {
  const item = deleteDialog.value.item;
  if (!item) return;
  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "negative",
      message: "刪除中...",
    });
    const res = await apiClient.post("/omenum/delete_Rmname", {
      branch: props.Branch,
      code: item.code,
    });
    if (res.data?.success) {
      $q.notify({ type: "positive", message: "刪除成功" });
      deleteDialog.value.visible = false;
      await fetchRmnames();
    } else {
      $q.notify({ type: "warning", message: res.data.message || "刪除失敗" });
    }
  } catch (err) {
    $q.notify({
      type: "negative",
      message: err.response?.data?.message || "刪除失敗",
    });
  } finally {
    $q.loading.hide();
  }
}

onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  await Promise.all([fetchRmnames(), fetchProducts()]);
  $q.loading.hide();
});
</script>
