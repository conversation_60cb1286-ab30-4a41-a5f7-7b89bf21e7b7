const sql = require("mssql");
const dbConfig = require("../config/db");
const sendEmail = require("./sendEmail");

exports.updateApprovalStatus = async (req, res) => {
  const { form_id, approver_id, status, memo, step } = req.body;

  if (!form_id || !approver_id || !status) {
    return res.status(400).json({ error: "❌ 缺少必要參數" });
  }

  try {
    const pool = await sql.connect(dbConfig);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    // ✅ 更新 `Forms_approver`，設定 `status` & `memo` & `Updated_at`
    await transaction
      .request()
      .input("form_id", sql.VarChar, form_id)
      .input("approver_id", sql.VarChar, approver_id)
      .input("status", sql.VarChar, status)
      .input("memo", sql.NVarChar, memo || "") // 避免 `null` 錯誤
      .input("step", sql.NVarChar, step)
      .query(
        `UPDATE Forms_approver 
         SET Status = @status, Memo = @memo, Updated = GETUTCDATE()
         WHERE Form_id = @form_id AND Approver_id = @approver_id AND Step_number=@step `
      );

    // ✅ 查詢 `Forms` 的 `Type` 和 `Uid`（申請人）
    const formResult = await transaction
      .request()
      .input("form_id", sql.VarChar, form_id)
      .query(`SELECT Type, userid FROM Forms WHERE Form_id = @form_id`);

    const formType = formResult.recordset[0]?.Type || "leave";
    const submitterUid = formResult.recordset[0]?.userid;

    if (status === "approve") {
      // ✅ 取得當前 `step_number`
      const stepResult = await transaction
        .request()
        .input("form_id", sql.VarChar, form_id)
        .input("approver_id", sql.VarChar, approver_id)

        .query(
          `SELECT Step_number FROM Forms_approver 
           WHERE Form_id = @form_id AND Approver_id = @approver_id`
        );

      const currentStep = parseInt(step);

      // ✅ 取得 `Forms_flows` 內的最大步驟數
      const flowResult = await transaction
        .request()
        .input("form_id", sql.VarChar, form_id)
        .query(
          `SELECT MAX(Step_number) AS max_step FROM Forms_approver 
           WHERE Form_id = @form_id`
        );

      const maxStep = parseInt(flowResult.recordset[0]?.max_step || 0, 10);
      if (currentStep < maxStep) {
        // ✅ 仍有下一個步驟，將 `下一位簽核人` 設為 `waiting`
        await transaction
          .request()
          .input("form_id", sql.VarChar, form_id)
          .input("next_step", sql.Int, currentStep + 1)
          .query(
            `UPDATE Forms_approver 
             SET Status = 'waiting' 
             WHERE Form_id = @form_id AND Step_number = @next_step`
          );
        await transaction
          .request()
          .input("form_id", sql.VarChar, form_id)
          .query(
            `UPDATE Forms 
             SET Updated = GETUTCDATE() 
             WHERE Form_id = @form_id`
          );

        // ✅ 查詢下一位簽核人的 Notify 欄位是否為 1
        const nextApproverResult = await transaction
          .request()
          .input("form_id", sql.VarChar, form_id)
          .input("next_step", sql.Int, currentStep + 1)
          .query(
            `SELECT FA.Approver_id, FA.Notify, U.Email
             FROM Forms_approver FA
             JOIN Users U ON FA.Approver_id = U.ID
             WHERE FA.Form_id = @form_id AND FA.Step_number = @next_step`
          );

        if (nextApproverResult.recordset.length > 0) {
          const nextApprover = nextApproverResult.recordset[0];
          if (nextApprover.Notify === 1) {
            // 需要發送通知，新增一筆 Forms_notify 記錄
            await transaction
              .request()
              .input("form_id", sql.VarChar, form_id)
              .input("step_number", sql.Int, currentStep + 1)
              .input("approver_id", sql.VarChar, nextApprover.Approver_id)
              .query(
                `INSERT INTO Forms_notify (Form_id, Step_number, Approver_id, Status, Form_status, Created, Sent)
                 VALUES (@form_id, @step_number, @approver_id, 'pending', 'waiting', GETDATE(), NULL)`
              );
          }
        }
      } else {
        // ✅ 已是最後一個步驟，更新 `Forms` 狀態為 `approve`
        await transaction
          .request()
          .input("form_id", sql.VarChar, form_id)
          .query(
            `UPDATE Forms SET Status = 'approve', Updated = GETUTCDATE() 
             WHERE Form_id = @form_id`
          );

        // ✅ 通知表單發送人（申請人）
        if (submitterUid) {
          // 查詢申請人的 Email
          const submitterResult = await transaction
            .request()
            .input("uid", sql.VarChar, submitterUid)
            .query(`SELECT Email, ID FROM Users WHERE id = @uid`);

          if (submitterResult.recordset.length > 0) {
            const submitter = submitterResult.recordset[0];
            // 新增一筆 Forms_notify 記錄
            await transaction
              .request()
              .input("form_id", sql.VarChar, form_id)
              .input("approver_id", sql.VarChar, submitter.ID)
              .input("step_number", sql.NVarChar, "-1")
              .query(
                `INSERT INTO Forms_notify (Form_id, Step_number, Approver_id, Status, Form_status, Created, Sent)
                  VALUES (@form_id, @step_number, @approver_id, 'pending', 'approved', GETUTCDATE(), NULL)`
              );
          }
        }
      }
    } else if (status === "reject") {
      // ✅ 直接將 `Forms.status` 設為 `reject`
      await transaction
        .request()
        .input("form_id", sql.VarChar, form_id)
        .query(
          `UPDATE Forms SET Status = 'reject', Updated = GETUTCDATE()
           WHERE Form_id = @form_id `
        );

      // ✅ 8. 更新 `Forms_approver` 中 **Status = '-'** 的資料為 `"up-reject"`
      await transaction
        .request()
        .input("form_id", sql.VarChar, form_id)
        .query(
          `UPDATE Forms_approver 
        SET Status = 'up-reject'
        WHERE Form_id = @form_id AND Status = '-'`
        );

      await transaction
        .request()
        .input("form_id", sql.VarChar, form_id)
        .query(
          `UPDATE Forms_approver 
        SET Status = 'reject'
        WHERE Form_id = @form_id AND Status = 'waiting'`
        );

      // ✅ 通知表單發送人（申請人）
      if (submitterUid) {
        // 查詢申請人的 Email
        const submitterResult = await transaction
          .request()
          .input("uid", sql.VarChar, submitterUid)
          .query(`SELECT Email, ID FROM Users WHERE id = @uid`);

        if (submitterResult.recordset.length > 0) {
          const submitter = submitterResult.recordset[0];
          // 新增一筆 Forms_notify 記錄
          await transaction
            .request()
            .input("form_id", sql.VarChar, form_id)
            .input("approver_id", sql.VarChar, submitter.ID)
            .input("step_number", sql.NVarChar, "-1")
            .query(
              `INSERT INTO Forms_notify (Form_id, Step_number, Approver_id, Status, Form_status, Created, Sent)
               VALUES (@form_id, @step_number, @approver_id, 'pending', 'rejected', GETUTCDATE(), NULL)`
            );
        }
      }
    }
    await transaction.commit();

    // 簽核狀態更新後，立即執行通知發送
    sendEmail
      .sendPendingNotifications()
      .catch((err) => console.error("❌ 發送郵件時發生錯誤:", err));

    res.json({ status: "success", message: "✅ 簽核狀態更新成功" });
  } catch (err) {
    console.error("❌ 簽核狀態更新失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法更新簽核狀態" });
  }
};

//取得簽核歷程
exports.getApprovalHistory = async (req, res) => {
  const { form_id } = req.body;

  if (!form_id) {
    return res.status(400).json({ error: "❌ 缺少 `form_id`" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 🔹 查詢 `Forms_approver`，關聯 `Users`、`Forms_flows` 表獲取簽核歷程
    const result = await pool
      .request()
      .input("form_id", sql.VarChar, form_id)
      .query(
        `SELECT 
              FA.Approver_id, 
              U.Name AS Approver,
              FA.Status, 
              FA.Updated AS Approval_time, 
              FA.Memo AS Comment,
              FA.Step_name AS Remark
           FROM Forms_approver FA
           LEFT JOIN Users U ON FA.Approver_id = U.ID
           WHERE FA.Form_id = @form_id
           ORDER BY FA.Step_number ASC`
      );

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 查詢簽核歷程失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法查詢簽核歷程" });
  }
};
exports.getSMainData = async (req, res) => {
  const { form_id } = req.body;
  if (!form_id) {
    return res.status(400).json({ error: "缺少 form_id 參數" });
  }

  try {
    await sql.connect(dbConfig);

    //console.log(`🔍 查詢主檔: ${form_id}`);

    const result = await sql.query(`
      SELECT 
          f.Form_id,
          f.Type,
          u.Name AS Submitter,
          u.ID AS Submitter_id,
          n.Name AS Doc_name,
          f.Status
      FROM Forms f
      LEFT JOIN Forms_name n ON f.Type = n.Type
      LEFT JOIN Users u ON f.userid = u.id
      WHERE f.Form_id = '${form_id}';
    `);

    if (result.recordset.length === 0) {
      return res.status(404).json({ error: "未找到該單證" });
    }

    //console.log("✅ 取得主檔:", result.recordset[0]);
    res.json(result.recordset[0]);
  } catch (error) {
    console.error("❌ SQL 查詢錯誤:", error);
    res.status(500).json({ error: "內部伺服器錯誤", details: error.message });
  } finally {
  }
};

exports.getSDetails = async (req, res) => {
  const { form_id, form_type } = req.body;
  if (!form_id || !form_type) {
    return res.status(400).json({ error: "缺少 form_id 或 form_type 參數" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 查詢明細資料
    const detailResult = await pool
      .request()
      .input("form_id", sql.VarChar, form_id).query(`
        SELECT * FROM Form_${form_type.trim()} WHERE Form_id = @form_id;
      `);

    if (detailResult.recordset.length === 0) {
      return res.status(404).json({ error: "未找到明細資料" });
    }

    // 查詢附件數量
    const attachmentResult = await pool
      .request()
      .input("form_id", sql.VarChar, form_id).query(`
        SELECT COUNT(*) as attachment_count 
        FROM Forms_attachments 
        WHERE Form_id = @form_id
      `);

    // 合併明細資料和附件數量
    const responseData = {
      ...detailResult.recordset[0],
      attachment: attachmentResult.recordset[0].attachment_count,
    };

    res.json(responseData);
  } catch (error) {
    console.error("❌ SQL 查詢錯誤:", error);
    res.status(500).json({ error: "內部伺服器錯誤", details: error.message });
  }
};

exports.getFormTypes = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool.request().query(`
      SELECT Type, Name ,Color FROM Forms_name ORDER BY sno
    `);

    res.json(result.recordset);
  } catch (error) {
    console.error("❌ 查詢表單類型失敗:", error);
    res.status(500).json({ error: "❌ 無法獲取表單類型" });
  } finally {
  }
};

exports.getFormFields = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool.request().query(`
      SELECT Form_Type, Field_Name, Display_Name 
      FROM Forms_detail
    `);

    const detailFieldMapping = {}; // 欄位名稱對應
    const visibleFields = {}; // 各表單的可見欄位

    result.recordset.forEach((row) => {
      // 存入 detailFieldMapping
      if (!detailFieldMapping[row.Field_Name.trim()]) {
        detailFieldMapping[row.Field_Name.trim()] = row.Display_Name.trim();
      }

      // 存入 visibleFields
      if (!visibleFields[row.Form_Type.trim()]) {
        visibleFields[row.Form_Type.trim()] = [];
      }
      visibleFields[row.Form_Type.trim()].push(row.Field_Name.trim());
    });

    res.json({ detailFieldMapping, visibleFields });
  } catch (error) {
    console.error("❌ 查詢表單欄位失敗:", error);
    res.status(500).json({ error: "內部錯誤" });
  }
};

exports.cancelform = async (req, res) => {
  const { form_id, status } = req.body;

  if (!form_id || !status) {
    return res.status(400).json({ error: "表單 ID 和狀態不可為空" });
  }

  try {
    // 連接 MSSQL
    const pool = await sql.connect(dbConfig);

    // 開始交易
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    // 更新 forms 表的 status 為 'cancel'
    await transaction
      .request()
      .input("form_id", sql.VarChar, form_id)
      .input("status", sql.VarChar, status)
      .query(`UPDATE forms SET status = @status WHERE Form_id = @form_id`);

    // 更新 Forms_approver 表的 status 為 'cancel'
    await transaction
      .request()
      .input("form_id", sql.VarChar, form_id)
      .input("status", sql.VarChar, status)
      .query(
        `UPDATE Forms_approver SET Status = @status WHERE Form_id = @form_id`
      );

    // 提交交易
    await transaction.commit();

    res.json({ success: true, message: "表單撤銷成功" });
  } catch (error) {
    console.error("撤銷表單失敗:", error);

    // 回滾交易
    if (transaction) await transaction.rollback();

    res.status(500).json({ error: "撤銷表單時發生錯誤" });
  }
};

exports.getFormFlows = async (req, res) => {
  try {
    const { form_type } = req.query;
    if (!form_type) {
      return res.status(400).json({ error: "缺少表單類型 (form_type)" });
    }

    const pool = await sql.connect(dbConfig); // 取得連線池
    const result = await pool
      .request()
      .input("form_type", sql.NVarChar, form_type) // 用 @ 來處理變數
      .query(`
        SELECT Step_number, approver_role, approver_id, Remark, 
               CAST(Notify AS INT) AS Notify  -- 把 BIT 轉成 INT
        FROM Forms_Flows 
        WHERE Form_type = @form_type
        ORDER BY Step_number ASC
      `);

    // 轉換 Notify 為 Boolean，避免傳回 Buffer
    const formattedRows = result.recordset.map((row) => ({
      Step_number: row.Step_number,
      approver_role: row.approver_role,
      approver_id: row.approver_id,
      Remark: row.Remark,
      Notify: row.Notify === 1, // 轉換 BIT 為 true/false
    }));

    res.json(formattedRows);
  } catch (error) {
    console.error("❌ 查詢簽核流程錯誤:", error);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

exports.saveFormFlows = async (req, res) => {
  try {
    const { form_type, steps, form_name, viewers } = req.body; // 加入 viewers
    if (!form_type || !Array.isArray(steps) || !form_name) {
      return res.status(400).json({ error: "缺少表單類型、名稱或步驟資訊" });
    }

    const pool = await sql.connect(dbConfig);
    const transaction = new sql.Transaction(pool);

    try {
      await transaction.begin();

      // **🔹 先更新 `Forms_name` 表的 `Name` 欄位**
      await transaction
        .request()
        .input("form_type", sql.NVarChar, form_type)
        .input("form_name", sql.NVarChar, form_name)
        .query(
          "UPDATE Forms_name SET Name = @form_name WHERE Type = @form_type"
        );

      // **🔹 刪除舊的 `Forms_Flows`**
      await transaction
        .request()
        .input("form_type", sql.NVarChar, form_type)
        .query("DELETE FROM Forms_Flows WHERE Form_type = @form_type");

      // **🔹 插入新的 `Forms_Flows`**
      const query = `
        INSERT INTO Forms_Flows (Form_type, Step_number, approver_role, approver_id, Remark, Notify)
        VALUES (@form_type, @step_number, @approver_role, @approver_id, @remark, @notify)
      `;

      for (const step of steps) {
        await transaction
          .request()
          .input("form_type", sql.NVarChar, form_type)
          .input("step_number", sql.Int, step.step_number)
          .input("approver_role", sql.NVarChar, step.approver_role)
          .input("approver_id", sql.NVarChar, step.approver_id || null)
          .input("remark", sql.NVarChar, step.remark)
          .input("notify", sql.Bit, step.notify ? 1 : 0)
          .query(query);
      }

      // **🔹 處理可檢視人員設定**
      if (Array.isArray(viewers)) {
        // 刪除舊的可檢視人員設定
        await transaction
          .request()
          .input("form_type", sql.NVarChar, form_type)
          .query("DELETE FROM Forms_viewer WHERE form_id = @form_type");

        // 插入新的可檢視人員設定
        if (viewers.length > 0) {
          for (const viewer of viewers) {
            // 處理物件格式 {label: '姓名', value: 'ID'} 或字串格式
            let user_id;
            if (typeof viewer === "object" && viewer !== null) {
              user_id = viewer.value;
            } else if (typeof viewer === "string") {
              user_id = viewer;
            }

            // 確保 user_id 是有效的字串
            if (
              user_id &&
              typeof user_id === "string" &&
              user_id.trim() !== "null"
            ) {
              await transaction
                .request()
                .input("form_type", sql.NVarChar, form_type)
                .input("user_id", sql.NVarChar, user_id.trim()).query(`
                  INSERT INTO Forms_viewer (form_id, user_id)
                  VALUES (@form_type, @user_id)
                `);
            }
          }
        }
      }

      await transaction.commit();
      res.json({ success: true });
    } catch (error) {
      await transaction.rollback();
      console.error("❌ 儲存失敗:", error);
      res.status(500).json({ error: "伺服器錯誤" });
    }
  } catch (error) {
    console.error("❌ 交易錯誤:", error);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

exports.getAttachments = async (req, res) => {
  const { form_id } = req.body; // 從 body 參數獲取

  if (!form_id) {
    return res.status(400).json({ error: "❌ 缺少表單 ID" });
  }

  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool
      .request()
      .input("form_id", sql.VarChar, form_id)
      .query(
        "SELECT sno, file_url, Created_at FROM Forms_attachments WHERE Form_id = @form_id"
      );

    res.json(result.recordset);
  } catch (err) {
    console.error("❌ 查詢附件失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法查詢附件" });
  }
};

exports.viewFullForm = async (req, res) => {
  const { form_id } = req.body;
  if (!form_id) {
    return res.status(400).json({ error: "缺少 form_id" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 🔹 1. 查主檔 Forms + Users + Forms_name
    const mainResult = await pool
      .request()
      .input("form_id", sql.VarChar, form_id).query(`
        SELECT 
          f.Form_id,
          f.Type,
          f.Status,
          f.Created,
          f.Updated,
          u.Name AS Submitter,
          u.ID AS Submitter_id,
          n.Name AS Doc_name
        FROM Forms f
        LEFT JOIN Forms_name n ON f.Type = n.Type
        LEFT JOIN Users u ON f.userid = u.id
        WHERE f.Form_id = @form_id
      `);

    const header = mainResult.recordset[0];
    if (!header) {
      return res.status(404).json({ error: "找不到主檔資料" });
    }

    // 🔹 2. 查明細 Form_xxx
    const detailResult = await pool
      .request()
      .input("form_id", sql.VarChar, form_id)
      .query(`SELECT * FROM Form_${header.Type} WHERE Form_id = @form_id`);

    const detail = detailResult.recordset[0] || {};

    // 🔹 3. 查歷程 Forms_approver + Users
    const historyResult = await pool
      .request()
      .input("form_id", sql.VarChar, form_id).query(`
        SELECT 
          FA.Approver_id, 
          U.Name AS Approver,
          FA.Status, 
          FA.Updated AS Approval_time, 
          FA.Memo AS Comment,
          FA.Step_name AS Remark
        FROM Forms_approver FA
        LEFT JOIN Users U ON FA.Approver_id = U.ID
        WHERE FA.Form_id = @form_id
        ORDER BY FA.Step_number ASC
      `);

    const history = historyResult.recordset || [];

    // 🔹 4. 查附件 Forms_attachments
    const attachmentResult = await pool
      .request()
      .input("form_id", sql.VarChar, form_id).query(`
        SELECT sno, file_url, Created_at 
        FROM Forms_attachments 
        WHERE Form_id = @form_id 
        ORDER BY Created_at
      `);

    const attachments = attachmentResult.recordset || [];

    // ✅ 回傳整包
    res.json({
      header,
      detail,
      history,
      attachments,
    });
  } catch (error) {
    console.error("❌ view_full_form 查詢失敗:", error);
    res.status(500).json({ error: "伺服器錯誤", detail: error.message });
  }
};

// 獲取表單可檢視人員
exports.getFormViewers = async (req, res) => {
  try {
    const { form_type } = req.query;
    if (!form_type) {
      return res.status(400).json({ error: "缺少表單類型 (form_type)" });
    }

    const pool = await sql.connect(dbConfig);
    const result = await pool
      .request()
      .input("form_type", sql.NVarChar, form_type).query(`
        SELECT fv.user_id, u.Name as user_name
        FROM Forms_viewer fv
        LEFT JOIN Users u ON RTRIM(fv.user_id) = u.ID
        WHERE fv.form_id = @form_type
      `);

    res.json(result.recordset);
  } catch (error) {
    console.error("❌ 查詢表單可檢視人員錯誤:", error);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

// 保存表單可檢視人員
exports.saveFormViewers = async (req, res) => {
  try {
    const { form_type, viewers } = req.body;
    if (!form_type || !Array.isArray(viewers)) {
      return res.status(400).json({ error: "缺少表單類型或可檢視人員資訊" });
    }

    const pool = await sql.connect(dbConfig);
    const transaction = new sql.Transaction(pool);

    try {
      await transaction.begin();

      // 刪除舊的可檢視人員設定
      await transaction
        .request()
        .input("form_type", sql.NVarChar, form_type)
        .query("DELETE FROM Forms_viewer WHERE form_id = @form_type");

      // 插入新的可檢視人員設定
      if (viewers.length > 0) {
        for (const viewer of viewers) {
          // 處理物件格式 {label: '姓名', value: 'ID'} 或字串格式
          let user_id;
          if (typeof viewer === "object" && viewer !== null) {
            user_id = viewer.value;
          } else if (typeof viewer === "string") {
            user_id = viewer;
          }

          // 確保 user_id 是有效的字串
          if (
            user_id &&
            typeof user_id === "string" &&
            user_id.trim() !== "null"
          ) {
            await transaction
              .request()
              .input("form_type", sql.NVarChar, form_type)
              .input("user_id", sql.NVarChar, user_id.trim()).query(`
                INSERT INTO Forms_viewer (form_id, user_id)
                VALUES (@form_type, @user_id)
              `);
          }
        }
      }

      await transaction.commit();
      res.json({ success: true, message: "可檢視人員設定已更新" });
    } catch (error) {
      await transaction.rollback();
      console.error("❌ 儲存可檢視人員失敗:", error);
      res.status(500).json({ error: "伺服器錯誤" });
    }
  } catch (error) {
    console.error("❌ 交易錯誤:", error);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

// 獲取通知記錄
exports.getNotifications = async (req, res) => {
  const { keyword, date_from, date_to, status, form_status } = req.query;

  try {
    const pool = await sql.connect(dbConfig);

    // 構建查詢條件
    let query = `
      SELECT 
        fn.Form_id, 
        fn.Step_number, 
        fn.Approver_id, 
        fn.Status,
        fn.Form_status, 
        fn.Created, 
        fn.Sent,
        u.Name AS Approver_name,
        f.Type AS Form_type,
        fn2.Name AS Form_name
      FROM Forms_notify fn
      LEFT JOIN Users u ON fn.Approver_id = u.ID
      LEFT JOIN Forms f ON fn.Form_id = f.Form_id
      LEFT JOIN Forms_name fn2 ON f.Type = fn2.Type
      WHERE 1=1
    `;

    // 添加過濾條件
    if (keyword) {
      query += ` AND (fn.Form_id LIKE '%${keyword}%' OR fn.Approver_id LIKE '%${keyword}%' OR u.Name LIKE '%${keyword}%')`;
    }

    if (date_from && date_to) {
      query += ` AND fn.Created BETWEEN '${date_from}' AND '${date_to} 23:59:59'`;
    } else if (date_from) {
      query += ` AND fn.Created >= '${date_from}'`;
    } else if (date_to) {
      query += ` AND fn.Created <= '${date_to} 23:59:59'`;
    }

    if (status) {
      query += ` AND fn.Status = '${status}'`;
    }

    if (form_status) {
      query += ` AND fn.Form_status = '${form_status}'`;
    }

    // 排序
    query += ` ORDER BY fn.Created DESC`;

    const result = await pool.request().query(query);
    res.json(result.recordset);
  } catch (error) {
    console.error("❌ 獲取通知記錄失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法獲取通知記錄" });
  }
};

// 重新發送通知
exports.resendNotification = async (req, res) => {
  const { form_id, step_number, approver_id } = req.body;

  if (!form_id || step_number === undefined || !approver_id) {
    return res.status(400).json({ error: "缺少必要參數" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 更新通知狀態為待發送
    await pool
      .request()
      .input("form_id", sql.VarChar, form_id)
      .input("step_number", sql.NVarChar, step_number)
      .input("approver_id", sql.VarChar, approver_id).query(`
        UPDATE Forms_notify 
        SET Status = 'pending', Sent = NULL
        WHERE Form_id = @form_id 
          AND Step_number = @step_number 
          AND Approver_id = @approver_id
      `);

    // 立即執行通知發送
    sendEmail().catch((err) => console.error("❌ 發送郵件時發生錯誤:", err));

    res.json({ success: true, message: "通知已重新排入發送佇列" });
  } catch (error) {
    console.error("❌ 重新發送通知失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法重新發送通知" });
  }
};

// 刪除歷史通知記錄
exports.deleteNotificationsHistory = async (req, res) => {
  const { date_before } = req.body;

  if (!date_before) {
    return res.status(400).json({ error: "缺少日期參數" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 查詢要刪除的記錄數量
    const countResult = await pool
      .request()
      .input("date", sql.Date, date_before).query(`
        SELECT COUNT(*) AS count 
        FROM Forms_notify 
        WHERE Created < @date
      `);

    const count = countResult.recordset[0].count;

    // 刪除指定日期之前的通知記錄
    await pool.request().input("date", sql.Date, date_before).query(`
        DELETE FROM Forms_notify 
        WHERE Created < @date
      `);

    res.json({
      success: true,
      message: `成功刪除 ${count} 筆歷史通知記錄`,
      deletedCount: count,
    });
  } catch (error) {
    console.error("❌ 刪除歷史通知記錄失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法刪除歷史通知記錄" });
  }
};
