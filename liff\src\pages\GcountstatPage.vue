<template>
  <q-page
    class="q-pa-sm flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">商品群組統計</div>
        </div>
      </q-card-section>

      <!-- 日期選擇 -->
      <q-card-section class="q-pt-none">
        <div class="row items-center no-wrap">
          <!-- 日期區間輸入框 -->
          <q-input
            v-model="formattedDate"
            label="選擇日期區間"
            dense
            outlined
            square
            class="col"
            readonly
          >
            <!-- 左側 icon -->
            <template v-slot:prepend>
              <calendar-days :stroke-width="1" :size="22" />
            </template>

            <!-- 右側按鈕 -->
            <template v-slot:append>
              <calendar-fold :stroke-width="1" :size="20" color="blue" />

              <q-popup-proxy
                cover
                transition-show="scale"
                transition-hide="scale"
              >
                <q-date
                  v-model="dateRange"
                  range
                  mask="YYYY-MM-DD"
                  navigation-min-year-month="2020/01"
                  :navigation-max-year-month="maxDateYearMonth"
                  today-btn
                  minimal
                >
                  <div class="row items-center justify-end">
                    <q-btn v-close-popup label="關閉" color="grey" flat />
                    <q-btn v-close-popup label="確定" color="primary" flat />
                  </div>
                </q-date>
              </q-popup-proxy>
            </template>
          </q-input>

          <!-- 搜尋按鈕 -->
          <q-btn
            flat
            round
            size="sm"
            color="red"
            class="search-button q-ml-sm"
            @click="openSearchDialog"
          >
            <Search size="20" />
            <q-tooltip>篩選統計</q-tooltip>
          </q-btn>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- 未搜尋時的提示訊息 -->
      <q-card-section
        v-if="!resultDateRange.from && !isLoading"
        class="q-pt-none q-py-sm"
      >
        <q-banner class="bg-blue-1 text-grey-8" rounded>
          <template v-slot:avatar>
            <Info class="text-primary" :stroke-width="1" :size="30" />
          </template>
          請輸入日期進行查詢
        </q-banner>
      </q-card-section>

      <!-- 查詢條件顯示 -->
      <div
        v-if="
          selectedCategory &&
          selectedBranches.length > 0 &&
          resultDateRange.from
        "
        class="q-mb-md q-mt-md"
      >
        <div class="query-info-container">
          <div class="query-info-card">
            <!-- 查詢區間 -->
            <div class="query-info-item query-info-date">
              <div class="query-info-icon">
                <q-icon name="date_range" size="sm" color="primary" />
              </div>
              <div class="query-info-content">
                <div class="query-info-label">查詢區間</div>
                <div class="query-info-value">
                  {{ formattedDate || "未選擇日期" }}
                </div>
              </div>
            </div>

            <div class="query-info-divider"></div>

            <!-- 行動裝置時的類別和門市布局 -->
            <div class="query-info-mobile-row">
              <!-- 統計類別 -->
              <div class="query-info-item query-info-category">
                <div class="query-info-icon">
                  <q-icon name="category" size="sm" color="secondary" />
                </div>
                <div class="query-info-content">
                  <div class="query-info-label">統計類別</div>
                  <div class="query-info-value">
                    {{ getCategoryName(selectedCategory) }}
                  </div>
                </div>
              </div>

              <!-- 選擇門市 -->
              <div class="query-info-item query-info-branch">
                <div class="query-info-icon">
                  <q-icon name="store" size="sm" color="accent" />
                </div>
                <div class="query-info-content">
                  <div class="query-info-label">選擇門市</div>
                  <div class="query-info-value">
                    {{ selectedBranches.length }} 個門市
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 載入中狀態 -->
      <div v-if="isLoading" class="q-pa-xl flex flex-center">
        <div class="fancy-loader">
          <svg
            class="loader-svg"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">資料整理中...</div>
        </div>
      </div>

      <!-- 無資料顯示 -->
      <q-card-section
        v-else-if="
          !statResults ||
          (statResults.length === 0 && resultDateRange.from && !isLoading)
        "
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無統計資料</div>
          <div class="empty-subtext">請選擇商品類別、門市與查詢區間</div>
        </div>
      </q-card-section>

      <!-- 統計結果 -->
      <template
        v-else-if="
          resultDateRange.from && statResults && statResults.length > 0
        "
      >
        <!-- 數據顯示 -->
        <q-tabs
          v-model="activeTab"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
        >
          <q-tab name="table">
            <template #default>
              <div class="column items-center justify-center">
                <sheet :size="24" class="q-mb-xs" />
                <div class="text-body2">表格</div>
              </div>
            </template>
          </q-tab>
          <q-tab name="detail">
            <template #default>
              <div class="column items-center justify-center">
                <list :size="24" class="q-mb-xs" />
                <div class="text-body2">明細</div>
              </div>
            </template>
          </q-tab>
          <q-tab name="chart">
            <template #default>
              <div class="column items-center justify-center">
                <chart-spline :size="24" class="q-mb-xs" />
                <div class="text-body2">圖表</div>
              </div>
            </template>
          </q-tab>
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="activeTab" animated>
          <!-- 表格視圖 -->
          <q-tab-panel name="table" class="q-pa-none">
            <div class="q-pa-md">
              <!-- 樞紐表格式 -->
              <div class="pivot-table-container" ref="tableContainer">
                <div class="table-header-actions">
                  <q-btn
                    flat
                    round
                    dense
                    color="primary"
                    icon="fullscreen"
                    @click="showFullscreenTable = true"
                    class="fullscreen-btn"
                  >
                    <q-tooltip>全螢幕顯示表格</q-tooltip>
                  </q-btn>
                </div>
                <table class="pivot-table">
                  <thead>
                    <tr>
                      <th class="corner-header">門市 \ 商品群組</th>
                      <template
                        v-for="groupName in uniqueGroupNames"
                        :key="groupName"
                      >
                        <th class="group-header" colspan="2">
                          {{ groupName }}
                          <template v-if="getGroupTotalFlag(groupName) === 0">
                            <q-btn flat round dense color="grey" size="xs">
                              <square-x :stroke-width="1.5" :size="18" />
                              <q-tooltip>不加入合計</q-tooltip>
                            </q-btn>
                          </template>
                          <template v-if="getGroupDisplayFlag(groupName) === 0">
                            <q-btn flat round dense color="grey" size="xs">
                              <eye-off :stroke-width="1.5" :size="16" />
                              <q-tooltip>不顯示</q-tooltip>
                            </q-btn>
                          </template>
                        </th>
                      </template>
                      <th
                        v-if="getCategoryTotalDisplayFlag() === 1"
                        class="total-header"
                        colspan="2"
                      >
                        門市合計
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- 各門市資料 -->
                    <template
                      v-for="branchName in uniqueBranchNames"
                      :key="branchName"
                    >
                      <tr>
                        <td class="branch-name">
                          {{ branchName }}
                        </td>
                        <template
                          v-for="groupName in uniqueGroupNames"
                          :key="groupName"
                        >
                          <td class="qty-cell">
                            {{
                              getBranchGroupQty(
                                branchName,
                                groupName
                              ).toLocaleString()
                            }}
                          </td>
                          <td class="percent-cell">
                            {{
                              getBranchGroupPercent(
                                branchName,
                                groupName
                              ).toFixed(1)
                            }}%
                          </td>
                        </template>
                        <template v-if="getCategoryTotalDisplayFlag() === 1">
                          <td class="total-qty">
                            {{ getBranchTotalQty(branchName).toLocaleString() }}
                          </td>
                          <td class="total-percent">
                            {{ getBranchTotalPercent(branchName).toFixed(1) }}%
                          </td>
                        </template>
                      </tr>
                    </template>
                    <!-- 合計列 -->
                    <tr class="grand-total-row">
                      <td class="grand-total-label">總計</td>
                      <template
                        v-for="groupName in uniqueGroupNames"
                        :key="groupName"
                      >
                        <td class="grand-total-qty">
                          {{ getGroupTotalQty(groupName).toLocaleString() }}
                        </td>
                        <td class="grand-total-percent">
                          {{ getGroupTotalPercent(groupName).toFixed(1) }}%
                        </td>
                      </template>
                      <template v-if="getCategoryTotalDisplayFlag() === 1">
                        <td class="grand-total-qty">
                          {{ getGrandTotal().toLocaleString() }}
                        </td>
                        <td class="grand-total-percent">100.0%</td>
                      </template>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </q-tab-panel>

          <!-- 明細視圖 -->
          <q-tab-panel name="detail" class="q-pa-none">
            <div class="q-pa-md">
              <q-table
                :rows="getAllDetails()"
                :columns="detailColumns"
                row-key="unique_key"
                dense
                :pagination="{
                  rowsPerPage: 10,
                }"
              />
            </div>
          </q-tab-panel>

          <!-- 圖表視圖 -->
          <q-tab-panel name="chart" class="q-pa-none">
            <div class="q-pa-md">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-6">
                  <q-card class="chart-card">
                    <q-card-section class="q-pb-none">
                      <div class="text-h6">商品群組占比</div>
                    </q-card-section>
                    <q-card-section>
                      <div style="height: 300px" ref="pieChartRef"></div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-md-6">
                  <q-card class="chart-card">
                    <q-card-section class="q-pb-none">
                      <div class="text-h6">門市銷售分布</div>
                    </q-card-section>
                    <q-card-section>
                      <div style="height: 300px" ref="branchPieChartRef"></div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </template>
    </q-card>

    <!-- 篩選對話框 -->
    <q-dialog v-model="filterDialog.show" position="right" full-height>
      <q-card style="width: 400px; max-width: 100vw">
        <q-card-section class="row items-center q-pb-xs">
          <div class="text-h6">統計設定</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <!-- 篩選選項 -->
        <q-card-section class="q-pt-md">
          <!-- 類別選擇 -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-xs">選擇商品類別</div>
            <q-select
              v-model="filterDialog.category"
              :options="categoryOptions"
              outlined
              dense
              emit-value
              map-options
              label="選擇類別"
              :rules="[(val) => !!val || '請選擇類別']"
            />
          </div>

          <q-separator spaced inset class="q-my-md" />

          <!-- 門市選擇 -->
          <div>
            <div class="row justify-between items-center q-mb-xs">
              <div class="text-subtitle2">選擇門市</div>
              <div>
                <q-btn
                  flat
                  dense
                  size="sm"
                  color="grey-7"
                  label="清空"
                  @click="clearAllBranchesDialog"
                />
              </div>
            </div>

            <!-- 門市搜尋與篩選 -->
            <div class="row q-col-gutter-sm q-mb-sm">
              <div class="col">
                <q-input
                  v-model="filterDialog.search"
                  label="搜尋門市"
                  outlined
                  dense
                  clearable
                >
                  <template v-slot:append>
                    <q-icon name="search" />
                  </template>
                </q-input>
              </div>
              <div class="col-auto">
                <q-toggle
                  v-model="filterDialog.onlyActive"
                  label="僅啟用"
                  color="primary"
                  left-label
                />
              </div>
            </div>

            <!-- 門市群組快速選擇 -->
            <div class="row q-col-gutter-xs q-mb-sm">
              <div class="col-4">
                <q-btn
                  :color="
                    isBranchGroupSelectedInDialog('MO')
                      ? 'deep-orange-8'
                      : 'grey-2'
                  "
                  :text-color="
                    isBranchGroupSelectedInDialog('MO')
                      ? 'white'
                      : 'deep-orange-8'
                  "
                  label="小蒙牛"
                  size="sm"
                  class="full-width"
                  :outline="!isBranchGroupSelectedInDialog('MO')"
                  :unelevated="isBranchGroupSelectedInDialog('MO')"
                  @click="toggleBranchGroupSelectionInDialog('MO')"
                />
              </div>
              <div class="col-4">
                <q-btn
                  :color="
                    isBranchGroupSelectedInDialog('SO') ? 'amber-8' : 'grey-2'
                  "
                  :text-color="
                    isBranchGroupSelectedInDialog('SO') ? 'white' : 'amber-8'
                  "
                  label="金大鋤"
                  size="sm"
                  class="full-width"
                  :outline="!isBranchGroupSelectedInDialog('SO')"
                  :unelevated="isBranchGroupSelectedInDialog('SO')"
                  @click="toggleBranchGroupSelectionInDialog('SO')"
                />
              </div>
              <div class="col-4">
                <q-btn
                  :color="
                    isBranchGroupSelectedInDialog('LD') ? 'red-8' : 'grey-2'
                  "
                  :text-color="
                    isBranchGroupSelectedInDialog('LD') ? 'white' : 'red-8'
                  "
                  label="燒肉老大"
                  size="sm"
                  class="full-width"
                  :outline="!isBranchGroupSelectedInDialog('LD')"
                  :unelevated="isBranchGroupSelectedInDialog('LD')"
                  @click="toggleBranchGroupSelectionInDialog('LD')"
                />
              </div>
            </div>

            <!-- 門市列表 -->
            <q-list
              bordered
              separator
              class="rounded-borders"
              style="max-height: 300px; overflow-y: auto"
            >
              <q-item
                v-for="branch in filteredBranchOptionsDialog"
                :key="branch.value"
                tag="label"
                clickable
                v-ripple
                :class="{
                  'branch-selected': filterDialog.branches.includes(
                    branch.value
                  ),
                }"
              >
                <q-item-section avatar>
                  <q-checkbox
                    v-model="filterDialog.branches"
                    :val="branch.value"
                    color="primary"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ branch.label.split(" (")[0] }}</q-item-label>
                  <q-item-label caption>{{ branch.value }}</q-item-label>
                </q-item-section>
                <q-item-section
                  side
                  v-if="
                    branch.group === 'MO' ||
                    branch.group === 'SO' ||
                    branch.group === 'LD' ||
                    branch.group === 'MH' ||
                    branch.group === 'YL' ||
                    branch.group === 'YS'
                  "
                >
                  <q-badge :color="getBranchGroupColor(branch.group)">
                    {{ getBranchGroupLabel(branch.group) }}
                  </q-badge>
                </q-item-section>
              </q-item>
              <q-item v-if="filteredBranchOptionsDialog.length === 0">
                <q-item-section class="text-grey text-center">
                  無符合的門市
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card-section>

        <q-separator />

        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn
            unelevated
            color="primary"
            label="搜尋"
            @click="applyFilterAndSearch"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 全螢幕表格對話框 -->
    <q-dialog
      v-model="showFullscreenTable"
      full-width
      full-height
      maximized
      persistent
      :transition-show="dialogTransition.show"
      :transition-hide="dialogTransition.hide"
    >
      <q-card class="column no-wrap">
        <q-card-section class="row items-center q-py-sm bg-primary text-white">
          <div class="text-subtitle1">
            <q-icon name="table_chart" class="q-mr-xs" size="sm" />
            商品群組統計
          </div>
          <q-space />
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            class="text-white"
            size="sm"
          >
            <q-tooltip>關閉</q-tooltip>
          </q-btn>
        </q-card-section>

        <q-card-section class="col q-pa-md scroll">
          <table class="pivot-table full-width">
            <thead>
              <tr>
                <th class="corner-header">門市 \ 商品群組</th>
                <template
                  v-for="groupName in uniqueGroupNames"
                  :key="groupName"
                >
                  <th class="group-header" colspan="2">
                    {{ groupName }}
                    <template v-if="getGroupTotalFlag(groupName) === 0">
                      <q-btn flat round dense color="grey" size="xs">
                        <square-x :stroke-width="1.5" :size="18" />
                        <q-tooltip>不加入合計</q-tooltip>
                      </q-btn>
                    </template>
                    <template v-if="getGroupDisplayFlag(groupName) === 0">
                      <q-btn flat round dense color="grey" size="xs">
                        <eye-off :stroke-width="1.5" :size="16" />
                        <q-tooltip>不顯示</q-tooltip>
                      </q-btn>
                    </template>
                  </th>
                </template>
                <th
                  v-if="getCategoryTotalDisplayFlag() === 1"
                  class="total-header"
                  colspan="2"
                >
                  門市合計
                </th>
              </tr>
            </thead>
            <tbody>
              <!-- 各門市資料 -->
              <template
                v-for="branchName in uniqueBranchNames"
                :key="branchName"
              >
                <tr>
                  <td class="branch-name">
                    {{ branchName }}
                  </td>
                  <template
                    v-for="groupName in uniqueGroupNames"
                    :key="groupName"
                  >
                    <td class="qty-cell">
                      {{
                        getBranchGroupQty(
                          branchName,
                          groupName
                        ).toLocaleString()
                      }}
                    </td>
                    <td class="percent-cell">
                      {{
                        getBranchGroupPercent(branchName, groupName).toFixed(1)
                      }}%
                    </td>
                  </template>
                  <template v-if="getCategoryTotalDisplayFlag() === 1">
                    <td class="total-qty">
                      {{ getBranchTotalQty(branchName).toLocaleString() }}
                    </td>
                    <td class="total-percent">
                      {{ getBranchTotalPercent(branchName).toFixed(1) }}%
                    </td>
                  </template>
                </tr>
              </template>
              <!-- 合計列 -->
              <tr class="grand-total-row">
                <td class="grand-total-label">總計</td>
                <template
                  v-for="groupName in uniqueGroupNames"
                  :key="groupName"
                >
                  <td class="grand-total-qty">
                    {{ getGroupTotalQty(groupName).toLocaleString() }}
                  </td>
                  <td class="grand-total-percent">
                    {{ getGroupTotalPercent(groupName).toFixed(1) }}%
                  </td>
                </template>
                <template v-if="getCategoryTotalDisplayFlag() === 1">
                  <td class="grand-total-qty">
                    {{ getGrandTotal().toLocaleString() }}
                  </td>
                  <td class="grand-total-percent">100.0%</td>
                </template>
              </tr>
            </tbody>
          </table>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive, onUnmounted } from "vue";
import { DateTime } from "luxon";
import { useQuasar } from "quasar";
import {
  Search,
  SquareX,
  EyeOff,
  Info,
  Maximize2,
  Minimize2,
} from "lucide-vue-next";
import * as echarts from "echarts";
import apiClient from "../api";

const $q = useQuasar();
const props = defineProps({ Branch: String });
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 日期相關
const today = DateTime.now().toFormat("yyyy-MM-dd");
const maxDateYearMonth = DateTime.now().toFormat("yyyy/MM");

// 控制全螢幕對話框顯示
const showFullscreenTable = ref(false);

// 對話框過場動畫
const dialogTransition = reactive({
  show: "slide-up",
  hide: "slide-down",
});

const dateRange = ref({
  from: today,
  to: today,
});

const resultDateRange = ref({ from: "", to: "" });

// 數據狀態
const isLoading = ref(false);
const activeTab = ref("table");
// 圖表相關
const pieChartRef = ref(null);
const branchPieChartRef = ref(null);

let pieChart = null;
let branchPieChart = null;

// 統計資料
const statResults = ref([]);
const selectedCategory = ref(null);
const selectedBranches = ref([]);

// 篩選對話框
const filterDialog = reactive({
  show: false,
  category: "",
  branches: [],
  search: "", // 新增搜尋關鍵字
  onlyActive: true, // 新增篩選啟用門市選項
});

// 資料選項
const categoryOptions = ref([]);
const branchOptions = ref([]);

// 過濾後的門市選項（根據對話框中的搜尋關鍵字和啟用狀態）
const filteredBranchOptionsDialog = computed(() => {
  let filtered = branchOptions.value;

  // 如果啟用了"僅啟用門市"篩選，只顯示 sts=1 的門市
  if (filterDialog.onlyActive) {
    filtered = filtered.filter((branch) => branch.status === "1");
  }

  // 如果有搜尋關鍵字，根據關鍵字過濾
  if (filterDialog.search) {
    const keyword = filterDialog.search.toLowerCase();
    filtered = filtered.filter(
      (branch) =>
        branch.label.toLowerCase().includes(keyword) ||
        branch.value.toLowerCase().includes(keyword) ||
        (branch.group && branch.group.toLowerCase().includes(keyword))
    );
  }

  return filtered;
});

// 獲取門市群組標籤
const getBranchGroupLabel = (group) => {
  switch (group) {
    case "MO":
      return "小蒙牛";
    case "SO":
      return "金大鋤";
    case "LD":
      return "燒肉老大";
    case "YL":
      return "鍋物";
    case "MH":
      return "和蒙";
    case "YS":
      return "赤神";
    default:
      return group;
  }
};

// 獲取門市群組顏色
const getBranchGroupColor = (group) => {
  switch (group) {
    case "MO":
      return "deep-orange";
    case "SO":
      return "amber-8";
    case "LD":
      return "red-8";
    case "YL":
      return "green-8";
    case "MH":
      return "purple-8";
    case "YS":
      return "blue-8";
    default:
      return "grey-7";
  }
};

// 門市群組列表
const branchGroups = computed(() => {
  const groups = new Set();
  branchOptions.value.forEach((branch) => {
    if (branch.group) {
      groups.add(branch.group);
    }
  });
  return Array.from(groups);
});

// 唯一門市名稱列表（用於樞紐表）
const uniqueBranchNames = computed(() => {
  return [...new Set(statResults.value.map((item) => item.branch_name))];
});

// 唯一商品群組名稱列表（用於樞紐表，考慮 display 字段）
const uniqueGroupNames = computed(() => {
  return [
    ...new Set(
      statResults.value
        .filter((item) => item.display === 1) // 只顯示 display=1 的群組
        .map((item) => item.group_name)
    ),
  ];
});

// 表格列定義

// 明細資料列定義
const detailColumns = [
  {
    name: "branch_name",
    label: "門市名稱",
    field: "branch_name",
    sortable: true,
  },
  {
    name: "item_name",
    label: "商品名稱",
    field: "item_name",
    sortable: true,
  },
  {
    name: "total_qty",
    label: "數量",
    field: "total_qty",
    format: (val) => val?.toLocaleString() || "0", // 格式化數量為千分位
    sortable: true,
  },
  {
    name: "total_amount",
    label: "總金額",
    field: "total_amount",
    format: (val) => `$${val?.toLocaleString() || "0"}`, // 格式化金額為千分位
    sortable: true,
  },
];

// 計算屬性
const formattedDate = computed(() => {
  const value = dateRange.value;

  if (!value) return "請選擇日期";

  if (typeof value === "string") {
    // 單日情況（只選一天）
    return value;
  }

  if (typeof value === "object" && value.from) {
    const { from, to } = value;
    return !to || from === to ? from : `${from} ~ ${to}`;
  }

  return "請選擇日期";
});

// 方法
const openSearchDialog = () => {
  filterDialog.category = selectedCategory.value || "";
  filterDialog.branches = [...selectedBranches.value];
  filterDialog.search = ""; // 清空搜尋關鍵字
  filterDialog.onlyActive = true; // 重置篩選啟用門市選項
  filterDialog.show = true;
};

// 清空所有門市 (篩選對話框)
const clearAllBranchesDialog = () => {
  filterDialog.branches = [];
};

// 檢查門市群組是否已全選 (篩選對話框)
const isBranchGroupSelectedInDialog = (group) => {
  // 獲取符合篩選條件的門市
  const filteredBranches = filteredBranchOptionsDialog.value
    .filter((branch) => branch.group === group)
    .map((branch) => branch.value);

  // 如果沒有符合條件的門市，則返回 false
  if (filteredBranches.length === 0) return false;

  return filteredBranches.every((branchId) =>
    filterDialog.branches.includes(branchId)
  );
};

// 切換門市群組選擇狀態 (篩選對話框)
const toggleBranchGroupSelectionInDialog = (group) => {
  // 獲取符合篩選條件的門市
  const filteredBranches = filteredBranchOptionsDialog.value
    .filter((branch) => branch.group === group)
    .map((branch) => branch.value);

  // 如果沒有符合條件的門市，則不做任何操作
  if (filteredBranches.length === 0) return;

  // 檢查是否已全選
  const allSelected = filteredBranches.every((branchId) =>
    filterDialog.branches.includes(branchId)
  );

  if (allSelected) {
    // 如果全選了，則取消選擇
    filterDialog.branches = filterDialog.branches.filter(
      (branchId) => !filteredBranches.includes(branchId)
    );
  } else {
    // 如果未全選，則全選
    const newBranches = [...filterDialog.branches];
    filteredBranches.forEach((branchId) => {
      if (!newBranches.includes(branchId)) {
        newBranches.push(branchId);
      }
    });
    filterDialog.branches = newBranches;
  }
};

const applyFilterAndSearch = () => {
  if (!filterDialog.category) {
    $q.notify({
      type: "warning",
      message: "請選擇商品類別",
      icon: "warning",
    });
    return;
  }

  if (filterDialog.branches.length === 0) {
    $q.notify({
      type: "warning",
      message: "請至少選擇一個門市",
      icon: "warning",
    });
    return;
  }

  selectedCategory.value = filterDialog.category;
  selectedBranches.value = [...filterDialog.branches];
  filterDialog.show = false;
  fetchData();
};

const getCategoryName = (categoryId) => {
  const category = categoryOptions.value.find((c) => c.value === categoryId);
  return category ? category.label.trim() : categoryId;
};

const loadCategories = async () => {
  try {
    const response = await apiClient.get(
      `${apiBaseUrl}/gcounts/get_categories`
    );
    if (response.data.success) {
      categoryOptions.value = response.data.data.map((cat) => ({
        label: cat.cname.trim(),
        value: cat.cid,
        total_display:
          cat.total_display !== undefined ? parseInt(cat.total_display) : 1,
      }));
    }
  } catch (error) {
    console.error("載入類別錯誤:", error);
    $q.notify({
      type: "negative",
      message: "載入類別失敗",
      icon: "error",
    });
  }
};

const loadBranchOptions = async () => {
  try {
    // 使用提供的SQL查詢獲取門市資訊
    const response = await apiClient.get(`${apiBaseUrl}/gcounts/get_branches`);

    if (response.data.success) {
      branchOptions.value = response.data.data.map((branch) => ({
        label: `${branch.cod_name.trim()} (${branch.cod_cust.trim()})`,
        value: branch.cod_cust.trim(),
        group: branch.cod_group?.trim() || "",
        status: branch.sts,
      }));
    }
  } catch (error) {
    console.error("載入門市錯誤:", error);
    $q.notify({
      type: "negative",
      message: "載入門市失敗",
      icon: "error",
    });
  }
};

// 計算特定門市總數量 (考慮 total_in_sum 字段)
const getBranchTotalQty = (branchName) => {
  // 只計算 total_in_sum=1 的群組
  return statResults.value
    .filter(
      (item) => item.branch_name === branchName && item.total_in_sum === 1
    )
    .reduce((sum, item) => sum + item.count, 0);
};

// 計算特定門市特定群組的數量
const getBranchGroupQty = (branchName, groupName) => {
  const item = statResults.value.find(
    (i) => i.branch_name === branchName && i.group_name === groupName
  );
  return item ? item.count : 0;
};

// 計算特定群組總數量
const getGroupTotalQty = (groupName) => {
  return statResults.value
    .filter((item) => item.group_name === groupName)
    .reduce((sum, item) => sum + item.count, 0);
};

// 計算總計數量 (考慮 total_in_sum 字段)
const getGrandTotal = () => {
  // 只計算 total_in_sum=1 的群組
  return statResults.value
    .filter((item) => item.total_in_sum === 1)
    .reduce((sum, item) => sum + item.count, 0);
};

// 獲取群組的 total 標記
const getGroupTotalFlag = (groupName) => {
  const group = statResults.value.find((item) => item.group_name === groupName);
  return group ? group.total_in_sum : 1; // 預設為1（加入合計）
};

// 獲取群組的 display 標記
const getGroupDisplayFlag = (groupName) => {
  const group = statResults.value.find((item) => item.group_name === groupName);
  return group ? group.display : 1; // 預設為1（顯示）
};

// 獲取類別的 total_display 標記
const getCategoryTotalDisplayFlag = () => {
  const category = categoryOptions.value.find(
    (c) => c.value === selectedCategory.value
  );
  return category && category.total_display !== undefined
    ? category.total_display
    : 1; // 預設為1（顯示）
};

// 檢查門市是否應該顯示（根據所選類別的 total_display 設定）
const isBranchDisplayed = (branchName) => {
  // 檢查類別是否設置為不顯示門市統計
  const categoryTotalDisplay = getCategoryTotalDisplayFlag();
  return categoryTotalDisplay === 1;
};

// 計算特定門市特定群組的占比 (考慮 total_in_sum 字段)
const getBranchGroupPercent = (branchName, groupName) => {
  const totalQty = getBranchTotalQty(branchName);
  const groupQty = getBranchGroupQty(branchName, groupName);

  // 如果門市總數為0，則占比也為0
  return totalQty > 0 ? ((groupQty / totalQty) * 100).toFixed(1) * 1 : 0;
};

// 計算特定門市總占比
const getBranchTotalPercent = (branchName) => {
  const totalQty = getBranchTotalQty(branchName);
  const grandTotal = getGrandTotal();
  return grandTotal > 0 ? ((totalQty / grandTotal) * 100).toFixed(1) * 1 : 0;
};

// 計算特定群組總占比 (考慮 total_in_sum 字段)
const getGroupTotalPercent = (groupName) => {
  const groupTotal = getGroupTotalQty(groupName);
  const grandTotal = getGrandTotal();

  // 如果總數為0，則占比也為0
  return grandTotal > 0 ? ((groupTotal / grandTotal) * 100).toFixed(1) * 1 : 0;
};

// 更新後端API調用，確保接收total_in_sum和display字段
const fetchData = async () => {
  if (!selectedCategory.value || selectedBranches.value.length === 0) {
    return;
  }

  const from = dateRange.value?.from || dateRange.value;
  const to = dateRange.value?.to || dateRange.value;

  if (!from) {
    $q.notify({
      type: "warning",
      message: "請選擇日期區間",
      icon: "warning",
    });
    return;
  }

  isLoading.value = true;

  try {
    const response = await apiClient.post(`${apiBaseUrl}/gcounts/get_stats`, {
      category: selectedCategory.value,
      branches: selectedBranches.value,
      from,
      to,
      onlyActive: filterDialog.onlyActive,
    });

    if (response.data.success) {
      // 確保每個結果都有 total_in_sum 和 display 字段，如果後端沒有提供則設為默認值
      statResults.value = response.data.data.map((item) => {
        // 確保 total_in_sum 和 display 是數字類型
        const total_in_sum =
          item.total_in_sum !== undefined ? parseInt(item.total_in_sum) : 1;
        const display = item.display !== undefined ? parseInt(item.display) : 1;

        return {
          ...item,
          total_in_sum: total_in_sum,
          display: display,
        };
      });

      resultDateRange.value = { from, to };

      // 更新圖表
      if (activeTab.value === "chart") {
        updateChart();
      }
    } else {
      throw new Error(response.data.message || "查詢失敗");
    }
  } catch (error) {
    console.error("統計錯誤:", error);
    $q.notify({
      type: "negative",
      message: `統計失敗: ${error.message}`,
      icon: "error",
    });
    statResults.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 更新圖表
const updateChart = () => {
  // 確保圖表容器存在
  if (!pieChartRef.value || !branchPieChartRef.value) {
    console.warn("圖表容器不存在，無法更新圖表");
    return;
  }

  try {
    // 銷毀舊的圖表實例，確保重新創建
    if (pieChart) {
      pieChart.dispose();
    }
    if (branchPieChart) {
      branchPieChart.dispose();
    }

    // 重新創建圖表
    pieChart = echarts.init(pieChartRef.value);
    branchPieChart = echarts.init(branchPieChartRef.value);

    // 更新圖表數據
    updatePieChart();
    updateBranchPieChart();
  } catch (error) {
    console.error("圖表更新錯誤:", error);
  }
};

// 監聽 activeTab 變化，當切換到圖表時更新圖表
watch(activeTab, (newTab, oldTab) => {
  if (newTab === "chart" && statResults.value.length > 0) {
    // 使用延遲確保DOM已完全渲染
    setTimeout(() => {
      updateChart();
    }, 300);
  }
});

// 監聽 statResults 變化，當數據更新時更新圖表
watch(
  statResults,
  (newResults) => {
    if (activeTab.value === "chart" && newResults.length > 0) {
      // 使用 setTimeout 確保 DOM 已完全渲染
      setTimeout(() => {
        updateChart();
      }, 200);
    }
  },
  { deep: true }
);

// 取得所有詳細資料
const getAllDetails = () => {
  const details = [];
  statResults.value.forEach((item) => {
    if (item.details && item.details.length > 0) {
      // 為每個詳細項添加門市名稱和唯一鍵，並移除空格
      item.details.forEach((detail) => {
        details.push({
          ...detail,
          branch_name: item.branch_name.trim(), // 從父項獲取門市名稱並移除空格
          code: detail.code?.trim() || "", // 移除商品編號的空格
          item_name: detail.item_name?.trim() || "", // 移除商品名稱的空格
          category_name: detail.category_name?.trim() || "", // 移除商品分類的空格
          unique_key: `${item.branch_name.trim()}-${detail.code?.trim() || ""}`, // 創建唯一鍵並移除空格
        });
      });
    }
  });
  return details;
};

// 切換表格全螢幕狀態
const toggleTableFullscreen = () => {
  showFullscreenTable.value = true;
};

// 監聽全屏狀態變化
const handleFullscreenChange = () => {
  const isDocFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  );

  // 檢查是否是表格容器進入了全屏模式
  const tableContainer = document.querySelector(".pivot-table-container");
  const fullscreenElement =
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement;

  // isTableFullscreen.value =
  //   isDocFullscreen && fullscreenElement === tableContainer;
};

// 更新商品群組占比圓餅圖
const updatePieChart = () => {
  if (!pieChartRef.value || !pieChart) return;

  try {
    // 準備圓餅圖數據 (只包含 display=1 且 total_in_sum=1 的群組)
    const pieData = uniqueGroupNames.value
      .map((groupName) => {
        const group = statResults.value.find(
          (item) => item.group_name === groupName
        );
        if (!group || group.total_in_sum !== 1) return null;

        return {
          name: groupName.trim(),
          value: getGroupTotalQty(groupName),
        };
      })
      .filter((item) => item && item.value > 0); // 過濾掉數量為0的項目和null項目

    // 如果沒有數據，顯示空狀態
    if (pieData.length === 0) {
      pieChart.setOption({
        title: {
          text: "無數據",
          left: "center",
          top: "center",
          textStyle: {
            color: "#999",
            fontSize: 16,
          },
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "60%"],
            data: [{ name: "無數據", value: 1 }],
            itemStyle: {
              color: "#eee",
            },
            label: {
              show: false,
            },
          },
        ],
      });
      return;
    }

    // 生成圖表配置
    const option = {
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b} : {c} ({d}%)",
      },
      legend: {
        type: "scroll", // 啟用滾動功能
        orient: "horizontal",
        bottom: 0,
        left: "center",
        data: pieData.map((item) => item.name),
        formatter: function (name) {
          // 截斷長名稱，保留前10個字符
          return name.length > 10 ? name.substring(0, 10) + "..." : name;
        },
        tooltip: {
          show: true,
          formatter: function (params) {
            return params.name;
          },
        },
      },
      series: [
        {
          name: "商品群組",
          type: "pie",
          radius: ["40%", "60%"],
          center: ["50%", "45%"],
          avoidLabelOverlap: true, // 避免標籤重疊
          label: {
            show: true,
            formatter: function (params) {
              // 截斷長名稱，保留前6個字符
              const name =
                params.name.length > 6
                  ? params.name.substring(0, 6) + "..."
                  : params.name;
              return `${name}: ${params.value.toLocaleString()} (${params.percent.toFixed(
                1
              )}%)`;
            },
            overflow: "truncate", // 截斷溢出的文字
            ellipsis: "...", // 使用省略號
            minMargin: 5, // 標籤之間的最小間距
          },
          emphasis: {
            label: {
              show: true,
              fontSize: "14",
              fontWeight: "bold",
            },
          },
          data: pieData,
        },
      ],
    };

    pieChart.setOption(option, true);
  } catch (error) {
    console.error("商品群組圖表更新錯誤:", error);
  }
};

// 更新門市銷售分布圓餅圖
const updateBranchPieChart = () => {
  if (!branchPieChartRef.value || !branchPieChart) return;

  try {
    // 準備圓餅圖數據
    const pieData = uniqueBranchNames.value
      .map((branchName) => {
        return {
          name: branchName.trim(),
          value: getBranchTotalQty(branchName),
        };
      })
      .filter((item) => item.value > 0); // 過濾掉數量為0的項目

    // 如果沒有數據，顯示空狀態
    if (pieData.length === 0) {
      branchPieChart.setOption({
        title: {
          text: "無數據",
          left: "center",
          top: "center",
          textStyle: {
            color: "#999",
            fontSize: 16,
          },
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "60%"],
            data: [{ name: "無數據", value: 1 }],
            itemStyle: {
              color: "#eee",
            },
            label: {
              show: false,
            },
          },
        ],
      });
      return;
    }

    // 生成圖表配置
    const option = {
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b} : {c} ({d}%)",
      },
      legend: {
        type: "scroll", // 啟用滾動功能
        orient: "horizontal",
        bottom: 0,
        left: "center",
        data: pieData.map((item) => item.name),
        formatter: function (name) {
          // 截斷長名稱，保留前10個字符
          return name.length > 10 ? name.substring(0, 10) + "..." : name;
        },
        tooltip: {
          show: true,
          formatter: function (params) {
            return params.name;
          },
        },
      },
      series: [
        {
          name: "門市銷售",
          type: "pie",
          radius: ["40%", "60%"],
          center: ["50%", "45%"],
          avoidLabelOverlap: true, // 避免標籤重疊
          label: {
            show: true,
            formatter: function (params) {
              // 截斷長名稱，保留前6個字符
              const name =
                params.name.length > 6
                  ? params.name.substring(0, 6) + "..."
                  : params.name;
              return `${name}: ${params.value.toLocaleString()} (${params.percent.toFixed(
                1
              )}%)`;
            },
            overflow: "truncate", // 截斷溢出的文字
            ellipsis: "...", // 使用省略號
            minMargin: 5, // 標籤之間的最小間距
          },
          emphasis: {
            label: {
              show: true,
              fontSize: "14",
              fontWeight: "bold",
            },
          },
          data: pieData,
        },
      ],
    };

    branchPieChart.setOption(option, true);
  } catch (error) {
    console.error("門市銷售圖表更新錯誤:", error);
  }
};

// 監聽圖表容器的可見性變化
const initChartWhenVisible = () => {
  // 使用 MutationObserver 監聽 DOM 變化
  const observer = new MutationObserver((mutations) => {
    // 只在圖表標籤激活且尚未初始化圖表時更新
    if (
      activeTab.value === "chart" &&
      pieChartRef.value &&
      branchPieChartRef.value &&
      (!pieChart || !branchPieChart)
    ) {
      // 使用 setTimeout 避免過於頻繁的更新
      clearTimeout(chartUpdateTimeout);
      chartUpdateTimeout = setTimeout(() => {
        updateChart();
      }, 300);
    }
  });

  // 監聽整個文檔的變化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
  });

  // 返回清理函數
  return () => {
    observer.disconnect();
    clearTimeout(chartUpdateTimeout);
  };
};

// 清理函數和超時變數
let cleanupObserver = null;
let chartUpdateTimeout = null;

// 在 onMounted 中添加初始化代碼
onMounted(async () => {
  await loadCategories();
  await loadBranchOptions();

  // 如果已有選擇項，自動載入數據
  if (selectedCategory.value && selectedBranches.value.length > 0) {
    fetchData();
  }

  // 處理圖表響應式
  const handleResize = () => {
    if (pieChart && pieChartRef.value) {
      try {
        pieChart.resize();
      } catch (e) {
        console.error("圖表調整大小錯誤:", e);
      }
    }
    if (branchPieChart && branchPieChartRef.value) {
      try {
        branchPieChart.resize();
      } catch (e) {
        console.error("圖表調整大小錯誤:", e);
      }
    }
  };

  window.addEventListener("resize", handleResize);

  // 監聽全屏狀態變化
  // document.addEventListener("fullscreenchange", handleFullscreenChange);
  // document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  // document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  // document.addEventListener("MSFullscreenChange", handleFullscreenChange);

  // 初始化圖表監聽
  cleanupObserver = initChartWhenVisible();
});

onUnmounted(() => {
  // 清理圖表實例
  if (pieChart) {
    try {
      pieChart.dispose();
      pieChart = null;
    } catch (e) {
      console.error("圖表清理錯誤:", e);
    }
  }
  if (branchPieChart) {
    try {
      branchPieChart.dispose();
      branchPieChart = null;
    } catch (e) {
      console.error("圖表清理錯誤:", e);
    }
  }

  // 移除事件監聽器
  window.removeEventListener("resize", () => {});
  // document.removeEventListener("fullscreenchange", handleFullscreenChange);
  // document.removeEventListener(
  //   "webkitfullscreenchange",
  //   handleFullscreenChange
  // );
  // document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
  // document.removeEventListener("MSFullscreenChange", handleFullscreenChange);

  // 清理 MutationObserver
  if (cleanupObserver) {
    cleanupObserver();
  }

  // 清理超時
  clearTimeout(chartUpdateTimeout);
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.search-button {
  min-height: 32px;
  min-width: 32px;
}

.custom-separator {
  height: 1px;
  background-color: #e0e0e0;
}

/* 全屏模式樣式 */
/* :fullscreen .pivot-table-container {
  width: 100%;
  overflow-x: auto;
} */

/* :fullscreen .q-card {
  border-radius: 0 !important;
} */

/* 針對 Safari */
/* :-webkit-full-screen .pivot-table-container {
  width: 100%;
  overflow-x: auto;
} */

/* :-webkit-full-screen .q-card {
  border-radius: 0 !important;
} */

/* 針對 Firefox */
/* :-moz-full-screen .pivot-table-container {
  width: 100%;
  overflow-x: auto;
} */

/* :-moz-full-screen .q-card {
  border-radius: 0 !important;
} */

/* 針對 MS Edge */
/* :-ms-fullscreen .pivot-table-container {
  width: 100%;
  overflow-x: auto;
} */

/* :-ms-fullscreen .q-card {
  border-radius: 0 !important;
} */

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: var(--q-primary);
  stroke-width: 5;
  stroke-linecap: round;
  stroke-dasharray: 283;
  stroke-dashoffset: 280;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 280;
  }
  50% {
    stroke-dashoffset: 75;
  }
  100% {
    stroke-dashoffset: 280;
  }
}

.loader-message {
  margin-top: 1rem;
  color: var(--q-primary);
  font-size: 0.9rem;
}

/* 空數據樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  fill: #ccc;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.2rem;
  font-weight: 500;
  color: #666;
  margin-bottom: 0.5rem;
}

.empty-subtext {
  font-size: 0.9rem;
  color: #999;
}

/* 樞紐表樣式 */
.pivot-table-container {
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pivot-table {
  border-collapse: collapse;
  width: 100%;
  font-size: 0.9em;
  color: #333;
}

.pivot-table th,
.pivot-table td {
  border: 1px solid #eee;
  padding: 8px 12px;
  text-align: left;
}

.pivot-table th.group-header,
.pivot-table th.total-header {
  text-align: center !important;
}

.pivot-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #555;
}

.pivot-table td {
  background-color: #fff;
}

.corner-header {
  border-bottom: 2px solid #eee;
  font-weight: bold;
  background-color: #f0f0f0;
}

.group-header {
  background-color: #f9f9f9;
  font-weight: bold;
  color: #444;
  border-bottom: 1px solid #eee;
  text-align: center !important;
}

.sub-header {
  background-color: #f0f0f0;
  font-weight: bold;
  color: #555;
  border-bottom: 1px solid #eee;
}

.total-header {
  background-color: #f0f0f0;
  font-weight: bold;
  color: #555;
  border-bottom: 1px solid #eee;
  text-align: center !important;
}

.branch-name {
  font-weight: bold;
  background-color: #f0f0f0;
  border-bottom: 1px solid #eee;
}

.qty-cell {
  text-align: right;
  font-weight: bold;
  color: #333;
}

.percent-cell {
  text-align: right;
  color: #333;
}

.total-qty {
  text-align: right;
  font-weight: bold;
  color: #333;
}

.total-percent {
  text-align: right;
  color: #333;
}

.grand-total-row {
  font-weight: bold;
  background-color: #f0f0f0;
}

.grand-total-label {
  font-weight: bold;
  background-color: #f0f0f0;
}

.grand-total-qty {
  text-align: right;
  font-weight: bold;
  color: #333;
}

.grand-total-percent {
  text-align: right;
  color: #333;
}

/* 表格全螢幕樣式 */
.pivot-table-container {
  position: relative;
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.table-header-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.fullscreen-btn {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-btn:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* 表格全螢幕模式 */
/* .pivot-table-container:fullscreen {
  padding: 20px;
  background-color: white;
  overflow: auto;
  display: flex;
  flex-direction: column;
} */

/* .pivot-table-container:fullscreen .pivot-table {
  margin: auto;
  width: auto;
  max-width: none;
} */

/* .pivot-table-container:fullscreen .table-header-actions {
  position: fixed;
  top: 16px;
  right: 16px;
} */

/* 針對 Safari */
/* .pivot-table-container:-webkit-full-screen {
  padding: 20px;
  background-color: white;
  overflow: auto;
  display: flex;
  flex-direction: column;
} */

/* .pivot-table-container:-webkit-full-screen .pivot-table {
  margin: auto;
  width: auto;
  max-width: none;
} */

/* .pivot-table-container:-webkit-full-screen .table-header-actions {
  position: fixed;
  top: 16px;
  right: 16px;
} */

/* 針對 Firefox */
/* .pivot-table-container:-moz-full-screen {
  padding: 20px;
  background-color: white;
  overflow: auto;
  display: flex;
  flex-direction: column;
} */

/* .pivot-table-container:-moz-full-screen .pivot-table {
  margin: auto;
  width: auto;
  max-width: none;
} */

/* .pivot-table-container:-moz-full-screen .table-header-actions {
  position: fixed;
  top: 16px;
  right: 16px;
} */

/* 針對 MS Edge */
/* .pivot-table-container:-ms-fullscreen {
  padding: 20px;
  background-color: white;
  overflow: auto;
  display: flex;
  flex-direction: column;
} */

/* .pivot-table-container:-ms-fullscreen .pivot-table {
  margin: auto;
  width: auto;
  max-width: none;
} */

/* .pivot-table-container:-ms-fullscreen .table-header-actions {
  position: fixed;
  top: 16px;
  right: 16px;
} */

/* 圖表卡片樣式 */
.chart-card {
  height: 100%;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.chart-card .q-card__section {
  padding: 12px 16px;
}

/* 查詢條件顯示樣式 */
.query-info-container {
  padding: 0 8px;
}

.query-info-card {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.query-info-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  flex: 1;
  min-width: 150px;
}

.query-info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.03);
  margin-right: 12px;
}

.query-info-content {
  flex: 1;
}

.query-info-label {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 2px;
}

.query-info-value {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.query-info-divider {
  width: 1px;
  height: 36px;
  background-color: #e0e0e0;
  margin: 0 16px;
  display: none;
}

.query-info-mobile-row {
  display: flex;
  flex: 1;
  width: 100%;
}

@media (min-width: 768px) {
  .query-info-divider {
    display: block;
  }

  .query-info-item {
    flex: 1;
  }

  .query-info-mobile-row {
    display: flex;
    flex: 2;
  }

  .query-info-category,
  .query-info-branch {
    flex: 1;
  }

  .query-info-date {
    flex: 1;
  }
}

@media (max-width: 767px) {
  .query-info-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .query-info-date {
    width: 100%;
    margin-bottom: 12px;
  }

  .query-info-mobile-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .query-info-category,
  .query-info-branch {
    flex: 1;
    min-width: 0;
  }

  .query-info-category {
    margin-right: 8px;
  }
}

@media (max-width: 600px) {
  .pivot-table th,
  .pivot-table td {
    padding: 6px 8px;
    font-size: 0.85em;
  }

  .empty-icon {
    width: 50px;
    height: 50px;
  }

  .empty-text {
    font-size: 1rem;
  }

  .empty-subtext {
    font-size: 0.8rem;
  }
}

/* 簡化的樣式 */
.branch-selected {
  background-color: rgba(25, 118, 210, 0.05);
  border-left: 3px solid var(--q-primary);
}
</style>
