-- 在 Dep 資料表中新增 Color 欄位
ALTER TABLE dep ADD Color NVARCHAR(20) NULL;

-- 更新現有部門的顏色設定
UPDATE dep SET Color = 'general' WHERE id = '1';     -- 總務部 → 總務綠
UPDATE dep SET Color = 'info' WHERE id = '2';        -- 資訊部 → 資訊藍  
UPDATE dep SET Color = 'marketing' WHERE id = '3';   -- 行銷部 → 行銷紫
UPDATE dep SET Color = 'hr' WHERE id = '4';          -- 人資部 → 人資橙

-- 如果有其他部門，可以繼續新增
-- UPDATE dep SET Color = 'branch1' WHERE id = '5';    -- 門市紅
-- UPDATE dep SET Color = 'branch2' WHERE id = '6';    -- 門市青
-- UPDATE dep SET Color = 'branch3' WHERE id = '7';    -- 門市黃

-- 為新增的欄位建立索引以提升查詢效能
CREATE INDEX IX_Dep_Color ON dep(Color);

-- 新增註釋說明
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'部門色系代碼，對應前端色系選項', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'dep',
    @level2type = N'COLUMN', @level2name = N'Color';

PRINT '部門 Color 欄位新增完成，已更新現有部門顏色設定';
