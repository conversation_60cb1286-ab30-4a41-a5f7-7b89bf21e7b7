<template>
  <q-page class="q-pa-sm" style="width: 100%; max-width: 800px; margin: 0 auto">
    <!-- 頁面標題卡片 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">權限群組管理</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="green"
              @click="openAddGroupDialog"
            >
              <userPlus :stroke-width="1" :size="26" />
              <q-tooltip>新增群組</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- 搜尋欄 -->
      <q-card-section class="q-pt-none q-pb-xs">
        <q-input
          v-model="searchQuery"
          placeholder="搜尋群組..."
          dense
          outlined
          clearable
          @update:model-value="filterGroups"
        >
          <template v-slot:prepend>
            <search :stroke-width="1.5" :size="20" class="text-grey-7" />
          </template>
          <template v-slot:append v-if="searchQuery">
            <q-icon
              name="close"
              class="cursor-pointer"
              @click="
                searchQuery = '';
                filterGroups();
              "
            />
          </template>
        </q-input>
      </q-card-section>
    </q-card>

    <!-- 群組列表 -->
    <div class="row q-col-gutter-md q-mt-md" style="width: 100%">
      <!-- 群組卡片 -->
      <div
        v-for="group in filteredGroups"
        :key="group.Code"
        class="col-12 col-sm-6"
      >
        <q-card class="group-card">
          <q-card-section class="q-pb-none">
            <div class="row items-center no-wrap">
              <div class="col">
                <div class="text-subtitle1 text-weight-medium text-primary">
                  {{ group.Name }}
                </div>
                <div class="text-caption text-grey-7">
                  群組代碼: {{ group.Code }}
                </div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  size="sm"
                  color="primary"
                  @click="openEditGroupDialog(group)"
                >
                  <edit2 :stroke-width="1.5" :size="18" />
                </q-btn>
                <q-btn
                  flat
                  round
                  size="sm"
                  color="negative"
                  @click="confirmDelete(group.Code)"
                  :disable="isAdminGroup(group.Code)"
                >
                  <trash2 :stroke-width="1.5" :size="18" />
                  <q-tooltip v-if="isAdminGroup(group.Code)">
                    系統管理員群組不可刪除
                  </q-tooltip>
                </q-btn>
              </div>
            </div>
          </q-card-section>

          <q-card-section class="q-pt-xs">
            <q-separator class="q-my-sm" />
            <div class="row items-center">
              <clipboardList
                :stroke-width="1.5"
                :size="18"
                class="text-grey-7 q-mr-xs"
              />
              <div class="text-caption text-grey-7">備註</div>
            </div>
            <div class="text-body2 q-mt-xs">
              {{ group.Remark || "無備註資訊" }}
            </div>
          </q-card-section>

          <!-- 群組成員數量 (可選功能) -->
          <q-card-section
            class="q-pt-none"
            v-if="group.memberCount !== undefined"
          >
            <div class="row items-center">
              <users
                :stroke-width="1.5"
                :size="18"
                class="text-grey-7 q-mr-xs"
              />
              <div class="text-caption text-grey-7">
                成員數量: {{ group.memberCount || 0 }}
              </div>
            </div>
          </q-card-section>

          <!-- 群組權限 (可選功能) -->
          <q-card-section
            class="q-pt-none"
            v-if="group.permissions && group.permissions.length"
          >
            <div class="row items-center">
              <shieldCheck
                :stroke-width="1.5"
                :size="18"
                class="text-grey-7 q-mr-xs"
              />
              <div class="text-caption text-grey-7">
                權限: {{ group.permissions.length }}
              </div>
            </div>
            <div class="q-mt-xs flex flex-wrap q-gutter-xs">
              <q-badge
                v-for="(perm, idx) in group.permissions.slice(0, 3)"
                :key="idx"
                outline
                color="primary"
                class="q-px-sm"
              >
                {{ perm }}
              </q-badge>
              <q-badge
                v-if="group.permissions.length > 3"
                outline
                color="grey"
                class="q-px-sm"
              >
                +{{ group.permissions.length - 3 }}
              </q-badge>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 無資料顯示 -->
      <div v-if="filteredGroups.length === 0" class="col-12">
        <q-card class="text-center q-pa-lg">
          <clipboardX
            :stroke-width="1.5"
            :size="48"
            class="text-grey-5 q-mb-md"
          />
          <div class="text-h6 text-grey-7">
            {{ searchQuery ? "找不到符合的群組" : "尚無群組資料" }}
          </div>
          <div class="text-body2 text-grey-6 q-mt-sm">
            {{ searchQuery ? "請嘗試其他關鍵字" : "點擊右上角按鈕新增群組" }}
          </div>
        </q-card>
      </div>
    </div>

    <!-- 新增/編輯群組對話框 -->
    <q-dialog v-model="groupDialog" persistent>
      <q-card style="width: 100%; max-width: 450px; border-radius: 10px">
        <q-card-section class="row items-center">
          <div class="text-h6 text-primary">
            {{ isEditing ? "編輯群組" : "新增群組" }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section class="q-pt-md">
          <div class="row q-col-gutter-md">
            <div class="col-12">
              <q-input
                v-model="groupForm.code"
                label="群組代碼"
                outlined
                dense
                :readonly="isEditing"
                :rules="[(val) => !!val || '群組代碼為必填']"
              >
                <template v-slot:prepend>
                  <hash :stroke-width="1.5" :size="20" class="text-grey-7" />
                </template>
              </q-input>
            </div>

            <div class="col-12">
              <q-input
                v-model="groupForm.name"
                label="群組名稱"
                outlined
                dense
                :rules="[(val) => !!val || '群組名稱為必填']"
              >
                <template v-slot:prepend>
                  <type :stroke-width="1.5" :size="20" class="text-grey-7" />
                </template>
              </q-input>
            </div>

            <div class="col-12">
              <q-input
                v-model="groupForm.remark"
                label="備註說明"
                outlined
                dense
                type="textarea"
                autogrow
              >
                <template v-slot:prepend>
                  <clipboardList
                    :stroke-width="1.5"
                    :size="20"
                    class="text-grey-7"
                  />
                </template>
              </q-input>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            :label="isEditing ? '更新' : '新增'"
            color="primary"
            @click="saveGroup"
            :loading="isSaving"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 刪除確認對話框 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card style="width: 350px; border-radius: 10px">
        <q-card-section class="column items-center q-pt-lg">
          <div class="delete-icon-wrapper">
            <alertTriangle
              :stroke-width="1.5"
              :size="35"
              class="text-negative"
            />
          </div>
          <div class="text-h6 text-weight-bold q-mt-md">確認刪除群組</div>
          <div class="text-body2 text-grey-7 text-center q-mt-sm">
            此操作將永久刪除該群組，且無法復原。<br />
            確定要繼續嗎？
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn
            unelevated
            label="刪除"
            color="negative"
            @click="deleteGroupConfirmed"
            :loading="isDeleting"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";
import {
  UserPlus as userPlus,
  Search as search,
  Edit2 as edit2,
  Trash2 as trash2,
  ClipboardList as clipboardList,
  Users as users,
  ShieldCheck as shieldCheck,
  ClipboardX as clipboardX,
  Hash as hash,
  Type as type,
  AlertTriangle as alertTriangle,
} from "lucide-vue-next";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 群組資料
const groups = ref([]);
const filteredGroups = ref([]);
const searchQuery = ref("");

// 對話框狀態
const groupDialog = ref(false);
const deleteDialog = ref(false);
const isEditing = ref(false);
const deleteTargetId = ref(null);
const isSaving = ref(false);
const isDeleting = ref(false);

// 表單資料
const groupForm = ref({
  code: "",
  name: "",
  remark: "",
});

// 獲取群組資料
const fetchUsersgroup = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/users/get_usersgroup`);
    groups.value = response.data.map((group) => ({
      ...group,
      Code: group.Code.trim(),
      Name: group.Name.trim(),
      Remark: group.Remark?.trim() || "",
    }));
    filterGroups();
  } catch (error) {
    console.error("讀取群組資料失敗:", error);
    $q.notify({
      type: "negative",
      message: "讀取群組資料失敗",
      position: "top",
    });
  }
};

// 過濾群組
const filterGroups = () => {
  if (!searchQuery.value) {
    filteredGroups.value = [...groups.value];
    return;
  }

  const query = searchQuery.value.toLowerCase();
  filteredGroups.value = groups.value.filter(
    (group) =>
      group.Name.toLowerCase().includes(query) ||
      group.Code.toLowerCase().includes(query) ||
      (group.Remark && group.Remark.toLowerCase().includes(query))
  );
};

// 開啟新增群組對話框
const openAddGroupDialog = () => {
  isEditing.value = false;
  groupForm.value = {
    code: "",
    name: "",
    remark: "",
  };
  groupDialog.value = true;
};

// 開啟編輯群組對話框
const openEditGroupDialog = (group) => {
  isEditing.value = true;
  groupForm.value = {
    code: group.Code,
    name: group.Name,
    remark: group.Remark || "",
  };
  groupDialog.value = true;
};

// 儲存群組
const saveGroup = async () => {
  // 表單驗證
  if (!groupForm.value.code.trim()) {
    return $q.notify({
      type: "warning",
      message: "請輸入群組代碼",
      position: "top",
    });
  }

  if (!groupForm.value.name.trim()) {
    return $q.notify({
      type: "warning",
      message: "請輸入群組名稱",
      position: "top",
    });
  }

  isSaving.value = true;

  try {
    if (isEditing.value) {
      // 更新群組
      await apiClient.put(
        `${apiBaseUrl}/users/usersgroup/${groupForm.value.code.trim()}`,
        {
          Name: groupForm.value.name.trim(),
          Remark: groupForm.value.remark.trim(),
        }
      );
      $q.notify({
        type: "positive",
        message: "群組資料已更新",
        position: "top",
      });
    } else {
      // 新增群組
      await apiClient.post(`${apiBaseUrl}/users/usersgroup`, {
        Code: groupForm.value.code.trim(),
        Name: groupForm.value.name.trim(),
        Remark: groupForm.value.remark.trim(),
      });
      $q.notify({
        type: "positive",
        message: "群組已新增成功",
        position: "top",
      });
    }

    groupDialog.value = false;
    await fetchUsersgroup();
  } catch (error) {
    console.error("儲存群組失敗:", error);
    $q.notify({
      type: "negative",
      message: isEditing.value ? "更新群組失敗" : "新增群組失敗",
      position: "top",
    });
  } finally {
    isSaving.value = false;
  }
};

// 確認刪除
const confirmDelete = (id) => {
  // 檢查是否為 Admin 群組
  if (isAdminGroup(id)) {
    $q.notify({
      type: "warning",
      message: "系統管理員群組不可刪除",
      position: "top",
    });
    return;
  }

  deleteTargetId.value = id;
  deleteDialog.value = true;
};

// 執行刪除
const deleteGroupConfirmed = async () => {
  // 再次確認不是 Admin 群組 (額外安全檢查)
  if (isAdminGroup(deleteTargetId.value)) {
    $q.notify({
      type: "warning",
      message: "系統管理員群組不可刪除",
      position: "top",
    });
    deleteDialog.value = false;
    return;
  }

  isDeleting.value = true;

  try {
    await apiClient.delete(
      `${apiBaseUrl}/users/usersgroup/${deleteTargetId.value}`
    );
    deleteDialog.value = false;
    await fetchUsersgroup();
    $q.notify({
      type: "info",
      message: "群組已刪除",
      position: "top",
    });
  } catch (error) {
    console.error("刪除群組失敗:", error);
    $q.notify({
      type: "negative",
      message: "刪除群組失敗",
      position: "top",
    });
  } finally {
    isDeleting.value = false;
  }
};

// 在 script setup 區塊中添加 isAdminGroup 函數
const isAdminGroup = (groupCode) => {
  return groupCode.toLowerCase() === "admin";
};

onMounted(() => {
  fetchUsersgroup();
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.custom-separator {
  height: 0.5px;
  margin-bottom: 16px;
}

.group-card {
  border-radius: 10px;
  transition: all 0.2s ease;
  border-left: 3px solid var(--q-primary);
}

.group-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.delete-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(255, 59, 48, 0.1);
}
</style>
