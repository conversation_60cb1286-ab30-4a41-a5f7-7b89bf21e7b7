<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 900px; margin: 0 auto; overflow: hidden"
  >
    <q-card
      class="no-shadow full-width"
      style="display: flex; flex-direction: column; height: calc(100dvh - 85px)"
    >
      <!-- 📌 置頂資訊區：返回 + 桌號 + Tabs -->
      <div
        class="bg-white q-pa-sm q-mx-sm q-mt-sm"
        style="position: sticky; top: 0; z-index: 10"
      >
        <!-- 🔙 返回與送出 -->
        <div class="row items-center q-mb-sm">
          <div class="col-auto">
            <q-btn
              flat
              color="red"
              icon="arrow_back"
              label="返回桌況"
              @click="goBack"
              style="padding-left: 0"
            />
          </div>
          <div class="col text-right">
            <q-btn
              color="green"
              icon="shopping_cart"
              @click="openCartDialog"
              unelevated
            >
              <span class="q-ml-sm">送出點餐</span>
              <q-badge v-if="totalQty > 0" color="red" floating>
                {{ totalQty }}
              </q-badge>
            </q-btn>
          </div>
        </div>

        <!-- 📌 桌號資訊 -->
        <q-banner class="bg-grey-2 text-dark q-mb-sm rounded-borders q-pa-sm">
          <div class="row items-center justify-between">
            <div class="text-h6 text-primary">
              桌號：<span class="text-weight-bold">{{
                props.OrderInfo.name
              }}</span>
            </div>
            <div class="text-caption text-grey-8">
              <q-icon name="people" class="q-mr-xs" />{{
                props.OrderInfo.person
              }}
              人
            </div>
          </div>
          <div class="q-mt-xs row items-center justify-between">
            <div class="text-subtitle2 text-grey-8">
              {{ props.OrderInfo.code }}
            </div>
            <q-btn
              flat
              dense
              icon="list_alt"
              label="點餐紀錄"
              color="brown"
              @click="openHistory()"
            />
          </div>
        </q-banner>

        <!-- 🔹 分類 Tabs：可橫向滑動 -->
        <div style="position: relative">
          <!-- 左箭頭遮罩 -->
          <div class="transparent-overlay absolute-left"></div>

          <!-- 左箭頭按鈕 -->
          <div
            class="absolute-left flex items-center"
            style="height: 100%; width: 32px; z-index: 2; pointer-events: none"
          >
            <q-btn
              round
              dense
              flat
              icon="chevron_left"
              @click="scrollTabs(-100)"
              style="pointer-events: auto"
            />
          </div>

          <!-- 中間 Tabs -->
          <div
            ref="tabsContainer"
            class="row no-wrap"
            style="
              overflow-x: auto;
              -webkit-overflow-scrolling: touch;
              scroll-behavior: smooth;
              scrollbar-width: none;
              -ms-overflow-style: none;
            "
          >
            <q-tabs
              v-model="activeCategory"
              class="text-primary bg-white q-px-lg"
              align="left"
              dense
              narrow-indicator
              swipeable
              inline-label
              shrink
              style="min-width: max-content"
            >
              <q-tab
                v-for="cat in sortedCategories"
                :key="cat.name"
                :name="cat.name"
                :label="cat.name"
              />
            </q-tabs>
          </div>

          <!-- 右箭頭遮罩 -->
          <div class="transparent-overlay absolute-right"></div>

          <!-- 右箭頭按鈕 -->
          <div
            class="absolute-right flex items-center"
            style="height: 100%; width: 32px; z-index: 2; pointer-events: none"
          >
            <q-btn
              round
              dense
              flat
              icon="chevron_right"
              @click="scrollTabs(100)"
              style="pointer-events: auto"
            />
          </div>
        </div>
      </div>

      <!-- 🍱 餐點列表 -->
      <div
        class="q-pa-sm scroll q-mx-sm q-mb-sm"
        style="flex: 1; overflow-y: auto"
      >
        <div class="row q-col-gutter-sm">
          <div
            v-for="item in filteredMenu"
            :key="item.code"
            class="col-12 col-sm-3 col-md-3"
          >
            <q-card
              v-ripple
              class="q-pa-sm square cursor-pointer"
              style="box-shadow: none; border: 1px solid #e0e0e0"
              @click="selectItem(item)"
            >
              <div class="text-subtitle1 ellipsis" style="padding-right: 32px">
                {{ item.name }}
              </div>
              <div class="text-caption text-grey">
                <template v-if="item.specs?.length > 1">
                  點擊選擇規格
                </template>
                <template v-else>
                  <span class="text-deep-orange">{{ item.specs[0]?.name }}</span
                  >： ${{ item.specs[0]?.price ?? 0 }}
                </template>
              </div>
              <q-btn
                icon="add"
                dense
                flat
                color="primary"
                class="absolute"
                style="top: 8px; right: 8px"
                @click.stop="selectItem(item)"
              />
            </q-card>
          </div>
        </div>
      </div>

      <!-- 🧾 Dialog：選擇規格與口味 -->
      <q-dialog v-model="showDialog" persistent>
        <q-card
          style="
            min-width: 320px;
            max-width: 500px;
            display: flex;
            flex-direction: column;
            max-height: 90vh;
          "
        >
          <!-- 標題列固定在頂部 -->
          <q-card-section
            class="row items-center justify-between q-py-sm bg-primary text-white"
            style="flex-shrink: 0"
          >
            <div class="text-h6 q-mx-sm">選擇規格與口味</div>
            <q-btn flat round icon="close" v-close-popup />
          </q-card-section>

          <!-- 品項名稱（固定） -->
          <q-card-section
            class="bg-grey-2 text-weight-bold text-subtitle1 text-center q-pa-sm rounded-borders"
            style="flex-shrink: 0"
          >
            {{ selectedItem?.name }}
          </q-card-section>

          <!-- 可滾動區域（內容區） -->
          <q-card-section
            class="q-gutter-md scroll"
            style="flex: 1; overflow-y: auto"
          >
            <!-- 規格選擇 -->
            <div v-if="selectedItem?.specs?.length">
              <div class="text-subtitle2 text-primary q-mb-xs">選擇規格</div>
              <q-option-group
                v-model="selectedSpec"
                :options="
                  selectedItem.specs.map((s) => ({
                    label: `${s.name}${
                      s.price ? `（${Number(s.price).toLocaleString()}元）` : ''
                    }`,
                    value: s, // ✅ 傳整個 spec 物件當 value
                  }))
                "
                type="radio"
                color="primary"
              />
            </div>

            <!-- 口味群組 -->
            <div
              v-for="group in selectedItem?.tastes || []"
              :key="group.taste_code"
              class="q-pt-sm"
            >
              <div class="text-subtitle2 text-primary">
                {{ group.taste_title }}
                <span class="text-caption text-grey-7">
                  {{
                    group.c_min > 0 && group.c_max > 0
                      ? `（最少選 ${group.c_min}，最多選 ${group.c_max}）`
                      : group.c_min > 0
                      ? `（最少選 ${group.c_min}）`
                      : group.c_max > 0
                      ? `（最多選 ${group.c_max}）`
                      : ""
                  }}
                </span>
              </div>
              <div
                v-if="isTasteGroupInvalid(group)"
                class="text-negative text-caption q-mt-xs"
              >
                請至少選擇 {{ group.c_min }} 項
              </div>
              <q-option-group
                :model-value="selectedTastesMap[group.taste_code]"
                :options="
                  group.items.map((i) => ({
                    label: `${i.name}${i.price ? `（+${i.price}元）` : ''}`,
                    value: i.name,
                  }))
                "
                type="checkbox"
                color="primary"
                :class="{
                  'q-field--error': isTasteGroupInvalid(group),
                  'q-mt-xs': true,
                }"
                @update:model-value="
                  (val) => handleTasteChange(group.taste_code, val, group)
                "
              />
            </div>

            <!-- 套餐群組 -->
            <div
              v-for="(group, groupIndex) in filteredSets"
              :key="`sets_${groupIndex}`"
              class="q-pt-sm"
            >
              <div class="text-subtitle2 text-primary">
                {{ group.sets_title }}
                <span class="text-caption text-grey-7">
                  {{
                    group.c_min > 0 && group.c_max > 0
                      ? `（最少選 ${group.c_min}，最多選 ${group.c_max}）`
                      : group.c_min > 0
                      ? `（最少選 ${group.c_min}）`
                      : group.c_max > 0
                      ? `（最多選 ${group.c_max}）`
                      : ""
                  }}
                </span>
              </div>
              <div
                v-if="isSetsGroupInvalid(group)"
                class="text-negative text-caption q-mt-xs"
              >
                請至少選擇 {{ group.c_min }} 項
              </div>

              <!-- 單選模式 -->
              <div v-if="group.mcq === '0'" class="q-gutter-y-sm q-mt-sm">
                <div
                  v-if="group.c_min > 0"
                  class="text-caption q-mb-sm"
                  :class="{ 'text-negative': isSetsGroupInvalid(group) }"
                >
                  已選擇
                  {{
                    selectedSetsMap[group.sets_code]?.selected?.length || 0
                  }}/{{ group.c_min }} 項
                </div>
                <div
                  v-for="item in group.items"
                  :key="item.code"
                  class="row items-center justify-between bg-grey-1 q-pa-sm rounded-borders"
                  :class="{
                    'bg-blue-1': isItemSelected(group.sets_code, item.code),
                  }"
                >
                  <div class="row items-center">
                    <q-checkbox
                      :model-value="isItemSelected(group.sets_code, item.code)"
                      @update:model-value="
                        (val) =>
                          toggleSetItem(group.sets_code, item.code, val, group)
                      "
                      color="primary"
                    />
                    <div class="q-ml-sm">
                      {{ item.name }}

                      <span
                        v-if="item.price && group.type === '1'"
                        class="text-caption text-grey-7"
                      >
                        （+{{ item.price }}元）
                      </span>
                      <span class="text-caption text-grey-7">
                        （{{ item.unit || "份" }}）
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 多選模式 -->
              <div v-else class="q-gutter-y-sm q-mt-sm">
                <div
                  v-for="item in group.items"
                  :key="item.code"
                  class="row items-center justify-between bg-grey-1 q-pa-sm rounded-borders"
                >
                  <div>
                    {{ item.name }}

                    <span
                      v-if="item.price && group.type === '1'"
                      class="text-caption text-grey-7"
                    >
                      （+{{ item.price }}元）
                    </span>
                    <span class="text-caption text-grey-7">
                      （{{ item.unit.trim() }}）
                    </span>
                  </div>
                  <div class="row items-center">
                    <q-btn
                      flat
                      round
                      dense
                      icon="remove"
                      color="grey"
                      @click="decrementSetItem(group.sets_code, item.code)"
                      :disable="!getSetItemQty(group.sets_code, item.code)"
                    />
                    <div class="q-mx-sm text-subtitle2">
                      {{ getSetItemQty(group.sets_code, item.code) }}
                    </div>
                    <q-btn
                      flat
                      round
                      dense
                      icon="add"
                      color="primary"
                      @click="incrementSetItem(group.sets_code, item.code)"
                      :disable="isSetItemMaxReached(group)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>

          <!-- 數量選擇（固定底部上方） -->
          <q-card-section
            class="row items-center justify-between q-mt-none q-px-md"
            style="flex-shrink: 0"
          >
            <div class="text-subtitle2">數量</div>
            <div class="row items-center q-gutter-sm">
              <q-btn
                flat
                round
                icon="remove"
                color="primary"
                @click="selectedQty > 1 && selectedQty--"
              />
              <q-input
                v-model.number="selectedQty"
                readonly
                dense
                outlined
                style="width: 80px"
                input-class="text-center text-h6"
              />
              <q-btn
                flat
                round
                icon="add"
                color="primary"
                @click="selectedQty++"
              />
            </div>
          </q-card-section>

          <!-- 操作列固定底部 -->
          <q-card-actions align="right" style="flex-shrink: 0">
            <q-btn
              flat
              label="確認"
              color="primary"
              :disable="
                (selectedItem?.specs?.length && !selectedSpec) ||
                !isTasteValid ||
                !isSetsValid
              "
              @click="confirmSelection"
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- 🛒 購物車明細 Dialog -->
      <q-dialog v-model="showCartDialog">
        <q-card style="min-width: 300px; max-width: 500px">
          <!-- 🔹 標題 -->
          <q-card-section
            class="row items-center text-primary text-h6 q-gutter-sm"
          >
            <q-icon name="shopping_cart" />
            <span>確認送出項目</span>
          </q-card-section>

          <!-- 📄 明細清單 -->
          <q-card-section style="max-height: 300px; overflow-y: auto">
            <div
              v-for="(item, i) in mergedItems"
              :key="i"
              class="q-pa-sm q-mb-sm bg-grey-2 rounded-borders"
            >
              <div class="row justify-between items-center no-wrap">
                <div class="col">
                  <!-- 🔹 品名 + 規格 + 數量 + 價格 -->
                  <div class="text-subtitle1">
                    {{ item.name }}
                    <span
                      v-if="
                        item.spec && (item.spec.name || item.spec) !== '標準'
                      "
                    >
                      （{{ item.spec.name || item.spec }}）
                    </span>
                    x {{ item.qty }}
                    <span class="text-caption q-ml-sm">
                      ｜${{ item.price * item.qty }}
                    </span>
                  </div>

                  <!-- 🔸 口味 -->
                  <div
                    v-if="item.tastes?.length"
                    class="text-caption text-grey-7 q-mt-xs"
                  >
                    口味：{{ item.tastes.map((t) => t.name || t).join("、") }}
                  </div>

                  <!-- 🍱 套餐 -->
                  <div
                    v-if="item.sets?.length"
                    class="text-caption text-grey-7 q-mt-xs"
                  >
                    <div
                      v-for="(set, setIdx) in item.sets"
                      :key="`set_${setIdx}`"
                    >
                      {{ set.sets_title }}：{{ set.item.name }}
                      <span v-if="set.item.sname">({{ set.item.sname }})</span>
                      x {{ set.qty }}
                      <span v-if="set.type === '1'"
                        >（+{{ set.item.price * set.qty }}元）</span
                      >
                    </div>
                  </div>
                </div>

                <!-- ❌ 刪除 -->
                <q-btn
                  flat
                  round
                  dense
                  color="negative"
                  icon="close"
                  @click="removeMergedItem(item)"
                />
              </div>
            </div>

            <!-- 空購物車 -->
            <div
              v-if="mergedItems.length === 0"
              class="text-grey text-center q-my-md"
            >
              🛒 購物車目前為空
            </div>
          </q-card-section>

          <!-- ✅ 操作按鈕 -->
          <q-card-actions align="right">
            <q-btn flat label="取消" v-close-popup />
            <q-btn
              :disable="mergedItems.length === 0"
              flat
              color="primary"
              label="確認送出"
              @click="submitOrder"
            />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-dialog v-model="orderHistoryDialog" persistent>
        <q-card
          style="
            width: 95vw;
            max-width: 540px;
            max-height: 450px;
            display: flex;
            flex-direction: column;
          "
        >
          <!-- 🔵 標題列 -->
          <q-card-section
            class="bg-brown-5 text-white q-py-sm q-px-md row items-center justify-between"
          >
            <div class="row items-center">
              <q-icon name="list_alt" size="24px" class="q-mr-sm" />
              <div class="text-subtitle1 text-weight-bold">點餐紀錄</div>
            </div>
            <q-btn
              flat
              round
              dense
              icon="close"
              color="white"
              @click="orderHistoryDialog = false"
            />
          </q-card-section>

          <!-- 📋 點餐清單 -->
          <q-separator />

          <q-card-section class="scroll" style="flex: 1; overflow-y: auto">
            <q-list bordered separator>
              <q-item
                v-for="(item, index) in orderHistory"
                :key="index"
                class="q-py-sm"
              >
                <q-item-section>
                  <div class="text-subtitle2 text-weight-medium">
                    {{ item.name }}
                    <span class="text-primary q-ml-sm">× {{ item.qty }}</span>
                  </div>
                  <div class="text-caption text-grey">
                    <template v-if="item.sname && item.sname !== '標準'">
                      規格：{{ item.sname }}
                    </template>
                    <template v-if="item.oremname">
                      <div>口味：{{ item.oremname }}</div>
                    </template>
                  </div>
                </q-item-section>

                <q-item-section side top>
                  <div class="text-subtitle1 text-negative">
                    ${{ Number(item.oprice).toLocaleString() }}
                  </div>
                </q-item-section>
              </q-item>

              <div
                v-if="orderHistory.length === 0"
                class="text-grey text-center q-my-md"
              >
                尚無紀錄
              </div>
            </q-list>
          </q-card-section>
        </q-card>
      </q-dialog>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const selectedTastesMap = ref({});
const selectedSetsMap = ref({});

// 根據當前選擇的規格過濾套餐
const filteredSets = computed(() => {
  if (!selectedItem.value) return [];

  // 確保 sets 存在且是數組
  const sets = selectedItem.value.sets || [];
  if (!Array.isArray(sets) || sets.length === 0) {
    return [];
  }

  const currentSpec = selectedSpec.value?.name || "";

  const filtered = sets.filter((set) => {
    // 如果套餐有指定規格，則只在選擇該規格時顯示
    if (set.sname) {
      const match = set.sname === currentSpec;
      return match;
    }
    // 沒有指定規格的套餐始終顯示
    return true;
  });

  return filtered;
});

const isTasteValid = computed(() => {
  if (
    !selectedItem.value ||
    !selectedItem.value.tastes ||
    selectedItem.value.tastes.length === 0
  )
    return true;
  return selectedItem.value.tastes.every((group) => {
    const selected = selectedTastesMap.value[group.taste_code] || [];
    return selected.length >= group.c_min;
  });
});

const isSetsValid = computed(() => {
  return filteredSets.value.every((group) => {
    // 如果沒有最小選擇要求，直接通過
    if (!group.c_min || group.c_min <= 0) return true;

    // 獲取該套餐的選擇情況
    const setData = selectedSetsMap.value[group.sets_code];
    if (!setData) return false;

    // 單選模式 (使用 checkbox)
    if (group.mcq === "0") {
      return (
        Array.isArray(setData.selected) &&
        setData.selected.length >= group.c_min
      );
    }

    // 多選模式：計算所有項目的總數量
    const totalSelected = Object.values(setData.items || {}).reduce(
      (sum, qty) => sum + qty,
      0
    );
    return totalSelected >= group.c_min;
  });
});

const isSetsGroupInvalid = (group) => {
  // 如果沒有最小選擇要求，不會無效
  if (!group.c_min || group.c_min <= 0) return false;

  const setData = selectedSetsMap.value[group.sets_code];
  if (!setData) return true;

  // 單選模式 (使用 checkbox)
  if (group.mcq === "0") {
    return (
      !Array.isArray(setData.selected) || setData.selected.length < group.c_min
    );
  }

  // 多選模式：計算所有項目的總數量
  const totalSelected = Object.values(setData.items || {}).reduce(
    (sum, qty) => sum + qty,
    0
  );
  return totalSelected < group.c_min;
};

const isSetItemMaxReached = (group) => {
  if (!group.c_max || group.c_max <= 0) return false;

  const setData = selectedSetsMap.value[group.sets_code];
  if (!setData || !setData.items) return false;

  const totalSelected = Object.values(setData.items).reduce(
    (sum, qty) => sum + qty,
    0
  );
  return totalSelected >= group.c_max;
};

const getSetItemQty = (setsCode, itemCode) => {
  return selectedSetsMap.value[setsCode]?.items?.[itemCode] || 0;
};

const handleSetsChange = (setsCode, val, qty, group) => {
  // 初始化套餐選擇狀態
  if (!selectedSetsMap.value[setsCode]) {
    selectedSetsMap.value[setsCode] = {
      selected: [],
      items: {},
      type: group.type,
      sets_title: group.sets_title,
    };
  }

  // 單選模式 (使用 checkbox)
  if (group.mcq === "0") {
    // 檢查最大選擇數量限制
    if (group.c_max > 0 && val.length > group.c_max) {
      // 如果超過最大選擇數，移除最早選擇的項目
      val = val.slice(-group.c_max);
    }

    selectedSetsMap.value[setsCode].selected = val;

    // 清除之前的項目
    selectedSetsMap.value[setsCode].items = {};

    // 為每個選中的項目添加到 items 中
    val.forEach((itemCode) => {
      const selectedItem = group.items.find((item) => item.code === itemCode);
      if (selectedItem) {
        selectedSetsMap.value[setsCode].items[itemCode] = 1; // 數量固定為 1
      }
    });
  }
  // 多選模式由 incrementSetItem 和 decrementSetItem 處理
};

const incrementSetItem = (setsCode, itemCode) => {
  const group = filteredSets.value.find((g) => g.sets_code === setsCode);
  if (!group) return;

  // 檢查是否達到最大選擇數
  if (isSetItemMaxReached(group)) return;

  // 初始化套餐選擇狀態
  if (!selectedSetsMap.value[setsCode]) {
    selectedSetsMap.value[setsCode] = {
      items: {},
      type: group.type,
      sets_title: group.sets_title,
    };
  }

  // 初始化項目數量
  if (!selectedSetsMap.value[setsCode].items) {
    selectedSetsMap.value[setsCode].items = {};
  }

  // 增加數量
  const currentQty = selectedSetsMap.value[setsCode].items[itemCode] || 0;
  selectedSetsMap.value[setsCode].items[itemCode] = currentQty + 1;
};

const decrementSetItem = (setsCode, itemCode) => {
  if (
    !selectedSetsMap.value[setsCode] ||
    !selectedSetsMap.value[setsCode].items
  )
    return;

  const currentQty = selectedSetsMap.value[setsCode].items[itemCode] || 0;
  if (currentQty <= 0) return;

  // 減少數量
  selectedSetsMap.value[setsCode].items[itemCode] = currentQty - 1;

  // 如果數量為 0，刪除該項目
  if (selectedSetsMap.value[setsCode].items[itemCode] === 0) {
    delete selectedSetsMap.value[setsCode].items[itemCode];
  }
};

const isTasteGroupInvalid = (group) => {
  const selected = selectedTastesMap.value[group.taste_code] || [];
  return group.c_min > 0 && selected.length < group.c_min;
};

const handleTasteChange = (tasteCode, val, group) => {
  // ✅ 如果有設定最大限制，才限制
  if (group.c_max > 0 && val.length > group.c_max) {
    val.pop(); // 超出限制，移除最後一個
  }

  selectedTastesMap.value[tasteCode] = val;
};

const showDialog = ref(false);

// ✅ 當選擇新 item 時初始化口味選擇

const orderHistoryDialog = ref(false);

const orderHistory = ref([]);

const tabsContainer = ref(null);
const selectedQty = ref(1);
const scrollTabs = (offset) => {
  if (tabsContainer.value) {
    tabsContainer.value.scrollLeft += offset;
  }
};

const totalQty = computed(() => {
  return selectedItems.value.reduce((sum, item) => sum + (item.qty || 1), 0);
});

const groupedList = computed(() => {
  const mergedMap = new Map();

  for (const item of selectedItems.value) {
    const specName = item.spec?.name || item.spec || "";
    const tasteNames = (item.tastes || []).map((t) => t.name || t).join(" ");

    // 加入套餐資訊到 key 中，確保有套餐的項目不會被合併
    const setsInfo = (item.sets || [])
      .map((s) => `${s.sets_code}:${s.item.code}:${s.qty}`)
      .slice()
      .sort()
      .join("|");

    const key = `${item.code}_${specName}_${tasteNames}_${setsInfo}`;

    const itemQty = item.qty || 1;

    if (!mergedMap.has(key)) {
      mergedMap.set(key, {
        code: item.code,
        name: item.name,
        spec: specName,
        unit: item.unit || "1份",
        price: item.price, // ✅ 不要重算
        tastes: tasteNames,
        sets: item.sets, // 加入套餐資訊
        qty: itemQty,
      });
    } else {
      mergedMap.get(key).qty += itemQty;
    }
  }

  return Array.from(mergedMap.values());
});

const props = defineProps({
  OrderInfo: Object,
});
const emit = defineEmits(["goBack"]);
const $q = useQuasar();

const goBack = () => emit("goBack");

const mergedItems = computed(() => {
  const map = new Map();

  selectedItems.value.forEach((item) => {
    // 🟡 取出規格名稱
    const specName =
      typeof item.spec === "string" ? item.spec : item.spec?.name || "";

    // 🟡 取出口味名稱陣列（轉為純文字），排序 + join 確保一致性
    const tasteNames = (item.tastes || [])
      .map((t) => (typeof t === "string" ? t : t.name || ""))
      .slice()
      .sort()
      .join("|");

    // 🟡 取出套餐資訊，轉為字串識別碼
    const setsInfo = (item.sets || [])
      .map((s) => `${s.sets_code}:${s.item.code}:${s.qty}`)
      .slice()
      .sort()
      .join("|");

    // 🟢 組成唯一 key
    const key = `${item.code}_${specName}_${tasteNames}_${setsInfo}`;

    const itemQty = item.qty || 1;

    if (!map.has(key)) {
      map.set(key, { ...item, qty: itemQty });
    } else {
      map.get(key).qty += itemQty;
    }
  });

  return Array.from(map.values());
});

// 🧾 分類與菜單資料
const sortedCategories = computed(() => {
  const unique = new Map();

  for (const item of menu.value) {
    const catName = item.category.name;
    const catSno = item.categorySno;

    if (!unique.has(catName)) {
      unique.set(catName, { name: catName, sno: catSno });
    }
  }

  return Array.from(unique.values()).sort((a, b) => a.sno - b.sno);
});

const menu = ref([]);

const activeCategory = ref("");
const filteredMenu = computed(() =>
  menu.value.filter((item) => item.category.name === activeCategory.value)
);

// 🛒 購物車項目
const selectedItems = ref([]);

// 🍲 點選品項相關變數
const selectedItem = ref(null);
const selectedSpec = ref("");

const selectItem = (item) => {
  //const hasSpecs = item.specs && item.specs.length > 1;
  const hasTastes = item.tastes && item.tastes.length > 0;
  const hasSets = item.sets && item.sets.length > 0;

  selectedItem.value = item;
  selectedSpec.value = item.specs?.[0] || null; // 不要存 name，存整個物件

  selectedQty.value = 1;

  // 重置口味選擇
  selectedTastesMap.value = {};

  // ✅ 初始化每組口味為空陣列（符合 q-option-group 結構）
  if (hasTastes) {
    item.tastes.forEach((group) => {
      selectedTastesMap.value[group.taste_code] = [];
    });
  }

  // 重置套餐選擇
  selectedSetsMap.value = {};

  // ✅ 有多規格或有口味或有套餐 → 顯示選擇 Dialog
  showDialog.value = true;
};

// 在 q-btn 確認按鈕中添加 isSetsValid 條件
// <q-btn
//   flat
//   label="確認"
//   color="primary"
//   :disable="(selectedItem?.specs?.length && !selectedSpec) || !isTasteValid || !isSetsValid"
//   @click="confirmSelection"
// />

const confirmSelection = () => {
  const spec = selectedSpec.value;
  const specPrice = Number(spec?.price ?? 0);

  // 處理口味
  const selectedTastes = [];
  for (const [taste_code, names] of Object.entries(selectedTastesMap.value)) {
    const group = selectedItem.value.tastes.find(
      (g) => g.taste_code === taste_code
    );
    if (group) {
      const matched = group.items.filter((i) => names.includes(i.name));
      selectedTastes.push(...matched);
    }
  }
  const tastePrice = selectedTastes.reduce(
    (sum, t) => sum + (Number(t.price) || 0),
    0
  );

  // 處理套餐
  const selectedSets = [];
  let setsPrice = 0;

  for (const [sets_code, setData] of Object.entries(selectedSetsMap.value)) {
    const group = filteredSets.value.find((g) => g.sets_code === sets_code);
    if (!group) continue;

    // 單選模式 (使用 checkbox)
    if (
      group.mcq === "0" &&
      Array.isArray(setData.selected) &&
      setData.selected.length > 0
    ) {
      // 處理每個選中的項目
      setData.selected.forEach((itemCode) => {
        const selectedItem = group.items.find((item) => item.code === itemCode);
        if (selectedItem) {
          selectedSets.push({
            sets_code,
            sets_title: group.sets_title,
            type: group.type,
            item: selectedItem,
            qty: 1,
          });

          // 如果是加點類型，計算價格
          if (group.type === "1") {
            setsPrice += Number(selectedItem.price || 0);
          }
        }
      });
    }
    // 多選模式
    else if (group.mcq === "1" && setData.items) {
      for (const [itemCode, qty] of Object.entries(setData.items)) {
        if (qty > 0) {
          const item = group.items.find((i) => i.code === itemCode);
          if (item) {
            selectedSets.push({
              sets_code,
              sets_title: group.sets_title,
              type: group.type,
              item,
              qty,
            });

            // 如果是加點類型，計算價格
            if (group.type === "1") {
              setsPrice += Number(item.price || 0) * qty;
            }
          }
        }
      }
    }
  }

  const totalPrice = specPrice + tastePrice + setsPrice;

  selectedItems.value.push({
    code: selectedItem.value.code,
    name: selectedItem.value.name,
    spec,
    tastes: selectedTastes,
    sets: selectedSets.length > 0 ? selectedSets : [], // 確保總是傳遞陣列
    price: totalPrice,
    unit: selectedItem.value.unit,
    qty: selectedQty.value,
  });

  showDialog.value = false;
};

// 🧾 購物車明細彈窗
const showCartDialog = ref(false);

const openCartDialog = () => {
  if (selectedItems.value.length === 0) {
    $q.notify({ type: "warning", message: "尚未選擇任何品項" });
    return;
  }
  showCartDialog.value = true;
};

const removeMergedItem = (targetItem) => {
  // 🔧 統一處理 spec.name
  const specName =
    typeof targetItem.spec === "string"
      ? targetItem.spec
      : targetItem.spec?.name || "";

  // 🔧 統一處理 taste 名稱
  const tasteNames = (targetItem.tastes || [])
    .map((t) => (typeof t === "string" ? t : t.name || ""))
    .slice()
    .sort()
    .join("|");

  // 🔧 統一處理套餐資訊
  const setsInfo = (targetItem.sets || [])
    .map((s) => `${s.sets_code}:${s.item.code}:${s.qty}`)
    .slice()
    .sort()
    .join("|");

  const key = `${targetItem.code}_${specName}_${tasteNames}_${setsInfo}`;

  // 🔁 過濾掉符合 key 的所有品項
  selectedItems.value = selectedItems.value.filter((item) => {
    const itemSpecName =
      typeof item.spec === "string" ? item.spec : item.spec?.name || "";

    const itemTasteNames = (item.tastes || [])
      .map((t) => (typeof t === "string" ? t : t.name || ""))
      .slice()
      .sort()
      .join("|");

    const itemSetsInfo = (item.sets || [])
      .map((s) => `${s.sets_code}:${s.item.code}:${s.qty}`)
      .slice()
      .sort()
      .join("|");

    const itemKey = `${item.code}_${itemSpecName}_${itemTasteNames}_${itemSetsInfo}`;

    return itemKey !== key;
  });
};

// 🚀 送單處理
const submitOrder = async () => {
  const payload = {
    ip: props.OrderInfo.ip,
    scode: props.OrderInfo.code,
    room: props.OrderInfo.roomcode,
    roomname: props.OrderInfo.name,
    person: props.OrderInfo.person,
    hostname: props.OrderInfo.hostname,
    APIPrint: props.OrderInfo.APIPrint,
    items: groupedList.value.map((item) => {
      // 直接傳遞套餐陣列，不轉換為字串
      return {
        code: item.code,
        name: item.name,
        spec: item.spec,
        unit: item.unit,
        price: item.price,
        tastes: item.tastes, // 已是空格分隔字串
        sets: item.sets, // 直接傳遞套餐陣列
        qty: item.qty,
      };
    }),
  };
  try {
    const res = await apiClient.post(
      `${apiBaseUrl}/weborder/insert_Order`,
      payload
    );
    if (res.data.success) {
      $q.notify({ type: "positive", message: res.data.message });
    } else {
      $q.notify({ type: "negative", message: "開桌失敗：" + res.data.message });
    }
  } catch (err) {
    console.error("開桌錯誤", err);
    $q.notify({ type: "negative", message: "發生錯誤：" + err.message });
  }
  showCartDialog.value = false;
  selectedItems.value = []; // ✅ 清空購物車
  // ✅ 你可以在這裡串接後端 API
  // await apiClient.post('/order/send', payload);
};

const fetchMenu = async () => {
  try {
    const res = await apiClient.get(
      `${apiBaseUrl}/orderinternal/get_InternalMenu`,
      {
        params: {
          branch: props.OrderInfo.Branch,
        },
      }
    );

    if (res.data?.success) {
      menu.value = res.data.data;
    } else {
      console.error("❌ 載入菜單失敗:", res.data.message);
    }
  } catch (err) {
    console.error("❌ 載入菜單時發生錯誤:", err.message);
  } finally {
  }
};

const openHistory = async () => {
  await fetchHistory();
  orderHistoryDialog.value = true;
};

const fetchHistory = async () => {
  if (!props.OrderInfo.ip) {
    console.error("缺少 IP 無法載入歷史");
    return;
  }

  try {
    const res = await apiClient.get(
      `${apiBaseUrl}/orderinternal/get_OrderHistory`,
      {
        params: {
          branch: props.OrderInfo.Branch,
          scode: props.OrderInfo.code,
        },
      }
    );

    if (res.data?.success) {
      orderHistory.value = res.data.data;
    } else {
      console.error("❌ 載入歷史失敗:", res.data.message);
    }
  } catch (err) {
    console.error("❌ 載入歷史時發生錯誤:", err.message);
  } finally {
  }
};

watch(menu, (newVal) => {
  if (newVal.length && sortedCategories.value.length) {
    activeCategory.value = sortedCategories.value[0].name;
  }
});
// 🚀 初始載入（或你可手動觸發）
onMounted(async () => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "載入中...",
  });
  await fetchMenu();
  $q.loading.hide();
});

const isItemSelected = (setsCode, itemCode) => {
  if (
    !selectedSetsMap.value[setsCode] ||
    !selectedSetsMap.value[setsCode].selected
  ) {
    return false;
  }
  return selectedSetsMap.value[setsCode].selected.includes(itemCode);
};

const toggleSetItem = (setsCode, itemCode, isChecked, group) => {
  // 初始化套餐選擇狀態
  if (!selectedSetsMap.value[setsCode]) {
    selectedSetsMap.value[setsCode] = {
      selected: [],
      items: {},
      type: group.type,
      sets_title: group.sets_title,
    };
  }

  // 確保 selected 是數組
  if (!Array.isArray(selectedSetsMap.value[setsCode].selected)) {
    selectedSetsMap.value[setsCode].selected = [];
  }

  // 更新選擇狀態
  if (isChecked) {
    // 檢查是否達到最大選擇數
    if (
      group.c_max > 0 &&
      selectedSetsMap.value[setsCode].selected.length >= group.c_max
    ) {
      // 如果達到最大選擇數，移除第一個選擇的項目
      selectedSetsMap.value[setsCode].selected.shift();
    }
    // 添加新選擇的項目
    selectedSetsMap.value[setsCode].selected.push(itemCode);
    // 同時更新 items 對象
    selectedSetsMap.value[setsCode].items[itemCode] = 1;
  } else {
    // 移除選擇的項目
    selectedSetsMap.value[setsCode].selected = selectedSetsMap.value[
      setsCode
    ].selected.filter((code) => code !== itemCode);
    // 同時更新 items 對象
    delete selectedSetsMap.value[setsCode].items[itemCode];
  }
};
</script>

<style scoped>
.transparent-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 32px;
  z-index: 1;
  pointer-events: none;
  background: linear-gradient(to right, white 60%, transparent);
}

.absolute-left {
  left: 0;
}

.absolute-right {
  right: 0;
  background: linear-gradient(to left, white 60%, transparent);
}
</style>
