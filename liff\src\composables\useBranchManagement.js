import { ref, computed } from "vue";
import { useQuasar, QSpinnerFacebook, QSpinnerGears } from "quasar";
import { DateTime } from "luxon";
import apiClient from "../api";

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

export function useBranchManagement() {
  const $q = useQuasar();

  // 響應式狀態
  const branches = ref([]);
  const selectedBranch = ref(null);
  const departments = ref([]);
  const tablesOptions = ref([]);
  const connectionStatus = ref("checking");
  const isLoading = ref(false);

  // 選項資料
  const statusOptions = ref([
    { label: "總部", value: "2" },
    { label: "正常", value: "1" },
    { label: "關閉", value: "0" },
  ]);

  const brandsOptions = ref([
    { label: "小蒙牛", value: "MO" },
    { label: "金大鋤", value: "SO" },
    { label: "精緻鍋物", value: "YL" },
    { label: "燒肉老大", value: "LD" },
    { label: "金大鋤mini", value: "SM" },
  ]);

  const InvestOptions = ref([
    { label: "直營", value: "0" },
    { label: "加盟", value: "1" },
  ]);

  // 工具函數
  const UTCformatDateTime = (dateString) => {
    if (!dateString) return "無日期";
    return DateTime.fromISO(dateString, { zone: "utc" })
      .setZone("Asia/Taipei")
      .toFormat("yyyy-MM-dd HH:mm");
  };

  const getStatusColor = (status) => {
    return status === "1" ? "green" : status === "0" ? "red" : "orange";
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      2: "總部",
      1: "正常",
      0: "關閉",
    };
    return statusMap[status] || "未知狀態";
  };

  // API 函數
  const fetchBranches = async () => {
    try {
      isLoading.value = true;
      const response = await apiClient.get(`${apiBaseUrl}/branch/get_branch`);
      branches.value = response.data.map((user) => {
        const cleaned = Object.fromEntries(
          Object.entries(user).map(([key, value]) => [
            key,
            typeof value === "string" ? value.trim() : value,
          ])
        );
        cleaned.Rtime = UTCformatDateTime(cleaned.Rtime);
        return cleaned;
      });
    } catch (error) {
      console.error("獲取門市資料失敗:", error);
      $q.notify({ type: "negative", message: "載入門市資料失敗" });
    } finally {
      isLoading.value = false;
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await apiClient.get(
        `${apiBaseUrl}/deps/get_departments`
      );
      departments.value = response.data.map((deps) => ({
        value: deps.Id.trim(),
        label: deps.Name.trim(),
      }));
    } catch (error) {
      console.error("載入部門失敗:", error);
      $q.notify({ type: "negative", message: "載入部門資料失敗" });
    }
  };

  const fetchTables = async () => {
    try {
      const response = await apiClient.get(`${apiBaseUrl}/system/get_Tables`);
      tablesOptions.value = response.data.map((tables) => ({
        value: tables.Tcode.trim(),
        label: tables.Tname.trim(),
      }));
    } catch (error) {
      console.error("載入資料類別失敗:", error);
      $q.notify({ type: "negative", message: "載入資料類別失敗" });
    }
  };

  const checkConnection = async () => {
    if (!selectedBranch.value?.Cod_cust) {
      connectionStatus.value = "offline";
      return;
    }

    try {
      const response = await apiClient.get(
        `${apiBaseUrl}/ping?cod_cust=${selectedBranch.value.Cod_cust}`
      );
      connectionStatus.value =
        response.data.status === "online" ? "online" : "offline";
    } catch (error) {
      connectionStatus.value = "offline";
    }
  };

  const syncTables = async (tables, branchCode, branchIp) => {
    $q.loading.show({
      spinner: QSpinnerGears,
      spinnerColor: "info",
      message: "資料同步中...",
    });

    try {
      const response = await apiClient.post(
        `${apiBaseUrl}/system/sync_Tables`,
        {
          className: tables,
          branchCode,
          branchIp,
        }
      );

      if (response.data.status === "success") {
        $q.notify({ type: "positive", message: "門市資料同步成功！" });
        await fetchBranches();

        // 更新選中門市的回收時間
        const updated = branches.value.find((b) => b.Cod_cust === branchCode);
        if (updated && selectedBranch.value) {
          selectedBranch.value.Rtime = updated.Rtime;
        }
      } else {
        $q.notify({
          type: "warning",
          message: "同步失敗：" + response.data.message,
        });
      }
    } catch (error) {
      console.error("同步錯誤：", error);
      $q.notify({ type: "negative", message: "同步失敗，伺服器錯誤" });
    } finally {
      $q.loading.hide();
    }
  };

  const saveBranch = async () => {
    if (!selectedBranch.value) return;

    try {
      isLoading.value = true;
      const payload = { ...selectedBranch.value };

      const response = await apiClient.post(
        `${apiBaseUrl}/branch/save_branch`,
        payload
      );

      if (response.data.status === "success") {
        // 更新本地 branches 列表
        const index = branches.value.findIndex(
          (b) => b.Cod_cust === selectedBranch.value.Cod_cust
        );
        if (index !== -1) {
          branches.value[index] = { ...selectedBranch.value };
        } else {
          branches.value.push({ ...selectedBranch.value });
        }

        $q.notify({ type: "positive", message: "儲存成功" });
        return true;
      } else {
        $q.notify({ type: "negative", message: "儲存失敗" });
        return false;
      }
    } catch (error) {
      console.error("儲存錯誤:", error);
      $q.notify({ type: "negative", message: "伺服器錯誤，請稍後再試" });
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const selectBranch = async (branch) => {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "yellow",
      message: "測試連線中...",
    });

    try {
      selectedBranch.value = { ...branch };
      await checkConnection();
    } finally {
      $q.loading.hide();
    }
  };

  const createNewBranch = (branchId) => {
    if (!branchId) return false;

    // 檢查是否已存在
    const exists = branches.value.some((b) => b.Cod_cust === branchId);
    if (exists) {
      $q.notify({
        type: "warning",
        message: `門市代號 ${branchId} 已存在`,
      });
      return false;
    }

    // 建立新的門市物件
    selectedBranch.value = {
      Cod_cust: branchId,
      Cod_name: "",
      Cod_group: "",
      Invest: "0",
      Sts: "1",
      District: "",
      AttendanceIP: "",
      Comment_Url: "",
    };

    connectionStatus.value = "offline";
    return true;
  };

  const resetForm = () => {
    selectedBranch.value = null;
    connectionStatus.value = "checking";
  };

  // 初始化函數
  const initialize = async () => {
    await Promise.all([fetchDepartments(), fetchTables()]);
  };

  return {
    // 響應式狀態
    branches,
    selectedBranch,
    departments,
    tablesOptions,
    connectionStatus,
    isLoading,

    // 選項資料
    statusOptions,
    brandsOptions,
    InvestOptions,

    // 工具函數
    UTCformatDateTime,
    getStatusColor,
    getStatusLabel,

    // API 函數
    fetchBranches,
    fetchDepartments,
    fetchTables,
    checkConnection,
    syncTables,
    saveBranch,
    selectBranch,
    createNewBranch,
    resetForm,
    initialize,
  };
}
