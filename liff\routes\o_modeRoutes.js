const express = require("express");
const router = express.Router();
const oModeController = require("../controllers/o_modeController");

// 消費方式管理相關路由
router.get("/get_consumption_modes", oModeController.getConsumptionModes);
router.post("/save_consumption_mode", oModeController.saveConsumptionMode);
router.post("/delete_consumption_mode", oModeController.deleteConsumptionMode);
router.post("/save_mode_sort", oModeController.saveModeSort);

// 隱藏商品管理
router.get("/get_available_items", oModeController.getAvailableItems);
router.get("/get_hidden_items", oModeController.getHiddenItems);
router.post("/save_hidden_items", oModeController.saveHiddenItems);
router.get("/get_available_modes", oModeController.getAvailableModes);

module.exports = router;
