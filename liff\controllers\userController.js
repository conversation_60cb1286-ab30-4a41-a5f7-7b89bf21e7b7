const sql = require("mssql");
const dbConfig = require("../config/db");

// **🔹 查詢使用者 API**
const getUserByUID = async (req, res) => {
  const { uid } = req.body; // 從前端取得 `uid`

  if (!uid) {
    return res.status(400).json({ error: "❌ 缺少 UID" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // **🔍 查詢 `users` 表，透過 `UID` 找到 `ID`、`姓名` 和 `部門名稱`**
    const userResult = await pool
      .request()
      .input("uid", sql.VarChar, uid)
      .query(
        "SELECT Users.ID, Users.Name, Dep.Id AS Did, Dep.Name AS Dname  FROM Users INNER JOIN Dep ON Users.dep = Dep.Id WHERE Users.UID=@uid or Users.username =@uid and sts='1'"
      );

    if (userResult.recordset.length === 0) {
      return res.status(404).json({ error: "❌ 找不到對應的使用者" });
    }

    const userData = userResult.recordset[0]; // 取得使用者資料
    //console.log("✅ 查詢成功，使用者:", userData);

    res.json([
      {
        ID: userData.ID.trim(),
        Name: userData.Name.trim(),
        Dname: userData.Dname.trim(),

        Did: userData.Did.trim(),
      },
    ]); // ✅ 回傳 JSON 陣列格式
  } catch (err) {
    console.error("❌ 查詢使用者失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法查詢使用者" });
  }
};

const getEnableUsers = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const userResult = await pool.query(
      "SELECT ID, Name FROM Users where sts='1' and dep is not null ORDER BY Name"
    );

    res.json(userResult.recordset);
  } catch (err) {
    console.error("❌ 查詢使用者失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法查詢使用者" });
  }
};

const getAllDepartment = async (req, res) => {
  try {
    // 連接資料庫
    await sql.connect(dbConfig);

    // 查詢 `Dep` 表的部門資料
    const result = await sql.query(`
      SELECT Id,Ulevel, Name,Manager FROM Dep
    `);

    // 格式化回應
    const departments = result.recordset;
    res.json(departments);
  } catch (error) {
    console.error("載入部門失敗", error);
    res.status(500).json({ message: "無法取得部門資料", error });
  } finally {
  }
};

const getAllUsers = async (req, res) => {
  try {
    // 連接資料庫
    await sql.connect(dbConfig);

    // 查詢 `Dep` 表的部門資料
    const result = await sql.query(`
      SELECT Uid, Name ,Phone ,Sts  ,Notify,Dep,ID, Username , Email FROM users
    `);

    // 格式化回應
    const users = result.recordset;
    res.json(users);
  } catch (error) {
    console.error("載入使用者失敗", error);
    res.status(500).json({ message: "無法取得使用者資料", error });
  } finally {
  }
};

const updateUser = async (req, res) => {
  try {
    const { Uid, Name, Sts, Dep, ID, Username, Notify, Email } = req.body;

    if (!Uid) {
      return res.status(400).json({ success: false, message: "缺少 Uid" });
    }

    await sql.connect(dbConfig);
    await sql.query(`
      UPDATE Users
      SET 
        Name = '${Name}',
        Sts = '${Sts}',
        Dep = '${Dep}',
        ID = '${ID}',
        Username = '${Username}',
        Notify = '${Notify}',
        Email='${Email}'
      WHERE Uid = '${Uid}'
    `);

    res.json({ success: true, message: "更新成功" });
  } catch (error) {
    console.error("❌ 更新使用者失敗:", error);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const getUsersGroup = async (req, res) => {
  try {
    await sql.connect(dbConfig);

    const result = await sql.query(
      "SELECT Code, Name, Remark FROM Users_group"
    );

    // 格式化回應

    res.json(result.recordset);
  } catch (error) {
    console.error("讀取群組資料失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法讀取群組資料" });
  }
};

const createUsersgroup = async (req, res) => {
  const { Code, Name, Remark } = req.body;
  try {
    const pool = await sql.connect(dbConfig);
    await pool
      .request()
      .input("Code", sql.VarChar, Code.trim()) // ✅ 確保 Code 無多餘空格
      .input("Name", sql.NVarChar, Name) // ✅ 避免 SQL 注入
      .input("Remark", sql.NVarChar, Remark) // ✅ 確保 Unicode 支援
      .query(
        "INSERT INTO Users_group (Code, Name, Remark) VALUES (@Code, @Name, @Remark)"
      );

    res.status(201).json({ Code, Name, Remark });
  } catch (error) {
    console.error("新增群組失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法新增群組" });
  }
};

// 更新群組
const updateUsersgroup = async (req, res) => {
  const { Code } = req.params;
  const { Name, Remark } = req.body;

  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool
      .request()
      .input("Name", sql.NVarChar, Name)
      .input("Remark", sql.NVarChar, Remark)
      .input("Code", sql.VarChar, Code.trim()) // ✅ 確保 Code 無空格
      .query(
        "UPDATE Users_group SET Name = @Name, Remark = @Remark WHERE Code = @Code"
      );

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({ error: "找不到該群組" });
    }

    res.json({ Code, Name, Remark });
  } catch (error) {
    console.error("更新群組失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法更新群組" });
  }
};

const deleteUsersgroup = async (req, res) => {
  const { Code } = req.params; // 取得群組編號

  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool
      .request()
      .input("Code", sql.VarChar, Code.trim()) // ✅ 確保 Code 無空格
      .query("DELETE FROM Users_group WHERE Code = @Code");

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({ error: "找不到該群組，無法刪除" });
    }

    res.json({ success: true, message: `群組 ${Code} 已刪除` });
  } catch (error) {
    console.error("刪除群組失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法刪除群組" });
  }
};
const getUserBranch = async (req, res) => {
  const userId = req.query.userId;
  //console.log(userId);
  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool.request().input("UserID", sql.VarChar, userId)
      .query(`
        SELECT Cod_Cust
        FROM Users_Branch
        WHERE ID = @UserID
      `);

    return res.json({ success: true, branches: result.recordset });
  } catch (error) {
    console.error("❌ getUserBranch error:", error);
    return res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const saveUserBranch = async (req, res) => {
  const { userId, branches } = req.body; // branches 是 array，例如 ["A001", "A002"]

  if (!userId || !Array.isArray(branches)) {
    return res.status(400).json({ success: false, message: "缺少參數" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 先刪除舊的授權
    await pool
      .request()
      .input("UserID", sql.VarChar, userId)
      .query(`DELETE FROM Users_Branch WHERE ID = @UserID`);

    // 批次新增新的授權門市
    for (const cod of branches) {
      await pool
        .request()
        .input("UserID", sql.VarChar, userId)
        .input("Cod_Cust", sql.VarChar, cod)
        .query(
          `INSERT INTO Users_Branch (ID, Cod_Cust) VALUES (@UserID, @Cod_Cust)`
        );
    }

    res.json({ success: true });
  } catch (err) {
    console.error("❌ 儲存授權門市失敗:", err);
    res.status(500).json({ success: false, message: "儲存失敗" });
  }
};

// 獲取用戶權限群組
const getUserAccess = async (req, res) => {
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).json({ success: false, message: "缺少用戶ID參數" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 獲取用戶的權限群組和對應的名稱
    const result = await pool.request().input("UserID", sql.VarChar, userId)
      .query(`
        SELECT ua.access, ug.Name as name 
        FROM Users_Access ua
        LEFT JOIN Users_group ug ON ua.access = ug.Code
        WHERE ua.id = @UserID
      `);

    // 返回完整的權限群組信息，包含代碼和名稱
    return res.json({
      success: true,
      access: result.recordset.map((item) => ({
        value: item.access.trim(),
        label: item.name ? item.name.trim() : item.access.trim(),
      })),
    });
  } catch (error) {
    console.error("❌ getUserAccess error:", error);
    return res
      .status(500)
      .json({ success: false, message: "獲取用戶權限群組失敗" });
  }
};

// 保存用戶權限群組
const saveUserAccess = async (req, res) => {
  const { userId, access } = req.body; // access 是權限群組陣列，例如 ["Admin", "Manager"]

  if (!userId || !Array.isArray(access)) {
    return res
      .status(400)
      .json({ success: false, message: "缺少參數或參數格式不正確" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 先刪除舊的授權群組
    await pool
      .request()
      .input("UserID", sql.VarChar, userId)
      .query(`DELETE FROM Users_Access WHERE id = @UserID`);

    // 批次新增新的授權群組
    for (const code of access) {
      await pool
        .request()
        .input("UserID", sql.VarChar, userId)
        .input("Access", sql.VarChar, code)
        .query(
          `INSERT INTO Users_Access (id, access) VALUES (@UserID, @Access)`
        );
    }

    res.json({ success: true, message: "權限群組設定已保存" });
  } catch (err) {
    console.error("❌ 儲存授權群組失敗:", err);
    res.status(500).json({ success: false, message: "儲存權限群組失敗" });
  }
};

// 檢查使用者是否為部門主管
const checkIsManager = async (req, res) => {
  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: "缺少使用者ID" });
  }

  try {
    await sql.connect(dbConfig);

    // 查詢使用者是否為某個部門的主管
    const result = await sql.query(`
      SELECT d.Id, d.Name
      FROM Dep d
      WHERE d.Manager = '${userId}'
    `);

    const isManager = result.recordset.length > 0;
    const departments = result.recordset;

    res.json({
      isManager,
      departments: isManager ? departments : [],
    });
  } catch (err) {
    console.error("檢查部門主管狀態失敗:", err);
    res.status(500).json({ error: "伺服器錯誤，無法檢查部門主管狀態" });
  }
};

// 獲取部門內所有員工
const getDepartmentMembers = async (req, res) => {
  const { departmentId } = req.query;

  if (!departmentId) {
    return res.status(400).json({ error: "缺少部門ID" });
  }

  try {
    await sql.connect(dbConfig);

    // 查詢部門內所有員工
    const result = await sql.query(`
      SELECT ID, Name, Email, Phone, Notify
      FROM Users
      WHERE Dep = '${departmentId}' AND Sts = '1'
      ORDER BY Name
    `);

    res.json(result.recordset);
  } catch (err) {
    console.error("獲取部門員工失敗:", err);
    res.status(500).json({ error: "伺服器錯誤，無法獲取部門員工" });
  }
};

// **✅ 匯出函式**
module.exports = {
  getUserByUID,
  getEnableUsers,
  getAllDepartment,
  getAllUsers,
  getUsersGroup,
  updateUser,
  createUsersgroup,
  updateUsersgroup,
  deleteUsersgroup,
  getUserBranch,
  saveUserBranch,
  getUserAccess,
  saveUserAccess,
  checkIsManager,
  getDepartmentMembers,
};
