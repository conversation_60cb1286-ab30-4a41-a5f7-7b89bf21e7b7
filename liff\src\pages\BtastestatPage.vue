<template>
  <q-page class="q-pa-sm" style="width: 100%; max-width: 800px; margin: 0 auto">
    <!-- 標題和搜尋區域 -->
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題與搜尋 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">口味統計</div>
        </div>
      </q-card-section>

      <!-- 日期選擇區塊 -->
      <q-card-section class="q-pt-none">
        <div class="row items-center no-wrap">
          <!-- 日期區間輸入框 -->
          <q-input
            v-model="formattedDate"
            label="選擇日期區間"
            dense
            outlined
            square
            class="col"
            readonly
          >
            <!-- 左側 icon -->
            <template v-slot:prepend>
              <q-icon name="event" size="sm" />
            </template>

            <!-- 日期選擇 popup -->
            <template v-slot:append>
              <q-icon
                name="calendar_month"
                color="primary"
                class="cursor-pointer"
                size="18px"
              >
                <q-popup-proxy
                  transition-show="scale"
                  transition-hide="scale"
                  cover
                >
                  <q-date
                    v-model="dateRange"
                    range
                    mask="YYYY-MM-DD"
                    emit-immediately
                  >
                    <div
                      class="row items-center justify-end q-gutter-sm q-pa-sm"
                    >
                      <q-btn label="確定" color="primary" flat v-close-popup />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>

          <!-- 篩選按鈕 -->
          <q-btn
            flat
            round
            size="sm"
            color="red"
            class="search-button q-ml-sm"
            @click="filterDialog = true"
          >
            <Search size="20" />
            <q-tooltip>篩選統計</q-tooltip>
          </q-btn>
        </div>
      </q-card-section>

      <!-- 查詢區間顯示 - 移到頂部固定區域 -->
      <q-card-section v-if="resultDateRange.from" class="q-pt-none q-pb-xs">
        <q-chip outline color="teal" class="full-width justify-center">
          <q-icon name="date_range" class="q-mr-xs" />
          查詢區間：{{ resultDateRange.from }} ~ {{ resultDateRange.to }}
        </q-chip>
      </q-card-section>

      <!-- 已選擇的口味標籤 -->
      <q-card-section v-if="selectedTastes.length > 0" class="q-px-sm q-py-xs">
        <div class="row justify-between items-center q-mb-xs">
          <div class="text-caption text-grey-8">已選擇的口味：</div>
          <q-btn
            flat
            dense
            size="sm"
            color="grey-7"
            label="清除全部"
            @click="clearAllTastes"
          />
        </div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="taste in selectedTastes"
            :key="taste"
            removable
            dense
            color="primary"
            text-color="white"
            @remove="removeTaste(taste)"
          >
            {{ getTasteLabel(taste) }}
          </q-chip>
        </div>
      </q-card-section>
    </q-card>

    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <!-- 載入中狀態 -->
      <div v-if="isLoading" class="q-pa-xl flex flex-center">
        <div class="fancy-loader">
          <svg
            class="loader-svg"
            viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">資料整理中...</div>
        </div>
      </div>

      <!-- 無資料顯示 -->
      <q-card-section
        v-else-if="
          tasteStats.length === 0 && resultDateRange.from && !isLoading
        "
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無口味統計資料</div>
          <div class="empty-subtext">請嘗試更改查詢條件或選擇不同的口味</div>
        </div>
      </q-card-section>

      <!-- 口味統計結果 -->
      <q-card-section v-if="tasteStats.length > 0" class="q-px-xs q-py-sm">
        <q-list bordered separator class="rounded-borders">
          <q-expansion-item
            v-for="(group, index) in tasteStats"
            :key="index"
            expand-separator
            class="q-mb-xs"
            header-class="taste-group-header"
          >
            <template v-slot:header>
              <q-item-section avatar>
                <q-icon name="restaurant" color="deep-orange" />
              </q-item-section>
              <q-item-section>
                <div class="text-subtitle2 text-weight-medium">
                  {{ group.className }}
                </div>
              </q-item-section>
              <q-item-section side>
                <div class="text-subtitle2 text-weight-bold text-deep-orange">
                  {{ group.tastes.length }} 種口味
                  <q-badge
                    v-if="group.totalCount > 0"
                    color="deep-orange"
                    text-color="white"
                    class="q-ml-xs"
                  >
                    {{ group.classPercentage }}%
                  </q-badge>
                </div>
              </q-item-section>
            </template>

            <!-- 口味明細列表 -->
            <q-list class="taste-details">
              <q-item
                v-for="(taste, i) in group.tastes"
                :key="i"
                class="q-py-sm taste-item"
              >
                <q-item-section avatar>
                  <q-icon name="local_dining" color="brown-6" />
                </q-item-section>
                <q-item-section>
                  <div class="text-subtitle2 text-brown-10">
                    {{ taste.tasteName }}
                  </div>
                </q-item-section>
                <q-item-section side>
                  <div>
                    <div class="text-subtitle2 text-right">
                      {{ taste.count }}
                      <q-badge outline color="brown" class="q-ml-xs"
                        >{{ taste.percentage }}%</q-badge
                      >
                    </div>
                    <div class="text-caption text-grey-7 text-right">
                      佔總體: {{ taste.overallPercentage }}%
                    </div>
                  </div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-expansion-item>
        </q-list>
      </q-card-section>
    </q-card>

    <!-- 篩選條件對話框 -->
    <q-dialog v-model="filterDialog">
      <q-card style="min-width: 350px; max-width: 90vw" class="filter-dialog">
        <!-- 標題區 -->
        <q-card-section class="row items-center">
          <q-avatar icon="filter_alt" color="primary" text-color="white" />
          <div class="text-h6 text-primary q-ml-sm">篩選條件</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <!-- 篩選選項 -->
        <q-card-section class="q-pt-md q-gutter-md">
          <!-- 排序方式 -->
          <div>
            <div class="text-subtitle2 text-grey-8 q-mb-xs">排序方式</div>
            <q-option-group
              v-model="sortBy"
              :options="[
                { label: '依名稱排序', value: 'name' },
                { label: '依數量排序', value: 'qty' },
              ]"
              type="radio"
              color="primary"
              inline
              dense
            />
          </div>

          <!-- 口味選擇 -->
          <div class="q-mt-md">
            <div class="text-subtitle2 text-grey-8 q-mb-sm">選擇口味</div>
            <q-select
              v-model="selectedTastes"
              :options="tasteOptions"
              multiple
              use-chips
              stack-label
              label="選擇要統計的口味"
              option-label="label"
              option-value="value"
              emit-value
              map-options
              dense
              outlined
              class="q-mb-sm"
              hide-dropdown-icon
            />
            <div class="text-caption text-grey-7">不選擇則統計所有口味</div>
          </div>
        </q-card-section>

        <q-separator />

        <!-- 動作按鈕 -->
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" v-close-popup color="grey-7" />
          <q-btn
            unelevated
            color="primary"
            label="搜尋"
            @click="fetchData()"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { DateTime } from "luxon";
import { useQuasar } from "quasar";

const today = DateTime.now().toFormat("yyyy-MM-dd");

const filterDialog = ref(false);
const sortBy = ref("name");
const props = defineProps({ Branch: String, Permissions: String });
import apiClient from "../api";
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const resultDateRange = ref({ from: "", to: "" });
const tasteStats = ref([]);
const tasteOptions = ref([]);
const selectedTastes = ref([]);
const isLoading = ref(false);

const dateRange = ref({
  from: today,
  to: today,
});

// 取得口味顯示名稱
function getTasteLabel(tasteValue) {
  const option = tasteOptions.value.find((opt) => opt.value === tasteValue);
  return option ? option.label : tasteValue;
}

// 載入口味選項
async function loadTasteOptions() {
  try {
    const res = await apiClient.get(
      `${apiBaseUrl}/btastestat/get_TasteOptions`,
      {
        params: {
          branch: props.Branch,
        },
      }
    );

    if (res.data.success) {
      tasteOptions.value = res.data.data;
    } else {
      console.error("❌ 載入口味選項失敗:", res.data.message);
    }
  } catch (err) {
    console.error("❌ API 錯誤:", err);
  }
}

// 清除所有已選擇的口味
function clearAllTastes() {
  selectedTastes.value = [];
  fetchData();
}

// 移除已選擇的口味
function removeTaste(taste) {
  selectedTastes.value = selectedTastes.value.filter((t) => t !== taste);
  fetchData();
}

async function fetchData() {
  const from = dateRange.value?.from || dateRange.value;
  const to = dateRange.value?.to || dateRange.value; // ✅ 若未選 to，使用 from 當作區間結尾

  if (!from) return;
  isLoading.value = true;
  try {
    const res = await apiClient.get(`${apiBaseUrl}/btastestat/get_TasteStats`, {
      params: {
        branch: props.Branch,
        from,
        to,
        sortBy: sortBy.value,
        selectedTastes:
          selectedTastes.value.length > 0
            ? JSON.stringify(selectedTastes.value)
            : null,
      },
    });

    if (res.data.success) {
      tasteStats.value = res.data.data; // ✅ 更新資料
      resultDateRange.value = { from, to }; // ⬅️ 儲存日期區間
      isLoading.value = false;
    } else {
      console.error("❌ 查詢失敗:", res.data.message);
      isLoading.value = false;
    }
  } catch (err) {
    console.error("❌ API 錯誤:", err);
    isLoading.value = false;
  }
}

const formattedDate = computed(() => {
  const value = dateRange.value;

  if (!value) return "請選擇日期";

  if (typeof value === "string") {
    // ✅ 單日情況（只選一天）
    return value;
  }

  if (typeof value === "object" && value.from) {
    const { from, to } = value;
    return !to || from === to ? from : `${from} ~ ${to}`;
  }

  return "請選擇日期";
});

onMounted(() => {
  loadTasteOptions();
});
</script>
<style>
.fixed-top-container {
  position: fixed;
  top: 60px; /* Increase top padding to avoid overlapping with toolbar */
  left: 0;
  right: 0;
  z-index: 1;
  padding: 0.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.search-button {
  min-height: 32px;
  min-width: 32px;
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #ff9800;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

.loader-message {
  margin-top: 1rem;
  color: #ff9800;
  font-size: 0.9rem;
}

.taste-item:hover {
  background-color: rgba(255, 248, 225, 0.5);
}

.taste-details {
  background-color: #fff;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 0.5rem;
}

.empty-text {
  font-size: 1rem;
  color: #616161;
  margin-bottom: 0.25rem;
}

.empty-subtext {
  font-size: 0.8rem;
  color: #9e9e9e;
}

.filter-dialog {
  border-radius: 10px;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}
</style>
