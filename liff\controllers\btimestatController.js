const sql = require("mssql");
require("dotenv").config();

// 🔧 支援切換 DB
const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");
  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

const getTimeStat = async (req, res) => {
  const { dates, cod_cust: branch } = req.body.payload || {};

  // ✅ 檢查輸入
  if (!Array.isArray(dates) || dates.length === 0 || !branch) {
    return res.status(400).json({ success: false, message: "❌ 缺少必要參數" });
  }

  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    // ✅ 建立日期參數與 SQL
    const dateParams = dates
      .map((d, i) => {
        request.input(`date${i}`, sql.NVarChar, d);
        return `@date${i}`;
      })
      .join(", ");

    const result = await request.query(`
      SELECT
        LEFT(RTIME, 2) AS Hour,
        SUM(ISNULL(person, 0) + ISNULL(boy, 0)) AS Persons,
        SUM(ISNULL(amount, 0)) AS Amount
      FROM ordernow
      WHERE checkout = 1 AND ndate IN (${dateParams})
      GROUP BY LEFT(RTIME, 2)
      ORDER BY Hour
    `);

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getTimeStat error:", err);
    return res
      .status(500)
      .json({ success: false, message: "伺服器錯誤", error: err.message });
  }
};

module.exports = {
  getTimeStat,
};
