<template>
  <q-page
    class="q-pa-md flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <q-card
      class="q-pa-sm no-shadow"
      style="width: 100%; max-width: 500px; border-radius: 12px"
    >
      <!-- 🔷 標題 -->
      <q-card-section class="text-h6 text-primary text-center">
        出據主機設定
      </q-card-section>

      <!-- 🖨️ 主機清單 -->
      <q-list separator>
        <div
          v-for="host in vclassList"
          :key="host.code"
          class="bg-white q-mb-sm"
          style="border: 1px solid #ccc; border-radius: 8px; overflow: hidden"
        >
          <q-expansion-item
            expand-separator
            class="q-pa-none"
            header-class="text-indigo"
          >
            <!-- 🔹 主機主列 -->
            <template #header>
              <q-item
                dense
                class="q-pa-none q-gutter-none items-center"
                style="width: 100%; position: relative"
              >
                <q-item-section>
                  <div class="text-indigo-10">{{ host.name }}</div>
                  <div class="text-caption text-grey-8">
                    <span class="text-brown-10">{{ host.code }}</span> |
                    序列：{{ resolvePno(host.pno) }} | 類型：{{
                      prnTypeMap[host.prn] || "未知"
                    }}
                  </div>
                </q-item-section>

                <!-- 最右側展開箭頭 -->
                <q-item-section side />

                <!-- 編輯按鈕 -->
                <q-btn
                  flat
                  dense
                  size="sm"
                  icon="edit"
                  color="primary"
                  @click.stop="openEditDialog(host)"
                  style="
                    position: absolute;
                    right: 14px;
                    top: 50%;
                    transform: translateY(-50%);
                    z-index: 1;
                  "
                />
              </q-item>
            </template>

            <!-- 🔽 詳細內容 -->
            <q-card-section class="q-pa-sm q-pt-none bg-grey-1 text-caption">
              <div class="row q-col-gutter-sm q-mb-xs q-pl-md">
                <!-- ✅ 列印方式 -->
                <div class="col-12">
                  <q-icon
                    name="print"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  列印方式：
                  <span class="text-weight-medium">{{
                    printModeMap[host.ptype] || "未知"
                  }}</span>
                </div>

                <div class="col-6">
                  <q-icon
                    name="computer"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  電腦名稱：
                  <span class="text-weight-medium">{{ host.prncn }}</span>
                </div>

                <div class="col-6">
                  <q-icon
                    name="confirmation_number"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  序列碼：
                  <span class="text-weight-medium">
                    {{
                      host.pseq?.toUpperCase() === "Z" ? "不出據" : host.pseq
                    }}
                  </span>
                </div>

                <div class="col-6">
                  <q-icon
                    name="developer_board"
                    size="16px"
                    class="q-mr-xs text-primary"
                  />
                  機型：
                  <span class="text-weight-medium">{{
                    host.prnc === "2" ? "感熱式" : "其他"
                  }}</span>
                </div>

                <!-- ✅ 新增：序列 (pno 轉數字顯示) -->
                <div class="col-6">
                  <q-icon name="tag" size="16px" class="q-mr-xs text-primary" />
                  序列：
                  <span class="text-weight-medium">{{
                    resolvePno(host.pno)
                  }}</span>
                </div>

                <!-- ✅ IP -->
                <div class="col-12" v-if="host.ip">
                  <q-icon name="lan" size="16px" class="q-mr-xs text-primary" />
                  設備 IP：
                  <span class="text-weight-medium">{{ host.ip }}</span>
                </div>
              </div>

              <!-- 🔻 刪除 -->
              <div class="row justify-end q-mt-sm q-pr-sm">
                <q-btn
                  flat
                  dense
                  icon="delete"
                  label="刪除"
                  color="negative"
                  @click.stop="deleteHost(host)"
                  class="q-px-sm"
                />
              </div>
            </q-card-section>
          </q-expansion-item>
        </div>
      </q-list>

      <!-- ➕ 新增主機按鈕 -->
      <q-btn
        color="green"
        icon="add"
        label="新增主機"
        class="full-width q-mt-md"
        @click="editHost(null)"
      />
    </q-card>
  </q-page>
  <q-dialog v-model="editDialog.visible" persistent>
    <q-card
      class="dialog-card"
      :class="$q.screen.lt.md ? 'mobile-dialog' : 'desktop-dialog'"
    >
      <!-- 標題列 -->
      <q-card-section class="dialog-header">
        <div class="text-h6 text-weight-medium text-primary">
          <q-icon name="dns" class="q-mr-sm" />
          {{ editDialog.isEdit ? "編輯主機" : "新增主機" }}
        </div>
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          class="text-grey-6"
        />
      </q-card-section>

      <q-separator />

      <!-- 表單內容 -->
      <q-card-section class="dialog-body">
        <div class="form-grid">
          <!-- 基本資訊 -->
          <q-input
            v-model="editDialog.form.code"
            label="主機編號"
            dense
            outlined
            :disable="editDialog.isEdit"
          >
            <template v-slot:prepend>
              <q-icon name="tag" color="primary" />
            </template>
          </q-input>

          <q-input
            v-model="editDialog.form.name"
            label="主機名稱"
            dense
            outlined
          >
            <template v-slot:prepend>
              <q-icon name="computer" color="primary" />
            </template>
          </q-input>

          <!-- 出據設定 -->
          <q-select
            v-model="editDialog.form.prn"
            label="出據類型"
            :options="[
              { label: '序列', value: '1' },
              { label: '不出據', value: '2' },
              { label: '網路', value: '3' },
              { label: '驅動', value: '4' },
            ]"
            dense
            outlined
            emit-value
            map-options
          >
            <template v-slot:prepend>
              <q-icon name="print" color="indigo" />
            </template>
          </q-select>

          <q-select
            v-model="editDialog.form.ptype"
            label="列印方式"
            :options="[
              { label: '單張（數量前）', value: '1' },
              { label: '連續（數量前）', value: '2' },
              { label: '單張（數量後）', value: '3' },
              { label: '連續（數量後）', value: '4' },
            ]"
            dense
            outlined
            emit-value
            map-options
          >
            <template v-slot:prepend>
              <q-icon name="print" color="indigo" />
            </template>
          </q-select>

          <!-- 裝置連線 -->
          <q-input
            v-model="editDialog.form.prncn"
            label="電腦名稱"
            dense
            outlined
          >
            <template v-slot:prepend>
              <q-icon name="computer" color="teal" />
            </template>
          </q-input>

          <q-select
            v-model="editDialog.form.prnc"
            label="機型"
            :options="[
              { label: '感熱式', value: '2' },
              { label: '其他', value: '1' },
            ]"
            dense
            outlined
            emit-value
            map-options
          >
            <template v-slot:prepend>
              <q-icon name="memory" color="teal" />
            </template>
          </q-select>

          <q-select
            v-model="editDialog.form.pno"
            label="序列編號"
            :options="[
              { label: '1', value: '1' },
              { label: '2', value: '2' },
              { label: '3', value: '3' },
              { label: '4', value: '4' },
              { label: '5', value: '5' },
              { label: '6', value: '6' },
              { label: '7', value: '7' },
              { label: '8', value: '8' },
              { label: '9', value: '9' },
              { label: '10', value: '10' },
              { label: '11', value: 'A' },
              { label: '12', value: 'B' },
              { label: '13', value: 'C' },
              { label: '14', value: 'D' },
              { label: '15', value: 'E' },
              { label: '不出據', value: 'Z' },
            ]"
            dense
            outlined
            emit-value
            map-options
          >
            <template v-slot:prepend>
              <q-icon name="pin" color="teal" />
            </template>
          </q-select>

          <q-input v-model="editDialog.form.ip" label="設備 IP" dense outlined>
            <template v-slot:prepend>
              <q-icon name="wifi" color="teal" />
            </template>
          </q-input>
        </div>
      </q-card-section>

      <!-- 操作按鈕 -->
      <q-card-actions class="dialog-actions">
        <q-btn
          color="primary"
          :label="editDialog.isEdit ? '更新' : '儲存'"
          @click="saveHost"
          unelevated
          class="action-btn"
          :loading="false"
        >
          <template v-slot:loading>
            <q-spinner-facebook />
          </template>
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 刪除確認對話框 -->
  <q-dialog v-model="deleteDialog" persistent>
    <q-card class="q-pa-lg q-rounded-borders" style="width: 320px">
      <q-card-section class="text-center">
        <q-icon
          name="delete_forever"
          color="negative"
          size="xl"
          class="q-mb-md"
        />
        <div class="text-h6 text-negative">確定刪除？</div>
        <div class="text-body2 text-grey-8 q-mt-sm">
          主機「{{ hostToDelete?.name }}」刪除後無法復原，是否繼續？
        </div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-actions class="q-mt-sm row justify-between">
        <q-btn
          label="取消"
          color="grey"
          flat
          class="full-width q-mr-xs"
          v-close-popup
        />
        <q-btn
          label="刪除"
          color="negative"
          unelevated
          class="full-width q-ml-xs"
          @click="deleteHostConfirmed"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";

import { useQuasar, QSpinnerFacebook } from "quasar";
import apiClient from "../api";
const $q = useQuasar();
const props = defineProps({ Branch: String });
const editDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    code: "",
    name: "",
    prn: "1",
    ptype: "1",
    prncn: "",
    prnc: "2",
    pno: "",
    pseq: "",
    ip: "",
  },
});

const openEditDialog = (host = null) => {
  if (host) {
    editDialog.value.form = { ...host };
    editDialog.value.isEdit = true;
  } else {
    editDialog.value.form = {
      code: "",
      name: "",
      prn: "1",
      ptype: "1",
      prncn: "",
      prnc: "2",
      pno: "",
      pseq: "",
      ip: "",
    };
    editDialog.value.isEdit = false;
  }
  editDialog.value.visible = true;
};

const resolvePno = (pno) => {
  if (!pno) return "";

  // 如果 pno 是 Z，顯示不出據
  if (pno === "Z") return "不出據";

  // 如果 pno 是 A-F，轉換為 11-15
  const letterMap = {
    A: "11",
    B: "12",
    C: "13",
    D: "14",
    E: "15",
    F: "16",
  };

  // 如果是字母，轉換為對應數字
  if (letterMap[pno.toUpperCase()]) {
    return letterMap[pno.toUpperCase()];
  }

  // 其他情況直接返回（1-10 的數字）
  return pno;
};

const vclassList = ref([]);

const prnTypeMap = {
  1: "序列出據",
  2: "不出據",
  3: "網路",
  4: "驅動",
};

const printModeMap = {
  1: "單張（數量前）",
  2: "連續（數量前）",
  3: "單張（數量後）",
  4: "連續（數量後）",
};

const fetchVclass = async () => {
  try {
    const res = await apiClient.get(`/ovclass/get_Vclass`, {
      params: { branch: props.Branch },
    });
    if (res.data.success) {
      vclassList.value = res.data.data;
    }
    //console.log(vclassList.value);
    $q.loading.hide();
  } catch (err) {
    console.error("❌ 載入出據主機失敗", err);
    $q.notify({ type: "negative", message: "出據主機載入失敗" });
    $q.loading.hide();
  }
};

const editHost = (host) => {
  openEditDialog(host);
};

const saveHost = async () => {
  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "儲存中...",
    });

    const formData = {
      ...editDialog.value.form,
      branch: props.Branch,
    };

    let res;
    if (editDialog.value.isEdit) {
      // 編輯現有主機
      res = await apiClient.put(`/ovclass/update_Vclass`, formData, {
        params: { branch: props.Branch },
      });
    } else {
      // 新增主機
      res = await apiClient.post(`/ovclass/create_Vclass`, formData, {
        params: { branch: props.Branch },
      });
    }

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: editDialog.value.isEdit ? "主機更新成功" : "主機新增成功",
      });
      editDialog.value.visible = false;
      await fetchVclass(); // 重新載入資料
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "操作失敗",
      });
    }
  } catch (err) {
    console.error("❌ 儲存主機失敗", err);
    $q.notify({
      type: "negative",
      message: "儲存失敗：" + (err.response?.data?.message || err.message),
    });
  } finally {
    $q.loading.hide();
  }
};

const deleteDialog = ref(false);
const hostToDelete = ref(null);

const deleteHost = async (host) => {
  hostToDelete.value = host;
  deleteDialog.value = true;
};

const deleteHostConfirmed = async () => {
  if (!hostToDelete.value) return;

  try {
    $q.loading.show({
      spinner: QSpinnerFacebook,
      spinnerColor: "warning",
      message: "刪除中...",
    });

    const res = await apiClient.delete(
      `/ovclass/delete_Vclass/${hostToDelete.value.code}`,
      {
        params: {
          branch: props.Branch,
        },
      }
    );

    if (res.data.success) {
      $q.notify({
        type: "positive",
        message: "主機刪除成功",
        icon: "check_circle",
        position: "top",
      });
      await fetchVclass(); // 重新載入資料
    } else {
      $q.notify({
        type: "negative",
        message: res.data.message || "刪除失敗",
        icon: "error",
        position: "top",
      });
    }
  } catch (err) {
    console.error("❌ 刪除主機失敗", err);
    $q.notify({
      type: "negative",
      message: "刪除失敗：" + (err.response?.data?.message || err.message),
      icon: "error",
      position: "top",
    });
  } finally {
    $q.loading.hide();
    deleteDialog.value = false;
    hostToDelete.value = null;
  }
};

onMounted(() => {
  $q.loading.show({
    spinner: QSpinnerFacebook,
    spinnerColor: "warning",
    message: "讀取中...",
  });
  fetchVclass();
});
</script>

<style scoped>
/* 對話框樣式 */
.dialog-card {
  border-radius: 12px;
  overflow: hidden;
}

.desktop-dialog {
  width: 100%;
  max-width: 600px;
  margin: 20px auto;
}

.mobile-dialog {
  width: 95%;
  max-width: 95%;
  margin: 10px auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.dialog-body {
  padding: 20px;
}

.dialog-actions {
  padding: 16px 20px;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

/* 表單網格佈局 */
.form-grid {
  display: grid;
  gap: 16px;
}

/* 桌面版：2列佈局 */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 手機版：1列佈局 */
@media (max-width: 767px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .dialog-header {
    padding: 12px 16px;
  }

  .dialog-body {
    padding: 16px;
  }

  .dialog-actions {
    padding: 12px 16px;
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}

/* 輸入框和選擇器樣式 */
.q-input,
.q-select {
  transition: all 0.2s ease;
}

.q-input:hover,
.q-select:hover {
  transform: translateY(-1px);
}

/* 按鈕樣式 */
.action-btn {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* 對話框動畫 */
.q-dialog__inner--minimized > div {
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
