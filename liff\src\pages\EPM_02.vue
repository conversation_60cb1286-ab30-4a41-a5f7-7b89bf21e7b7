<template>
  <q-page
    class="bg-white q-pa-sm flex justify-center items-start"
    style="width: 100%; max-width: 800px; margin: 0 auto"
  >
    <!-- 標題容器 -->
    <div class="header-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">加班申請單</div>
          </div>
        </q-card-section>
        <!-- Progress Indicator -->
        <q-card-section class="q-pt-none q-pb-sm">
          <div v-if="loadingFlows" class="text-center q-pa-md">
            <q-spinner-dots color="primary" size="2em" />
          </div>
          <div v-else class="progress-indicator">
            <template v-for="(flowStep, index) in flowSteps" :key="index">
              <div
                class="progress-step"
                :class="{ active: step === flowStep.number }"
              >
                <div class="progress-dot">{{ flowStep.number }}</div>
                <div class="progress-label">{{ flowStep.name }}</div>
              </div>
              <div
                v-if="index < flowSteps.length - 1"
                class="progress-line"
              ></div>
            </template>
          </div>
        </q-card-section>
      </q-card>
    </div>
    <!-- Main Form -->
    <div class="q-pa-md" style="width: 100%; max-width: 581px; margin: 0 auto">
      <div class="form-container">
        <!-- 基本資料 -->
        <div class="form-section">
          <div class="section-title">基本資料</div>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="user.department"
                label="部門"
                outlined
                dense
                disable
                bg-color="grey-2"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-select
                v-model="otType"
                :options="otOptions"
                label="加班類別"
                outlined
                dense
              />
            </div>
          </div>
        </div>
        <!-- 加班時間 -->
        <div class="form-section">
          <div class="section-title">加班時間</div>
          <div class="row q-col-gutter-sm q-mb-md">
            <div class="col-12 col-sm-4">
              <q-input
                v-model="startDate"
                label="日期"
                type="date"
                outlined
                dense
                class="full-width"
              />
            </div>
            <div class="col-12 col-sm-4">
              <q-input
                v-model="startTime"
                label="開始時間"
                type="time"
                outlined
                dense
                class="full-width"
              />
            </div>
            <div class="col-12 col-sm-4">
              <q-input
                v-model="endTime"
                label="結束時間"
                type="time"
                outlined
                dense
                class="full-width"
              />
            </div>
          </div>
          <div class="time-display q-pa-sm q-mb-md">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <div class="time-label">總時數</div>
                <q-input
                  v-model="totalHours"
                  outlined
                  dense
                  readonly
                  class="q-mb-none full-width"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 加班原因 -->
        <div class="form-section">
          <div class="section-title">加班原因</div>
          <q-input
            v-model="reason"
            type="textarea"
            outlined
            autogrow
            class="q-mb-md"
            placeholder="請填寫加班原因..."
          />
        </div>
        <!-- 附件上傳 -->
        <div class="form-section">
          <div class="section-title">附件上傳</div>
          <div class="file-upload">
            <div class="upload-title q-mb-sm">
              <q-icon
                name="attach_file"
                size="sm"
                class="q-mr-xs text-primary"
              />
              <span class="text-subtitle2">附件上傳</span>
              <span class="text-caption text-grey-7 q-ml-sm"
                >(可上傳照片或PDF文件)</span
              >
            </div>
            <div class="upload-area q-mb-md">
              <q-file
                v-model="tempFileUpload"
                outlined
                dense
                multiple
                accept=".jpg,.png,.pdf"
                @update:model-value="handleFileSelection"
                class="upload-input full-width"
                hide-bottom-space
                label="選擇檔案"
                clearable
              >
                <template v-slot:hint> 最多可上傳5個檔案 </template>
              </q-file>
            </div>
            <!-- Selected Files List -->
            <div v-if="selectedFiles.length > 0" class="file-list q-mb-md">
              <div class="text-caption q-mb-xs text-grey-8">
                <q-icon name="info" size="xs" class="q-mr-xs" />
                已選擇 {{ selectedFiles.length }} 個檔案
              </div>
              <q-list bordered separator class="rounded-borders">
                <q-item
                  v-for="(file, index) in selectedFiles"
                  :key="index"
                  dense
                  class="file-item"
                >
                  <q-item-section avatar>
                    <q-icon
                      :name="getFileIconName(file.name)"
                      :color="getFileIconColor(file.name)"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-tooltip>{{ file.name }}</q-tooltip>
                    <q-item-label lines="1" class="file-name">{{
                      file.name
                    }}</q-item-label>
                    <q-item-label caption>{{
                      formatFileSize(file.size)
                    }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn
                      flat
                      round
                      dense
                      color="grey-7"
                      icon="close"
                      @click="removeFile(index)"
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>
        <!-- 送出按鈕 -->
        <div class="form-actions">
          <q-btn outline unelevated color="primary" @click="submitot">
            <template v-slot:default>
              <Send class="q-mr-sm" :stroke-width="1.5" :size="20" />
              送出
            </template>
          </q-btn>
        </div>
      </div>
    </div>
    <!-- Confirm Dialog -->
    <q-dialog v-model="confirmDialog" persistent>
      <q-card class="confirm-dialog">
        <q-card-section class="bg-primary text-white">
          <div class="text-subtitle1">確認提交申請</div>
        </q-card-section>
        <q-card-section>
          <div class="confirm-content">
            <div class="confirm-item">
              <div class="confirm-label">加班類別</div>
              <div class="confirm-value">{{ otType }}</div>
            </div>
            <div class="confirm-item">
              <div class="confirm-label">加班時間</div>
              <div class="confirm-value">
                {{ startDate }} {{ startTime }} ~ {{ endTime }}
              </div>
            </div>
            <div class="confirm-item">
              <div class="confirm-label">總時數</div>
              <div class="confirm-value">{{ totalHours }}</div>
            </div>
            <div class="confirm-item">
              <div class="confirm-label">加班原因</div>
              <div class="confirm-value">{{ reason }}</div>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn
            flat
            label="確認送出"
            color="primary"
            @click="confirmSubmitOt"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import apiClient from "../api";
import { ref, computed } from "vue";
import { DateTime } from "luxon";
import { usePendingStore } from "../stores/pendingStore";
import { useRouter, useRoute } from "vue-router";
import { useQuasar } from "quasar";
import { useFormFlows } from "../composables/useFormFlows";
import { Send } from "lucide-vue-next";

const route = useRoute();
const router = useRouter();
const $q = useQuasar();
const pendingStore = usePendingStore();
const props = defineProps({ Dname: String, Userid: String });
const user = ref({ userId: props.Userid, department: props.Dname });
const step = ref(1);

// 使用 useFormFlows composable
const { flowSteps, loading: loadingFlows } = useFormFlows("ot");

const otType = ref("");
const otOptions = ref(["時數假", "休假招回"]);
const startDate = ref("");
const startTime = ref("");
const endTime = ref("");
const reason = ref("");
const totalHours = computed(() => {
  if (!startTime.value || !endTime.value) return "0.0";
  const start = parseTime(startTime.value);
  const end = parseTime(endTime.value);
  if (end > start) {
    return ((end - start) / (1000 * 60 * 60)).toFixed(1);
  }
  return "0.0";
});
function parseTime(time) {
  const [hours, minutes] = time.split(":").map(Number);
  return new Date(0, 0, 0, hours, minutes);
}
// 附件上傳
const selectedFiles = ref([]);
const tempFileUpload = ref(null);
const attachmentsCount = ref(0);
function handleFileSelection(files) {
  if (!files) return;
  if (Array.isArray(files)) {
    selectedFiles.value = [...selectedFiles.value, ...files];
  } else {
    selectedFiles.value = [...selectedFiles.value, files];
  }
  if (selectedFiles.value.length > 5) {
    selectedFiles.value = selectedFiles.value.slice(0, 5);
    $q.notify({
      color: "warning",
      message: "最多只能上傳5個檔案",
      icon: "warning",
    });
  }
  attachmentsCount.value = selectedFiles.value.length;
  tempFileUpload.value = null;
}
function removeFile(index) {
  selectedFiles.value.splice(index, 1);
  attachmentsCount.value = selectedFiles.value.length;
}
function getFileIconName(filename) {
  const extension = filename.split(".").pop().toLowerCase();
  if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
    return "image";
  } else if (extension === "pdf") {
    return "picture_as_pdf";
  } else {
    return "insert_drive_file";
  }
}
function getFileIconColor(filename) {
  const extension = filename.split(".").pop().toLowerCase();
  if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
    return "green";
  } else if (extension === "pdf") {
    return "red";
  } else {
    return "blue";
  }
}
function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
const confirmDialog = ref(false);
function submitot() {
  confirmDialog.value = true;
}
const confirmSubmitOt = async () => {
  const requestData = {
    user_id: user.value.userId,
    otType: otType.value,
    stime: toUTCDateTime(startDate.value, startTime.value),
    etime: toUTCDateTime(startDate.value, endTime.value),
    totalHours: totalHours.value,
    reason: reason.value,
    attachmentsCount: attachmentsCount.value,
  };
  try {
    const response = await apiClient.post("/formsend/submit_ot", requestData);
    const result = response.data;
    if (result.status === "success") {
      await pendingStore.fetchPendingTasks(props.Userid);
      if (selectedFiles.value.length > 0) {
        await uploadAttachments(result.form_id, selectedFiles.value);
      }
      const username = route.query.username;
      router.push({
        path: "/thanks",
        query: { formId: result.form_id, username },
      });
    } else {
      $q.notify({
        color: "negative",
        message: "申請送出失敗",
        caption: result.error || "請稍後再試",
        icon: "error",
      });
    }
  } catch (error) {
    $q.notify({
      color: "negative",
      message: "API 錯誤",
      caption: "請確認網路連線",
      icon: "error",
    });
  }
};
const toUTCDateTime = (date, time) => {
  return DateTime.fromFormat(`${date} ${time}`, "yyyy-MM-dd HH:mm", {
    zone: "Asia/Taipei",
  })
    .toUTC()
    .toISO();
};
const uploadAttachments = async (formId, attachments = []) => {
  if (!attachments || attachments.length === 0) return;

  // 取得當前年月作為資料夾名稱 (格式: YYYYMM)
  const now = new Date();
  const yearMonth = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(
    2,
    "0"
  )}`;
  const uploadPath = `uploads/form/ot/${yearMonth}/`;

  const formData = new FormData();
  formData.append("form_id", formId);
  formData.append("upload_path", uploadPath);

  attachments.forEach((file) => {
    formData.append("attachments", file);
  });

  try {
    const response = await apiClient.post("/upload_attachments", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    const result = response.data;
    if (result.status !== "success") {
      $q.notify({
        color: "warning",
        message: "附件上傳失敗",
        caption: result.error || "表單已送出，但附件未能成功上傳",
        icon: "warning",
      });
    }
  } catch (error) {
    $q.notify({
      color: "warning",
      message: "API 錯誤 (附件上傳)",
      caption: "表單已送出，但附件未能成功上傳",
      icon: "warning",
    });
  }
};
</script>

<style scoped>
.header-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 16px;
  padding: 0;
}
.header-container .q-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.title-section {
  padding-left: 16px;
  padding-right: 16px;
}
.progress-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}
.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.progress-dot {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #424242;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8rem;
  font-weight: 500;
}
.progress-step.active .progress-dot {
  background-color: var(--q-primary);
  color: white;
}
.progress-label {
  margin-top: 4px;
  font-size: 0.75rem;
  color: #616161;
}
.progress-step.active .progress-label {
  color: var(--q-primary);
  font-weight: 500;
}
.progress-line {
  flex: 1;
  height: 1px;
  background-color: #e0e0e0;
  margin: 0 8px;
  position: relative;
  top: -14px;
}
.form-container {
  max-width: 600px;
  margin: 0 auto;
}
.form-section {
  margin-bottom: 24px;
}
.section-title {
  font-weight: 500;
  font-size: 0.9rem;
  color: #424242;
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e0e0;
}
.time-display {
  background-color: #f5f5f5;
  border-radius: 4px;
}
.time-label {
  font-size: 0.75rem;
  color: #757575;
  margin-bottom: 2px;
}
.time-value {
  font-weight: 500;
  color: #424242;
  font-size: 1.1rem;
}
.file-upload {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
  max-width: 100%;
  overflow: hidden;
}
.upload-title {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #424242;
}
.upload-area {
  display: flex;
  align-items: center;
}
.upload-input {
  flex: 1;
}
.full-width {
  width: 100%;
}
.file-list {
  margin-top: 12px;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 8px;
  width: 100%;
}
.file-item {
  padding: 4px 8px;
}
.file-name {
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}
.file-size {
  color: #757575;
  min-width: 50px;
  text-align: right;
}
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}
.confirm-dialog {
  min-width: 300px;
}
.confirm-content {
  padding: 8px 0;
}
.confirm-item {
  display: flex;
  margin-bottom: 8px;
}
.confirm-label {
  width: 80px;
  font-size: 0.85rem;
  color: #757575;
}
.confirm-value {
  flex: 1;
  font-weight: 500;
}
.time-input {
  min-width: 120px;
}
@media (max-width: 599px) {
  .q-mt-xl {
    margin-top: 100px !important;
  }
  .time-input {
    min-width: unset;
  }
}
</style>
