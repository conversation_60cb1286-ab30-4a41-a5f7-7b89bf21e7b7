<template>
  <q-page
    class="q-pa-sm flex justify-center"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">權限管理</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="green"
              @click="pageDialog = true"
            >
              <file-search :stroke-width="1" :size="26" />
              <q-tooltip>選擇頁面</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              color="red"
              @click="addPageDialog = true"
              class="q-ml-md"
            >
              <file-plus-2 :stroke-width="1" :size="26" />
              <q-tooltip>新增頁面</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />
      <!-- 選擇頁面提示 -->
      <q-card-section v-if="!selectedPage" class="q-pt-none">
        <q-banner class="bg-blue-1 text-grey-8" rounded>
          <template v-slot:avatar>
            <Info class="text-primary" :stroke-width="1" :size="30" />
          </template>
          請點擊右上角按鈕選擇要管理的頁面
        </q-banner>
      </q-card-section>
    </q-card>

    <!-- 主內容區域 - 權限設定表單 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
      v-if="selectedPage"
    >
      <!-- 頁面設定區塊 -->
      <q-card-section>
        <div class="text-primary q-mb-md flex items-center">
          <Settings :stroke-width="1" :size="20" class="q-mr-sm" />
          頁面基本設定
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6">
            <q-input
              v-model="selectedPageId"
              label="頁面編號"
              outlined
              dense
              :readonly="isEditing"
            >
              <template v-slot:prepend>
                <Hash :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-input v-model="selectedPageName" label="頁面名稱" outlined dense>
              <template v-slot:prepend>
                <Type :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-input v-model="selectedIcon" label="圖示" outlined dense>
              <template v-slot:prepend>
                <Palette :stroke-width="1" :size="18" />
              </template>
              <template v-slot:append>
                <component
                  :is="selectedIcon || 'FileQuestion'"
                  :stroke-width="1"
                  :size="20"
                />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-input v-model="selectedColor" label="顏色" outlined dense>
              <template v-slot:prepend>
                <Paintbrush :stroke-width="1" :size="18" />
              </template>
              <template v-slot:append>
                <div
                  :style="{
                    width: '20px',
                    height: '20px',
                    backgroundColor: selectedColor,
                    borderRadius: '4px',
                  }"
                ></div>
              </template>
            </q-input>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 頁面來源設定 -->
        <div class="text-primary q-mb-md flex items-center">
          <File :stroke-width="1" :size="20" class="q-mr-sm" />
          頁面來源設定
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12">
            <q-select
              v-model="selectedPageSource"
              :options="pageSourceOptions"
              label="來源"
              outlined
              dense
              emit-value
              map-options
            >
              <template v-slot:prepend>
                <File :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 頁面層級設定 -->
        <div class="text-primary q-mb-md flex items-center">
          <Layers :stroke-width="1" :size="20" class="q-mr-sm" />
          頁面層級設定
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-4">
            <q-select
              v-model="selectedParentId"
              :options="filteredPageOptions"
              label="上層"
              outlined
              dense
              emit-value
              map-options
            >
              <template v-slot:prepend>
                <Folder :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>

          <div class="col-12 col-sm-4">
            <q-input
              v-model="selectedSlevel"
              label="階層"
              outlined
              dense
              type="number"
            >
              <template v-slot:prepend>
                <Layers :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-4">
            <q-input
              v-model="selectedSort"
              label="排序"
              outlined
              dense
              type="number"
            >
              <template v-slot:prepend>
                <ArrowUpDown :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 權限設定區塊 -->
        <div class="q-mt-md">
          <div class="text-primary q-mb-md flex items-center">
            <Users :stroke-width="1" :size="20" class="q-mr-sm" />
            <div>權限設定</div>
          </div>

          <div>
            <!-- 權限群組選擇區塊 -->
            <div class="row q-mb-md">
              <q-select
                v-model="newGroup"
                :options="groupOptions"
                label="選擇群組"
                outlined
                dense
                class="col"
                use-input
                input-debounce="300"
                emit-value
                map-options
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <q-avatar color="primary" text-color="white">
                        {{ scope.opt.label.charAt(0) }}
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
              <q-btn
                flat
                round
                @click="addGroup"
                class="q-ml-sm"
                :disable="!newGroup"
              >
                <UserPlus class="text-primary" :stroke-width="1" :size="30" />
                <q-tooltip>新增權限群組</q-tooltip>
              </q-btn>
            </div>

            <!-- 已選擇群組列表 -->
            <div
              v-if="!allowGroups.length"
              class="text-center q-pa-lg bg-grey-2 rounded-borders q-mt-md"
            >
              <Info class="text-grey" :stroke-width="1" :size="30" />
              <div class="text-grey q-mt-sm">
                尚未設定權限群組，請選擇群組並點擊新增按鈕
              </div>
            </div>

            <!-- 優化群組顯示 -->
            <div v-else class="row q-col-gutter-sm q-mt-md">
              <div
                v-for="group in allowGroupsData"
                :key="group.value"
                class="col-6 col-sm-4 col-md-3"
              >
                <q-card class="group-card">
                  <q-card-section class="text-center q-py-sm">
                    <div class="text-subtitle2 ellipsis row items-center">
                      <circle-user-round
                        class="text-primary absolute-left q-ml-sm q-mt-xs"
                        :stroke-width="1"
                        :size="26"
                        style="top: 2px"
                      />
                      <div class="full-width text-center">
                        {{ group.label }}
                      </div>
                    </div>
                    <q-btn
                      flat
                      round
                      color="negative"
                      size="sm"
                      class="absolute-top-right q-mt-xs q-mr-xs"
                      @click="removeGroup(group.value)"
                    >
                      <X :stroke-width="1" :size="26" />
                    </q-btn>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- 按鈕 -->
      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          outline
          unelevated
          type="submit"
          color="secondary"
          class="q-px-md"
          :disable="isSaveDisabled"
          @click="savePermissions"
        >
          <template v-slot:default>
            <Save :stroke-width="1" :size="26" class="q-mr-sm" />
            儲存
          </template>
        </q-btn>
      </q-card-actions>
    </q-card>

    <!-- 選擇頁面 Dialog -->
    <q-dialog v-model="pageDialog">
      <q-card
        class="page-dialog-card"
        style="width: 100%; max-width: 420px; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="text-center page-dialog-title">
          <Search class="w-5 h-5 text-primary q-mr-sm" />
          選擇頁面
        </q-card-section>

        <!-- 搜尋框 -->
        <q-card-section class="q-pt-none q-pb-xs">
          <q-input
            v-model="searchQuery"
            placeholder="搜尋頁面..."
            dense
            outlined
            clearable
          >
            <template v-slot:prepend>
              <Search class="w-4 h-4 text-grey-7" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 樹狀頁面列表 -->
        <q-card-section
          class="page-dialog-content"
          style="max-height: 400px; overflow-y: auto"
        >
          <q-tree
            :nodes="treeNodes"
            node-key="id"
            label-key="name"
            no-connectors
            :filter="searchQuery"
          >
            <template v-slot:default-header="prop">
              <div
                class="row items-center full-width cursor-pointer tree-node"
                @click="selectPage(prop.node.id)"
              >
                <div class="col-auto">
                  <component
                    :is="prop.node.icon || 'FileQuestion'"
                    class="w-5 h-5 q-mr-sm tree-icon-grey"
                  />
                </div>
                <div class="col" :class="'level-' + prop.node.level">
                  {{ prop.node.name }}
                </div>
              </div>
            </template>
          </q-tree>
        </q-card-section>

        <!-- 按鈕區 -->
        <q-card-actions align="right" class="q-pa-sm page-dialog-actions">
          <q-btn label="取消" color="grey" flat @click="pageDialog = false" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 新增頁面 Dialog -->
    <q-dialog v-model="addPageDialog">
      <q-card style="width: 100%; max-width: 380px" class="rounded-borders">
        <!-- 標題 -->
        <q-card-section class="text-primary q-pa-md text-center">
          <div class="row items-center justify-center q-mb-sm">
            <FilePlus class="w-6 h-6 text-primary" />
          </div>
          <div class="text-subtitle1 text-weight-medium">新增頁面</div>
        </q-card-section>

        <!-- 內容區 -->
        <q-card-section class="q-pt-none">
          <q-input
            v-model="newPageId"
            label="頁面編號"
            outlined
            dense
            autofocus
            clearable
            class="full-width"
          >
            <template v-slot:prepend>
              <Hash class="w-4 h-4 text-grey-7" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 按鈕區 -->
        <q-card-actions align="right" class="q-pa-sm">
          <q-btn
            label="取消"
            color="grey"
            flat
            @click="addPageDialog = false"
          />

          <q-btn
            outline
            unelevated
            color="secondary"
            @click="confirmAddPage"
            :disable="!newPageId.trim()"
          >
            <template v-slot:default>
              <Check class="q-mr-xs" />
              確認
            </template>
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import apiClient from "../api";
import { ref, onMounted, computed, watch } from "vue";
import { useQuasar } from "quasar";

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const selectedPage = ref("");
const isEditing = ref(true);
const pageDialog = ref(false);
const selectedPageId = ref("");
const selectedPageName = ref("");
const selectedSort = ref("");
const selectedSlevel = ref("");
const selectedIcon = ref("");
const selectedParentId = ref("");
const selectedColor = ref("");
const selectedPageSource = ref("");
const allowGroups = ref([]);
const searchQuery = ref("");

// 新增 newGroup 變量
const newGroup = ref(null);

// 添加 allowGroupsData 計算屬性
const allowGroupsData = computed(() => {
  return allowGroups.value.map((groupValue) => {
    const group = groupOptions.value.find((opt) => opt.value === groupValue);
    return group || { value: groupValue, label: groupValue };
  });
});

// 添加 addGroup 方法
const addGroup = () => {
  if (newGroup.value && !allowGroups.value.includes(newGroup.value)) {
    allowGroups.value.push(newGroup.value);
    newGroup.value = null;
  }
};

// 添加 removeGroup 方法
const removeGroup = (groupValue) => {
  allowGroups.value = allowGroups.value.filter((g) => g !== groupValue);
};

// 搜尋過濾
const filteredMenus = computed(() => {
  if (!searchQuery.value) {
    return allpageOptions.value.filter((m) => m.Slevel === 1);
  }

  const query = searchQuery.value.toLowerCase();

  // 如果直接搜到頁面，就直接顯示
  const directMatches = allpageOptions.value.filter(
    (m) => m.Name.toLowerCase().includes(query) && m.Slevel === 1
  );

  // 如果是下層頁面匹配，需要顯示其上層
  const childMatches = allpageOptions.value.filter(
    (m) => m.Name.toLowerCase().includes(query) && m.Slevel > 1
  );

  // 收集所有需要顯示的父層
  const parentIds = new Set();
  childMatches.forEach((child) => {
    if (child.Parent_id) {
      parentIds.add(child.Parent_id.trim());

      // 如果是第三層，還需要找到第二層的父層
      if (child.Slevel === 3) {
        const parent = allpageOptions.value.find(
          (p) => p.Page_id === child.Parent_id
        );
        if (parent && parent.Parent_id) {
          parentIds.add(parent.Parent_id.trim());
        }
      }
    }
  });

  // 合併直接匹配和父層
  return [
    ...directMatches,
    ...allpageOptions.value.filter(
      (m) => m.Slevel === 1 && parentIds.has(m.Page_id.trim())
    ),
  ];
});

const addPageDialog = ref(false);
const newPageId = ref("");

const confirmAddPage = () => {
  if (!newPageId.value.trim()) {
    return $q.notify({ type: "negative", message: "請輸入頁面編號" });
  }
  selectedPage.value = newPageId.value.trim();
  // 設定頁面資料（自動填入預設值）
  selectedPageId.value = newPageId.value.trim();
  selectedPageName.value = "";
  selectedSort.value = "0";
  selectedParentId.value = "";
  selectedSlevel.value = "1";
  selectedIcon.value = "Layout";
  selectedColor.value = "primary";
  selectedPageSource.value = "Page";
  allowGroups.value = ["Admin"];
  // 關閉對話框
  addPageDialog.value = false;
  // 清空輸入框
  newPageId.value = "";

  $q.notify({
    type: "positive",
    message: "已創建新頁面，請完善資料後儲存",
    icon: "check_circle",
    position: "top",
  });
};

const isSaveDisabled = computed(() => {
  return false; // 不再需要檢查 formType
});

const filteredPageOptions = ref([]);
const pageOptions = ref([]);
const allpageOptions = ref([]);

const loadPageData = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/page/get_FormsPage`); // 後端 API
    if (response.data) {
      // 這裡假設 API 回傳陣列
      allpageOptions.value = response.data.map((page) =>
        Object.fromEntries(
          Object.entries(page).map(([key, value]) => [
            key,
            typeof value === "string" ? value.trim() : value,
          ])
        )
      );
      // 設定 pageOptions 讓 QSelect 顯示
      pageOptions.value = allpageOptions.value.map((page) => ({
        label: page.Name,
        value: page.Page_id,
      }));
    }
  } catch (error) {
    console.error("讀取頁面資料失敗:", error);
    $q.notify({
      type: "negative",
      message: "讀取頁面資料失敗",
      icon: "error",
      position: "top",
    });
  }
};

const pageSourceOptions = [
  { label: "頁面", value: "Page" },
  { label: "表單", value: "Form" },
  { label: "主分類", value: "Kind" },
  { label: "中分類", value: "Type" },
];

const groupOptions = ref([]);
const fetchUsersgroup = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/users/get_usersgroup`);
    groupOptions.value = response.data.map((group) => ({
      label: group.Name.trim(),
      value: group.Code.trim(),
    }));
  } catch (error) {
    console.error("讀取群組資料失敗:", error);
    $q.notify({
      type: "negative",
      message: "讀取群組資料失敗",
      icon: "error",
      position: "top",
    });
  }
};

const selectPage = async (pageId) => {
  // 根據 Page_id 找到對應的資料
  const page = allpageOptions.value.find((p) => p.Page_id === pageId);
  if (page) {
    selectedPage.value = page;
    selectedPageId.value = page.Page_id;
    selectedPageName.value = page.Name;
    selectedSort.value = page.Sort;
    selectedSlevel.value = page.Slevel;
    selectedIcon.value = page.Icon;
    selectedParentId.value = page.Parent_id;
    selectedColor.value = page.Color;
    selectedPageSource.value = page.Source;
    allowGroups.value = Array.isArray(page.Allow)
      ? page.Allow.map((group) => group.trim())
      : [];
  }
  filteredPageOptions.value = [...pageOptions.value].filter(
    (option) => option.value.trim() !== selectedPageId.value.trim()
  );

  pageDialog.value = false;
};

const savePermissions = async () => {
  if (!selectedPageId.value) {
    return $q.notify({
      type: "negative",
      message: "請選擇頁面",
      icon: "error",
      position: "top",
    });
  }

  try {
    const payload = {
      page_id: selectedPageId.value,
      page_name: selectedPageName.value,
      page_source: selectedPageSource.value,
      color: selectedColor.value,
      icon: selectedIcon.value,
      parent_id: selectedParentId.value,
      sort: selectedSort.value,
      slevel: selectedSlevel.value,
      allow_groups: allowGroups.value.length > 0 ? allowGroups.value : null,
    };

    const response = await apiClient.post(
      `${apiBaseUrl}/page/save_Permissions`,
      payload
    );

    if (response.data.success) {
      // 如果後端回傳最新的 `page` 資料，更新前端變數
      if (response.data.page) {
        selectedPageName.value = response.data.page.Name;
        selectedPageSource.value = response.data.page.Source;
        // 不再需要 Attach 欄位
        allowGroups.value = Array.isArray(response.data.page.Allow)
          ? response.data.page.Allow.map((group) => group.trim())
          : [];
      }

      $q.notify({
        type: "positive",
        message: response.data.message || "權限設定已儲存",
        icon: "save",
        position: "top",
      });
      loadPageData();
    } else {
      throw new Error(response.data.message || "儲存失敗");
    }
  } catch (error) {
    console.error("儲存權限失敗:", error);
    $q.notify({
      type: "negative",
      message: "權限儲存失敗: " + (error.message || "未知錯誤"),
      icon: "error",
      position: "top",
    });
  }
};

// 轉換為樹狀結構數據
const treeNodes = computed(() => {
  if (!allpageOptions.value.length) return [];

  // 先找出所有第一層節點
  const level1Nodes = allpageOptions.value
    .filter((page) => page.Slevel === 1)
    .map((page) => ({
      id: page.Page_id,
      name: page.Name,
      icon: page.Icon,
      color: page.Color,
      level: 1,
      children: [],
    }));

  // 添加第二層節點
  level1Nodes.forEach((node) => {
    const level2Pages = allpageOptions.value.filter(
      (page) => page.Slevel === 2 && page.Parent_id?.trim() === node.id.trim()
    );

    node.children = level2Pages.map((page) => ({
      id: page.Page_id,
      name: page.Name,
      icon: page.Icon,
      color: page.Color,
      level: 2,
      children: [],
    }));

    // 添加第三層節點
    node.children.forEach((childNode) => {
      const level3Pages = allpageOptions.value.filter(
        (page) =>
          page.Slevel === 3 && page.Parent_id?.trim() === childNode.id.trim()
      );

      childNode.children = level3Pages.map((page) => ({
        id: page.Page_id,
        name: page.Name,
        icon: page.Icon,
        color: page.Color,
        level: 3,
      }));
    });
  });

  return level1Nodes;
});

onMounted(async () => {
  await loadPageData();
  await fetchUsersgroup();
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.group-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tree-node:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.tree-icon-grey {
  color: #9e9e9e;
}

.level-1 {
  padding-left: 0px;
}

.level-2 {
  padding-left: 16px;
}

.level-3 {
  padding-left: 32px;
}

.level-4 {
  padding-left: 48px;
}

.custom-separator {
  height: 0.5px;
  margin-bottom: 16px;
}

.page-dialog-card {
  overflow: hidden;
}

.page-dialog-title {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
}

.page-dialog-content {
  padding-top: 0;
}

.page-dialog-actions {
  background-color: #f5f5f5;
}

/* 修正 q-banner 中 icon 的垂直置中問題 */
.q-banner__avatar {
  align-self: center !important;
}
</style>
