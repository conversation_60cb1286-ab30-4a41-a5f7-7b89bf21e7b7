import { ref } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

export function useDepartmentAndGroup() {
  const $q = useQuasar();

  // 響應式數據
  const departmentOptions = ref([]);
  const groupOptions = ref([]);

  // API 函數
  const fetchDepartments = async () => {
    try {
      const response = await apiClient.get("/users/get_all_department");
      departmentOptions.value = response.data.map((dep) => ({
        label: dep.Name.trim(),
        value: dep.Id.trim(),
      }));
    } catch (error) {
      console.error("載入部門失敗:", error);
      $q.notify({ type: "negative", message: "載入部門失敗" });
    }
  };

  const fetchUserGroups = async () => {
    try {
      const response = await apiClient.get("/users/get_usersgroup");
      groupOptions.value = response.data.map((group) => ({
        label: group.Name.trim(),
        value: group.Code.trim(),
      }));
    } catch (error) {
      console.error("讀取群組資料失敗:", error);
      $q.notify({ type: "negative", message: "載入群組失敗" });
    }
  };

  return {
    // 響應式數據
    departmentOptions,
    groupOptions,

    // API 函數
    fetchDepartments,
    fetchUserGroups,
  };
}
