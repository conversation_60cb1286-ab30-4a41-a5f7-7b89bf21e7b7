class NotImplementedException extends Error {}

class DeviceStatus {
  constructor(byte) {
    this.byte = byte;
    this.bits = [];
    this.bitsAsc = [];

    for (let j = 7; j >= 0; j--) {
      let bit = byte & (1 << j) ? 1 : 0;
      this.bits.push(bit);
    }

    this.bitsAsc = this.bits.slice().reverse();
  }

  getBits() {
    return this.bits.join("");
  }

  static commands() {
    throw new NotImplementedException();
  }

  static getClassName() {
    throw new NotImplementedException();
  }

  toJSON() {
    return {
      className: this.constructor.getClassName(),
      byte: this.byte,
      bits: this.getBits(),
      statuses: [],
    };
  }
}

class PrinterStatus extends DeviceStatus {
  static commands() {
    return [0x10, 0x04, 1];
  }

  static getClassName() {
    return "PrinterStatus";
  }

  toJSON() {
    const result = super.toJSON();
    for (let i = 0; i < 8; i++) {
      let label = "Fixed";
      let status = "ok";
      switch (i) {
        case 2:
          label =
            this.bitsAsc[i] === 1
              ? "Drawer pin 3 is HIGH"
              : "Drawer pin 3 is LOW";
          break;
        case 3:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label = this.bitsAsc[i] === 1 ? "Offline" : "Online";
          break;
        case 5:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1
              ? "Waiting for recovery"
              : "Not waiting for recovery";
          break;
        case 6:
          label = this.bitsAsc[i] === 1 ? "Feed button pressed" : "Not pressed";
          break;
      }
      result.statuses.push({ bit: i, value: this.bitsAsc[i], label, status });
    }
    return result;
  }
}

class OfflineCauseStatus extends DeviceStatus {
  static commands() {
    return [0x10, 0x04, 2];
  }

  static getClassName() {
    return "OfflineCauseStatus";
  }

  toJSON() {
    const result = super.toJSON();
    for (let i = 0; i < 8; i++) {
      let label = "Fixed";
      let status = "ok";
      switch (i) {
        case 2:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label = this.bitsAsc[i] === 1 ? "Cover is open" : "Cover is closed";
          break;
        case 3:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1
              ? "Paper feed by button"
              : "No feed by button";
          break;
        case 5:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1 ? "Paper end stop" : "No paper end stop";
          break;
        case 6:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label = this.bitsAsc[i] === 1 ? "Error occurred" : "No error";
          break;
      }
      result.statuses.push({ bit: i, value: this.bitsAsc[i], label, status });
    }
    return result;
  }
}

class ErrorCauseStatus extends DeviceStatus {
  static commands() {
    return [0x10, 0x04, 3];
  }

  static getClassName() {
    return "ErrorCauseStatus";
  }

  toJSON() {
    const result = super.toJSON();
    for (let i = 0; i < 8; i++) {
      let label = "Fixed";
      let status = "ok";
      switch (i) {
        case 2:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1
              ? "Recoverable error"
              : "No recoverable error";
          break;
        case 3:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1 ? "Autocutter error" : "No autocutter error";
          break;
        case 5:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1
              ? "Unrecoverable error"
              : "No unrecoverable error";
          break;
        case 6:
          status = this.bitsAsc[i] === 1 ? "error" : "ok";
          label =
            this.bitsAsc[i] === 1
              ? "Auto-recoverable error"
              : "No auto-recoverable error";
          break;
      }
      result.statuses.push({ bit: i, value: this.bitsAsc[i], label, status });
    }
    return result;
  }
}

class RollPaperSensorStatus extends DeviceStatus {
  static commands() {
    return [0x10, 0x04, 4];
  }

  static getClassName() {
    return "RollPaperSensorStatus";
  }

  toJSON() {
    const result = super.toJSON();
    for (let i = 0; i < 8; i++) {
      let label = "Fixed";
      let status = "ok";
      if (i === 2 || i === 3) {
        const val = `${this.bitsAsc[2]}${this.bitsAsc[3]}`;
        if (val === "11") {
          status = "warning";
          label = "Paper near end";
        } else if (val === "00") {
          label = "Paper adequate";
        }
        if (i === 2) {
          result.statuses.push({ bit: "2,3", value: val, label, status });
        }
      } else if (i === 5 || i === 6) {
        const val = `${this.bitsAsc[5]}${this.bitsAsc[6]}`;
        if (val === "11") {
          status = "error";
          label = "Paper not present";
        } else if (val === "00") {
          label = "Paper present";
        }
        if (i === 5) {
          result.statuses.push({ bit: "5,6", value: val, label, status });
        }
      } else {
        result.statuses.push({ bit: i, value: this.bitsAsc[i], label, status });
      }
    }
    return result;
  }
}

module.exports = {
  PrinterStatus,
  OfflineCauseStatus,
  ErrorCauseStatus,
  RollPaperSensorStatus,
};
