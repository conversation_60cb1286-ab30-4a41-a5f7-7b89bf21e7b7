import axios from "axios";
import router from "./router"; // ✅ 直接匯入 Vue Router
import { isTokenExpired } from "./utils/auth"; // ✅ 確保 `isTokenExpired` 正確匯入

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL; // ✅ 讀取環境變數

// 創建 axios 實例
const apiClient = axios.create({
  baseURL: apiBaseUrl,
});

// 🔥【請求攔截器】自動加上 `Authorization`
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("jwt_token");

    if (token) {
      if (isTokenExpired(token)) {
        console.warn("🚨 JWT 已過期，執行登出動作...");
        localStorage.removeItem("jwt_token");

        router.push({ path: "/", query: { expired: true } }); // ✅ 直接跳轉

        return Promise.reject("Token 已過期");
      }

      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    console.error("❌ axios 請求攔截器錯誤：", error);
    return Promise.reject(error);
  }
);

// 🔥【回應攔截器】自動處理 401 未授權錯誤
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      console.warn("🚨 401 未授權，清除 Token 並跳轉登入頁");
      localStorage.removeItem("jwt_token");

      router.push({ path: "/", query: { unauthorized: true } }); // ✅ 直接跳轉
    } else {
      console.error("❌ axios 回應攔截器錯誤：", error);
    }
    return Promise.reject(error);
  }
);

export default apiClient;
