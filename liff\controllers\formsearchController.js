//單據查詢主功能
const sql = require("mssql");
const dbConfig = require("../config/db");

const formsearch = async (req, res) => {
  const {
    uid,
    userid,
    queryKeyword,
    date_from,
    date_to,
    submitter_id,
    form_type,
  } = req.query;
  if (!userid) {
    return res.status(400).json({ error: "❌ 缺少 userid 參數" });
  }
  //console.log(userid);
  try {
    const pool = await sql.connect(dbConfig);
    let whereClauses = [];

    whereClauses.push(`
      (
        F.userid = @userid 
        OR EXISTS (
          SELECT 1 FROM Forms_approver A
          WHERE A.Form_id = F.Form_id 
          AND A.approver_id = @userid
        )
        OR EXISTS (
          SELECT 1 FROM Forms_viewer V
          WHERE V.form_id = F.Type
          AND V.user_id = @userid
        )
      )
    `);

    if (queryKeyword) {
      whereClauses.push("F.Form_id LIKE @queryKeyword");
    }
    // 🔹 **修正 `date_from` 和 `date_to` 的處理**
    const validDateFrom =
      date_from && date_from !== "null" && date_from !== "" ? date_from : null;
    const validDateTo =
      date_to && date_to !== "null" && date_to !== "" ? date_to : null;

    if (validDateFrom && validDateTo) {
      whereClauses.push(
        "CAST(F.Created AS DATE) BETWEEN @date_from AND @date_to"
      );
    }

    if (submitter_id) {
      whereClauses.push(
        "F.id = (SELECT id FROM Users WHERE ID = @submitter_id)"
      );
    }

    if (form_type) {
      whereClauses.push("F.Type = @form_type");
    }

    const finalWhereClause =
      whereClauses.length > 0 ? `WHERE ${whereClauses.join(" AND ")}` : "";

    //console.log("🔍 執行 SQL:", finalWhereClause);

    const request = pool.request();
    //request.input("uid", sql.VarChar, uid.trim());
    request.input("userid", sql.VarChar, userid.trim());

    if (queryKeyword) {
      request.input("queryKeyword", sql.VarChar, `%${queryKeyword}%`);
    }

    // ✅ **只有 `validDateFrom` 和 `validDateTo` 有效時才傳入**
    if (validDateFrom && validDateTo) {
      request.input("date_from", sql.Date, validDateFrom);
      request.input("date_to", sql.Date, validDateTo);
    }

    if (submitter_id) {
      request.input("submitter_id", sql.VarChar, submitter_id);
    }

    if (form_type) {
      request.input("form_type", sql.VarChar, form_type);
    }

    const result = await request.query(`
          SELECT 
              f.Form_id,
              n.Name AS doc_name,
              u.id AS UID,
              u.Name AS submitter,
              f.Created AS submit_date,
              f.Status AS fstatus
          FROM Forms F
          LEFT JOIN Forms_name n ON F.Type = n.Type
          LEFT JOIN Users U ON F.userid = U.id
          ${finalWhereClause}
          ORDER BY F.Created DESC;
        `);

    //console.log("✅ 查詢成功:", result.recordset.length, "筆資料");
    res.json(result.recordset);
  } catch (error) {
    console.error("❌ SQL 執行失敗:", error);
    res
      .status(500)
      .json({ error: "❌ 內部伺服器錯誤", details: error.message });
  } finally {
  }
};
module.exports = { formsearch };
