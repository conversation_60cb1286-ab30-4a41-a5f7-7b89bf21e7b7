<!-- src/components/NotificationCenter.vue -->
<template>
  <div class="notification-wrapper">
    <q-btn
      flat
      round
      dense
      class="notification-trigger"
      :class="{ 'has-unread': totalUnreadCount > 0 }"
    >
      <!-- 通知鈴鐺 -->
      <div class="notification-icon-container">
        <Bell
          class="notification-icon"
          :class="{ 'text-red': totalUnreadCount > 0 }"
          :size="22"
        />
        <div v-if="totalUnreadCount > 0" class="notification-badge">
          {{ totalUnreadCount > 99 ? "99+" : totalUnreadCount }}
        </div>
      </div>

      <!-- 通知選單 -->
      <q-menu
        v-model="showMenu"
        class="notification-menu"
        transition-show="jump-down"
        transition-hide="jump-up"
        fit
        anchor="bottom middle"
        self="top middle"
      >
        <div class="modern-notification-container">
          <!-- 標題區 -->
          <div class="notification-header q-px-sm q-py-md">
            <div class="text-subtitle1 text-weight-medium">通知中心</div>
            <div class="notification-actions">
              <div class="notification-counter text-caption q-mr-sm">
                {{
                  totalUnreadCount > 0
                    ? `${totalUnreadCount} 個未讀`
                    : "全部已讀"
                }}
              </div>
              <q-btn
                flat
                round
                dense
                icon="refresh"
                size="sm"
                :loading="loading"
                @click="refreshNotifications"
              >
                <q-tooltip>刷新通知</q-tooltip>
              </q-btn>
            </div>
          </div>

          <!-- 通知列表 -->
          <div class="notification-list q-px-sm q-pb-sm">
            <!-- 待簽核 -->
            <div
              class="notification-item q-py-sm"
              :class="{ 'has-notification': pendingCount > 0 }"
              @click="handleNotificationClick('pending')"
            >
              <div class="item-icon">
                <FileText :size="18" />
              </div>
              <div class="item-content">
                <div class="item-title">
                  待簽核
                  <q-badge
                    v-if="pendingCount > 0"
                    rounded
                    color="red"
                    class="q-ml-sm"
                    >{{ pendingCount }}</q-badge
                  >
                </div>
                <div class="item-subtitle">
                  {{ pendingCount > 0 ? "有待處理的簽核項目" : "無待簽核項目" }}
                </div>
              </div>
            </div>

            <!-- 公告 -->
            <div
              class="notification-item q-py-sm"
              :class="{ 'has-notification': unreadBulletinCount > 0 }"
              @click="handleNotificationClick('unread')"
            >
              <div class="item-icon">
                <message-square-text :size="18" />
              </div>
              <div class="item-content">
                <div class="item-title">
                  公告
                  <q-badge
                    v-if="unreadBulletinCount > 0"
                    rounded
                    color="red"
                    class="q-ml-sm"
                    >{{ unreadBulletinCount }}</q-badge
                  >
                </div>
                <div class="item-subtitle">
                  {{
                    unreadBulletinCount > 0 ? "有未讀的公告訊息" : "無未讀公告"
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-menu>
    </q-btn>

    <!-- 必讀公告對話框 -->
    <q-dialog
      v-model="requiredBulletinDialog.show"
      transition-show="scale"
      transition-hide="scale"
    >
      <q-card class="modern-dialog notification-dialog">
        <div class="dialog-header">
          <div class="alert-icon-container">
            <AlertCircle size="32" class="text-white" />
          </div>
        </div>

        <q-card-section class="text-center q-pt-xl q-pb-md">
          <div class="text-h6 q-mt-lg">重要公告提醒</div>

          <div class="text-subtitle1 q-mt-md q-mb-sm">
            您有
            <span class="text-red-8 text-weight-bold">{{
              requiredBulletinDialog.count
            }}</span>
            則必讀公告
          </div>
          <div class="text-body2 text-grey-7">
            這些公告需要您確認閱讀，請及時處理
          </div>
        </q-card-section>

        <q-card-actions align="center" class="q-pa-md">
          <q-btn
            flat
            label="稍後處理"
            color="grey-7"
            class="q-px-md"
            v-close-popup
          />
          <q-btn
            unelevated
            color="primary"
            label="立即查看"
            class="q-px-lg q-ml-sm"
            @click="viewRequiredBulletins"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from "vue";
import { useRouter } from "vue-router";
import { usePendingStore } from "../stores/pendingStore";
import apiClient from "../api";
import { Bell, FileText, MessageSquare, AlertCircle } from "lucide-vue-next";

const props = defineProps({
  userId: {
    type: String,
    required: true,
  },
  userDep: {
    type: String,
    default: "",
  },
  userPermissions: {
    type: [String, Array],
    default: () => [],
  },
  userBranches: {
    type: Array,
    default: () => [],
  },
});

const router = useRouter();
const pendingStore = usePendingStore();
const emit = defineEmits(["open-bulletins"]);

// 狀態管理
const showMenu = ref(false);
const unreadBulletinCount = ref(0);
const requiredBulletinCount = ref(0);
const checkInterval = ref(null);
const loading = ref(false);
const requiredBulletinDialog = ref({
  show: false,
  count: 0,
});

// 計算屬性
const pendingCount = computed(() => pendingStore.pendingTasks);
const totalUnreadCount = computed(
  () => unreadBulletinCount.value + pendingCount.value
);

// 檢查未讀公告和待簽核
const checkNotifications = async () => {
  if (loading.value) return;
  loading.value = true;

  try {
    // 檢查待簽核數量
    await pendingStore.fetchPendingTasks(props.userId);

    // 準備查詢參數
    const params = {
      userDep: props.userDep,
      userGroup: Array.isArray(props.userPermissions)
        ? JSON.stringify(props.userPermissions)
        : props.userPermissions,
      userBranches:
        props.userBranches.length > 0
          ? JSON.stringify(props.userBranches)
          : undefined,
    };

    // 檢查一般未讀公告
    const unreadResponse = await apiClient.get(
      `/btin/unread-count/${props.userId}`,
      { params }
    );
    if (unreadResponse.data.success) {
      unreadBulletinCount.value = unreadResponse.data.count || 0;
    }
    // console.log("未讀公告:", unreadBulletinCount.value);
    // console.log("待簽核:", pendingCount.value);
    // console.log("總數:", totalUnreadCount.value);

    // 檢查必讀公告（只用於顯示提醒對話框）
    const requiredResponse = await apiClient.get(
      `/btin/required-unread-count/${props.userId}`,
      { params }
    );
    if (requiredResponse.data.success) {
      requiredBulletinCount.value = requiredResponse.data.count || 0;
      if (requiredResponse.data.count > 0) {
        requiredBulletinDialog.value = {
          show: true,
          count: requiredResponse.data.count,
        };
      }
    }
  } catch (error) {
    console.error("檢查通知錯誤:", error);
  } finally {
    loading.value = false;
  }
};

// 處理通知點擊
const handleNotificationClick = (type) => {
  showMenu.value = false; // 立即隱藏選單

  switch (type) {
    case "pending":
      emit("open-bulletins", "pending");
      break;
    case "unread":
      emit("open-bulletins", "unread");
      localStorage.setItem("bulletin_filter", "unread");
      nextTick(() => {
        window.dispatchEvent(
          new CustomEvent("filter-bulletins", {
            detail: { filter: "unread" },
          })
        );
      });
      break;
  }
};

// 查看必讀公告
const viewRequiredBulletins = () => {
  showMenu.value = false; // 立即隱藏選單
  requiredBulletinDialog.value.show = false;
  emit("open-bulletins", "required");
  localStorage.setItem("bulletin_filter", "required");
  nextTick(() => {
    window.dispatchEvent(
      new CustomEvent("filter-bulletins", {
        detail: { filter: "required" },
      })
    );
  });
};

// 添加事件總線，用於在公告閱讀後通知更新
const refreshNotifications = () => {
  checkNotifications();
};

// 啟動定期檢查
const startPeriodicCheck = () => {
  if (checkInterval.value) {
    clearInterval(checkInterval.value);
  }

  // 每5分鐘檢查一次
  checkInterval.value = setInterval(() => {
    checkNotifications();
  }, 5 * 60 * 1000);
};

// 生命週期鉤子
onMounted(async () => {
  if (props.userId) {
    await checkNotifications();
    startPeriodicCheck();

    // 添加全局事件監聽，當公告被閱讀時更新通知
    window.addEventListener("bulletin-read", refreshNotifications);
  }
});

onBeforeUnmount(() => {
  if (checkInterval.value) {
    clearInterval(checkInterval.value);
  }

  // 移除事件監聽
  window.removeEventListener("bulletin-read", refreshNotifications);
});
</script>

<style scoped>
.notification-wrapper {
  position: relative;
}

.notification-trigger {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-trigger:hover {
  background: rgba(0, 0, 0, 0.05);
}

.notification-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon {
  color: #666;
  transition: all 0.3s ease;
}

.notification-icon.text-red {
  color: #f44336;
  animation: bell-shake 2s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
  transform-origin: top;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 18px;
  height: 18px;
  padding: 0 5px;
  border-radius: 9px;
  background: #f44336;
  color: white;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 現代化通知容器 */
.modern-notification-container {
  width: 100%;
  min-width: 240px;
  max-width: 280px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.notification-header {
  background: #f5f7fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  text-align: center;
}

.notification-counter {
  margin-top: 4px;
  color: #666;
  text-align: center;
}

.notification-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-list {
  background: white;
  overflow-x: hidden;
  padding-top: 8px;
}

.notification-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 4px;
  min-width: 0; /* 確保內容可以收縮 */
}

.notification-item:hover {
  background: #f5f7fa;
}

.notification-item.has-notification {
  background: rgba(244, 67, 54, 0.05);
}

.notification-item.has-notification:hover {
  background: rgba(244, 67, 54, 0.08);
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px; /* 確保圖標不會收縮 */
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f5f7fa;
  color: #666;
  margin: 0 12px 0 4px;
  transition: all 0.2s ease;
  flex-shrink: 0; /* 防止圖標收縮 */
}

.has-notification .item-icon {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.item-content {
  flex: 1;
  min-width: 0; /* 確保文字可以收縮 */
  overflow: hidden;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.item-subtitle {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 動畫效果 */
@keyframes bell-shake {
  0% {
    transform: rotate(0);
  }
  1% {
    transform: rotate(15deg);
  }
  3% {
    transform: rotate(-13deg);
  }
  5% {
    transform: rotate(11deg);
  }
  7% {
    transform: rotate(-9deg);
  }
  9% {
    transform: rotate(7deg);
  }
  11% {
    transform: rotate(-5deg);
  }
  13% {
    transform: rotate(3deg);
  }
  15% {
    transform: rotate(-2deg);
  }
  17% {
    transform: rotate(1deg);
  }
  19% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(0);
  }
}

/* 現代化對話框 */
.modern-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.notification-menu {
  margin-top: 8px;
}

/* 新增必讀公告對話框樣式 */
.notification-dialog {
  border-radius: 12px;
  max-width: 360px;
  width: 90%;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  height: 60px;
  background: linear-gradient(135deg, #ff4b2b 0%, #ff416c 100%);
  display: flex;
  justify-content: center;
  position: relative;
}

.alert-icon-container {
  width: 56px;
  height: 56px;
  background-color: #ff416c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: -28px;
  box-shadow: 0 4px 12px rgba(255, 65, 108, 0.3);
  border: 3px solid white;
  z-index: 1;
}

/* 確保對話框在移動設備上顯示良好 */
@media (max-width: 599px) {
  .notification-dialog {
    width: 95%;
  }
}
</style>
