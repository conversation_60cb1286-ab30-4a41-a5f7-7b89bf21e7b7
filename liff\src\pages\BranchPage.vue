<template>
  <q-page
    class="q-pa-sm flex justify-center"
    style="width: 100%; max-width: 800px; margin: 0 auto; position: relative"
  >
    <q-card class="q-pa-xs no-shadow" style="width: 100%; border-radius: 10px">
      <!-- 標題 -->
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">門市管理</div>
          <div>
            <q-btn
              flat
              round
              size="sm"
              color="green"
              @click="handleOpenBranchDialog"
            >
              <package :stroke-width="1" :size="26" />
              <q-tooltip>選擇門市</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              color="red"
              @click="openAddDialog"
              class="q-ml-md"
            >
              <package-plus :stroke-width="1" :size="26" />
              <q-tooltip>新增門市</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-mt-md custom-separator" />

      <!-- 選擇門市提示 -->
      <q-card-section v-if="!selectedBranch" class="q-pt-none">
        <q-banner class="bg-blue-1 text-grey-8" rounded>
          <template v-slot:avatar>
            <Info class="text-primary" :stroke-width="1" :size="30" />
          </template>
          請點擊右上角按鈕選擇要管理的門市
        </q-banner>
      </q-card-section>
    </q-card>

    <!-- 主內容區域 - 門市設定表單 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
      v-if="selectedBranch"
    >
      <!-- 基本資訊設定區塊 -->
      <q-card-section>
        <div class="text-primary q-mb-md flex items-center">
          <Store :stroke-width="1" :size="20" class="q-mr-sm" />
          門市基本資訊
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6">
            <q-input
              v-model="selectedBranch.Cod_cust"
              label="門市代號"
              readonly
              outlined
              dense
            >
              <template v-slot:prepend>
                <Hash :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-input
              v-model="selectedBranch.Cod_name"
              label="門市名稱"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Type :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12 col-sm-6">
            <q-select
              emit-value
              map-options
              v-model="selectedBranch.Cod_group"
              :options="brandsOptions"
              label="品牌"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Building :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>

          <div class="col-12 col-sm-6">
            <q-select
              emit-value
              map-options
              v-model="selectedBranch.Invest"
              :options="InvestOptions"
              label="類型"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Tag :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>

          <div class="col-12">
            <q-select
              emit-value
              map-options
              v-model="selectedBranch.District"
              :options="departments"
              label="所屬部門"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Users :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>

          <div class="col-12">
            <q-select
              emit-value
              map-options
              v-model="selectedBranch.Sts"
              :options="statusOptions"
              label="狀態"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Activity :stroke-width="1" :size="18" />
              </template>
            </q-select>
          </div>
        </div>

        <q-separator class="q-my-md" />

        <!-- 系統設定區塊 -->
        <div class="text-primary q-mb-md flex items-center q-px-none">
          <Settings :stroke-width="1" :size="20" class="q-mr-sm" />
          系統設定
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12">
            <div class="row items-center full-width">
              <q-input
                v-model="selectedBranch.Ip"
                label="POS 資料庫位置"
                outlined
                dense
                class="col-grow"
                disable
              >
                <template v-slot:prepend>
                  <Database :stroke-width="1" :size="18" />
                </template>
                <template v-slot:append>
                  <component
                    :is="connectionStatus === 'online' ? 'Wifi' : 'WifiOff'"
                    :class="
                      connectionStatus === 'online' ? 'text-green' : 'text-grey'
                    "
                    :stroke-width="1.5"
                    :size="28"
                  />
                </template>
              </q-input>
            </div>
          </div>

          <div class="col-12">
            <div class="row items-center full-width">
              <q-select
                emit-value
                map-options
                v-model="selectedTable"
                :options="tablesOptions"
                label="資料類別"
                outlined
                dense
                class="col-grow"
                :disable="connectionStatus !== 'online'"
              >
                <template v-slot:prepend>
                  <Table :stroke-width="1" :size="18" />
                </template>
              </q-select>

              <q-btn
                unelevated
                color="secondary"
                class="q-pr-sm q-ml-sm"
                @click="handleSyncTables"
                :disable="connectionStatus !== 'online' || !selectedTable"
                :loading="isSyncing"
              >
                <template v-slot:default>
                  <refresh-cw :stroke-width="1.5" :size="20" class="q-mr-xs" />
                  同步
                </template>
              </q-btn>
            </div>
          </div>

          <div class="col-12">
            <q-input
              v-model="selectedBranch.Rtime"
              label="POS 資料回收時間"
              outlined
              dense
              disable
            >
              <template v-slot:prepend>
                <Clock :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12">
            <q-input
              v-model="selectedBranch.AttendanceIP"
              label="差勤設備IP"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Timer :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>

          <div class="col-12">
            <q-input
              v-model="selectedBranch.Comment_Url"
              label="Google評論網址"
              outlined
              dense
            >
              <template v-slot:prepend>
                <Link :stroke-width="1" :size="18" />
              </template>
            </q-input>
          </div>
        </div>
      </q-card-section>

      <!-- 按鈕 -->
      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          flat
          label="重設"
          color="grey"
          class="q-mr-sm"
          @click="resetForm"
        />
        <q-btn
          outline
          unelevated
          color="secondary"
          class="q-px-md"
          @click="handleSaveBranch"
          :loading="isLoading"
        >
          <template v-slot:default>
            <Save :stroke-width="1" :size="18" class="q-mr-xs" />
            儲存
          </template>
        </q-btn>
      </q-card-actions>
    </q-card>

    <!-- 選擇門市 Dialog -->
    <q-dialog v-model="branchDialog">
      <q-card
        class="page-dialog-card"
        style="width: 100%; max-width: 420px; border-radius: 10px"
      >
        <div class="dialog-fixed-header">
          <!-- 標題 -->
          <q-card-section class="text-center page-dialog-title q-pb-none">
            <package
              :stroke-width="1"
              :size="24"
              class="text-primary q-mr-sm"
            />
            選擇門市
          </q-card-section>

          <!-- 搜尋與篩選 -->
          <q-card-section class="q-pt-none q-pb-xs">
            <div class="row items-center no-wrap">
              <q-input
                v-model="searchQuery"
                dense
                outlined
                placeholder="輸入名稱或代號"
                clearable
                class="col"
                @clear="resetSearch"
              >
                <template v-slot:prepend>
                  <search :stroke-width="1" :size="24" />
                </template>
              </q-input>
              <q-toggle
                v-model="filterActive"
                label="營業中"
                dense
                color="primary"
                class="q-ml-sm"
              />
            </div>
          </q-card-section>
        </div>

        <!-- 門市列表 -->
        <q-card-section
          class="page-dialog-content scroll"
          style="height: 400px; padding-top: 8px"
        >
          <q-list bordered separator v-if="filteredBranches.length">
            <q-item
              v-for="branch in filteredBranches"
              :key="branch.Cod_cust"
              clickable
              v-ripple
              @click="handleSelectBranch(branch)"
            >
              <q-item-section>
                <q-item-label class="text-weight-medium">
                  {{ branch.Cod_name }}
                </q-item-label>
                <q-item-label caption class="text-grey-8">
                  代號: {{ branch.Cod_cust }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-badge
                  :color="getStatusColor(branch.Sts)"
                  class="q-pa-xs"
                  outline
                >
                  {{ getStatusLabel(branch.Sts) }}
                </q-badge>
              </q-item-section>
            </q-item>
          </q-list>

          <!-- 無符合結果 -->
          <div v-else class="text-center text-grey-8 q-pa-md">
            <search class="w-6 h-6 q-mb-sm" />
            <div>沒有符合的門市</div>
          </div>
        </q-card-section>

        <!-- 按鈕區 -->
        <q-card-actions align="right" class="q-pa-sm page-dialog-actions">
          <q-btn label="取消" color="grey" flat @click="closeBranchDialog" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 新增門市 Dialog -->
    <q-dialog v-model="addPageDialog">
      <q-card style="width: 100%; max-width: 380px" class="rounded-borders">
        <!-- 標題 -->
        <q-card-section class="text-primary q-pa-md text-center">
          <div class="row items-center justify-center q-mb-sm">
            <Store class="w-6 h-6 text-primary" />
          </div>
          <div class="text-subtitle1 text-weight-medium">新增門市</div>
        </q-card-section>

        <!-- 內容區 -->
        <q-card-section class="q-pt-none">
          <q-input
            v-model="newBranchId"
            label="門市編號"
            outlined
            dense
            autofocus
            clearable
            class="full-width"
            :rules="[(val) => validateBranchId(val) || '請輸入6位數門市編號']"
          >
            <template v-slot:prepend>
              <Hash class="w-4 h-4 text-grey-7" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 按鈕區 -->
        <q-card-actions align="right" class="q-pa-sm">
          <q-btn label="取消" color="grey" flat @click="closeAddDialog" />
          <q-btn
            outline
            unelevated
            color="secondary"
            @click="handleConfirmAddBranch"
            :disable="!validateBranchId(newBranchId)"
          >
            <template v-slot:default>
              <Check class="q-mr-xs" />
              確認
            </template>
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useBranchManagement } from "../composables/useBranchManagement";
import { useBranchDialogs } from "../composables/useBranchDialogs";

// 使用 composables
const {
  // 響應式狀態
  branches,
  selectedBranch,
  departments,
  tablesOptions,
  connectionStatus,
  isLoading,

  // 選項資料
  statusOptions,
  brandsOptions,
  InvestOptions,

  // 工具函數
  getStatusColor,
  getStatusLabel,

  // API 函數
  fetchBranches,
  syncTables,
  saveBranch,
  selectBranch,
  createNewBranch,
  resetForm,
  initialize,
} = useBranchManagement();

const {
  // 響應式狀態
  branchDialog,
  addPageDialog,
  searchQuery,
  filterActive,
  newBranchId,

  // 計算屬性
  getFilteredBranches,

  // 對話框控制
  openBranchDialog,
  closeBranchDialog,
  openAddDialog,
  closeAddDialog,
  resetSearch,

  // 驗證
  validateBranchId,
} = useBranchDialogs();

// 本地狀態
const selectedTable = ref("");
const isSyncing = ref(false);

// 計算屬性
const filteredBranches = computed(() => getFilteredBranches(branches.value));

// 事件處理函數
const handleOpenBranchDialog = async () => {
  await fetchBranches();
  openBranchDialog();
};

const handleSelectBranch = async (branch) => {
  selectedTable.value = "";
  closeBranchDialog();
  await selectBranch(branch);
};

const handleSyncTables = async () => {
  if (!selectedTable.value || !selectedBranch.value) return;

  isSyncing.value = true;
  try {
    await syncTables(
      selectedTable.value,
      selectedBranch.value.Cod_cust,
      selectedBranch.value.Ip
    );
  } finally {
    isSyncing.value = false;
  }
};

const handleSaveBranch = async () => {
  const success = await saveBranch();
  if (success) {
    selectedTable.value = "";
  }
};

const handleConfirmAddBranch = async () => {
  if (!validateBranchId(newBranchId.value)) return;

  const success = createNewBranch(newBranchId.value);
  if (success) {
    closeAddDialog();
  }
};

// 生命週期
onMounted(async () => {
  await initialize();
});
</script>

<style>
.title-section {
  padding-bottom: 8px;
}

.custom-separator {
  height: 0.5px;
  margin-bottom: 16px;
}

.page-dialog-card {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-fixed-header {
  background: white;
  z-index: 1;
}

.page-dialog-title {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
}

.page-dialog-content {
  padding-top: 0;
}

.page-dialog-content.scroll {
  overflow-y: auto;
}

.page-dialog-actions {
  background-color: #f5f5f5;
}

/* 修正 q-banner 中 icon 的垂直置中問題 */
.q-banner__avatar {
  align-self: center !important;
}
</style>
