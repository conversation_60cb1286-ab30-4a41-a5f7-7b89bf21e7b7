const mysql = require("mysql2/promise");
const mysqlPool = require("../config/mysqldb");
const sql = require("mssql");
const dbConfig = require("../config/db");

// 獲取出勤紀錄
const getAttendanceRecords = async (req, res) => {
  const { sn, year_month } = req.query;

  if (!sn || !year_month) {
    return res.status(400).json({ message: "缺少必要參數" });
  }

  try {
    // 從 year_month 解析年月
    const [year, month] = year_month.split("-");

    // 查詢用戶的人資內部編號
    const [userRows] = await mysqlPool.execute(
      "SELECT id FROM users WHERE sn = ?",
      [sn]
    );

    if (userRows.length === 0) {
      return res.status(404).json({ message: "找不到該員工" });
    }

    const userId = userRows[0].id;

    // 查詢該用戶在指定年月的出勤紀錄
    // 直接返回原始時間欄位，讓前端處理格式化
    const [records] = await mysqlPool.execute(
      `SELECT 
        att_date, 
        first_in,
        first_out,
        second_in,
        second_out,
        late_time, 
        first_in_f, 
        first_out_f, 
        second_in_f, 
        second_out_f 
      FROM att_records 
      WHERE user_id = ? 
      AND YEAR(att_date) = ? 
      AND MONTH(att_date) = ? 
      ORDER BY att_date ASC`,
      [userId, year, month]
    );

    // 添加調試資訊
    // console.log("查詢到的出勤紀錄:", records.length, "筆");
    // if (records.length > 0) {
    //   console.log("第一筆紀錄範例:", records[0]);
    // }

    res.json(records);
  } catch (error) {
    console.error("獲取出勤紀錄失敗:", error);
    res.status(500).json({ message: "獲取出勤紀錄失敗", error: error.message });
  }
};

// 獲取用戶資訊
const getUserInfo = async (req, res) => {
  const { sn } = req.query;

  if (!sn) {
    return res.status(400).json({ message: "缺少員工編號參數" });
  }

  try {
    // 查詢用戶資訊
    const [userRows] = await mysqlPool.execute(
      "SELECT id, sn, name FROM users WHERE sn = ?",
      [sn]
    );

    if (userRows.length === 0) {
      return res.status(404).json({ message: "找不到該員工" });
    }

    res.json(userRows[0]);
  } catch (error) {
    console.error("獲取用戶資訊失敗:", error);
    res.status(500).json({ message: "獲取用戶資訊失敗", error: error.message });
  }
};

// 獲取門市資料
const getBranchs = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool.request().query(`
      SELECT 
        Cod_cust AS Cod_Cust, 
        Cod_name AS Cod_Name, 
        Cod_group, 
        Invest, 
        District, 
        Sts, 
        Ip, 
        Rtime, 
        AttendanceIP, 
        Comment_Url, 
        Comment_Rating, 
        Comment_Count, 
        Comment_At, 
        AttendanceSync
      FROM Branch 
      WHERE Cod_group<>'A' AND Sts='1' AND AttendanceIP IS NOT NULL AND AttendanceIP <> ''
    `);

    // 確保返回的是陣列
    res.json(result.recordset);
  } catch (error) {
    console.error("獲取門市資料失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法獲取門市資料" });
  }
};

module.exports = {
  getAttendanceRecords,
  getUserInfo,
  getBranchs,
};
