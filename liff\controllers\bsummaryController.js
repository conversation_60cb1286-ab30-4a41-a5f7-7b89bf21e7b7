const sql = require("mssql");
const { DateTime } = require("luxon");
require("dotenv").config();

const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");
  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

const getDailyReport = async (req, res) => {
  const { cod_cust, date } = req.query;
  const branch = cod_cust;

  if (!branch || !date) {
    return res.status(400).json({ error: "❌ 缺少 cod_cust 或 date 參數" });
  }
  const formattedDate = DateTime.fromISO(date).toFormat("yyyy/MM/dd"); // ✅ 轉換格式
  try {
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();
    request.input("date", sql.NVarChar, formattedDate); // ndate 是 nvarchar

    const result = await request.batch(`
      -- 現金與信用卡
      SELECT 
        ISNULL(SUM(cntot), 0) AS cash,
        ISNULL(SUM(cdtot), 0) AS credit,
        ISNULL(SUM(chtot), 0) AS chtot,
        ISNULL(SUM(cntot), 0)+ISNULL(SUM(cdtot), 0)+ISNULL(SUM(chtot), 0) AS summary 
      FROM OrderNow
      WHERE checkout = 1 AND ndate = @date;

      -- 其他支付
      SELECT name, SUM(amt) AS total
      FROM OrderOtherPay
      WHERE name <> ''
        AND code IN (
          SELECT code FROM OrderNow
          WHERE checkout = 1 AND ndate = @date
        )
      GROUP BY name;

      -- 禮券售出 N1 < 0
      SELECT ISNULL(ABS(SUM(RMName.N1 * OrderMainN.QTY)), 0) AS couponSold
      FROM OrderMainN
      JOIN OrderNow ON OrderNow.CODE = OrderMainN.SCODE
      JOIN OMenuMDetail ON OrderMainN.CODE = OMenuMDetail.CODE
      JOIN RMName ON OMenuMDetail.RCODE = RMName.CODE
      WHERE OrderNow.checkout = 1 AND OrderNow.ndate = @date AND RMName.N1 < 0;

      -- 禮券回收 N1 > 0 總金額
     SELECT ISNULL(ABS(SUM(RMName.N1 * OrderMainN.QTY)), 0) AS couponUsed
     FROM OrderMainN
     JOIN OrderNow ON OrderNow.CODE = OrderMainN.SCODE
     JOIN OMenuMDetail ON OrderMainN.CODE = OMenuMDetail.CODE
     JOIN RMName ON OMenuMDetail.RCODE = RMName.CODE
     WHERE OrderNow.checkout = 1 AND OrderNow.ndate = @date AND RMName.N1 > 0;

      -- 禮券回收明細
     SELECT rmname.NAME AS name,ISNULL(ABS(SUM(RMName.N1 * OrderMainN.QTY)), 0) AS amount
     FROM OrderMainN
     LEFT JOIN OrderNow ON OrderNow.CODE = OrderMainN.SCODE
     LEFT JOIN OMenuMDetail ON OrderMainN.CODE = OMenuMDetail.CODE
     LEFT JOIN RMName ON OMenuMDetail.RCODE = RMName.CODE
     WHERE OrderNow.checkout = '1' AND CAST(OrderNow.NDATE AS DATE) = @date
     AND OMenuMDetail.RCODE <> ''
     AND RMName.N1 > 0
     GROUP BY rmname.NAME;

      -- 未稅業績
      SELECT ISNULL(SUM(amount), 0) - ISNULL(SUM(Ptot1), 0) - ISNULL(SUM(DAMT1), 0) AS realTotal
      FROM OrderNow
      WHERE checkout = 1 AND ndate = @date;

      -- 發票金額minvo
      SELECT
        ISNULL(SUM(CASE WHEN chk = 0 THEN amt + discount ELSE 0 END), 0) AS invoiceValid,
        ISNULL(SUM(CASE WHEN chk = 1 THEN amt + discount ELSE 0 END), 0) AS invoiceVoided
      FROM minvo
      WHERE code IN (SELECT code FROM OrderNow WHERE  ndate = @date);

      -- 來客數
      SELECT ISNULL(SUM(isnull(person,0) + isnull(boy,0)), 0) AS totalGuests
      FROM OrderNow
      WHERE checkout = 1 AND ndate = @date;

      -- 📌 改為使用 timeseg 表格對應時段來客數
      SELECT 
      timeseg.name AS time,
      ISNULL(SUM(ISNULL(OrderNow.person, 0) + ISNULL(OrderNow.boy, 0)), 0) AS count,
      timeseg.stime,
      timeseg.etime
      FROM OrderNow
      LEFT JOIN timeseg ON timeseg.code = OrderNow.ts
      WHERE OrderNow.checkout = 1 AND OrderNow.ndate = @date
      GROUP BY timeseg.name, timeseg.stime, timeseg.etime
      ORDER BY timeseg.stime;

      -- 作廢發票明細
      SELECT
      ino,                                -- 發票號碼
      amt + discount AS amount            -- 金額（含折扣）
      FROM minvo
      WHERE chk = 1                         -- chk = 1 表示作廢
      AND code IN (
      SELECT code
      FROM OrderNow
      WHERE ndate = @date
  );

      -- 🔸賣禮券未開發票及貴賓券（統計名稱與數量）
      SELECT 
      RMName.NAME AS name,
      SUM(OrderMainN.QTY) AS qty
      FROM OrderMainN
      JOIN OrderNow ON OrderNow.CODE = OrderMainN.SCODE
      JOIN OMenuMDetail ON OrderMainN.CODE = OMenuMDetail.CODE
      JOIN RMName ON OMenuMDetail.RCODE = RMName.CODE
      WHERE 
      OrderNow.checkout = 1 
      AND OrderNow.ndate = @date 
      AND RMName.N1 = 0
      GROUP BY RMName.NAME;
    `);

    const [
      paymentRes,
      otherPayRes,
      couponSoldRes,
      couponUsedRes, // <--- ✅ 正確位置
      couponUsedDetailRes, // <--- ✅ 明細在後
      realTotalRes,
      invoiceRes,
      guestRes,
      guestByTimeRes,
      voidInvoiceListRes, // ✅ 作廢發票明細
      unbilledCouponsRes,
    ] = result.recordsets;

    const couponUsedDetails = couponUsedDetailRes || [];
    const cash = paymentRes[0]?.cash || 0;
    const credit = paymentRes[0]?.credit || 0;
    const otherpay = paymentRes[0]?.chtot || 0;
    const summary = paymentRes[0]?.summary || 0;
    const otherPayments = otherPayRes || [];
    const couponSold = couponSoldRes[0]?.couponSold || 0;
    const couponUsed = couponUsedRes[0]?.couponUsed || 0; // ✅ 修正這裡
    const realTotalTax =
      (realTotalRes[0]?.realTotal || 0) + couponUsed - couponSold;
    const realTotal = Math.round(realTotalTax / 1.05);
    const invoiceValid = invoiceRes[0]?.invoiceValid || 0;
    const invoiceVoided = invoiceRes[0]?.invoiceVoided || 0;
    const totalGuests = guestRes[0]?.totalGuests || 0;
    const guestByTime = guestByTimeRes || [];
    const voidInvoiceList = voidInvoiceListRes || [];
    const unbilledCoupons =
      unbilledCouponsRes?.map((row) => ({
        name: row.name,
        qty: row.qty,
      })) || [];

    res.json({
      cash,
      credit,
      otherpay,
      summary,
      otherPayments,
      couponSold,
      couponUsed,
      couponUsedDetails,
      realTotalTax,
      realTotal,
      invoiceValid,
      invoiceVoided,
      totalGuests,
      guestByTime,
      voidInvoiceList,
      unbilledCoupons,
    });
  } catch (err) {
    console.error("❌ getDailyReport 錯誤:", err);
    res.status(500).json({ error: "伺服器錯誤" });
  }
};

module.exports = {
  getDailyReport,
};
