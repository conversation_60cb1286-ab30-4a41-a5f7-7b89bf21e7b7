const sql = require("mssql");
const dbConfig = require("../config/db");

const getFormsPage = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    // 查詢所有頁面資料
    const pagesResult = await pool
      .request()
      .query(
        "SELECT Page_id, Name, Source, Icon, Color, Parent_id, Sort, Slevel FROM Pages ORDER BY sort"
      );

    // 查詢所有頁面權限
    const accessResult = await pool
      .request()
      .query("SELECT Page_id, Access FROM Pages_Access");

    // 將權限資料整合到頁面資料中
    const pages = pagesResult.recordset.map((page) => {
      // 找出此頁面的所有權限
      const accessRecords = accessResult.recordset.filter(
        (access) => access.Page_id.trim() === page.Page_id.trim()
      );

      // 直接使用陣列形式的權限
      const allowGroups = accessRecords.map((record) => record.Access.trim());

      return {
        ...page,
        Allow: allowGroups,
      };
    });

    res.json(pages);
  } catch (err) {
    console.error("Error:", err);
    res.status(500).json({ message: "取得頁面資料失敗" });
  }
};

const getFormsDetail = async (req, res) => {
  const { form_type } = req.query;

  if (!form_type) {
    return res.status(400).json({ message: "缺少表單類型參數 (form_type)" });
  }

  try {
    await sql.connect(dbConfig);
    const request = new sql.Request();
    request.input("form_type", sql.VarChar, form_type);
    const result = await request.query(`
      SELECT Field_name, Display_name 
      FROM Forms_detail 
      WHERE Form_type = @form_type
    `);

    res.json(result.recordset);
  } catch (err) {
    console.error("SQL 錯誤:", err);
    res.status(500).json({ message: "伺服器錯誤" });
  }
};

const getFormsField = async (req, res) => {
  const { table } = req.query;

  if (!table) {
    return res.status(400).json({ message: "缺少表單表格名稱參數 (table)" });
  }

  try {
    await sql.connect(dbConfig);
    const request = new sql.Request();

    // 查詢資料表的所有欄位名稱
    const query = `
      SELECT COLUMN_NAME AS Field_name
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = @table
    `;

    request.input("table", sql.VarChar, table);
    const result = await request.query(query);

    res.json(result.recordset);
  } catch (err) {
    console.error("SQL 錯誤:", err);
    res.status(500).json({ message: "伺服器錯誤", error: err.message });
  }
};

const saveFormMapping = async (req, res) => {
  const { form_type, fields } = req.body;

  //console.log("收到的請求資料:", req.body); // ✅ 確保收到正確的資料

  try {
    await sql.connect(dbConfig);

    // 先刪除舊的表單欄位對應
    const deleteRequest = new sql.Request();
    deleteRequest.input("form_type", sql.VarChar, form_type);
    await deleteRequest.query(`
      DELETE FROM Forms_detail WHERE Form_type = @form_type
    `);

    // 逐筆插入新資料
    for (const field of fields) {
      const insertRequest = new sql.Request();
      insertRequest.input("form_type", sql.VarChar, form_type);
      insertRequest.input("field_name", sql.VarChar, field.field_name);
      insertRequest.input("display_name", sql.NVarChar, field.display_name);

      await insertRequest.query(`
        INSERT INTO Forms_detail (Form_type, Field_name, Display_name) 
        VALUES (@form_type, @field_name, @display_name)
      `);
    }

    res.json({ success: true, message: "表單對應儲存成功" });
  } catch (err) {
    console.error("SQL 錯誤:", err);
    res.status(500).json({ success: false, message: "伺服器錯誤" });
  }
};

const savePermissions = async (req, res) => {
  const {
    page_id,
    page_name,
    page_source,
    color,
    icon,
    parent_id,
    sort,
    slevel,
    allow_groups,
  } = req.body;

  try {
    const pool = await sql.connect(dbConfig);
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // 檢查頁面是否存在
      const checkRequest = new sql.Request(transaction);
      checkRequest.input("page_id", sql.VarChar, page_id);
      const checkResult = await checkRequest.query(
        "SELECT Page_id FROM Pages WHERE Page_id = @page_id"
      );

      if (checkResult.recordset.length === 0) {
        // 新增頁面
        const insertRequest = new sql.Request(transaction);
        insertRequest.input("page_id", sql.VarChar, page_id);
        insertRequest.input("page_name", sql.VarChar, page_name);
        insertRequest.input("page_source", sql.VarChar, page_source);
        insertRequest.input("icon", sql.VarChar, icon || null);
        insertRequest.input("color", sql.VarChar, color || null);
        insertRequest.input("parent_id", sql.VarChar, parent_id || null);
        insertRequest.input("sort", sql.Int, parseInt(sort) || 0);
        insertRequest.input("slevel", sql.Int, parseInt(slevel) || 1);

        await insertRequest.query(`
          INSERT INTO Pages (Page_id, Name, Source, Icon, Color, Parent_id, Sort, Slevel)
          VALUES (@page_id, @page_name, @page_source, @icon, @color, @parent_id, @sort, @slevel)
        `);

        // 新增權限資料
        if (allow_groups) {
          // 處理陣列或字串格式的權限
          const groups = Array.isArray(allow_groups)
            ? allow_groups
            : allow_groups.trim() !== ""
            ? allow_groups.split(",")
            : [];

          for (const group of groups) {
            if (
              group &&
              (typeof group === "string" ? group.trim() !== "" : true)
            ) {
              const accessRequest = new sql.Request(transaction);
              accessRequest.input("page_id", sql.VarChar, page_id);
              accessRequest.input(
                "access",
                sql.VarChar,
                typeof group === "string" ? group.trim() : group
              );
              await accessRequest.query(`
                  INSERT INTO Pages_Access (Page_id, Access)
                  VALUES (@page_id, @access)
                `);
            }
          }
        }

        await transaction.commit();
        res.json({
          success: true,
          message: "新增頁面成功",
          page: {
            Page_id: page_id,
            Name: page_name,
            Source: page_source,
            Allow: allow_groups,
            Icon: icon,
            Color: color,
            Parent_id: parent_id,
            Sort: sort,
            Slevel: slevel,
          },
        });
      } else {
        // 更新頁面
        const updateRequest = new sql.Request(transaction);
        updateRequest.input("page_id", sql.VarChar, page_id);
        updateRequest.input("page_name", sql.VarChar, page_name);
        updateRequest.input("page_source", sql.VarChar, page_source);
        updateRequest.input("icon", sql.VarChar, icon || null);
        updateRequest.input("color", sql.VarChar, color || null);
        updateRequest.input("parent_id", sql.VarChar, parent_id || null);
        updateRequest.input("sort", sql.Int, parseInt(sort) || 0);
        updateRequest.input("slevel", sql.Int, parseInt(slevel) || 1);

        await updateRequest.query(`
          UPDATE Pages
          SET Name = @page_name,
              Source = @page_source,
              Icon = @icon,
              Color = @color,
              Parent_id = @parent_id,
              Sort = @sort,
              Slevel = @slevel
          WHERE Page_id = @page_id
        `);

        // 刪除現有權限
        const deleteRequest = new sql.Request(transaction);
        deleteRequest.input("page_id", sql.VarChar, page_id);
        await deleteRequest.query(`
          DELETE FROM Pages_Access WHERE Page_id = @page_id
        `);

        // 新增權限資料
        if (allow_groups) {
          // 處理陣列或字串格式的權限
          const groups = Array.isArray(allow_groups)
            ? allow_groups
            : allow_groups.trim() !== ""
            ? allow_groups.split(",")
            : [];

          for (const group of groups) {
            if (
              group &&
              (typeof group === "string" ? group.trim() !== "" : true)
            ) {
              const accessRequest = new sql.Request(transaction);
              accessRequest.input("page_id", sql.VarChar, page_id);
              accessRequest.input(
                "access",
                sql.VarChar,
                typeof group === "string" ? group.trim() : group
              );
              await accessRequest.query(`
                  INSERT INTO Pages_Access (Page_id, Access)
                  VALUES (@page_id, @access)
                `);
            }
          }
        }

        await transaction.commit();
        res.json({
          success: true,
          message: "更新頁面成功",
          page: {
            Page_id: page_id,
            Name: page_name,
            Source: page_source,
            Allow: allow_groups,
            Icon: icon,
            Color: color,
            Parent_id: parent_id,
            Sort: sort,
            Slevel: slevel,
          },
        });
      }
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  } catch (err) {
    console.error("Error:", err);
    res.status(500).json({
      success: false,
      message: "儲存權限設定失敗",
      error: err.message,
    });
  }
};

module.exports = {
  getFormsPage,
  getFormsDetail,
  getFormsField,
  saveFormMapping,
  savePermissions,
};
