import HomePage from "../pages/HomePage.vue";
import LoginPage from "../pages/LoginPage.vue";
import LINEPage from "../pages/LINELoginPage.vue";
import Thanks from "../pages/ThanksPage.vue";

const routes = [
  // {
  //   path: "/",
  //   component: () => import("layouts/MainLayout.vue"),
  //   children: [{ path: "", component: () => import("pages/IndexPage.vue") }],
  // },

  {
    path: "/home",
    component: HomePage,
  },
  {
    path: "/",
    component: LoginPage,
  },
  {
    path: "/linelogin",
    component: LINEPage,
  },
  {
    path: "/thanks",
    component: Thanks,
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: "/:catchAll(.*)*",
    component: () => import("pages/ErrorNotFound.vue"),
  },
];

export default routes;
