<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題卡片 -->
    <q-card class="no-shadow" style="border-radius: 12px; overflow: hidden">
      <q-card-section class="title-section q-py-xs">
        <div class="row items-center justify-between">
          <div class="text-subtitle1 text-primary">薪資單</div>
          <div class="row items-center">
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              @click="showDetailDialog = true"
              class="q-mr-xs"
            >
              <info :stroke-width="1" :size="20" />
            </q-btn>
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_left"
              @click="adjustMonth(-1)"
              color="primary"
            />
            <q-input
              v-model="selectedMonth"
              readonly
              dense
              outlined
              class="month-selector"
              input-class="text-center"
            >
            </q-input>
            <q-btn
              flat
              round
              size="sm"
              icon="chevron_right"
              @click="adjustMonth(1)"
              color="primary"
              :disable="!canGoToNextMonth"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 主內容 -->
    <q-card
      class="no-shadow q-mt-sm"
      style="border-radius: 12px; overflow: hidden"
    >
      <!-- 載入中 -->
      <div v-if="loading" class="q-pa-xl">
        <div class="fancy-loader">
          <svg class="loader-svg" viewBox="0 0 100 100">
            <circle class="loader-circle" cx="50" cy="50" r="40" />
          </svg>
          <div class="loader-message">薪資資料載入中...</div>
        </div>
      </div>
      <!-- 查無資料 -->
      <q-card-section
        v-else-if="!payslip || !payslip.details || !payslip.details.length"
        class="empty-state-container"
      >
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path
              d="M13,9h-2v2H9v2h2v2h2v-2h2v-2h-2V9z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20 M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z"
            />
          </svg>
          <div class="empty-text">查無薪資資料</div>
          <div class="empty-subtext">請選擇其他年月或聯絡人資</div>
        </div>
      </q-card-section>
      <!-- 薪資內容 -->
      <div v-else>
        <!-- 基本資訊卡片 -->
        <q-card-section class="q-pt-md q-pb-sm">
          <div class="row q-col-gutter-sm info-cards-container">
            <div class="col-4">
              <div class="stats-card person-card">
                <div class="stats-icon">
                  <user-round :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">姓名</div>
                  <div class="person-value">
                    {{ payslip.user.name }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-4">
              <div class="stats-card month-card">
                <div class="stats-icon">
                  <calendar-clock :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">結算月份</div>
                  <div>
                    {{ selectedMonth }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-4">
              <div class="stats-card salary-card">
                <div class="stats-icon">
                  <circle-dollar-sign :stroke-width="1" :size="20" />
                </div>
                <div class="stats-content">
                  <div class="stats-label">實領薪資</div>
                  <div class="salary-value">
                    ${{ netSalary.toLocaleString() }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <q-separator inset />

        <!-- 薪資明細 -->
        <q-card-section>
          <div class="text-primary q-mb-md flex items-center justify-between">
            <div class="flex items-center">
              <scroll-text :stroke-width="1" :size="20" class="q-mr-sm" />
              薪資明細
            </div>
          </div>

          <!-- 加項明細 -->
          <div class="leave-stats-card q-mb-lg">
            <div class="leave-stats-header special-leave">
              <div class="leave-stats-title">
                <circle-plus :stroke-width="1.5" :size="20" class="q-mr-sm" />
                薪資加項
              </div>
              <q-badge color="primary" class="leave-stats-badge">
                ${{ totalPlus.toLocaleString() }}
              </q-badge>
            </div>
            <div class="leave-stats-content">
              <div class="leave-stats-grid">
                <div
                  v-for="item in plusItems"
                  :key="item.code"
                  class="leave-stats-item"
                >
                  <div class="leave-stats-label">{{ item.name }}</div>
                  <div class="leave-stats-value positive">
                    ${{ Number(item.v01).toLocaleString() }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 減項明細 -->
          <div class="leave-stats-card">
            <div class="leave-stats-header overtime-leave">
              <div class="leave-stats-title">
                <circle-minus :stroke-width="1.5" :size="20" class="q-mr-sm" />
                薪資減項
              </div>
              <q-badge class="leave-stats-badge bg-teal">
                ${{ totalMinus.toLocaleString() }}
              </q-badge>
            </div>
            <div class="leave-stats-content">
              <div class="leave-stats-grid">
                <div
                  v-for="item in minusItems"
                  :key="item.code"
                  class="leave-stats-item"
                >
                  <div class="leave-stats-label">{{ item.name }}</div>
                  <div class="leave-stats-value negative">
                    ${{ Number(item.v01).toLocaleString() }}
                  </div>
                </div>
              </div>
              <div class="leave-stats-footer">
                <div class="leave-stats-total-label">實領薪資</div>
                <div class="leave-stats-total-value positive">
                  ${{ netSalary.toLocaleString() }}
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </div>
    </q-card>

    <!-- 詳細說明對話框 -->
    <q-dialog v-model="showDetailDialog">
      <q-card style="min-width: 350px; max-width: 700px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">薪資說明</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-separator />
        <q-card-section>
          <p>薪資單顯示您當月的薪資明細，包含各項加項與減項。</p>
          <p>如有任何疑問，請聯絡人資部門。</p>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="關閉" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { DateTime } from "luxon";
import apiClient from "../api";
import { useQuasar } from "quasar";

const props = defineProps({ Userid: String });
const $q = useQuasar();

const loading = ref(false);
const payslip = ref(null);
const selectedMonth = ref(DateTime.now().toFormat("yyyy-MM"));
const lastSettlementMonth = ref(null);
const showDetailDialog = ref(false);

const fetchLastSettlementMonth = async () => {
  try {
    const { data } = await apiClient.get(`/payslip/last-settlement-month`);
    lastSettlementMonth.value = data.lastSettlementMonth.trim();
    console.log(data.lastSettlementMonth);
    // 檢查回傳的月份是否有效
    if (
      data.lastSettlementMonth &&
      DateTime.fromFormat(data.lastSettlementMonth.trim(), "yyyy-MM").isValid
    ) {
      selectedMonth.value = data.lastSettlementMonth.trim();
    } else {
      // 如果無效，使用當前月份
      selectedMonth.value = DateTime.now().toFormat("yyyy-MM");
    }
  } catch (e) {
    console.error("無法獲取最後結算月份:", e);
    // 如果無法獲取，使用當前月份
    selectedMonth.value = DateTime.now().toFormat("yyyy-MM");
  }
};

const fetchPayslip = async () => {
  loading.value = true;
  try {
    // 確保使用有效的日期格式
    let yearMonth = selectedMonth.value;
    if (yearMonth) {
      yearMonth = yearMonth.trim();
    }

    if (
      !yearMonth ||
      yearMonth === "Invalid DateTime" ||
      !DateTime.fromFormat(yearMonth, "yyyy-MM").isValid
    ) {
      yearMonth = DateTime.now().toFormat("yyyy-MM");
      selectedMonth.value = yearMonth; // 更新為有效的日期
    }

    const { data } = await apiClient.get(`/payslip/payslip`, {
      params: {
        user_id: props.Userid,
        year_month: yearMonth,
      },
    });
    payslip.value = data;
  } catch (e) {
    payslip.value = null;
    $q.notify({
      type: "negative",
      message: "獲取薪資資料失敗",
      position: "top",
      timeout: 3000,
    });
  } finally {
    loading.value = false;
  }
};

const adjustMonth = (offset) => {
  try {
    // 確保 selectedMonth 是有效的日期格式
    let currentDate;
    let monthValue = selectedMonth.value;

    if (monthValue) {
      monthValue = monthValue.trim();
    }

    if (!monthValue || monthValue === "Invalid DateTime") {
      currentDate = DateTime.now();
    } else {
      currentDate = DateTime.fromFormat(monthValue, "yyyy-MM");
      if (!currentDate.isValid) {
        currentDate = DateTime.now();
      }
    }

    const newMonth = currentDate.plus({ months: offset }).toFormat("yyyy-MM");

    // 檢查是否超過最後結算月份
    if (lastSettlementMonth.value && offset > 0) {
      const lastMonth = DateTime.fromFormat(
        lastSettlementMonth.value.trim(),
        "yyyy-MM"
      );
      const targetMonth = DateTime.fromFormat(newMonth.trim(), "yyyy-MM");

      if (targetMonth > lastMonth) {
        return; // 不允許超過最後結算月份
      }
    }

    selectedMonth.value = newMonth;
  } catch (error) {
    console.error("日期調整錯誤:", error);
    selectedMonth.value = DateTime.now().toFormat("yyyy-MM");
  }
};

// 檢查是否可以切換到下個月
const canGoToNextMonth = computed(() => {
  if (!lastSettlementMonth.value) return true;

  const currentMonth = DateTime.fromFormat(selectedMonth.value, "yyyy-MM");
  const lastMonth = DateTime.fromFormat(lastSettlementMonth.value, "yyyy-MM");

  return currentMonth < lastMonth;
});

const totalPlus = computed(() => {
  if (!payslip.value || !payslip.value.details) return 0;
  return payslip.value.details
    .filter((item) => item.plus === 1)
    .reduce((sum, item) => sum + Number(item.v01 || 0), 0);
});

const totalMinus = computed(() => {
  if (!payslip.value || !payslip.value.details) return 0;
  return payslip.value.details
    .filter((item) => item.plus === 0)
    .reduce((sum, item) => sum + Number(item.v01 || 0), 0);
});

const netSalary = computed(() => totalPlus.value - totalMinus.value);

const plusItems = computed(() => {
  if (!payslip.value || !payslip.value.details) return [];
  return payslip.value.details.filter(
    (item) => item.plus === 1 && Number(item.v01) !== 0
  );
});

const minusItems = computed(() => {
  if (!payslip.value || !payslip.value.details) return [];
  return payslip.value.details.filter(
    (item) => item.plus === 0 && Number(item.v01) !== 0
  );
});

onMounted(async () => {
  await fetchLastSettlementMonth();
  await fetchPayslip();
});

watch([selectedMonth, () => props.Userid], fetchPayslip);
</script>

<style>
.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

.month-selector {
  min-width: 110px;
}

@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .month-selector {
    min-width: 90px;
    width: 90px !important;
  }
}

/* 載入動畫樣式 */
.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 283;
  stroke-linecap: round;
  transform-origin: 50% 50%;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 283;
  }
  50% {
    stroke-dashoffset: 70;
  }
  100% {
    stroke-dashoffset: 283;
  }
}

.loader-message {
  margin-top: 1rem;
  color: #1976d2;
  font-size: 0.9rem;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  fill: #bdbdbd;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #757575;
  margin-bottom: 0.5rem;
}

.empty-subtext {
  font-size: 0.9rem;
  color: #9e9e9e;
}

/* 基本資訊卡片樣式優化 */
.info-cards-container {
  margin: 0 -6px;
}

@media (max-width: 599px) {
  .info-cards-container {
    margin: 0 -2px;
  }

  .info-cards-container > div {
    padding: 0 2px;
  }

  /* 響應式調整 */
  .stats-card {
    padding: 8px 6px 8px 20px;
  }

  .stats-icon {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  .stats-label {
    font-size: 10px;
  }

  .person-card .person-value,
  .month-card .month-value,
  .salary-card .salary-value,
  .stats-card div:not(.stats-label) {
    font-size: 12px;
  }
}

/* 薪資明細卡片樣式 */
.leave-stats-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: white;
  margin-bottom: 16px;
}

.leave-stats-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.leave-stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.leave-stats-header.special-leave {
  background: linear-gradient(135deg, #1976d2, #64b5f6);
  color: white;
}

.leave-stats-header.overtime-leave {
  background: linear-gradient(135deg, #f44336, #ef9a9a);
  color: white;
}

.leave-stats-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 1rem;
}

.leave-stats-badge {
  font-size: 1.1rem;
  padding: 4px 12px;
}

.leave-stats-content {
  padding: 16px;
}

.leave-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.leave-stats-item {
  padding: 12px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.2s ease;
}

.leave-stats-item:hover {
  background-color: #e3f2fd;
  transform: translateY(-2px);
}

.leave-stats-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 4px;
}

.leave-stats-value {
  font-size: 1.1rem;
  font-weight: 500;
}

.leave-stats-value.positive {
  color: #1976d2;
}

.leave-stats-value.negative {
  color: #f44336;
}

.leave-stats-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.leave-stats-total-label {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.leave-stats-total-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.leave-stats-total-value.positive {
  color: #4caf50;
}

/* 統計卡片樣式 */
.stats-card {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eaeaea;
  transition: all 0.3s ease;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 6px;
}

.stats-content {
  flex: 1;
  min-width: 0; /* 確保文字可以正確縮減 */
}

.stats-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stats-value {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.person-value {
  color: #8e24aa;
  font-weight: 600;
}

.month-value {
  color: #ff8f00;
  font-weight: 600;
}

.salary-value {
  color: #388e3c;
  font-weight: 600;
}

/* 卡片特定樣式 */
.person-card::before {
  background: linear-gradient(180deg, #ce93d8 0%, #8e24aa 100%);
}

.person-card .stats-icon {
  color: #8e24aa;
  background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
}

.month-card::before {
  background: linear-gradient(180deg, #ffcc80 0%, #ff8f00 100%);
}

.month-card .stats-icon {
  color: #ff8f00;
  background: linear-gradient(135deg, #fff8e1 0%, #ffe082 100%);
}

.salary-card::before {
  background: linear-gradient(180deg, #a5d6a7 0%, #388e3c 100%);
}

.salary-card .stats-icon {
  color: #388e3c;
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}

/* 響應式調整 */
@media (max-width: 599px) {
  .leave-stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
