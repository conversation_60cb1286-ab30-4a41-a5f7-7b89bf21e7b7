<template>
  <q-item
    clickable
    tag="a"
    :href="props.link"
    :target="props.external ? '_blank' : undefined"
    :class="[
      'essential-link',
      props.active ? 'essential-link--active' : '',
      props.dense ? 'q-py-xs' : 'q-py-sm',
    ]"
    v-ripple
  >
    <q-item-section
      v-if="props.icon"
      avatar
      :class="props.iconColor ? `text-${props.iconColor}` : ''"
    >
      <q-icon :name="props.icon" :size="props.iconSize" />
    </q-item-section>

    <q-item-section>
      <q-item-label class="essential-link__title">
        {{ props.title }}
        <q-badge
          v-if="props.badge"
          :color="props.badgeColor || 'negative'"
          :label="props.badge"
          class="q-ml-sm"
        />
      </q-item-label>
      <q-item-label
        v-if="props.caption"
        caption
        :lines="props.captionLines || 1"
      >
        {{ props.caption }}
      </q-item-label>
    </q-item-section>

    <q-item-section v-if="props.rightIcon || $slots.right" side>
      <slot name="right">
        <q-icon v-if="props.rightIcon" :name="props.rightIcon" size="16px" />
      </slot>
    </q-item-section>
  </q-item>
</template>

<script setup>
defineOptions({
  name: "EssentialLink",
});

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  caption: {
    type: String,
    default: "",
  },
  link: {
    type: String,
    default: "#",
  },
  icon: {
    type: String,
    default: "",
  },
  iconSize: {
    type: String,
    default: "md",
  },
  iconColor: {
    type: String,
    default: "",
  },
  rightIcon: {
    type: String,
    default: "",
  },
  badge: {
    type: [String, Number],
    default: "",
  },
  badgeColor: {
    type: String,
    default: "negative",
  },
  active: {
    type: Boolean,
    default: false,
  },
  external: {
    type: Boolean,
    default: false,
  },
  dense: {
    type: Boolean,
    default: false,
  },
  captionLines: {
    type: Number,
    default: 1,
  },
});
</script>

<style lang="scss" scoped>
.essential-link {
  border-radius: 8px;
  margin: 0 4px 4px 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  &--active {
    background-color: var(--q-primary-1, rgba(25, 118, 210, 0.1));
    color: var(--q-primary, #1976d2);
    font-weight: 500;

    &:hover {
      background-color: var(--q-primary-2, rgba(25, 118, 210, 0.2));
    }

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background-color: var(--q-primary, #1976d2);
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }
  }

  &__title {
    font-weight: 500;
    letter-spacing: 0.01em;
  }
}
</style>
