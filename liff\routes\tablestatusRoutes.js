const express = require("express");
const {
  getSetting,
  getTablestatus,
  getOpenMode,
  openTable,
  getOpenBase,
  saveSetting,
  getPrintErrors,
  checkDatabase,
  cancelService,
} = require("../controllers/tablestatusController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.post("/check_Database", authMiddleware, checkDatabase); // 更新桌況
router.get("/get_PrintErrors", authMiddleware, getPrintErrors); // 抓取設定檔
router.get("/get_Setting", authMiddleware, getSetting); // 抓取設定檔
router.post("/save_Setting", authMiddleware, saveSetting); // 更新桌況
router.post("/get_Tablestatus", authMiddleware, getTablestatus); // 更新桌況
router.get("/get_OpenMode", authMiddleware, getOpenMode); // 抓取點餐模式
router.post("/open_Table", authMiddleware, openTable); // 開桌
router.get("/get_OpenBase", authMiddleware, getOpenBase); // 抓取開桌鍋 基本盤
router.post("/cancel_Service", authMiddleware, cancelService); // 開桌

module.exports = router;
