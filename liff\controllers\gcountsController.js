const sql = require("mssql");
require("dotenv").config();
const dbConfig = require("../config/db");
// const { getPoolByBranch, sql: mssql } = require("../services/dbPoolManager");

// 🔧 支援切換 DB - 從 btimestatController 參考
const getDbConfig = (branch) => {
  if (!branch) throw new Error("缺少 branch");
  return {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_HOST,
    database: branch,
    port: Number(process.env.DB_PORT),
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  };
};

// ==== 類別管理 ====

// 獲取所有商品類別
const getCategories = async (req, res) => {
  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const result = await pool.request().query(`
      SELECT cid, cname, sno, ISNULL(total_display, 1) as total_display FROM Gcounts ORDER BY sno
    `);

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getCategories 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "獲取類別失敗",
      error: err.message,
    });
  }
};

// 新增或更新商品類別
const saveCategory = async (req, res) => {
  const { category } = req.body;

  if (!category || !category.cname) {
    return res.status(400).json({ success: false, message: "類別資料不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 檢查表格是否存在，不存在則創建
    await ensureTableExists(pool);

    // 檢查類別是否已存在
    const checkRequest = pool.request();
    const checkResult = await checkRequest
      .input("checkCid", sql.VarChar(6), category.cid)
      .query(`SELECT * FROM Gcounts WHERE cid = @checkCid`);

    if (checkResult.recordset.length > 0) {
      // 更新已存在的類別
      const updateRequest = pool.request();
      await updateRequest
        .input("updateCid", sql.VarChar(6), category.cid)
        .input("cname", sql.NVarChar(100), category.cname)
        .input("sno", sql.Int, category.sno || 0)
        .input(
          "total_display",
          sql.Int,
          category.total_display !== undefined ? category.total_display : 1
        ).query(`
          UPDATE Gcounts SET 
            cname = @cname, 
            sno = @sno,
            total_display = @total_display
          WHERE cid = @updateCid
        `);
    } else {
      // 如果沒有提供 cid，生成新的
      if (!category.cid) {
        const maxRequest = pool.request();
        const maxResult = await maxRequest.query(`
          SELECT MAX(CAST(SUBSTRING(cid, 2, LEN(cid)-1) AS INT)) as max_id FROM Gcounts
        `);

        const maxId = maxResult.recordset[0].max_id || 0;
        category.cid = `C${(maxId + 1).toString().padStart(5, "0")}`;
      }

      // 新增類別
      const insertRequest = pool.request();
      await insertRequest
        .input("insertCid", sql.VarChar(6), category.cid)
        .input("cname", sql.NVarChar(100), category.cname)
        .input("sno", sql.Int, category.sno || 0)
        .input(
          "total_display",
          sql.Int,
          category.total_display !== undefined ? category.total_display : 1
        ).query(`
          INSERT INTO Gcounts (cid, cname, sno, total_display) 
          VALUES (@insertCid, @cname, @sno, @total_display)
        `);
    }

    return res.json({
      success: true,
      message: "儲存成功",
      data: category,
    });
  } catch (err) {
    console.error("❌ saveCategory 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "儲存類別失敗",
      error: err.message,
    });
  }
};

// 刪除商品類別
const deleteCategory = async (req, res) => {
  const { cid } = req.body;

  if (!cid) {
    return res.status(400).json({ success: false, message: "缺少類別ID" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 刪除類別前先刪除相關聯的群組和項目
    const groupsRequest = pool.request();
    const groupsResult = await groupsRequest
      .input("groupsCid", sql.VarChar(6), cid)
      .query(`SELECT gid FROM Gcounts_group WHERE gcid = @groupsCid`);

    const groups = groupsResult.recordset;
    for (const group of groups) {
      const deleteItemRequest = pool.request();
      await deleteItemRequest
        .input("itemGid", sql.VarChar(6), group.gid)
        .query(`DELETE FROM Gcounts_item WHERE gid = @itemGid`);
    }

    const deleteGroupRequest = pool.request();
    await deleteGroupRequest
      .input("deleteGroupCid", sql.VarChar(6), cid)
      .query(`DELETE FROM Gcounts_group WHERE gcid = @deleteGroupCid`);

    const deleteCategoryRequest = pool.request();
    await deleteCategoryRequest
      .input("deleteCid", sql.VarChar(6), cid)
      .query(`DELETE FROM Gcounts WHERE cid = @deleteCid`);

    return res.json({ success: true, message: "刪除成功" });
  } catch (err) {
    console.error("❌ deleteCategory 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "刪除類別失敗",
      error: err.message,
    });
  }
};

// ==== 群組管理 ====

// 獲取所有商品群組
const getGroups = async (req, res) => {
  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    const result = await request.query(`
      SELECT gid, gcid, gname, sno, branch, 
             ISNULL(total, 1) as total, 
             ISNULL(display, 1) as display 
      FROM Gcounts_group 
      ORDER BY gcid, sno
    `);

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getGroups 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "獲取群組失敗",
      error: err.message,
    });
  }
};

// 新增或更新商品群組
const saveGroup = async (req, res) => {
  const { group } = req.body;

  if (!group || !group.gname || !group.gcid) {
    return res.status(400).json({ success: false, message: "群組資料不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 確保表格存在
    await ensureTableExists(pool);

    // 檢查群組是否已存在
    const checkRequest = pool.request();
    const checkResult = await checkRequest
      .input("checkGid", sql.VarChar(6), group.gid)
      .input("checkGcid", sql.VarChar(6), group.gcid)
      .query(
        `SELECT * FROM Gcounts_group WHERE gid = @checkGid AND gcid = @checkGcid`
      );

    if (checkResult.recordset.length > 0) {
      // 更新群組
      const updateRequest = pool.request();
      await updateRequest
        .input("updateGid", sql.VarChar(6), group.gid)
        .input("updateGcid", sql.VarChar(6), group.gcid)
        .input("gname", sql.NVarChar(100), group.gname)
        .input("sno", sql.Int, group.sno || 0)
        .input("branch", sql.VarChar(6), group.branch)
        .input("total", sql.Int, group.total !== undefined ? group.total : 1)
        .input(
          "display",
          sql.Int,
          group.display !== undefined ? group.display : 1
        ).query(`
          UPDATE Gcounts_group SET 
            gname = @gname, 
            sno = @sno,
            branch = @branch,
            total = @total,
            display = @display
          WHERE gid = @updateGid AND gcid = @updateGcid
        `);
    } else {
      // 如果沒有提供 gid，生成新的
      if (!group.gid) {
        const maxRequest = pool.request();
        const maxResult = await maxRequest.query(`
          SELECT MAX(CAST(SUBSTRING(gid, 2, LEN(gid)-1) AS INT)) as max_id FROM Gcounts_group
        `);

        const maxId = maxResult.recordset[0].max_id || 0;
        group.gid = `G${(maxId + 1).toString().padStart(5, "0")}`;
      }

      // 新增群組
      const insertRequest = pool.request();
      await insertRequest
        .input("insertGid", sql.VarChar(6), group.gid)
        .input("insertGcid", sql.VarChar(6), group.gcid)
        .input("gname", sql.NVarChar(100), group.gname)
        .input("sno", sql.Int, group.sno || 0)
        .input("branch", sql.VarChar(6), group.branch)
        .input("total", sql.Int, group.total !== undefined ? group.total : 1)
        .input(
          "display",
          sql.Int,
          group.display !== undefined ? group.display : 1
        ).query(`
          INSERT INTO Gcounts_group (gid, gcid, gname, sno, branch, total, display) 
          VALUES (@insertGid, @insertGcid, @gname, @sno, @branch, @total, @display)
        `);
    }

    return res.json({
      success: true,
      message: "儲存成功",
      data: group,
    });
  } catch (err) {
    console.error("❌ saveGroup 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "儲存群組失敗",
      error: err.message,
    });
  }
};

// 刪除商品群組
const deleteGroup = async (req, res) => {
  const { gid } = req.body;

  if (!gid) {
    return res.status(400).json({ success: false, message: "缺少群組ID" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 先刪除群組下的商品項目
    const deleteItemsRequest = pool.request();
    await deleteItemsRequest
      .input("deleteItemsGid", sql.VarChar(6), gid)
      .query(`DELETE FROM Gcounts_item WHERE gid = @deleteItemsGid`);

    // 再刪除群組
    const deleteGroupRequest = pool.request();
    await deleteGroupRequest
      .input("deleteGroupGid", sql.VarChar(6), gid)
      .query(`DELETE FROM Gcounts_group WHERE gid = @deleteGroupGid`);

    return res.json({ success: true, message: "刪除成功" });
  } catch (err) {
    console.error("❌ deleteGroup 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "刪除群組失敗",
      error: err.message,
    });
  }
};

// 儲存類別排序
const saveCategorySort = async (req, res) => {
  const { categories } = req.body;

  if (!Array.isArray(categories) || categories.length === 0) {
    return res.status(400).json({ success: false, message: "參數不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 批量更新排序號
    for (const category of categories) {
      // 為每個類別創建新的請求對象，避免參數重複聲明
      const request = pool.request();

      await request
        .input("cid", sql.VarChar(6), category.cid)
        .input("sno", sql.Int, category.sno)
        .query(`UPDATE Gcounts SET sno = @sno WHERE cid = @cid`);
    }

    return res.json({ success: true, message: "類別排序已儲存" });
  } catch (err) {
    console.error("❌ saveCategorySort 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "儲存類別排序失敗",
      error: err.message,
    });
  }
};

// 儲存群組排序
const saveGroupSort = async (req, res) => {
  const { groups } = req.body;

  if (!Array.isArray(groups) || groups.length === 0) {
    return res.status(400).json({ success: false, message: "參數不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 批量更新排序號
    for (const group of groups) {
      // 為每個群組創建新的請求對象，避免參數重複聲明
      const request = pool.request();

      await request
        .input("gid", sql.VarChar(6), group.gid)
        .input("sno", sql.Int, group.sno)
        .query(`UPDATE Gcounts_group SET sno = @sno WHERE gid = @gid`);
    }

    return res.json({ success: true, message: "群組排序已儲存" });
  } catch (err) {
    console.error("❌ saveGroupSort 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "儲存群組排序失敗",
      error: err.message,
    });
  }
};

// ==== 商品項目管理 ====

// 獲取群組下的商品項目
const getGroupItems = async (req, res) => {
  const { gid } = req.query;

  if (!gid) {
    return res.status(400).json({ success: false, message: "缺少群組ID" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    const result = await request
      .input("gid", sql.VarChar(6), gid)
      .query(`SELECT * FROM Gcounts_item WHERE gid = @gid`);

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getGroupItems 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "獲取商品項目失敗",
      error: err.message,
    });
  }
};

// 獲取參照門市的商品項目
const getBranchItems = async (req, res) => {
  const { branch, gid } = req.query;

  if (!branch) {
    return res.status(400).json({ success: false, message: "缺少門市參數" });
  }

  try {
    // 使用門市的資料庫連接
    const dbConfig = getDbConfig(branch);
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    // 使用提供的SQL查詢門市的商品資料表，包含分類名稱
    const result = await request.query(`
      SELECT omenum.code as items_code, omenum.name as items_name, cclass.name as category_name
      FROM omenum
      LEFT JOIN cclass ON omenum.cclass = cclass.code
      ORDER BY omenum.code
    `);

    // 如果有提供群組ID，排除已在此群組的項目
    if (gid) {
      const mainPool = await sql.connect(
        process.env.SQLSERVER_CONNECTION_STRING
      );
      const existingResult = await mainPool
        .request()
        .input("gid", sql.VarChar(6), gid)
        .query(`SELECT items_code FROM Gcounts_item WHERE gid = @gid`);

      // 獲取已存在項目的編號集合
      const existingCodes = new Set(
        existingResult.recordset.map((r) => r.items_code)
      );

      // 過濾掉已存在的項目
      const filteredItems = result.recordset.filter(
        (item) => !existingCodes.has(item.items_code)
      );

      return res.json({ success: true, data: filteredItems });
    }

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getBranchItems 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "獲取門市商品項目失敗",
      error: err.message,
    });
  }
};

// 新增商品項目到群組
const addItems = async (req, res) => {
  const { gid, items } = req.body;

  if (!gid || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({ success: false, message: "參數不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();

    // 批量插入商品項目
    for (const item of items) {
      // 为每个项目创建新的请求对象，避免参数重复声明
      const request = pool.request();

      await request
        .input("gid", sql.VarChar(6), gid)
        .input("items_code", sql.VarChar(20), item.items_code)
        .input("items_name", sql.NVarChar(100), item.items_name).query(`
          IF NOT EXISTS (SELECT 1 FROM Gcounts_item WHERE gid = @gid AND items_code = @items_code)
          BEGIN
            INSERT INTO Gcounts_item (gid, items_code, items_name) 
            VALUES (@gid, @items_code, @items_name)
          END
        `);
    }

    return res.json({ success: true, message: "新增成功" });
  } catch (err) {
    console.error("❌ addItems 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "新增商品項目失敗",
      error: err.message,
    });
  }
};

// 從群組中刪除商品項目
const deleteItem = async (req, res) => {
  const { gid, items_code } = req.body;

  if (!gid || !items_code) {
    return res.status(400).json({ success: false, message: "參數不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    await request
      .input("gid", sql.VarChar(6), gid)
      .input("items_code", sql.VarChar(20), items_code)
      .query(
        `DELETE FROM Gcounts_item WHERE gid = @gid AND items_code = @items_code`
      );

    return res.json({ success: true, message: "刪除成功" });
  } catch (err) {
    console.error("❌ deleteItem 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "刪除商品項目失敗",
      error: err.message,
    });
  }
};

// ==== 統計功能 ====

// 獲取產品統計
const getProductStats = async (req, res) => {
  const { category, branches, from, to } = req.body;
  //console.log(req.body);
  if (
    !category ||
    !Array.isArray(branches) ||
    branches.length === 0 ||
    !from ||
    !to
  ) {
    return res.status(400).json({ success: false, message: "參數不完整" });
  }

  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    // 1. 獲取類別下的所有群組
    const groupsResult = await request.input("cid", sql.VarChar(6), category)
      .query(`
        SELECT G.gid, G.gname, G.gcid, G.branch, G.sno,
               ISNULL(G.total, 1) as total, 
               ISNULL(G.display, 1) as display,
               C.cname as category_name,
               ISNULL(C.total_display, 1) as total_display
        FROM Gcounts_group G
        JOIN Gcounts C ON G.gcid = C.cid
        WHERE G.gcid = @cid
        ORDER BY G.sno
      `);

    const groups = groupsResult.recordset;
    if (groups.length === 0) {
      return res.json({ success: true, data: [], message: "該類別下沒有群組" });
    }

    // 2. 獲取每個群組的商品項目
    const allGroupItems = [];
    for (const group of groups) {
      // 為每個群組創建新的請求對象
      const groupRequest = pool.request();
      const itemsResult = await groupRequest
        .input("groupId", sql.VarChar(6), group.gid)
        .query(`SELECT * FROM Gcounts_item WHERE gid = @groupId`);

      allGroupItems.push({
        group,
        items: itemsResult.recordset,
      });
    }

    // 3. 對每個門市查詢統計數據
    const allStats = [];

    // 轉換日期格式為 YYYY/MM/DD
    const fromDate = new Date(from);
    const toDate = new Date(to);
    const fromFormatted = `${fromDate.getFullYear()}/${String(
      fromDate.getMonth() + 1
    ).padStart(2, "0")}/${String(fromDate.getDate()).padStart(2, "0")}`;
    const toFormatted = `${toDate.getFullYear()}/${String(
      toDate.getMonth() + 1
    ).padStart(2, "0")}/${String(toDate.getDate()).padStart(2, "0")}`;

    for (const branch of branches) {
      try {
        // 獲取門市名稱 (從總部資料庫)
        const branchInfoRequest = pool.request();
        const branchInfoResult = await branchInfoRequest
          .input("branchCode", sql.VarChar(10), branch)
          .query(`SELECT cod_name FROM branch WHERE cod_cust = @branchCode`);

        const branchName =
          branchInfoResult.recordset.length > 0
            ? branchInfoResult.recordset[0].cod_name
            : `門市 ${branch}`;

        // 使用門市的資料庫連接 (切換到門市資料庫)
        const dbConfig = getDbConfig(branch);
        const branchPool = await new sql.ConnectionPool(dbConfig).connect();
        const branchRequest = branchPool.request();

        // 對每個群組查詢銷售數據
        for (const { group, items } of allGroupItems) {
          if (items.length === 0) continue;

          // 構建 IN 查詢的條件
          const itemCodes = items
            .map((item) => `'${item.items_code}'`)
            .join(",");

          // 使用提供的SQL查詢，正確計算商品數量
          const query = `
            SELECT 
              OrderMainN.code,
              SUM(OrderMainN.qty) as total_qty,
              SUM(OrderMainN.qty * OrderMainN.oprice) as total_amount,
              omenum.name as item_name,
              cclass.name as category_name
            FROM OrderMainN
            LEFT JOIN ordernow ON ordernow.code = OrderMainN.scode
            LEFT JOIN omenum ON OrderMainN.code = omenum.code
            LEFT JOIN cclass ON omenum.cclass = cclass.code
            WHERE 
              ordernow.CHECKOUT = '1' 
              AND ordernow.Ndate >= @fromDate 
              AND ordernow.Ndate <= @toDate 
              AND OrderMainN.code IN (${itemCodes})
            GROUP BY 
              OrderMainN.code,
              omenum.name,
              cclass.name
          `;

          // 為每個查詢創建新的請求對象
          const statRequest = branchPool.request();
          statRequest.input("fromDate", sql.VarChar, fromFormatted);
          statRequest.input("toDate", sql.VarChar, toFormatted);
          const result = await statRequest.query(query);

          if (result.recordset && result.recordset.length > 0) {
            // 計算該群組在該門市的總數
            let groupTotal = 0;
            result.recordset.forEach((record) => {
              groupTotal += record.total_qty || 0;
            });

            // 添加到統計結果
            allStats.push({
              branch_id: branch,
              branch_name: branchName,
              group_id: group.gid,
              group_name: group.gname,
              category_id: group.gcid,
              category_name: group.category_name,
              count: groupTotal,
              details: result.recordset,
            });
          } else {
            // 如果沒有數據，添加0記錄
            allStats.push({
              branch_id: branch,
              branch_name: branchName,
              group_id: group.gid,
              group_name: group.gname,
              category_id: group.gcid,
              category_name: group.category_name,
              count: 0,
              details: [],
            });
          }
        }
      } catch (branchErr) {
        console.error(`❌ 處理門市 ${branch} 時出錯:`, branchErr);
        // 繼續處理下一個門市
      }
    }

    // 4. 計算占比
    const branchTotals = {};
    const grandTotal = allStats.reduce((sum, stat) => {
      // 只有 total=1 的群組才計入合計
      const group = groups.find((g) => g.gid === stat.group_id);
      return sum + (group && group.total === 1 ? stat.count : 0);
    }, 0);

    allStats.forEach((stat) => {
      // 計算該門市的總數 (只計算 total=1 的群組)
      const group = groups.find((g) => g.gid === stat.group_id);
      if (group && group.total === 1) {
        branchTotals[stat.branch_id] = branchTotals[stat.branch_id] || 0;
        branchTotals[stat.branch_id] += stat.count;
      }
    });

    const result = allStats.map((stat) => {
      const branchTotal = branchTotals[stat.branch_id] || 0;
      const group = groups.find((g) => g.gid === stat.group_id);

      return {
        ...stat,
        total_in_sum: group ? group.total : 1, // 添加是否計入合計的標記
        display: group ? group.display : 1, // 添加是否顯示的標記
        total_display: group ? group.total_display : 1, // 添加是否顯示門市統計的標記
        branch_percent:
          branchTotal > 0 ? Math.round((stat.count / branchTotal) * 100) : 0,
        total_percent:
          grandTotal > 0 ? Math.round((stat.count / grandTotal) * 100) : 0,
      };
    });

    return res.json({
      success: true,
      data: result,
      summary: {
        grandTotal,
        branchTotals,
      },
    });
  } catch (err) {
    console.error("❌ getProductStats 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "獲取產品統計失敗",
      error: err.message,
    });
  }
};

// ==== 輔助函數 ====

// 確保資料表存在
const ensureTableExists = async (pool) => {
  try {
    const request = pool.request();

    // 檢查 Gcounts 表是否存在
    const checkGcounts = await request.query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Gcounts'
    `);

    if (checkGcounts.recordset.length === 0) {
      console.log("🔧 建立資料表 Gcounts");
      await request.query(`
        CREATE TABLE Gcounts (
          cid VARCHAR(6) PRIMARY KEY,
          cname NVARCHAR(100) NOT NULL,
          sno INT DEFAULT 0,
          total_display INT DEFAULT 1
        )
      `);
    }

    // 檢查 Gcounts_group 表是否存在
    const checkGcountsGroup = await request.query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Gcounts_group'
    `);

    if (checkGcountsGroup.recordset.length === 0) {
      console.log("🔧 建立資料表 Gcounts_group");
      await request.query(`
        CREATE TABLE Gcounts_group (
          gid VARCHAR(6) NOT NULL,
          gcid VARCHAR(6) NOT NULL,
          gname NVARCHAR(100) NOT NULL,
          sno INT DEFAULT 0,
          branch INT NOT NULL,
          total INT DEFAULT 1,
          display INT DEFAULT 1,
          PRIMARY KEY (gid, gcid),
          FOREIGN KEY (gcid) REFERENCES Gcounts(cid) ON DELETE CASCADE
        )
      `);
    }

    // 檢查 Gcounts_item 表是否存在
    const checkGcountsItem = await request.query(`
      SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Gcounts_item'
    `);

    if (checkGcountsItem.recordset.length === 0) {
      console.log("🔧 建立資料表 Gcounts_item");
      await request.query(`
        CREATE TABLE Gcounts_item (
          gid VARCHAR(6) NOT NULL,
          items_code VARCHAR(20) NOT NULL,
          items_name NVARCHAR(100) NOT NULL,
          PRIMARY KEY (gid, items_code),
          FOREIGN KEY (gid) REFERENCES Gcounts_group(gid) ON DELETE CASCADE
        )
      `);
    }
  } catch (err) {
    console.error("❌ ensureTableExists 錯誤:", err);
    throw err;
  }
};

// 生成日期範圍
const getDateRange = (startDate, endDate) => {
  const dates = [];
  let currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
};

// 獲取門市資訊
const getBranches = async (req, res) => {
  try {
    // 連接總部資料庫
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    const request = pool.request();

    // 使用提供的SQL查詢獲取門市資訊
    const result = await request.query(`
      SELECT cod_cust, cod_name, cod_group, sts FROM branch
      WHERE invest is not null
      ORDER BY cod_group, cod_cust
    `);

    return res.json({ success: true, data: result.recordset });
  } catch (err) {
    console.error("❌ getBranches 錯誤:", err);
    return res.status(500).json({
      success: false,
      message: "獲取門市資訊失敗",
      error: err.message,
    });
  }
};

module.exports = {
  // 類別管理
  getCategories,
  saveCategory,
  deleteCategory,

  // 群組管理
  getGroups,
  saveGroup,
  deleteGroup,
  saveCategorySort,
  saveGroupSort,

  // 商品項目管理
  getGroupItems,
  getBranchItems,
  addItems,
  deleteItem,

  // 統計功能
  getProductStats,

  // 門市管理
  getBranches,
};
