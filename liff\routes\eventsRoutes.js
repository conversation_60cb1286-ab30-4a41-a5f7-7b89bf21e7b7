const express = require("express");
const {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  searchEvents,
  getDepartments,
  hideEvent,
} = require("../controllers/eventsController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 獲取所有事件
router.get("/get_events", authMiddleware, getEvents);

// 獲取部門列表和顏色
router.get("/departments", authMiddleware, getDepartments);

// 搜尋事件
router.get("/events/search", authMiddleware, searchEvents);

// 獲取事件詳情
router.get("/events/:id", authMiddleware, getEventById);

// 新增事件
router.post("/events", authMiddleware, createEvent);

// 更新事件
router.put("/events/:id", authMiddleware, updateEvent);

// 隱藏事件
router.put("/hide/:id", authMiddleware, hideEvent);

// 刪除事件
router.delete("/events/:id", authMiddleware, deleteEvent);

module.exports = router;
