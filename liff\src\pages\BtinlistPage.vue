<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto; position: relative"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">公告列表</div>
            <q-btn
              flat
              round
              size="sm"
              color="red"
              class="search-button"
              @click="openSearchDialog"
            >
              <Search />
              <q-tooltip>搜尋公告</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>

        <!-- 公告統計 - 重新設計的區域 -->
        <q-card-section class="q-pt-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': filterStatus === 'all' }"
                  @click="filterStatus = 'all'"
                >
                  <div class="stats-icon">
                    <message-square-text :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ bulletins.length }}</div>
                    <div class="stats-label">全部公告</div>
                  </div>
                </div>
              </div>
              <!-- 未讀公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card unread-card"
                  :class="{ 'active-filter': filterStatus === 'unread' }"
                  @click="filterStatus = 'unread'"
                >
                  <div class="stats-icon">
                    <mail :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ unreadCount }}</div>
                    <div class="stats-label">未讀公告</div>
                  </div>
                </div>
              </div>

              <!-- 必讀公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card required-card"
                  :class="{ 'active-filter': filterStatus === 'required' }"
                  @click="filterStatus = 'required'"
                >
                  <div class="stats-icon">
                    <triangle-alert :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ requiredCount }}</div>
                    <div class="stats-label">必讀公告</div>
                  </div>
                </div>
              </div>

              <!-- 部門公告統計卡片 -->
              <div class="col-6 col-sm-6 col-md-3">
                <div
                  class="stats-card dep-card"
                  :class="{ 'active-filter': filterStatus === 'mydep' }"
                  @click="filterStatus = 'mydep'"
                >
                  <div class="stats-icon">
                    <building :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">
                      {{
                        filteredBulletins.filter(
                          (b) => b.dep.trim() === props.Did.trim()
                        ).length
                      }}
                    </div>
                    <div class="stats-label">部門公告</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- 添加分頁控制器到固定區域 -->
        <q-card-section class="q-pt-none q-pb-xs">
          <div class="row justify-end">
            <q-pagination
              v-model="currentPage"
              :max="totalPages"
              :max-pages="isMobile ? 3 : 5"
              boundary-numbers
              direction-links
              color="primary"
              active-color="blue"
              active-text-color="white"
              class="pagination-controls"
              size="sm"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <!-- 公告列表 -->
      <q-card-section class="q-pa-xs">
        <div v-if="loading.table" class="loader-container">
          <q-spinner size="40px" color="grey-5" />
          <div class="text-subtitle1 q-mt-sm q-pl-md text-grey-5">
            載入公告中...
          </div>
        </div>

        <div
          v-else-if="filteredBulletins.length === 0"
          class="empty-state-container"
        >
          <div class="empty-state q-pa-md flex flex-center column text-grey-5">
            <message-square-warning
              :size="50"
              :stroke-width="1"
              style="width: 60px; height: 50px; margin-bottom: 12px"
            />
            <div class="empty-text text-grey-5">
              {{ searchForm.title ? "沒有找到符合的公告" : "目前沒有公告" }}
            </div>
          </div>
        </div>

        <div v-else class="bulletin-list-container">
          <!-- 移除原有的分頁控制 -->
          <q-list separator class="bulletin-list">
            <div
              v-for="bulletin in paginatedBulletins"
              :key="bulletin.id"
              class="bg-white q-mb-xs"
              :class="{ 'unread-bulletin': !isRead(bulletin) }"
              style="
                border: 1px solid #eee;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
              "
            >
              <q-expansion-item
                expand-separator
                class="q-pa-none"
                header-class="text-indigo bulletin-header"
                @show="markAsRead(bulletin)"
              >
                <!-- 公告主列 -->
                <template #header>
                  <q-item
                    dense
                    class="q-pa-none q-gutter-none items-center"
                    style="width: 100%; position: relative; padding: 8px 10px"
                  >
                    <!-- 公告資訊 -->
                    <q-item-section>
                      <!-- 狀態標記 -->
                      <div class="row q-gutter-x-xs q-pb-md">
                        <q-badge
                          v-if="!isRead(bulletin)"
                          color="blue"
                          text-color="white"
                          label="未讀"
                          outline
                          class="bulletin-badge"
                        />
                        <q-badge
                          v-if="bulletin.priority === 1"
                          color="negative"
                          text-color="white"
                          label="急件"
                          outline
                          class="bulletin-badge"
                        />
                        <q-badge
                          v-if="bulletin.required === 1"
                          color="orange"
                          text-color="white"
                          label="必讀"
                          outline
                          class="bulletin-badge"
                        />
                      </div>

                      <!-- 標題 -->
                      <div
                        class="text-indigo-10 text-weight-medium bulletin-title q-mb-xs"
                      >
                        【{{ getDepartmentName(bulletin.dep) }}】{{
                          bulletin.title
                        }}
                      </div>

                      <!-- 發布日期 -->
                      <div class="text-caption text-grey-8 bulletin-meta">
                        <span class="flex items-center">
                          <calendar-check
                            :stroke-width="1"
                            :size="16"
                            class="q-mr-xs"
                          />
                          {{
                            new Date(bulletin.publish_at).toLocaleDateString()
                          }}
                          <template v-if="bulletin.expires_at">
                            {{
                              " - " +
                              new Date(bulletin.expires_at).toLocaleDateString()
                            }}
                          </template>
                        </span>
                      </div>
                    </q-item-section>

                    <!-- 全螢幕按鈕 -->
                    <q-item-section side>
                      <q-btn
                        flat
                        round
                        dense
                        color="primary"
                        size="sm"
                        class="fullscreen-btn"
                        @click.stop="openFullscreen(bulletin)"
                      >
                        <fullscreen :stroke-width="1" :size="22" />
                        <q-tooltip>全螢幕檢視</q-tooltip>
                      </q-btn>
                    </q-item-section>
                  </q-item>
                </template>

                <!-- 詳細內容 -->
                <q-card-section class="q-pa-sm q-pt-sm bg-grey-1">
                  <!-- 基本資訊 -->
                  <div class="row q-col-gutter-xs q-mb-sm">
                    <div class="col-12">
                      <div
                        class="bg-white q-pa-sm rounded-borders content-container"
                        v-html="bulletin.contents"
                      ></div>
                    </div>
                  </div>

                  <!-- 附件列表 -->
                  <div
                    v-if="bulletin.attachments && bulletin.attachments.length"
                    class="q-mb-sm"
                  >
                    <q-card flat bordered class="attachment-card">
                      <q-card-section class="q-py-sm">
                        <div
                          class="row items-center text-subtitle2 text-primary q-mb-sm"
                        >
                          <q-icon
                            name="attach_file"
                            class="q-mr-xs"
                            size="sm"
                            color="primary"
                          />
                          <span class="text-weight-medium">附件列表</span>
                        </div>
                        <div class="row q-col-gutter-md">
                          <div
                            v-for="attachment in bulletin.attachments"
                            :key="attachment.id"
                            class="col-12 col-sm-6"
                          >
                            <q-item class="attachment-item q-pa-sm">
                              <q-item-section avatar>
                                <q-icon
                                  :name="getFileIcon(attachment.file_type)"
                                  size="md"
                                  :color="getFileColor(attachment.file_type)"
                                />
                              </q-item-section>
                              <q-item-section>
                                <div
                                  class="text-subtitle2 cursor-pointer text-primary attachment-link"
                                  @click="previewAttachment(attachment)"
                                >
                                  附件 {{ attachment.sno }}
                                </div>
                                <div class="text-caption text-grey-7">
                                  {{ getFileTypeName(attachment.file_type) }}
                                </div>
                              </q-item-section>
                              <q-item-section side>
                                <q-btn
                                  flat
                                  round
                                  color="primary"
                                  icon="download"
                                  size="sm"
                                  class="download-btn"
                                  @click="downloadAttachment(attachment)"
                                >
                                  <q-tooltip>下載附件</q-tooltip>
                                </q-btn>
                              </q-item-section>
                            </q-item>
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>

                  <!-- 確認閱讀按鈕 (僅顯示在必讀公告且未確認閱讀時) -->
                  <div
                    v-if="bulletin.required === 1 && !isConfirmedRead(bulletin)"
                    class="row justify-center q-mt-sm"
                  >
                    <q-btn
                      unelevated
                      color="primary"
                      icon="check_circle"
                      label="確認已閱讀"
                      size="sm"
                      @click="confirmRead(bulletin)"
                    />
                  </div>
                </q-card-section>
              </q-expansion-item>
            </div>
          </q-list>
        </div>
      </q-card-section>
    </q-card>

    <!-- 搜尋對話框 -->
    <q-dialog v-model="searchDialog.show">
      <q-card style="width: 500px; max-width: 90vw; border-radius: 10px">
        <q-card-section
          class="row items-center bg-primary text-white q-pb-xs"
          style="border-radius: 10px 10px 0 0"
        >
          <div class="text-subtitle1">
            <q-icon name="search" class="q-mr-xs" size="sm" />
            搜尋公告
          </div>
          <q-space />
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            class="text-white"
            size="sm"
          />
        </q-card-section>

        <q-card-section class="q-pt-sm">
          <q-form>
            <!-- 主要搜尋條件區域 -->
            <div class="q-pa-xs">
              <!-- 公告標題 -->
              <q-input
                v-model="searchForm.title"
                label="公告標題"
                outlined
                dense
                clearable
                class="q-mb-sm"
              >
                <template #prepend>
                  <q-icon name="title" color="primary" size="xs" />
                </template>
              </q-input>

              <!-- 發布單位 -->
              <q-select
                v-model="searchForm.dep"
                :options="depOptions"
                label="發布單位"
                outlined
                dense
                clearable
                emit-value
                map-options
                class="q-mb-sm"
              >
                <template #prepend>
                  <q-icon name="business" color="primary" size="xs" />
                </template>
              </q-select>

              <!-- 發布日期範圍 -->
              <q-input
                v-model="searchForm.startDate"
                label="發布日期"
                outlined
                dense
                clearable
                mask="####-##-##"
                placeholder="YYYY-MM-DD"
              >
                <template #prepend>
                  <q-icon name="event" color="primary" size="xs" />
                </template>
                <template #append>
                  <q-icon name="event" class="cursor-pointer" size="xs">
                    <q-popup-proxy
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-date v-model="searchForm.startDate" mask="YYYY-MM-DD">
                        <div class="row items-center justify-end">
                          <q-btn
                            v-close-popup
                            label="確定"
                            color="primary"
                            flat
                            dense
                          />
                        </div>
                      </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>

            <!-- 按鈕區域 -->
            <div class="row justify-end q-mt-sm q-gutter-sm">
              <q-btn
                outline
                label="重置"
                color="grey"
                @click="clearFilter"
                icon="refresh"
                dense
              />
              <q-btn
                unelevated
                label="搜尋"
                color="primary"
                icon="search"
                dense
                @click="
                  () => {
                    searchBulletins();
                    searchDialog.show = false;
                  }
                "
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 附件預覽對話框 -->
    <q-dialog v-model="previewDialog.show">
      <q-card
        style="
          width: 90vw;
          max-width: 900px;
          max-height: 90vh;
          border-radius: 10px;
        "
      >
        <q-card-section class="row items-center bg-primary text-white q-py-sm">
          <div class="text-subtitle1">
            <q-icon name="visibility" class="q-mr-xs" size="sm" />
            附件預覽
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup size="sm" />
        </q-card-section>

        <q-card-section class="q-pa-none" style="height: 80vh">
          <div
            v-if="previewDialog.loading"
            class="full-height flex flex-center"
          >
            <q-spinner-dots color="primary" size="36px" />
            <div class="text-grey q-mt-sm q-ml-sm text-subtitle2">
              載入中...
            </div>
          </div>
          <div
            v-else-if="previewDialog.error"
            class="full-height flex flex-center column"
          >
            <q-icon name="error" color="negative" size="36px" />
            <div class="text-negative q-mt-sm text-subtitle2">
              {{ previewDialog.error }}
            </div>
            <q-btn
              flat
              color="primary"
              label="下載檔案"
              icon="download"
              class="q-mt-md"
              size="sm"
              @click="downloadAttachment(previewDialog.attachment)"
            />
          </div>
          <iframe
            v-else
            :src="previewDialog.url"
            style="width: 100%; height: 100%; border: none"
          ></iframe>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 全螢幕預覽對話框 -->
    <q-dialog
      v-model="fullscreenDialog.show"
      full-width
      full-height
      maximized
      transition-show="fade"
      transition-hide="fade"
    >
      <q-card class="full-height">
        <q-card-section class="row items-center bg-primary text-white q-py-sm">
          <div class="text-subtitle1">
            <q-icon name="fullscreen" class="q-mr-xs" size="sm" />
            {{
              fullscreenDialog.bulletin ? fullscreenDialog.bulletin.title : ""
            }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup size="sm" />
        </q-card-section>

        <q-card-section class="scroll" style="height: calc(100% - 50px)">
          <!-- 公告內容 -->
          <div class="q-mb-md">
            <div
              class="bg-white q-pa-md rounded-borders content-container"
              style="max-height: none"
              v-html="
                fullscreenDialog.bulletin
                  ? fullscreenDialog.bulletin.contents
                  : ''
              "
            ></div>
          </div>

          <!-- 附件列表 -->
          <div
            v-if="
              fullscreenDialog.bulletin &&
              fullscreenDialog.bulletin.attachments &&
              fullscreenDialog.bulletin.attachments.length
            "
            class="q-mt-lg"
          >
            <q-card flat bordered class="attachment-card">
              <q-card-section class="q-py-sm">
                <div
                  class="row items-center text-subtitle2 text-primary q-mb-sm"
                >
                  <q-icon
                    name="attach_file"
                    class="q-mr-xs"
                    size="sm"
                    color="primary"
                  />
                  <span class="text-weight-medium">附件列表</span>
                </div>
                <div class="row q-col-gutter-md">
                  <div
                    v-for="attachment in fullscreenDialog.bulletin.attachments"
                    :key="attachment.id"
                    class="col-12 col-sm-6 col-md-4"
                  >
                    <q-item class="attachment-item q-pa-sm">
                      <q-item-section avatar>
                        <q-icon
                          :name="getFileIcon(attachment.file_type)"
                          size="md"
                          :color="getFileColor(attachment.file_type)"
                        />
                      </q-item-section>
                      <q-item-section>
                        <div
                          class="text-subtitle2 cursor-pointer text-primary attachment-link"
                          @click="previewAttachment(attachment)"
                        >
                          附件 {{ attachment.sno }}
                        </div>
                        <div class="text-caption text-grey-7">
                          {{ getFileTypeName(attachment.file_type) }}
                        </div>
                      </q-item-section>
                      <q-item-section side>
                        <q-btn
                          flat
                          round
                          color="primary"
                          icon="download"
                          size="sm"
                          class="download-btn"
                          @click="downloadAttachment(attachment)"
                        >
                          <q-tooltip>下載附件</q-tooltip>
                        </q-btn>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- 確認閱讀按鈕 (僅顯示在必讀公告且未確認閱讀時) -->
          <div
            v-if="
              fullscreenDialog.bulletin &&
              fullscreenDialog.bulletin.required === 1 &&
              !isConfirmedRead(fullscreenDialog.bulletin)
            "
            class="row justify-center q-mt-lg"
          >
            <q-btn
              unelevated
              color="primary"
              icon="check_circle"
              label="確認已閱讀"
              size="md"
              @click="confirmRead(fullscreenDialog.bulletin)"
            />
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  computed,
  watch,
  onBeforeUnmount,
} from "vue";
import { QSpinner } from "quasar";
import { useQuasar } from "quasar";
import apiClient from "../api";
import { Search } from "lucide-vue-next";
const props = defineProps({
  Permissions: {
    type: [String, Array],
    default: () => [],
  },
  Userid: String,
  Did: String,
  Dname: String,
  UserBranch: Array,
});

const $q = useQuasar();
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 檢測是否為移動設備
const isMobile = computed(() => {
  return $q.screen.lt.sm;
});

// 篩選功能
const filterStatus = ref("all");
const selectedBranch = ref(null);
const branchOptions = computed(() => {
  if (!props.UserBranch || !Array.isArray(props.UserBranch)) return [];

  return props.UserBranch.map((branchCode) => {
    // 這裡可以通過API獲取門市名稱，但為簡化起見，我們暫時只使用代碼
    return {
      label: `門市 ${branchCode}`,
      value: branchCode,
    };
  });
});

// 添加一個方法來設置篩選條件
const setFilter = (filter) => {
  if (filter === "required") {
    filterStatus.value = "required";
  } else if (filter === "unread") {
    filterStatus.value = "unread";
  } else {
    filterStatus.value = filter || "all";
  }
};

// 選項和常數
const priorityOptions = [
  { label: "最高", value: 1 },
  { label: "高", value: 2 },
  { label: "中", value: 3 },
  { label: "低", value: 4 },
  { label: "最低", value: 5 },
];

const requiredOptions = [
  { label: "必讀", value: 1 },
  { label: "非必讀", value: 0 },
];

// 表格列定義
const columns = [
  {
    name: "title",
    align: "left",
    label: "標題",
    field: "title",
    sortable: true,
  },
  {
    name: "dep",
    align: "left",
    label: "發布單位",
    field: "dep",
    sortable: true,
  },
  {
    name: "priority",
    align: "center",
    label: "優先等級",
    field: "priority",
    sortable: true,
    format: (val) => priorityOptions.find((o) => o.value === val)?.label || val,
  },
  {
    name: "required",
    align: "center",
    label: "必讀",
    field: "required",
    sortable: true,
  },
  {
    name: "publish_at",
    align: "center",
    label: "發布時間",
    field: "publish_at",
    sortable: true,
    format: (val) => new Date(val).toLocaleString(),
  },
];

const statsColumns = [
  { name: "name", align: "left", label: "姓名", field: "name" },
  {
    name: "read_status",
    align: "center",
    label: "閱讀狀態",
    field: "read_status",
  },
  {
    name: "read_at",
    align: "center",
    label: "閱讀時間",
    field: "read_at",
    format: (val) => (val ? new Date(val).toLocaleString() : "-"),
  },
];

// 資料和狀態
const bulletins = ref([]);
const readStatus = ref({}); // 用於存儲已讀狀態
const confirmedReadStatus = ref({}); // 用於存儲確認閱讀狀態
const sessionReadStatus = ref({}); // 用於存儲當前會話中已讀的公告
const sessionConfirmedReadStatus = ref({}); // 用於存儲當前會話中確認閱讀的公告
const loading = reactive({
  table: false,
  save: false,
  search: false,
});
const unreadCount = ref(0);
const requiredCount = ref(0);

const filteredBulletins = computed(() => {
  let result = [...bulletins.value];

  // 篩選狀態
  if (filterStatus.value === "read") {
    result = result.filter((bulletin) => isRead(bulletin));
  } else if (filterStatus.value === "unread") {
    // 在未讀篩選狀態下，顯示未讀公告和當前會話中已讀的公告
    result = result.filter(
      (bulletin) => !isRead(bulletin) || sessionReadStatus.value[bulletin.id]
    );
  } else if (filterStatus.value === "required") {
    result = result.filter((bulletin) => bulletin.required === 1);
  } else if (filterStatus.value === "mydep") {
    result = result.filter(
      (bulletin) => bulletin.dep.trim() === props.Did.trim()
    );
  }

  return result;
});

// 在 script 部分添加分頁相關的變數和計算屬性
const currentPage = ref(1);
const pageSize = ref(5); // 每頁顯示5筆公告

// 計算總頁數
const totalPages = computed(() => {
  return Math.ceil(filteredBulletins.value.length / pageSize.value);
});

// 計算當前頁的公告
const paginatedBulletins = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return filteredBulletins.value.slice(startIndex, endIndex);
});

// 監聽篩選條件變化，重置頁碼
watch(filterStatus, () => {
  currentPage.value = 1;
});

// 監聽搜尋結果變化，重置頁碼
watch(
  () => bulletins.value.length,
  () => {
    currentPage.value = 1;
  }
);

// 部門選項
const depOptions = ref([]);

// 搜尋對話框
const searchDialog = reactive({
  show: false,
});

// 附件預覽對話框
const previewDialog = reactive({
  show: false,
  url: "",
  loading: false,
  error: null,
  attachment: null,
});

const searchForm = reactive({
  title: "",
  contents: "",
  dep: "",
  priority: null,
  required: null,
  startDate: "",
  endDate: "",
});

// 方法
const loadBulletins = async () => {
  try {
    loading.table = true;

    // 準備查詢參數
    const params = {
      userDep: props.Did,
      userBranches: props.UserBranch
        ? JSON.stringify(props.UserBranch)
        : undefined,
    };

    // 處理用戶群組參數
    if (Array.isArray(props.Permissions)) {
      params.userGroup = JSON.stringify(props.Permissions);
    } else if (typeof props.Permissions === "string") {
      // 向後兼容：將字符串轉換為數組
      params.userGroup = JSON.stringify([props.Permissions]);
    } else {
      params.userGroup = JSON.stringify([]);
    }

    const response = await apiClient.get(
      `/btin/user-bulletins/${props.Userid}`,
      { params }
    );

    if (response.data.success) {
      bulletins.value = response.data.data.map((bulletin) => ({
        ...bulletin,
        dep: bulletin.dep.trim(),
        dep_name: bulletin.dep_name?.trim() || "",
      }));

      // 載入公告的閱讀狀態
      await loadReadStatus();

      // 檢查 localStorage 是否有篩選條件
      const storedFilter = localStorage.getItem("bulletin_filter");
      if (storedFilter && typeof storedFilter === "string") {
        setFilter(storedFilter);
        // 使用後清除，避免影響下次打開
        localStorage.removeItem("bulletin_filter");
      } else {
        // 檢查 URL 參數是否有篩選條件
        const urlParams = new URLSearchParams(window.location.search);
        const filterParam = urlParams.get("filter");
        if (filterParam && typeof filterParam === "string") {
          setFilter(filterParam);
        }
      }

      // 計算未讀和必讀數量
      await calculateCounts();
    } else {
      $q.notify({
        type: "negative",
        message: response.data.message || "獲取公告失敗",
      });
    }
  } catch (error) {
    console.error(
      "詳細錯誤信息:",
      error.response ? error.response.data : error.message
    );
    $q.notify({
      type: "negative",
      message: "獲取公告列表失敗",
    });
  } finally {
    loading.table = false;
  }
};

// 載入閱讀狀態
const loadReadStatus = async () => {
  try {
    const response = await apiClient.get(`/btin/read-status/${props.Userid}`);
    if (response.data.success) {
      // 將閱讀狀態轉換為以公告ID為鍵的對象
      const statusMap = {};
      const confirmedMap = {};

      response.data.data.forEach((item) => {
        statusMap[item.id] = true;
        confirmedMap[item.id] = item.acknowledged === 1;
      });

      readStatus.value = statusMap;
      confirmedReadStatus.value = confirmedMap;

      // 計算未讀數量
      await calculateCounts();
    }
  } catch (error) {
    console.error("載入閱讀狀態錯誤:", error);
  }
};

// 計算未讀和必讀公告數量
const calculateCounts = async () => {
  try {
    // 獲取用戶真正需要閱讀的公告列表（與閱讀統計中的邏輯一致）
    const params = {
      userDep: props.Did,
      userGroup: Array.isArray(props.Permissions)
        ? JSON.stringify(props.Permissions)
        : props.Permissions,
      userBranches:
        props.UserBranch.length > 0
          ? JSON.stringify(props.UserBranch)
          : undefined,
    };

    // 獲取用戶真正需要閱讀的公告ID列表
    const unreadResponse = await apiClient.get(
      `/btin/unread-bulletins/${props.Userid}`,
      { params }
    );

    if (unreadResponse.data.success) {
      const userBulletinIds = new Set(
        unreadResponse.data.data.map((b) => b.id)
      );

      // 計算未讀數量 - 只計算用戶真正需要閱讀的公告
      unreadCount.value = bulletins.value.filter(
        (bulletin) => userBulletinIds.has(bulletin.id) && !isRead(bulletin)
      ).length;

      // 計算未確認的必讀公告數量 - 只計算用戶真正需要閱讀的公告
      requiredCount.value = bulletins.value.filter(
        (bulletin) =>
          userBulletinIds.has(bulletin.id) &&
          bulletin.required === 1 &&
          !isConfirmedRead(bulletin)
      ).length;
    } else {
      // 如果API調用失敗，使用原來的邏輯作為備用
      unreadCount.value = bulletins.value.filter(
        (bulletin) => !isRead(bulletin)
      ).length;

      requiredCount.value = bulletins.value.filter(
        (bulletin) => bulletin.required === 1 && !isConfirmedRead(bulletin)
      ).length;
    }
  } catch (error) {
    console.error("計算未讀數量錯誤:", error);
    // 如果API調用失敗，使用原來的邏輯作為備用
    unreadCount.value = bulletins.value.filter(
      (bulletin) => !isRead(bulletin)
    ).length;

    requiredCount.value = bulletins.value.filter(
      (bulletin) => bulletin.required === 1 && !isConfirmedRead(bulletin)
    ).length;
  }
};

// 檢查公告是否已讀
const isRead = (bulletin) => {
  return readStatus.value[bulletin.id] === true;
};

// 檢查公告是否已確認閱讀
const isConfirmedRead = (bulletin) => {
  return confirmedReadStatus.value[bulletin.id] === true;
};

// 標記公告為已讀
const markAsRead = async (bulletin) => {
  // 如果已經標記為已讀，則不需要再次標記
  if (isRead(bulletin)) return;

  try {
    const response = await apiClient.post(
      `/btin/bulletins/${bulletin.id}/read`,
      {
        user_id: props.Userid,
        userDep: props.Did, // 添加用戶部門信息
        acknowledged: 0, // 初始標記為已讀但未確認
      }
    );

    if (response.data.success) {
      // 更新本地閱讀狀態
      readStatus.value = {
        ...readStatus.value,
        [bulletin.id]: true,
      };

      // 更新當前會話的閱讀狀態
      sessionReadStatus.value = {
        ...sessionReadStatus.value,
        [bulletin.id]: true,
      };

      // 重新計算數量
      await calculateCounts();

      // 觸發全局事件，通知NotificationCenter更新
      window.dispatchEvent(new CustomEvent("bulletin-read"));
    }
  } catch (error) {
    console.error("標記公告為已讀錯誤:", error);
  }
};

// 確認閱讀公告
const confirmRead = async (bulletin) => {
  try {
    const response = await apiClient.post(
      `/btin/bulletins/${bulletin.id}/confirm`,
      {
        user_id: props.Userid,
        userDep: props.Did, // 添加用戶部門信息
        acknowledged: 1, // 標記為已確認閱讀
      }
    );

    if (response.data.success) {
      // 更新本地確認閱讀狀態
      confirmedReadStatus.value = {
        ...confirmedReadStatus.value,
        [bulletin.id]: true,
      };

      // 更新當前會話的確認閱讀狀態
      sessionConfirmedReadStatus.value = {
        ...sessionConfirmedReadStatus.value,
        [bulletin.id]: true,
      };

      // 重新計算數量
      await calculateCounts();

      // 觸發全局事件，通知NotificationCenter更新
      window.dispatchEvent(new CustomEvent("bulletin-read"));

      // 顯示成功提示
      $q.notify({
        type: "positive",
        message: "已確認閱讀",
      });
    }
  } catch (error) {
    console.error("確認閱讀公告錯誤:", error);
  }
};

// 打開搜尋對話框
const openSearchDialog = () => {
  searchDialog.show = true;
};

const clearFilter = () => {
  // 重置所有搜尋條件
  searchForm.title = "";
  searchForm.dep = "";
  searchForm.startDate = "";
  searchForm.priority = null;
  searchForm.required = null;
  searchForm.endDate = "";

  // 重新載入公告列表
  loadBulletins();
};

const searchBulletins = async () => {
  try {
    loading.search = true;
    loading.table = true;

    const params = {
      // 添加用戶資訊
      userDep: props.Did,
      userGroup: props.Permissions,
      userBranches: props.UserBranch
        ? JSON.stringify(props.UserBranch)
        : undefined,
    };

    // 處理標題篩選條件
    if (searchForm.title && searchForm.title.trim() !== "") {
      params.title = searchForm.title.trim();
    }

    // 處理部門篩選條件
    if (searchForm.dep) {
      params.dep = searchForm.dep;
    }

    // 處理發布時間篩選條件
    if (searchForm.startDate && searchForm.startDate.trim() !== "") {
      params.startDate = searchForm.startDate.trim();
    }

    const response = await apiClient.get(
      `/btin/user-bulletins/${props.Userid}/search`,
      {
        params,
      }
    );

    if (response.data.success) {
      bulletins.value = response.data.data;
      // 重新計算數量
      await calculateCounts();
    } else {
      $q.notify({
        type: "negative",
        message: response.data.message || "搜尋公告失敗",
      });
    }
  } catch (error) {
    console.error("搜尋公告錯誤:", error);
    $q.notify({
      type: "negative",
      message: "搜尋公告失敗",
    });
  } finally {
    loading.search = false;
    loading.table = false;
  }
};

// 檢查用戶權限
const checkUserRole = async () => {
  try {
    // 載入部門選項
    await loadDepartments();
  } catch (error) {
    console.error("檢查用戶權限錯誤:", error);
  }
};

// 載入部門選項
const loadDepartments = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/deps/get_departments`);
    if (response.data && Array.isArray(response.data)) {
      depOptions.value = response.data.map((dep) => ({
        label: dep.Name.trim(),
        value: dep.Id.trim(),
      }));
    }
  } catch (error) {
    console.error("載入部門選項錯誤:", error);
    depOptions.value = [];
  }
};

// 部門名稱轉換函數
const getDepartmentName = (depCode) => {
  // 在部門選項中查找匹配的部門
  const dep = depOptions.value.find((dep) => dep.value === depCode.trim());

  // 如果找到匹配的部門，返回其名稱，否則返回原始代碼
  return dep ? dep.label : depCode;
};

// 附件相關
// 下載附件
const downloadAttachment = (attachment) => {
  const fileUrl = `${apiBaseUrl}/uploads/${attachment.file_path}`;
  const link = document.createElement("a");
  link.href = fileUrl;
  link.setAttribute(
    "download",
    `附件${attachment.sno}.${attachment.file_type || "pdf"}`
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 預覽附件
const previewAttachment = (attachment) => {
  previewDialog.attachment = attachment;
  previewDialog.loading = true;
  previewDialog.error = null;
  previewDialog.show = true;

  const fileUrl = `${apiBaseUrl}/uploads/${attachment.file_path}`;
  previewDialog.url = fileUrl;

  // 檢查檔案類型，確定是否可以在瀏覽器中預覽
  const fileType = attachment.file_type
    ? attachment.file_type.toLowerCase()
    : "";
  const previewableTypes = ["pdf", "jpg", "jpeg", "png", "gif"];

  if (!previewableTypes.includes(fileType)) {
    previewDialog.error = `無法預覽此類型的檔案 (${
      fileType || "未知"
    })，請下載後查看`;
    previewDialog.loading = false;
    return;
  }

  // 使用 Image 物件檢查圖片是否可以載入
  if (["jpg", "jpeg", "png", "gif"].includes(fileType)) {
    const img = new Image();
    img.onload = () => {
      previewDialog.loading = false;
    };
    img.onerror = () => {
      previewDialog.error = "無法載入圖片，請下載後查看";
      previewDialog.loading = false;
    };
    img.src = fileUrl;
  } else {
    // PDF 或其他類型，直接顯示
    setTimeout(() => {
      previewDialog.loading = false;
    }, 1000);
  }
};

// 搜尋用優先等級反轉值
const searchPriorityRating = computed({
  get: () => (searchForm.priority ? 6 - searchForm.priority : null),
  set: (val) => {
    searchForm.priority = val ? 6 - val : null;
  },
});

// 篩選公告
const filterBulletins = () => {
  // 篩選邏輯已在 computed 屬性中實現
  // 在點擊統計卡片時會設置 filterStatus，然後通過 computed 屬性自動過濾
};

// 監聽篩選事件
const setupFilterListener = () => {
  // 監聽篩選事件
  const handleFilterEvent = (event) => {
    if (
      event.detail &&
      event.detail.filter &&
      typeof event.detail.filter === "string"
    ) {
      setFilter(event.detail.filter);
    }
  };
  window.addEventListener("filter-bulletins", handleFilterEvent);
  return () => {
    window.removeEventListener("filter-bulletins", handleFilterEvent);
  };
};

// 生命週期鉤子
onMounted(() => {
  // 初始化會話閱讀狀態
  sessionReadStatus.value = {};
  sessionConfirmedReadStatus.value = {};

  // 設置篩選事件監聽
  const cleanupFilterListener = setupFilterListener();

  // 載入公告列表
  loadBulletins();

  // 檢查用戶權限
  checkUserRole();

  // 組件卸載時清理事件監聽
  onBeforeUnmount(() => {
    cleanupFilterListener();
    // 清除會話閱讀狀態
    sessionReadStatus.value = {};
    sessionConfirmedReadStatus.value = {};
  });
});

// 全螢幕預覽
const fullscreenDialog = reactive({
  show: false,
  bulletin: null,
});

const openFullscreen = (bulletin) => {
  fullscreenDialog.bulletin = bulletin;
  fullscreenDialog.show = true;
  // 標記公告為已讀
  markAsRead(bulletin);
};

// 添加檔案類型相關的輔助函數
// 獲取檔案圖示
const getFileIcon = (fileType) => {
  if (!fileType) return "insert_drive_file";

  fileType = fileType.toLowerCase();

  if (["jpg", "jpeg", "png", "gif", "bmp"].includes(fileType)) {
    return "image";
  } else if (["pdf"].includes(fileType)) {
    return "picture_as_pdf";
  } else if (["doc", "docx"].includes(fileType)) {
    return "description";
  } else if (["xls", "xlsx"].includes(fileType)) {
    return "table_chart";
  } else if (["ppt", "pptx"].includes(fileType)) {
    return "slideshow";
  } else if (["zip", "rar", "7z"].includes(fileType)) {
    return "folder_zip";
  } else if (["txt", "csv"].includes(fileType)) {
    return "text_snippet";
  }

  return "insert_drive_file";
};

// 獲取檔案顏色
const getFileColor = (fileType) => {
  if (!fileType) return "grey";

  fileType = fileType.toLowerCase();

  if (["jpg", "jpeg", "png", "gif", "bmp"].includes(fileType)) {
    return "deep-purple";
  } else if (["pdf"].includes(fileType)) {
    return "red";
  } else if (["doc", "docx"].includes(fileType)) {
    return "blue";
  } else if (["xls", "xlsx"].includes(fileType)) {
    return "green";
  } else if (["ppt", "pptx"].includes(fileType)) {
    return "orange";
  } else if (["zip", "rar", "7z"].includes(fileType)) {
    return "brown";
  } else if (["txt", "csv"].includes(fileType)) {
    return "teal";
  }

  return "grey";
};

// 獲取檔案類型名稱
const getFileTypeName = (fileType) => {
  if (!fileType) return "未知檔案";

  fileType = fileType.toLowerCase();

  const typeMap = {
    jpg: "JPG 圖片",
    jpeg: "JPEG 圖片",
    png: "PNG 圖片",
    gif: "GIF 圖片",
    bmp: "BMP 圖片",
    pdf: "PDF 文件",
    doc: "Word 文件",
    docx: "Word 文件",
    xls: "Excel 表格",
    xlsx: "Excel 表格",
    ppt: "PowerPoint 簡報",
    pptx: "PowerPoint 簡報",
    zip: "壓縮檔案",
    rar: "壓縮檔案",
    "7z": "壓縮檔案",
    txt: "文字檔案",
    csv: "CSV 表格",
  };

  return typeMap[fileType] || `${fileType.toUpperCase()} 檔案`;
};
</script>

<style scoped>
.unread-bulletin {
  border-left: 4px solid #1976d2 !important;
}

.content-container {
  overflow-wrap: break-word;
  word-wrap: break-word;
  max-height: 500px;
  overflow-y: auto;
}

.content-container::-webkit-scrollbar {
  width: 6px;
}

.content-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* 保留内容中的文本对齐方式 */
.content-container :deep(p) {
  text-align: inherit;
  margin: 0.5em 0;
}

.content-container :deep(img) {
  max-width: 100%;
  height: auto;
  /* 修改：移除 display: block 属性，保留图片的对齐方式 */
  margin: 0 auto; /* 当段落设置为居中时，这会辅助居中效果 */
}

/* 确保段落内的样式能正确显示 */
.content-container :deep([style*="text-align: center"]) {
  text-align: center !important;
}

.content-container :deep([style*="text-align: right"]) {
  text-align: right !important;
}

.content-container :deep([style*="text-align: left"]) {
  text-align: left !important;
}

.content-container :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
}

.content-container :deep(th),
.content-container :deep(td) {
  border: 1px solid #ddd;
  padding: 8px;
}

.content-container :deep(pre) {
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

/* 公告統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-card:hover::after {
  opacity: 1;
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-svg {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.unread-card::before {
  background-color: #1976d2;
}

.unread-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.required-card::before {
  background-color: #ff9800;
}

.required-card .stats-icon {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.all-card::before {
  background-color: #4caf50;
}

.all-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.dep-card::before {
  background-color: #9c27b0;
}

.dep-card .stats-icon {
  color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 重新整理按鈕樣式 */
.refresh-button-container {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1;
}

.refresh-button {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.refresh-spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: loader-rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 150, 200;
  stroke-dashoffset: -10;
  stroke-linecap: round;
  animation: loader-dash 1.5s ease-in-out infinite;
}

.loader-message {
  margin-top: 12px;
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -125;
  }
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  fill: #e0e0e0;
  margin-bottom: 12px;
}

.empty-text {
  color: #757575;
  font-size: 14px;
  font-weight: 500;
}

/* 響應式調整 */
@media (max-width: 599px) {
  .title-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .q-card {
    border-radius: 8px;
  }

  .stats-card {
    padding: 8px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-svg {
    width: 20px;
    height: 20px;
  }

  .stats-value {
    font-size: 16px;
    margin-bottom: 1px;
  }

  .stats-label {
    font-size: 10px;
  }

  .pagination-controls {
    transform: scale(0.9);
  }
}

/* 新增的樣式 */
.bulletin-list {
  margin: 0;
  padding: 0;
}

.bulletin-header {
  min-height: auto;
  padding: 0;
}

.bulletin-badge {
  font-size: 10px;
  padding: 0 4px;
  height: 18px;
}

.bulletin-title {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  padding: 0 2px;
}

.bulletin-meta {
  font-size: 11px;
}

.attachment-link {
  font-size: 12px;
}

.filter-select {
  font-size: 13px;
}

/* 載入動畫樣式 */
.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.fancy-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader-svg {
  width: 50px;
  height: 50px;
  animation: loader-rotate 2s linear infinite;
}

.loader-circle {
  fill: none;
  stroke: #1976d2;
  stroke-width: 5;
  stroke-dasharray: 150, 200;
  stroke-dashoffset: -10;
  stroke-linecap: round;
  animation: loader-dash 1.5s ease-in-out infinite;
}

.loader-message {
  margin-top: 12px;
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  fill: #e0e0e0;
  margin-bottom: 12px;
}

.empty-text {
  color: #757575;
  font-size: 14px;
  font-weight: 500;
}

/* 超小螢幕響應式調整 */
@media (max-width: 340px) {
  .stats-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }

  .stats-svg {
    width: 16px;
    height: 16px;
  }

  .stats-value {
    font-size: 14px;
  }

  .stats-label {
    font-size: 9px;
  }

  .bulletin-title {
    font-size: 12px;
  }

  .bulletin-meta {
    font-size: 10px;
  }
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }
}

/* 移除原有的分頁容器樣式 */
.bulletin-list-container {
  position: relative;
}

@media (max-width: 599px) {
  .bulletin-list-container {
    padding-top: 0;
  }
}

/* 標題區塊樣式 */
.title-section {
  padding-left: 16px;
  padding-right: 16px;
}

/* 搜尋按鈕樣式 */
.search-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 全螢幕按鈕樣式 */

/* 全螢幕對話框樣式 */
.full-height {
  height: 100%;
}

.full-height .q-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.full-height .q-card-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.full-height .scroll {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.full-height .q-card-section:last-child {
  flex-shrink: 0;
}

/* 全螢幕對話框過渡動畫 */
.q-dialog--fullscreen.q-dialog--maximized .q-dialog__inner--maximized {
  transition: transform 0.3s ease-out;
}

.q-dialog--fullscreen.q-dialog--maximized.q-dialog--hide
  .q-dialog__inner--maximized {
  transform: scale(0.9);
}

.q-dialog--fullscreen.q-dialog--maximized.q-dialog--show
  .q-dialog__inner--maximized {
  transform: scale(1);
}

/* 附件列表樣式 */
.attachment-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.attachment-card:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.attachment-item {
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background-color: rgba(0, 0, 0, 0.01);
}

.attachment-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
  border-color: #e0e0e0;
}

.attachment-link {
  font-weight: 500;
  text-decoration: none !important;
  transition: color 0.2s ease;
}

.attachment-link:hover {
  color: #1565c0;
  text-decoration: underline !important;
}

.download-btn {
  opacity: 0.8;
  transition: all 0.2s ease;
}

.download-btn:hover {
  opacity: 1;
  transform: translateY(-2px);
}

/* 標題和統計卡片容器樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 8px;
}

/* 為標題和統計卡片容器中的卡片添加陰影和背景 */
.title-stats-container .q-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
