const express = require("express");
const {
  getKclass,
  createKclass,
  updateKclass,
  deleteKclass,
  getAvailableVclass,
} = require("../controllers/o_kclassController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 所有路由都需要認證
router.use(authMiddleware);

// 讀取廚房部門列表
router.get("/get_Kclass", getKclass);

// 新增廚房部門
router.post("/create_Kclass", createKclass);

// 更新廚房部門
router.put("/update_Kclass", updateKclass);

// 刪除廚房部門
router.delete("/delete_Kclass/:code", deleteKclass);

// 取得可用的出據主機列表
router.get("/get_AvailableVclass", getAvailableVclass);

module.exports = router;
