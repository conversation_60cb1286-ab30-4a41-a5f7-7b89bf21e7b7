import { ref, computed, watch } from "vue";
import { useQuasar } from "quasar";
import apiClient from "../api";

export function useBranchAuthorization() {
  const $q = useQuasar();

  // 響應式數據
  const branchOptions = ref([]);
  const authorizedBranches = ref([]);
  const isSavingBranches = ref(false);
  const newBranch = ref(null);
  const isLoadingBranches = ref(false);

  // 計算屬性
  const displayValue = computed(() =>
    authorizedBranches.value.length
      ? `${authorizedBranches.value.length} 個門市已選`
      : "未選擇"
  );

  // 顯示數據 - 轉換成卡片顯示需要的格式
  const branchDisplayData = computed(() => {
    if (!branchOptions.value.length || !authorizedBranches.value.length) {
      return [];
    }

    return authorizedBranches.value.map((branchCode) => {
      const branch = branchOptions.value.find(
        (opt) => opt.value === branchCode
      );

      // 確保返回的是簡單的字符串值
      return {
        value: branchCode,
        label: branch ? branch.label : branchCode,
      };
    });
  });

  // 過濾後的門市選項 - 移除已選中的
  const filteredBranchOptions = computed(() => {
    if (!branchOptions.value.length) return [];

    const selectedSet = new Set(authorizedBranches.value);
    return branchOptions.value.filter(
      (branch) => !selectedSet.has(branch.value)
    );
  });

  // API 函數
  const fetchBranches = async () => {
    try {
      isLoadingBranches.value = true;
      const response = await apiClient.get("/branch/get_branch");
      branchOptions.value = response.data
        .filter((branch) => branch.Sts === "1")
        .map((branch) => ({
          value: branch.Cod_cust.trim(),
          label: branch.Cod_name.trim(),
        }));
    } catch (error) {
      console.error("獲取門市資料失敗:", error);
      $q.notify({ type: "negative", message: "載入門市失敗" });
    } finally {
      isLoadingBranches.value = false;
    }
  };

  const fetchUserBranch = async (userId) => {
    if (!userId) return;

    try {
      const response = await apiClient.get("/users/get_userBranch", {
        params: { userId },
      });

      if (response.data.success) {
        authorizedBranches.value = response.data.branches.map((b) =>
          b.Cod_Cust.trim()
        );
      }
    } catch (error) {
      console.error("授權門市載入失敗:", error);
      $q.notify({ type: "negative", message: "載入授權門市失敗" });
    }
  };

  const saveAuthorizedBranches = async (userId) => {
    try {
      isSavingBranches.value = true;
      const payload = {
        userId,
        branches: authorizedBranches.value,
      };

      const response = await apiClient.post("/users/save_UserBranch", payload);

      if (response.data.success) {
        // 移除成功提示
        return true;
      } else {
        $q.notify({ type: "negative", message: "儲存失敗" });
        return false;
      }
    } catch (error) {
      console.error("儲存授權門市失敗:", error);
      $q.notify({ type: "negative", message: "儲存錯誤" });
      return false;
    } finally {
      isSavingBranches.value = false;
    }
  };

  const resetAuthorizedBranches = () => {
    authorizedBranches.value = [];
    newBranch.value = null;
  };

  // 新增單個門市
  const addBranch = (branch) => {
    if (!branch) return false;

    // 確保我們只存儲門市代碼
    const branchCode = typeof branch === "object" ? branch.value : branch;

    if (branchCode && !authorizedBranches.value.includes(branchCode)) {
      authorizedBranches.value.push(branchCode);
      newBranch.value = null;
      return true;
    }
    return false;
  };

  // 移除單個門市
  const removeBranch = (branchCode) => {
    const index = authorizedBranches.value.indexOf(branchCode);
    if (index !== -1) {
      authorizedBranches.value.splice(index, 1);
      return true;
    }
    return false;
  };

  return {
    // 響應式數據
    branchOptions,
    authorizedBranches,
    isSavingBranches,
    newBranch,
    isLoadingBranches,

    // 計算屬性
    displayValue,
    branchDisplayData,
    filteredBranchOptions,

    // API 函數
    fetchBranches,
    fetchUserBranch,
    saveAuthorizedBranches,
    resetAuthorizedBranches,
    addBranch,
    removeBranch,
  };
}
