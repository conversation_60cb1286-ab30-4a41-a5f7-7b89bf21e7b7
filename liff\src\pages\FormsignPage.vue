<template>
  <q-page
    class="q-pa-sm"
    style="width: 100%; max-width: 1000px; margin: 0 auto"
  >
    <!-- 標題和統計卡片容器 -->
    <div class="title-stats-container">
      <q-card
        class="q-pa-xs no-shadow"
        style="width: 100%; border-radius: 10px"
      >
        <!-- 標題 -->
        <q-card-section class="title-section q-py-xs">
          <div class="row items-center justify-between">
            <div class="text-subtitle1 text-primary">待簽核單據</div>
            <q-btn
              flat
              round
              size="sm"
              color="primary"
              class="search-button"
              @click="openAdvancedSearch"
            >
              <Search class="w-4 h-4" />
              <q-tooltip>進階搜尋</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>

        <!-- 搜尋輸入框 -->
        <q-card-section class="q-pt-none">
          <q-input
            v-model="queryKeyword"
            placeholder="輸入單號或關鍵字搜尋"
            outlined
            dense
            clearable
            class="search-input"
            @update:model-value="handleQueryChange"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
        </q-card-section>

        <!-- 統計卡片區域 -->
        <q-card-section class="q-py-none">
          <div class="stats-container">
            <div class="row q-col-gutter-sm justify-center">
              <!-- 全部單據統計卡片 -->
              <div class="col-6">
                <div
                  class="stats-card all-card"
                  :class="{ 'active-filter': activeTab === 'all' }"
                  @click="activeTab = 'all'"
                >
                  <div class="stats-icon">
                    <Files :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ pendingRequests.length }}</div>
                    <div class="stats-label">待簽核單據</div>
                  </div>
                </div>
              </div>

              <!-- 待處理統計卡片 -->
              <div class="col-6">
                <div
                  class="stats-card pending-card"
                  :class="{ 'active-filter': activeTab === 'pending' }"
                  @click="activeTab = 'pending'"
                >
                  <div class="stats-icon">
                    <Clock :stroke-width="1" :size="22" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-value">{{ urgentRequests.length }}</div>
                    <div class="stats-label">緊急待簽</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 單據列表卡片 -->
    <q-card
      class="q-pa-xs no-shadow q-mt-sm"
      style="width: 100%; border-radius: 10px"
    >
      <q-card-section class="q-pa-xs">
        <!-- 無資料狀態 -->
        <div v-if="filteredRequests.length === 0" class="empty-state-container">
          <div class="empty-state">
            <svg class="empty-icon" viewBox="0 0 24 24">
              <path
                d="M20,2H4C2.9,2,2,2.9,2,4v18l4-4h14c1.1,0,2-0.9,2-2V4C22,2.9,21.1,2,20,2z M20,16H5.2L4,17.2V4h16V16z"
              />
              <path d="M11,12h2v2h-2V12z" />
              <path d="M11,6h2v4h-2V6z" />
            </svg>
            <div class="empty-text">
              {{ queryKeyword ? "沒有找到符合的單據" : "目前沒有待簽核單據" }}
            </div>
          </div>
        </div>

        <!-- 單據列表 -->
        <div v-else class="request-list-container">
          <!-- 分頁控制器 -->
          <div class="row justify-end q-mb-sm">
            <q-pagination
              v-model="currentPage"
              :max="totalPages"
              :max-pages="5"
              boundary-numbers
              direction-links
              color="primary"
              active-color="blue"
              active-text-color="white"
              class="pagination-controls"
              size="sm"
            />
          </div>

          <q-list separator class="request-list">
            <q-item
              v-for="request in paginatedRequests"
              :key="request.Form_id"
              class="bg-white q-mb-xs request-item"
              :class="{
                'urgent-request': request.is_urgent,
                'pending-request':
                  !request.is_urgent && request.fstatus === 'pending',
              }"
              clickable
              @click="openDetails(request)"
            >
              <!-- 左側內容 -->
              <q-item-section>
                <div class="row items-center q-mb-xs">
                  <div
                    class="text-indigo-10 text-weight-medium request-title q-mb-xs"
                  >
                    {{ request.submitter }} - {{ request.doc_name }}
                  </div>
                  <q-badge
                    v-if="request.is_urgent"
                    color="negative"
                    class="q-ml-sm"
                    label="緊急"
                  />
                </div>
                <div class="text-caption text-grey-8 request-meta">
                  <div class="row items-center q-mt-xs">
                    <hash :stroke-width="1" :size="16" class="q-mr-xs" />
                    {{ request.Form_id }}
                  </div>
                  <div class="row items-center q-mt-xs">
                    <calendar-clock
                      :stroke-width="1"
                      :size="16"
                      class="q-mr-xs"
                    />
                    {{ request.submit_date }}
                  </div>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-card-section>
    </q-card>

    <!-- 進階搜尋對話框 -->
    <q-dialog v-model="advancedSearchDialog">
      <q-card style="width: 500px; max-width: 90vw">
        <q-card-section class="row items-center bg-primary text-white q-pb-xs">
          <div class="text-subtitle1">
            <q-icon name="search" class="q-mr-xs" size="sm" />
            進階搜尋
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-sm">
          <q-form>
            <!-- 日期區間 -->
            <q-input
              v-model="formattedDate"
              label="選擇日期區間"
              outlined
              readonly
              dense
              class="full-width"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="dateRange" range mask="YYYY-MM-DD">
                      <div
                        class="row items-center justify-end q-gutter-sm q-pa-sm"
                      >
                        <q-btn
                          label="清除"
                          color="grey"
                          flat
                          @click="clearDateRange"
                        />
                        <q-btn
                          label="確定"
                          color="primary"
                          flat
                          v-close-popup
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>

            <!-- 送單人員 -->
            <q-select
              v-model="searchForm.submitter"
              :options="submitterOptions"
              label="送單人員"
              outlined
              dense
              class="q-mt-md"
              use-input
              hide-selected
              fill-input
              input-debounce="300"
              @filter="filterSubmitters"
            />

            <!-- 表單類型 -->
            <q-select
              v-model="searchForm.formType"
              :options="formTypeOptions"
              label="表單類型"
              outlined
              dense
              class="q-mt-md"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            label="清除條件"
            color="red"
            flat
            @click="clearSearchFilters"
          />
          <q-btn
            label="取消"
            color="grey"
            flat
            @click="advancedSearchDialog = false"
          />
          <q-btn label="查詢" color="primary" @click="fetchRequests" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 簽核明細 Dialog -->
    <q-dialog v-model="detailsDialog" maximized>
      <q-layout view="hHh LpR fFf" container class="bg-white">
        <q-header elevated class="bg-primary text-white" style="width: 100%">
          <q-toolbar class="q-px-md">
            <q-toolbar-title class="row items-center">
              <q-icon name="description" size="sm" class="q-mr-sm" />
              <span class="text-subtitle1 text-weight-medium">簽核明細</span>
            </q-toolbar-title>
            <q-btn flat round dense icon="close" v-close-popup />
          </q-toolbar>
        </q-header>

        <q-page-container>
          <q-page padding>
            <div class="row q-col-gutter-md">
              <!-- 左側內容 -->
              <div class="col-12 col-md-8">
                <!-- 簽核進度 -->
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-subtitle1 text-weight-bold q-mb-md">
                      <q-icon name="timeline" class="q-mr-sm" />
                      簽核進度
                    </div>
                    <div class="approval-steps">
                      <div
                        v-for="(step, index) in approvalSteps"
                        :key="index"
                        class="step-item"
                        :class="{
                          'step-done': step.done,
                          'step-current': currentStep === index + 1,
                          'step-waiting':
                            !step.done && currentStep !== index + 1,
                        }"
                      >
                        <div class="step-dot">
                          <q-icon :name="step.icon" size="16px" />
                        </div>
                        <div class="step-label">{{ step.title }}</div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>

                <!-- 單據內容 -->
                <q-card flat bordered class="q-mt-md">
                  <q-card-section>
                    <div class="text-subtitle1 text-weight-bold q-mb-md">
                      <q-icon name="article" class="q-mr-sm" />
                      單據內容
                    </div>
                    <div class="row q-col-gutter-md">
                      <template v-if="Object.keys(formattedDetails).length">
                        <div
                          v-for="(value, key) in formattedDetails"
                          :key="key"
                          class="col-12"
                        >
                          <div
                            class="row items-center q-py-sm"
                            style="border-bottom: 1px solid #f0f0f0"
                          >
                            <div class="col-4 text-grey-8">{{ key }}</div>
                            <div class="col-8">{{ value }}</div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </q-card-section>
                </q-card>

                <!-- 附件 -->
                <q-card-section v-if="attachments.length" class="q-pt-md">
                  <div class="text-subtitle2 text-primary">附件：</div>
                  <q-list
                    bordered
                    separator
                    style="max-height: 150px; overflow-y: auto"
                  >
                    <q-item v-for="(file, idx) in attachments" :key="file.sno">
                      <q-item-section>
                        <q-item-label>
                          <a
                            :href="file.file_url"
                            target="_blank"
                            class="text-blue"
                          >
                            附件{{ idx + 1 }}.{{
                              file.file_url
                                ? file.file_url.split(".").pop()
                                : ""
                            }}
                          </a>
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-card-section>
              </div>

              <!-- 右側內容 -->
              <div class="col-12 col-md-4">
                <!-- 簽核意見 -->
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-subtitle1 text-weight-bold q-mb-md">
                      <q-icon name="rate_review" class="q-mr-sm" />
                      簽核意見
                    </div>
                    <q-input
                      v-model="approvalComment"
                      outlined
                      type="textarea"
                      rows="4"
                      placeholder="請輸入簽核意見..."
                      :rules="[
                        (val) => val.length <= 100 || '意見不能超過100字',
                      ]"
                      class="q-mb-md"
                    />
                    <div class="row q-gutter-sm justify-center">
                      <q-btn
                        unelevated
                        color="negative"
                        icon-right="cancel"
                        label="駁回"
                        @click="openStatusDialog(false)"
                        class="col"
                      />
                      <q-btn
                        unelevated
                        color="positive"
                        icon-right="check_circle"
                        label="簽核"
                        @click="openStatusDialog(true)"
                        class="col"
                      />
                    </div>
                  </q-card-section>
                </q-card>

                <!-- 簽核歷程 -->
                <q-card flat bordered class="q-mt-md">
                  <q-card-section>
                    <div class="text-subtitle1 text-weight-bold q-mb-md">
                      <q-icon name="history" class="q-mr-sm" />
                      簽核歷程
                    </div>
                    <div class="approval-timeline">
                      <div
                        v-for="(history, index) in approvalHistory"
                        :key="index"
                        class="timeline-item q-mb-md"
                      >
                        <div class="row items-start no-wrap">
                          <div class="timeline-icon">
                            <q-avatar
                              :color="getStatusColor(history.Status)"
                              text-color="white"
                              size="32px"
                            >
                              <q-icon
                                :name="getStatusIcon(history.Status)"
                                size="20px"
                              />
                            </q-avatar>
                          </div>
                          <div class="timeline-content q-ml-md col">
                            <div class="text-weight-medium">
                              {{ history.Remark }}
                            </div>
                            <div class="text-caption text-grey-8">
                              {{ history.Approver }}
                              <q-badge
                                :color="getStatusColor(history.Status)"
                                class="q-ml-sm"
                                outline
                              >
                                {{ getStatusLabel(history.Status) }}
                              </q-badge>
                            </div>
                            <template
                              v-if="
                                history.Status !== 'waiting' &&
                                history.Status !== '-' &&
                                history.Status !== 'up-reject' &&
                                history.Status !== 'cancel'
                              "
                            >
                              <div class="text-caption text-grey-8 q-mt-xs">
                                {{ history.Approval_time }}
                                <template v-if="history.Comment">
                                  - {{ history.Comment }}
                                </template>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-page>
        </q-page-container>
      </q-layout>
    </q-dialog>

    <!-- 簽核結果 Dialog -->
    <q-dialog v-model="statusDialog">
      <q-card class="text-center" style="min-width: 300px">
        <q-card-section class="row items-center justify-center q-pb-none">
          <q-avatar
            :color="isApproved ? 'positive' : 'warning'"
            text-color="white"
            size="56px"
          >
            <q-icon :name="isApproved ? 'check_circle' : 'error'" size="32px" />
          </q-avatar>
        </q-card-section>

        <q-card-section class="q-pt-md">
          <div class="text-h6">{{ isApproved ? "已簽核" : "已駁回" }}</div>
          <div class="text-grey-8 q-mt-sm">
            單號: {{ selectedRequest?.Form_id }}
          </div>
        </q-card-section>

        <q-card-actions align="center">
          <q-btn flat color="primary" label="確定" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import apiClient from "../api";
import { ref, onMounted, computed, reactive } from "vue";
import { DateTime } from "luxon";
import { usePendingStore } from "../stores/pendingStore";
import { useQuasar } from "quasar";
import { Search, Files, CheckCircle, Clock } from "lucide-vue-next";

const $q = useQuasar();
const props = defineProps({ Userid: String });
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 基本狀態
const pendingRequests = ref([]);
const approvalHistory = ref([]);
const detailsDialog = ref(false);
const statusDialog = ref(false);
const selectedRequest = ref(null);
const attachments = ref([]);
const approvalComment = ref("");
// 移除原本的 step ref
const currentStep = computed(() => {
  if (!approvalHistory.value?.length) return 1;

  const currentStepIndex = approvalHistory.value.findIndex((history) =>
    ["waiting", "-"].includes(history.Status?.trim())
  );

  return currentStepIndex !== -1
    ? currentStepIndex + 1
    : approvalHistory.value.length;
});
const isApproved = ref(false);
const queryKeyword = ref("");
const activeTab = ref("all");
const loading = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(5);

// 進階搜尋相關
const advancedSearchDialog = ref(false);
const dateRange = ref({ from: "", to: "" });
const searchForm = reactive({
  submitter: null,
  formType: null,
});
const submitterOptions = ref([]);
const formTypeOptions = ref([]);

// 表單欄位相關
const visibleFields = ref({});
const detailFieldMapping = ref({});
const detailData = ref({});

// 計算屬性
const urgentRequests = computed(() =>
  pendingRequests.value.filter((r) => r.is_urgent)
);

const filteredRequests = computed(() => {
  let result = [...pendingRequests.value];

  // 只保留標籤篩選中的緊急待簽
  if (activeTab.value === "pending") {
    result = result.filter((r) => r.is_urgent);
  }

  // 關鍵字搜尋
  if (queryKeyword.value) {
    const keyword = queryKeyword.value.toLowerCase();
    result = result.filter(
      (r) =>
        r.Form_id?.toLowerCase().includes(keyword) ||
        r.submitter?.toLowerCase().includes(keyword) ||
        r.doc_name?.toLowerCase().includes(keyword)
    );
  }

  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredRequests.value.length / itemsPerPage.value);
});

const paginatedRequests = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  return filteredRequests.value.slice(start, start + itemsPerPage.value);
});

const formattedDate = computed(() => {
  if (!dateRange.value) return "請選擇日期";
  if (typeof dateRange.value === "string") return dateRange.value;
  if (!dateRange.value.from) return "請選擇日期";
  return dateRange.value.from === dateRange.value.to
    ? dateRange.value.from
    : `${dateRange.value.from} ~ ${dateRange.value.to}`;
});

const formattedDetails = computed(() => {
  if (!detailData.value || !selectedRequest.value?.form_type) {
    return {};
  }

  const formType = selectedRequest.value.form_type.trim();
  const allowedFields = visibleFields.value[formType] || [];

  return Object.fromEntries(
    Object.entries(detailData.value)
      .filter(([key]) => allowedFields.includes(key))
      .map(([key, value]) => [
        detailFieldMapping.value[key] ?? key,
        ["Stime", "Etime"].includes(key)
          ? formatDateTime(value)
          : value?.toString().trim() || "無資料",
      ])
  );
});

// 添加簽核進度計算邏輯
const approvalSteps = computed(() => {
  if (!approvalHistory.value?.length) return [];

  return approvalHistory.value.map((history, index) => {
    const status = history.Status?.trim() || "";
    const isDone = ["approve", "reject"].includes(status);

    return {
      title: history.Remark,
      icon: getStatusIcon(status),
      done: isDone,
      color: getStatusColor(status),
      name: index + 1,
    };
  });
});

// 方法
const formatDateTime = (dateString) => {
  if (!dateString) return "無日期";
  return DateTime.fromISO(dateString, { zone: "utc" })
    .setZone("Asia/Taipei")
    .toFormat("yyyy-MM-dd HH:mm");
};

const UTCformatDateTime = (dateString) => {
  if (!dateString) return "無日期";
  return DateTime.fromISO(dateString, { zone: "utc" })
    .setZone("Asia/Taipei")
    .toFormat("yyyy-MM-dd HH:mm");
};

const getStatusLabel = (status) => {
  switch ((status || "").trim()) {
    case "pending":
      return "進行中";
    case "reject":
      return "已駁回";
    case "approve":
      return "已核准";
    case "cancel":
      return "已撤回";
    case "-":
      return "待上層處理";
    case "waiting":
      return "待處理";
    case "up-reject":
      return "上層已駁回";
    default:
      return "未知狀態";
  }
};

const getStatusColor = (status) => {
  switch ((status || "").trim()) {
    case "pending":
      return "blue";
    case "reject":
      return "negative";
    case "approve":
      return "positive";
    case "cancel":
      return "grey";
    case "waiting":
      return "warning";
    case "-":
      return "grey";
    case "up-reject":
      return "grey";
    default:
      return "grey";
  }
};

const getStatusIcon = (status) => {
  switch ((status || "").trim()) {
    case "approve":
      return "check_circle";
    case "reject":
      return "cancel";
    case "waiting":
      return "hourglass_empty";
    case "-":
      return "more_horiz";
    case "up-reject":
      return "remove_circle_outline";
    default:
      return "help_outline";
  }
};

const handleQueryChange = () => {
  if (!queryKeyword.value || queryKeyword.value.length === 0) {
    fetchRequests();
  }
};

const openAdvancedSearch = async () => {
  await Promise.all([fetchSubmitters(), fetchFormTypes()]);
  advancedSearchDialog.value = true;
};

const fetchSubmitters = async () => {
  try {
    const response = await apiClient.post(
      `${apiBaseUrl}/users/get_enable_users`
    );
    submitterOptions.value = response.data.map((user) => ({
      label: `${user.Name} (${user.ID})`,
      value: user.ID,
    }));
  } catch (error) {
    console.error("載入送單人員失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入送單人員失敗",
      icon: "error",
    });
  }
};

const fetchFormTypes = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/forms/get_form_types`);
    formTypeOptions.value = response.data.map((form) => ({
      label: form.Name,
      value: form.Type,
    }));
  } catch (error) {
    console.error("載入表單類型失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入表單類型失敗",
      icon: "error",
    });
  }
};

const filterSubmitters = (val, update) => {
  if (val === "") {
    update(() => {
      submitterOptions.value = submitterOptions.value;
    });
    return;
  }

  const needle = val.toLowerCase();
  update(() => {
    submitterOptions.value = submitterOptions.value.filter((user) =>
      user.label.toLowerCase().includes(needle)
    );
  });
};

const clearDateRange = () => {
  dateRange.value = { from: "", to: "" };
};

const clearSearchFilters = () => {
  dateRange.value = { from: "", to: "" };
  searchForm.submitter = null;
  searchForm.formType = null;
  queryKeyword.value = "";
  fetchRequests();
  advancedSearchDialog.value = false;
};

// 🔹 查詢待簽核（從後端 API）
const fetchRequests = async () => {
  try {
    loading.value = true;
    const response = await apiClient.post(
      `${apiBaseUrl}/get_pending_requests`,
      { userid: props.Userid }
    );

    pendingRequests.value = response.data.map((request) => ({
      ...request,
      submit_date: UTCformatDateTime(request.submit_date),
    }));
    //console.log("pendingRequests.value", pendingRequests.value);
    // 獲取每個請求的詳細資料以取得附件數量
    const detailsPromises = pendingRequests.value.map((request) =>
      apiClient.post(`${apiBaseUrl}/forms/get_SDetails`, {
        form_id: request.Form_id,
        form_type: request.form_type.trim(),
      })
    );

    const detailsResponses = await Promise.all(detailsPromises);

    // 更新附件數量
    pendingRequests.value = pendingRequests.value.map((request, index) => ({
      ...request,
      attachment: detailsResponses[index].data?.attachment || 0,
    }));

    //console.log("處理後的請求:", pendingRequests.value);
  } catch (error) {
    console.error("❌ 查詢待簽核請假單失敗:", error);
    $q.notify({
      type: "negative",
      message: "查詢失敗",
      icon: "error",
    });
    pendingRequests.value = [];
  } finally {
    loading.value = false;
    advancedSearchDialog.value = false;
  }
};

const isUrgentRequest = (request) => {
  // 判斷是否為緊急單據的邏輯
  const submitDate = DateTime.fromISO(request.submit_date);
  const now = DateTime.now();
  const diffHours = now.diff(submitDate, "hours").hours;

  return diffHours >= 24 || request.priority === "urgent";
};

// 修改 openDetails 函數
const openDetails = async (request) => {
  selectedRequest.value = request;
  approvalComment.value = "";
  detailsDialog.value = true;
  attachments.value = [];
  approvalHistory.value = [];
  detailData.value = {};

  try {
    const [detailResponse, historyResponse] = await Promise.all([
      apiClient.post(`${apiBaseUrl}/forms/get_SDetails`, {
        form_id: request.Form_id,
        form_type: request.form_type.trim(),
      }),
      apiClient.post(`${apiBaseUrl}/forms/get_approval_history`, {
        form_id: request.Form_id,
      }),
    ]);

    detailData.value = detailResponse.data || {};
    approvalHistory.value = historyResponse.data || [];

    // 更新 selectedRequest 的附件數量
    selectedRequest.value = {
      ...selectedRequest.value,
      attachment: detailResponse.data?.attachment || 0,
    };

    // 如果有附件，則獲取附件列表
    if (detailResponse.data?.attachment > 0) {
      const attachmentResponse = await apiClient.post(
        `${apiBaseUrl}/forms/get_attachments`,
        { form_id: request.Form_id }
      );
      attachments.value = attachmentResponse.data || [];
    }
  } catch (error) {
    console.error("❌ 查詢明細失敗:", error);
    $q.notify({
      type: "negative",
      message: "讀取單據詳情失敗",
      icon: "error",
    });
  }
};

const openStatusDialog = async (approved) => {
  try {
    isApproved.value = approved;
    const formId = selectedRequest.value.Form_id;
    const step = selectedRequest.value.step;
    const approverId = props.Userid;
    const memo = approvalComment.value.trim();

    // 如果是駁回且沒有填寫意見，顯示錯誤訊息
    if (!approved && !memo) {
      $q.notify({
        type: "negative",
        message: "駁回時必須填寫原因",
        icon: "error",
        position: "center",
      });
      return;
    }

    await apiClient.post(`${apiBaseUrl}/forms/update-approval-status`, {
      form_id: formId,
      approver_id: approverId,
      status: approved ? "approve" : "reject",
      memo: memo,
      step: step,
    });

    await Promise.all([
      pendingStore.fetchPendingTasks(props.Userid),
      fetchRequests(),
    ]);

    detailsDialog.value = false;
    statusDialog.value = true;

    $q.notify({
      type: "positive",
      message: `單據已${approved ? "簽核" : "駁回"}`,
      icon: approved ? "check_circle" : "cancel",
    });
  } catch (error) {
    console.error("簽核失敗:", error);
    $q.notify({
      type: "negative",
      message: "簽核失敗",
      icon: "error",
    });
  }
};

const loadFormFields = async () => {
  try {
    const response = await apiClient.get(`${apiBaseUrl}/forms/get_form_fields`);
    detailFieldMapping.value = response.data.detailFieldMapping;
    visibleFields.value = response.data.visibleFields;
  } catch (error) {
    console.error("獲取欄位失敗:", error);
    $q.notify({
      type: "negative",
      message: "載入欄位設定失敗",
      icon: "error",
    });
  }
};

// 載入初始資料
const pendingStore = usePendingStore();

onMounted(async () => {
  try {
    await Promise.all([fetchRequests(), loadFormFields()]);
  } catch (error) {
    console.error("初始化失敗:", error);
  }
});
</script>

<style scoped>
.request-meta {
  font-size: 11px;
}
/* 基本樣式 */
.title-stats-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  padding-bottom: 8px;
}

.search-button {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  transition: all 0.3s ease;
}

.search-input:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 統計卡片樣式 */
.stats-container {
  padding: 8px 0;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  background-color: #fff;
  border: 1px solid #eaeaea;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 卡片特定樣式 */
.all-card::before {
  background-color: #1976d2;
}

.all-card .stats-icon {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.pending-card::before {
  background-color: #ff9800;
}

.pending-card .stats-icon {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.approved-card::before {
  background-color: #4caf50;
}

.approved-card .stats-icon {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

/* 活動篩選樣式 */
.active-filter {
  border-color: currentColor;
  background-color: rgba(0, 0, 0, 0.02);
}

.active-filter::before {
  width: 6px;
}

.active-filter .stats-value {
  color: currentColor;
}

/* 單據列表樣式 */
.request-list-container {
  position: relative;
}

.request-list {
  margin: 0;
  padding: 0;
}

.request-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.request-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.urgent-request {
  border-left: 4px solid #f44336;
}

.pending-request {
  border-left: 4px solid #1976d2;
}

/* 空狀態樣式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  fill: #e0e0e0;
  margin-bottom: 12px;
}

.empty-text {
  color: #757575;
  font-size: 14px;
  font-weight: 500;
}

/* 分頁控制樣式 */
.pagination-controls {
  display: flex;
  justify-content: center;
}

@media (max-width: 599px) {
  .pagination-controls {
    transform: scale(0.85);
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 16px;
  }

  .stats-label {
    font-size: 10px;
  }
}

@media (max-width: 340px) {
  .stats-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }

  .stats-value {
    font-size: 14px;
  }

  .stats-label {
    font-size: 9px;
  }
}
</style>

<style lang="scss" scoped>
.approval-timeline {
  position: relative;
  padding: 8px 0;

  .timeline-item {
    position: relative;
    padding: 8px 0;

    &:not(:last-child)::after {
      content: "";
      position: absolute;
      left: 16px;
      top: 40px;
      bottom: -8px;
      width: 2px;
      background: #e0e0e0;
    }
  }

  .timeline-icon {
    position: relative;
    z-index: 1;
  }

  .timeline-content {
    min-height: 40px;
  }
}

.stepper-custom {
  :deep(.q-stepper__tab) {
    padding: 8px 8px;
    min-height: unset;

    .q-stepper__title {
      font-size: 0.875rem;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
    }

    .q-stepper__dot {
      margin-right: 4px;
    }
  }

  @media (max-width: 599px) {
    :deep(.q-stepper__tab) {
      padding: 4px;

      .q-stepper__title {
        font-size: 0.75rem;
        max-width: 60px;
      }

      .q-stepper__dot {
        width: 20px;
        height: 20px;
        margin-right: 2px;

        .q-stepper__dot-holder {
          width: 20px;
          height: 20px;
          font-size: 12px;

          .q-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.approval-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0 12px;

  &::before {
    content: "";
    position: absolute;
    top: 12px;
    left: 24px;
    right: 24px;
    height: 2px;
    background: #e0e0e0;
    z-index: 0;
  }

  .step-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    z-index: 1;

    .step-dot {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: white;
      border: 2px solid #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 4px;
    }

    .step-label {
      font-size: 12px;
      color: #666;
      text-align: center;
      max-width: 80px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.step-done {
      .step-dot {
        background: var(--q-positive);
        border-color: var(--q-positive);
        color: white;
      }
      .step-label {
        color: var(--q-positive);
      }
    }

    &.step-current {
      .step-dot {
        background: var(--q-primary);
        border-color: var(--q-primary);
        color: white;
      }
      .step-label {
        color: var(--q-primary);
        font-weight: 500;
      }
    }

    &.step-waiting {
      .step-dot {
        background: white;
        border-color: #bdbdbd;
        color: #bdbdbd;
      }
      .step-label {
        color: #bdbdbd;
      }
    }
  }
}

@media (max-width: 599px) {
  .approval-steps {
    padding: 0 8px;

    .step-item {
      .step-dot {
        width: 20px;
        height: 20px;
      }

      .step-label {
        font-size: 11px;
        max-width: 60px;
      }
    }

    &::before {
      top: 10px;
      left: 16px;
      right: 16px;
    }
  }
}
</style>
