import{d as Tt,h as sn,e as kn,i as Mn,o as vn,a as Fn,c as Ce,w as ut,F as ln,g as on,b as Un,r as wn,s as Wn,f as Vn,T as xn,j as $n}from"./index.036468ba.js";/*!
  * shared v9.14.2
  * (c) 2024 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */const Ue=typeof window!="undefined",ce=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Yn=(e,t,n)=>Gn({l:e,k:t,s:n}),Gn=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),X=e=>typeof e=="number"&&isFinite(e),Xn=e=>un(e)==="[object Date]",we=e=>un(e)==="[object RegExp]",$e=e=>F(e)&&Object.keys(e).length===0,B=Object.assign,Hn=Object.create,W=(e=null)=>Hn(e);let Pt;const We=()=>Pt||(Pt=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:W());function yt(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Kn=Object.prototype.hasOwnProperty;function z(e,t){return Kn.call(e,t)}const H=Array.isArray,Y=e=>typeof e=="function",C=e=>typeof e=="string",$=e=>typeof e=="boolean",U=e=>e!==null&&typeof e=="object",jn=e=>U(e)&&Y(e.then)&&Y(e.catch),cn=Object.prototype.toString,un=e=>cn.call(e),F=e=>{if(!U(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},Bn=e=>e==null?"":H(e)||F(e)&&e.toString===cn?JSON.stringify(e,null,2):String(e);function Jn(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}function Ye(e){let t=e;return()=>++t}function Qn(e,t){typeof console!="undefined"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ke=e=>!U(e)||H(e);function Fe(e,t){if(ke(e)||ke(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:s}=n.pop();Object.keys(r).forEach(o=>{o!=="__proto__"&&(U(r[o])&&!U(s[o])&&(s[o]=Array.isArray(r[o])?[]:W()),ke(s[o])||ke(r[o])?s[o]=r[o]:n.push({src:r[o],des:s[o]}))})}}/*!
  * message-compiler v9.14.2
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function qn(e,t,n){return{line:e,column:t,offset:n}}function Ve(e,t,n){const r={start:e,end:t};return n!=null&&(r.source=n),r}const Zn=/\{([0-9a-zA-Z]+)\}/g;function fn(e,...t){return t.length===1&&zn(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(Zn,(n,r)=>t.hasOwnProperty(r)?t[r]:"")}const _n=Object.assign,Rt=e=>typeof e=="string",zn=e=>e!==null&&typeof e=="object";function mn(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}const It={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},er={[It.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function tr(e,t,...n){const r=fn(er[e]||"",...n||[]),s={message:String(r),code:e};return t&&(s.location=t),s}const D={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},nr={[D.EXPECTED_TOKEN]:"Expected token: '{0}'",[D.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[D.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[D.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[D.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[D.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[D.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[D.EMPTY_PLACEHOLDER]:"Empty placeholder",[D.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[D.INVALID_LINKED_FORMAT]:"Invalid linked format",[D.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[D.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[D.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[D.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[D.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[D.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function pe(e,t,n={}){const{domain:r,messages:s,args:o}=n,c=fn((s||nr)[e]||"",...o||[]),u=new SyntaxError(String(c));return u.code=e,t&&(u.location=t),u.domain=r,u}function rr(e){throw e}const ae=" ",ar="\r",j=`
`,sr=String.fromCharCode(8232),lr=String.fromCharCode(8233);function or(e){const t=e;let n=0,r=1,s=1,o=0;const c=p=>t[p]===ar&&t[p+1]===j,u=p=>t[p]===j,i=p=>t[p]===lr,d=p=>t[p]===sr,O=p=>c(p)||u(p)||i(p)||d(p),T=()=>n,L=()=>r,h=()=>s,R=()=>o,k=p=>c(p)||i(p)||d(p)?j:t[p],A=()=>k(n),y=()=>k(n+o);function v(){return o=0,O(n)&&(r++,s=0),c(n)&&n++,n++,s++,t[n]}function _(){return c(n+o)&&o++,o++,t[n+o]}function E(){n=0,r=1,s=1,o=0}function I(p=0){o=p}function N(){const p=n+o;for(;p!==n;)v();o=0}return{index:T,line:L,column:h,peekOffset:R,charAt:k,currentChar:A,currentPeek:y,next:v,peek:_,reset:E,resetPeek:I,skipToPeek:N}}const ue=void 0,cr=".",Dt="'",ir="tokenizer";function ur(e,t={}){const n=t.location!==!1,r=or(e),s=()=>r.index(),o=()=>qn(r.line(),r.column(),r.index()),c=o(),u=s(),i={currentType:14,offset:u,startLoc:c,endLoc:c,lastType:14,lastOffset:u,lastStartLoc:c,lastEndLoc:c,braceNest:0,inLinked:!1,text:""},d=()=>i,{onError:O}=t;function T(a,l,m,...P){const x=d();if(l.column+=m,l.offset+=m,O){const M=n?Ve(x.startLoc,l):null,f=pe(a,M,{domain:ir,args:P});O(f)}}function L(a,l,m){a.endLoc=o(),a.currentType=l;const P={type:l};return n&&(P.loc=Ve(a.startLoc,a.endLoc)),m!=null&&(P.value=m),P}const h=a=>L(a,14);function R(a,l){return a.currentChar()===l?(a.next(),l):(T(D.EXPECTED_TOKEN,o(),0,l),"")}function k(a){let l="";for(;a.currentPeek()===ae||a.currentPeek()===j;)l+=a.currentPeek(),a.peek();return l}function A(a){const l=k(a);return a.skipToPeek(),l}function y(a){if(a===ue)return!1;const l=a.charCodeAt(0);return l>=97&&l<=122||l>=65&&l<=90||l===95}function v(a){if(a===ue)return!1;const l=a.charCodeAt(0);return l>=48&&l<=57}function _(a,l){const{currentType:m}=l;if(m!==2)return!1;k(a);const P=y(a.currentPeek());return a.resetPeek(),P}function E(a,l){const{currentType:m}=l;if(m!==2)return!1;k(a);const P=a.currentPeek()==="-"?a.peek():a.currentPeek(),x=v(P);return a.resetPeek(),x}function I(a,l){const{currentType:m}=l;if(m!==2)return!1;k(a);const P=a.currentPeek()===Dt;return a.resetPeek(),P}function N(a,l){const{currentType:m}=l;if(m!==8)return!1;k(a);const P=a.currentPeek()===".";return a.resetPeek(),P}function p(a,l){const{currentType:m}=l;if(m!==9)return!1;k(a);const P=y(a.currentPeek());return a.resetPeek(),P}function S(a,l){const{currentType:m}=l;if(!(m===8||m===12))return!1;k(a);const P=a.currentPeek()===":";return a.resetPeek(),P}function b(a,l){const{currentType:m}=l;if(m!==10)return!1;const P=()=>{const M=a.currentPeek();return M==="{"?y(a.peek()):M==="@"||M==="%"||M==="|"||M===":"||M==="."||M===ae||!M?!1:M===j?(a.peek(),P()):w(a,!1)},x=P();return a.resetPeek(),x}function K(a){k(a);const l=a.currentPeek()==="|";return a.resetPeek(),l}function te(a){const l=k(a),m=a.currentPeek()==="%"&&a.peek()==="{";return a.resetPeek(),{isModulo:m,hasSpace:l.length>0}}function w(a,l=!0){const m=(x=!1,M="",f=!1)=>{const g=a.currentPeek();return g==="{"?M==="%"?!1:x:g==="@"||!g?M==="%"?!0:x:g==="%"?(a.peek(),m(x,"%",!0)):g==="|"?M==="%"||f?!0:!(M===ae||M===j):g===ae?(a.peek(),m(!0,ae,f)):g===j?(a.peek(),m(!0,j,f)):!0},P=m();return l&&a.resetPeek(),P}function q(a,l){const m=a.currentChar();return m===ue?ue:l(m)?(a.next(),m):null}function Xe(a){const l=a.charCodeAt(0);return l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===95||l===36}function He(a){return q(a,Xe)}function Ke(a){const l=a.charCodeAt(0);return l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===95||l===36||l===45}function je(a){return q(a,Ke)}function Be(a){const l=a.charCodeAt(0);return l>=48&&l<=57}function Je(a){return q(a,Be)}function Qe(a){const l=a.charCodeAt(0);return l>=48&&l<=57||l>=65&&l<=70||l>=97&&l<=102}function re(a){return q(a,Qe)}function Oe(a){let l="",m="";for(;l=Je(a);)m+=l;return m}function qe(a){A(a);const l=a.currentChar();return l!=="%"&&T(D.EXPECTED_TOKEN,o(),0,l),a.next(),"%"}function ye(a){let l="";for(;;){const m=a.currentChar();if(m==="{"||m==="}"||m==="@"||m==="|"||!m)break;if(m==="%")if(w(a))l+=m,a.next();else break;else if(m===ae||m===j)if(w(a))l+=m,a.next();else{if(K(a))break;l+=m,a.next()}else l+=m,a.next()}return l}function Ze(a){A(a);let l="",m="";for(;l=je(a);)m+=l;return a.currentChar()===ue&&T(D.UNTERMINATED_CLOSING_BRACE,o(),0),m}function ze(a){A(a);let l="";return a.currentChar()==="-"?(a.next(),l+=`-${Oe(a)}`):l+=Oe(a),a.currentChar()===ue&&T(D.UNTERMINATED_CLOSING_BRACE,o(),0),l}function St(a){return a!==Dt&&a!==j}function et(a){A(a),R(a,"'");let l="",m="";for(;l=q(a,St);)l==="\\"?m+=tt(a):m+=l;const P=a.currentChar();return P===j||P===ue?(T(D.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),P===j&&(a.next(),R(a,"'")),m):(R(a,"'"),m)}function tt(a){const l=a.currentChar();switch(l){case"\\":case"'":return a.next(),`\\${l}`;case"u":return Re(a,l,4);case"U":return Re(a,l,6);default:return T(D.UNKNOWN_ESCAPE_SEQUENCE,o(),0,l),""}}function Re(a,l,m){R(a,l);let P="";for(let x=0;x<m;x++){const M=re(a);if(!M){T(D.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,`\\${l}${P}${a.currentChar()}`);break}P+=M}return`\\${l}${P}`}function nt(a){return a!=="{"&&a!=="}"&&a!==ae&&a!==j}function rt(a){A(a);let l="",m="";for(;l=q(a,nt);)m+=l;return m}function at(a){let l="",m="";for(;l=He(a);)m+=l;return m}function st(a){const l=m=>{const P=a.currentChar();return P==="{"||P==="%"||P==="@"||P==="|"||P==="("||P===")"||!P||P===ae?m:(m+=P,a.next(),l(m))};return l("")}function be(a){A(a);const l=R(a,"|");return A(a),l}function Ee(a,l){let m=null;switch(a.currentChar()){case"{":return l.braceNest>=1&&T(D.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),a.next(),m=L(l,2,"{"),A(a),l.braceNest++,m;case"}":return l.braceNest>0&&l.currentType===2&&T(D.EMPTY_PLACEHOLDER,o(),0),a.next(),m=L(l,3,"}"),l.braceNest--,l.braceNest>0&&A(a),l.inLinked&&l.braceNest===0&&(l.inLinked=!1),m;case"@":return l.braceNest>0&&T(D.UNTERMINATED_CLOSING_BRACE,o(),0),m=Ne(a,l)||h(l),l.braceNest=0,m;default:{let x=!0,M=!0,f=!0;if(K(a))return l.braceNest>0&&T(D.UNTERMINATED_CLOSING_BRACE,o(),0),m=L(l,1,be(a)),l.braceNest=0,l.inLinked=!1,m;if(l.braceNest>0&&(l.currentType===5||l.currentType===6||l.currentType===7))return T(D.UNTERMINATED_CLOSING_BRACE,o(),0),l.braceNest=0,he(a,l);if(x=_(a,l))return m=L(l,5,Ze(a)),A(a),m;if(M=E(a,l))return m=L(l,6,ze(a)),A(a),m;if(f=I(a,l))return m=L(l,7,et(a)),A(a),m;if(!x&&!M&&!f)return m=L(l,13,rt(a)),T(D.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,m.value),A(a),m;break}}return m}function Ne(a,l){const{currentType:m}=l;let P=null;const x=a.currentChar();switch((m===8||m===9||m===12||m===10)&&(x===j||x===ae)&&T(D.INVALID_LINKED_FORMAT,o(),0),x){case"@":return a.next(),P=L(l,8,"@"),l.inLinked=!0,P;case".":return A(a),a.next(),L(l,9,".");case":":return A(a),a.next(),L(l,10,":");default:return K(a)?(P=L(l,1,be(a)),l.braceNest=0,l.inLinked=!1,P):N(a,l)||S(a,l)?(A(a),Ne(a,l)):p(a,l)?(A(a),L(l,12,at(a))):b(a,l)?(A(a),x==="{"?Ee(a,l)||P:L(l,11,st(a))):(m===8&&T(D.INVALID_LINKED_FORMAT,o(),0),l.braceNest=0,l.inLinked=!1,he(a,l))}}function he(a,l){let m={type:14};if(l.braceNest>0)return Ee(a,l)||h(l);if(l.inLinked)return Ne(a,l)||h(l);switch(a.currentChar()){case"{":return Ee(a,l)||h(l);case"}":return T(D.UNBALANCED_CLOSING_BRACE,o(),0),a.next(),L(l,3,"}");case"@":return Ne(a,l)||h(l);default:{if(K(a))return m=L(l,1,be(a)),l.braceNest=0,l.inLinked=!1,m;const{isModulo:x,hasSpace:M}=te(a);if(x)return M?L(l,0,ye(a)):L(l,4,qe(a));if(w(a))return L(l,0,ye(a));break}}return m}function lt(){const{currentType:a,offset:l,startLoc:m,endLoc:P}=i;return i.lastType=a,i.lastOffset=l,i.lastStartLoc=m,i.lastEndLoc=P,i.offset=s(),i.startLoc=o(),r.currentChar()===ue?L(i,14):he(r,i)}return{nextToken:lt,currentOffset:s,currentPosition:o,context:d}}const fr="parser",_r=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function mr(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"\uFFFD"}}}function dr(e={}){const t=e.location!==!1,{onError:n,onWarn:r}=e;function s(_,E,I,N,...p){const S=_.currentPosition();if(S.offset+=N,S.column+=N,n){const b=t?Ve(I,S):null,K=pe(E,b,{domain:fr,args:p});n(K)}}function o(_,E,I,N,...p){const S=_.currentPosition();if(S.offset+=N,S.column+=N,r){const b=t?Ve(I,S):null;r(tr(E,b,p))}}function c(_,E,I){const N={type:_};return t&&(N.start=E,N.end=E,N.loc={start:I,end:I}),N}function u(_,E,I,N){N&&(_.type=N),t&&(_.end=E,_.loc&&(_.loc.end=I))}function i(_,E){const I=_.context(),N=c(3,I.offset,I.startLoc);return N.value=E,u(N,_.currentOffset(),_.currentPosition()),N}function d(_,E){const I=_.context(),{lastOffset:N,lastStartLoc:p}=I,S=c(5,N,p);return S.index=parseInt(E,10),_.nextToken(),u(S,_.currentOffset(),_.currentPosition()),S}function O(_,E,I){const N=_.context(),{lastOffset:p,lastStartLoc:S}=N,b=c(4,p,S);return b.key=E,I===!0&&(b.modulo=!0),_.nextToken(),u(b,_.currentOffset(),_.currentPosition()),b}function T(_,E){const I=_.context(),{lastOffset:N,lastStartLoc:p}=I,S=c(9,N,p);return S.value=E.replace(_r,mr),_.nextToken(),u(S,_.currentOffset(),_.currentPosition()),S}function L(_){const E=_.nextToken(),I=_.context(),{lastOffset:N,lastStartLoc:p}=I,S=c(8,N,p);return E.type!==12?(s(_,D.UNEXPECTED_EMPTY_LINKED_MODIFIER,I.lastStartLoc,0),S.value="",u(S,N,p),{nextConsumeToken:E,node:S}):(E.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,I.lastStartLoc,0,Z(E)),S.value=E.value||"",u(S,_.currentOffset(),_.currentPosition()),{node:S})}function h(_,E){const I=_.context(),N=c(7,I.offset,I.startLoc);return N.value=E,u(N,_.currentOffset(),_.currentPosition()),N}function R(_){const E=_.context(),I=c(6,E.offset,E.startLoc);let N=_.nextToken();if(N.type===9){const p=L(_);I.modifier=p.node,N=p.nextConsumeToken||_.nextToken()}switch(N.type!==10&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(N)),N=_.nextToken(),N.type===2&&(N=_.nextToken()),N.type){case 11:N.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(N)),I.key=h(_,N.value||"");break;case 5:N.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(N)),I.key=O(_,N.value||"");break;case 6:N.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(N)),I.key=d(_,N.value||"");break;case 7:N.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(N)),I.key=T(_,N.value||"");break;default:{s(_,D.UNEXPECTED_EMPTY_LINKED_KEY,E.lastStartLoc,0);const p=_.context(),S=c(7,p.offset,p.startLoc);return S.value="",u(S,p.offset,p.startLoc),I.key=S,u(I,p.offset,p.startLoc),{nextConsumeToken:N,node:I}}}return u(I,_.currentOffset(),_.currentPosition()),{node:I}}function k(_){const E=_.context(),I=E.currentType===1?_.currentOffset():E.offset,N=E.currentType===1?E.endLoc:E.startLoc,p=c(2,I,N);p.items=[];let S=null,b=null;do{const w=S||_.nextToken();switch(S=null,w.type){case 0:w.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(w)),p.items.push(i(_,w.value||""));break;case 6:w.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(w)),p.items.push(d(_,w.value||""));break;case 4:b=!0;break;case 5:w.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(w)),p.items.push(O(_,w.value||"",!!b)),b&&(o(_,It.USE_MODULO_SYNTAX,E.lastStartLoc,0,Z(w)),b=null);break;case 7:w.value==null&&s(_,D.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,Z(w)),p.items.push(T(_,w.value||""));break;case 8:{const q=R(_);p.items.push(q.node),S=q.nextConsumeToken||null;break}}}while(E.currentType!==14&&E.currentType!==1);const K=E.currentType===1?E.lastOffset:_.currentOffset(),te=E.currentType===1?E.lastEndLoc:_.currentPosition();return u(p,K,te),p}function A(_,E,I,N){const p=_.context();let S=N.items.length===0;const b=c(1,E,I);b.cases=[],b.cases.push(N);do{const K=k(_);S||(S=K.items.length===0),b.cases.push(K)}while(p.currentType!==14);return S&&s(_,D.MUST_HAVE_MESSAGES_IN_PLURAL,I,0),u(b,_.currentOffset(),_.currentPosition()),b}function y(_){const E=_.context(),{offset:I,startLoc:N}=E,p=k(_);return E.currentType===14?p:A(_,I,N,p)}function v(_){const E=ur(_,_n({},e)),I=E.context(),N=c(0,I.offset,I.startLoc);return t&&N.loc&&(N.loc.source=_),N.body=y(E),e.onCacheKey&&(N.cacheKey=e.onCacheKey(_)),I.currentType!==14&&s(E,D.UNEXPECTED_LEXICAL_ANALYSIS,I.lastStartLoc,0,_[I.offset]||""),u(N,E.currentOffset(),E.currentPosition()),N}return{parse:v}}function Z(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"\u2026":t}function Er(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:o=>(n.helpers.add(o),o)}}function kt(e,t){for(let n=0;n<e.length;n++)pt(e[n],t)}function pt(e,t){switch(e.type){case 1:kt(e.cases,t),t.helper("plural");break;case 2:kt(e.items,t);break;case 6:{pt(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function Nr(e,t={}){const n=Er(e);n.helper("normalize"),e.body&&pt(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function gr(e){const t=e.body;return t.type===2?Mt(t):t.cases.forEach(n=>Mt(n)),e}function Mt(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=mn(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}const Lr="minifier";function Le(e){switch(e.t=e.type,e.type){case 0:{const t=e;Le(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)Le(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)Le(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Le(t.key),t.k=t.key,delete t.key,t.modifier&&(Le(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw pe(D.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:Lr,args:[e.type]})}delete e.type}const Tr="parser";function Ir(e,t){const{sourceMap:n,filename:r,breakLineCode:s,needIndent:o}=t,c=t.location!==!1,u={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:o,indentLevel:0};c&&e.loc&&(u.source=e.loc.source);const i=()=>u;function d(A,y){u.code+=A}function O(A,y=!0){const v=y?s:"";d(o?v+"  ".repeat(A):v)}function T(A=!0){const y=++u.indentLevel;A&&O(y)}function L(A=!0){const y=--u.indentLevel;A&&O(y)}function h(){O(u.indentLevel)}return{context:i,push:d,indent:T,deindent:L,newline:h,helper:A=>`_${A}`,needIndent:()=>u.needIndent}}function pr(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Te(e,t.key),t.modifier?(e.push(", "),Te(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function Or(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const s=t.items.length;for(let o=0;o<s&&(Te(e,t.items[o]),o!==s-1);o++)e.push(", ");e.deindent(r()),e.push("])")}function br(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const s=t.cases.length;for(let o=0;o<s&&(Te(e,t.cases[o]),o!==s-1);o++)e.push(", ");e.deindent(r()),e.push("])")}}function hr(e,t){t.body?Te(e,t.body):e.push("null")}function Te(e,t){const{helper:n}=e;switch(t.type){case 0:hr(e,t);break;case 1:br(e,t);break;case 2:Or(e,t);break;case 6:pr(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw pe(D.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:Tr,args:[t.type]})}}const Cr=(e,t={})=>{const n=Rt(t.mode)?t.mode:"normal",r=Rt(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,o=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,c=t.needIndent?t.needIndent:n!=="arrow",u=e.helpers||[],i=Ir(e,{mode:n,filename:r,sourceMap:s,breakLineCode:o,needIndent:c});i.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),i.indent(c),u.length>0&&(i.push(`const { ${mn(u.map(T=>`${T}: _${T}`),", ")} } = ctx`),i.newline()),i.push("return "),Te(i,e),i.deindent(c),i.push("}"),delete e.helpers;const{code:d,map:O}=i.context();return{ast:e,code:d,map:O?O.toJSON():void 0}};function Ar(e,t={}){const n=_n({},t),r=!!n.jit,s=!!n.minify,o=n.optimize==null?!0:n.optimize,u=dr(n).parse(e);return r?(o&&gr(u),s&&Le(u),{ast:u,code:""}):(Nr(u,n),Cr(u,n))}/*!
  * core-base v9.14.2
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Sr(){typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(We().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(We().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const fe=[];fe[0]={w:[0],i:[3,0],["["]:[4],o:[7]};fe[1]={w:[1],["."]:[2],["["]:[4],o:[7]};fe[2]={w:[2],i:[3,0],[0]:[3,0]};fe[3]={i:[3,0],[0]:[3,0],w:[1,1],["."]:[2,1],["["]:[4,1],o:[7,1]};fe[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],o:8,l:[4,0]};fe[5]={["'"]:[4,0],o:8,l:[5,0]};fe[6]={['"']:[4,0],o:8,l:[6,0]};const Pr=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function yr(e){return Pr.test(e)}function Rr(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function Dr(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function kr(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:yr(t)?Rr(t):"*"+t}function Mr(e){const t=[];let n=-1,r=0,s=0,o,c,u,i,d,O,T;const L=[];L[0]=()=>{c===void 0?c=u:c+=u},L[1]=()=>{c!==void 0&&(t.push(c),c=void 0)},L[2]=()=>{L[0](),s++},L[3]=()=>{if(s>0)s--,r=4,L[0]();else{if(s=0,c===void 0||(c=kr(c),c===!1))return!1;L[1]()}};function h(){const R=e[n+1];if(r===5&&R==="'"||r===6&&R==='"')return n++,u="\\"+R,L[0](),!0}for(;r!==null;)if(n++,o=e[n],!(o==="\\"&&h())){if(i=Dr(o),T=fe[r],d=T[i]||T.l||8,d===8||(r=d[0],d[1]!==void 0&&(O=L[d[1]],O&&(u=o,O()===!1))))return;if(r===7)return t}}const vt=new Map;function vr(e,t){return U(e)?e[t]:null}function Fr(e,t){if(!U(e))return null;let n=vt.get(t);if(n||(n=Mr(t),n&&vt.set(t,n)),!n)return null;const r=n.length;let s=e,o=0;for(;o<r;){const c=s[n[o]];if(c===void 0||Y(s))return null;s=c,o++}return s}const Ur=e=>e,wr=e=>"",Wr="text",Vr=e=>e.length===0?"":Jn(e),xr=Bn;function Ft(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function $r(e){const t=X(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(X(e.named.count)||X(e.named.n))?X(e.named.count)?e.named.count:X(e.named.n)?e.named.n:t:t}function Yr(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Gr(e={}){const t=e.locale,n=$r(e),r=U(e.pluralRules)&&C(t)&&Y(e.pluralRules[t])?e.pluralRules[t]:Ft,s=U(e.pluralRules)&&C(t)&&Y(e.pluralRules[t])?Ft:void 0,o=y=>y[r(n,y.length,s)],c=e.list||[],u=y=>c[y],i=e.named||W();X(e.pluralIndex)&&Yr(n,i);const d=y=>i[y];function O(y){const v=Y(e.messages)?e.messages(y):U(e.messages)?e.messages[y]:!1;return v||(e.parent?e.parent.message(y):wr)}const T=y=>e.modifiers?e.modifiers[y]:Ur,L=F(e.processor)&&Y(e.processor.normalize)?e.processor.normalize:Vr,h=F(e.processor)&&Y(e.processor.interpolate)?e.processor.interpolate:xr,R=F(e.processor)&&C(e.processor.type)?e.processor.type:Wr,A={list:u,named:d,plural:o,linked:(y,...v)=>{const[_,E]=v;let I="text",N="";v.length===1?U(_)?(N=_.modifier||N,I=_.type||I):C(_)&&(N=_||N):v.length===2&&(C(_)&&(N=_||N),C(E)&&(I=E||I));const p=O(y)(A),S=I==="vnode"&&H(p)&&N?p[0]:p;return N?T(N)(S,I):S},message:O,type:R,interpolate:h,normalize:L,values:B(W(),c,i)};return A}const dn=It.__EXTEND_POINT__,me=Ye(dn),Xr={NOT_FOUND_KEY:dn,FALLBACK_TO_TRANSLATE:me(),CANNOT_FORMAT_NUMBER:me(),FALLBACK_TO_NUMBER_FORMAT:me(),CANNOT_FORMAT_DATE:me(),FALLBACK_TO_DATE_FORMAT:me(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:me(),__EXTEND_POINT__:me()},En=D.__EXTEND_POINT__,de=Ye(En),ne={INVALID_ARGUMENT:En,INVALID_DATE_ARGUMENT:de(),INVALID_ISO_DATE_ARGUMENT:de(),NOT_SUPPORT_NON_STRING_MESSAGE:de(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:de(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:de(),NOT_SUPPORT_LOCALE_TYPE:de(),__EXTEND_POINT__:de()};function le(e){return pe(e,null,void 0)}function Ot(e,t){return t.locale!=null?Ut(t.locale):Ut(e.locale)}let ot;function Ut(e){if(C(e))return e;if(Y(e)){if(e.resolvedOnce&&ot!=null)return ot;if(e.constructor.name==="Function"){const t=e();if(jn(t))throw le(ne.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ot=t}else throw le(ne.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw le(ne.NOT_SUPPORT_LOCALE_TYPE)}function Hr(e,t,n){return[...new Set([n,...H(t)?t:U(t)?Object.keys(t):C(t)?[t]:[n]])]}function Nn(e,t,n){const r=C(n)?n:xe,s=e;s.__localeChainCache||(s.__localeChainCache=new Map);let o=s.__localeChainCache.get(r);if(!o){o=[];let c=[n];for(;H(c);)c=wt(o,c,t);const u=H(t)||!F(t)?t:t.default?t.default:null;c=C(u)?[u]:u,H(c)&&wt(o,c,!1),s.__localeChainCache.set(r,o)}return o}function wt(e,t,n){let r=!0;for(let s=0;s<t.length&&$(r);s++){const o=t[s];C(o)&&(r=Kr(e,t[s],n))}return r}function Kr(e,t,n){let r;const s=t.split("-");do{const o=s.join("-");r=jr(e,o,n),s.splice(-1,1)}while(s.length&&r===!0);return r}function jr(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const s=t.replace(/!/g,"");e.push(s),(H(n)||F(n))&&n[s]&&(r=n[s])}return r}const Br="9.14.2",Ge=-1,xe="en-US",Wt="",Vt=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function Jr(){return{upper:(e,t)=>t==="text"&&C(e)?e.toUpperCase():t==="vnode"&&U(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&C(e)?e.toLowerCase():t==="vnode"&&U(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&C(e)?Vt(e):t==="vnode"&&U(e)&&"__v_isVNode"in e?Vt(e.children):e}}let gn;function Qr(e){gn=e}let Ln;function qr(e){Ln=e}let Tn;function Zr(e){Tn=e}let In=null;const xt=e=>{In=e},zr=()=>In;let $t=0;function ea(e={}){const t=Y(e.onWarn)?e.onWarn:Qn,n=C(e.version)?e.version:Br,r=C(e.locale)||Y(e.locale)?e.locale:xe,s=Y(r)?xe:r,o=H(e.fallbackLocale)||F(e.fallbackLocale)||C(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s,c=F(e.messages)?e.messages:ct(s),u=F(e.datetimeFormats)?e.datetimeFormats:ct(s),i=F(e.numberFormats)?e.numberFormats:ct(s),d=B(W(),e.modifiers,Jr()),O=e.pluralRules||W(),T=Y(e.missing)?e.missing:null,L=$(e.missingWarn)||we(e.missingWarn)?e.missingWarn:!0,h=$(e.fallbackWarn)||we(e.fallbackWarn)?e.fallbackWarn:!0,R=!!e.fallbackFormat,k=!!e.unresolving,A=Y(e.postTranslation)?e.postTranslation:null,y=F(e.processor)?e.processor:null,v=$(e.warnHtmlMessage)?e.warnHtmlMessage:!0,_=!!e.escapeParameter,E=Y(e.messageCompiler)?e.messageCompiler:gn,I=Y(e.messageResolver)?e.messageResolver:Ln||vr,N=Y(e.localeFallbacker)?e.localeFallbacker:Tn||Hr,p=U(e.fallbackContext)?e.fallbackContext:void 0,S=e,b=U(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,K=U(S.__numberFormatters)?S.__numberFormatters:new Map,te=U(S.__meta)?S.__meta:{};$t++;const w={version:n,cid:$t,locale:r,fallbackLocale:o,messages:c,modifiers:d,pluralRules:O,missing:T,missingWarn:L,fallbackWarn:h,fallbackFormat:R,unresolving:k,postTranslation:A,processor:y,warnHtmlMessage:v,escapeParameter:_,messageCompiler:E,messageResolver:I,localeFallbacker:N,fallbackContext:p,onWarn:t,__meta:te};return w.datetimeFormats=u,w.numberFormats=i,w.__datetimeFormatters=b,w.__numberFormatters=K,w}const ct=e=>({[e]:W()});function bt(e,t,n,r,s){const{missing:o,onWarn:c}=e;if(o!==null){const u=o(e,n,t,s);return C(u)?u:t}else return t}function Ae(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function ta(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function na(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(ta(e,t[r]))return!0;return!1}function it(e){return n=>ra(n,e)}function ra(e,t){const n=sa(t);if(n==null)throw Se(0);if(ht(n)===1){const o=oa(n);return e.plural(o.reduce((c,u)=>[...c,Yt(e,u)],[]))}else return Yt(e,n)}const aa=["b","body"];function sa(e){return _e(e,aa)}const la=["c","cases"];function oa(e){return _e(e,la,[])}function Yt(e,t){const n=ia(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const r=fa(t).reduce((s,o)=>[...s,ft(e,o)],[]);return e.normalize(r)}}const ca=["s","static"];function ia(e){return _e(e,ca)}const ua=["i","items"];function fa(e){return _e(e,ua,[])}function ft(e,t){const n=ht(t);switch(n){case 3:return Me(t,n);case 9:return Me(t,n);case 4:{const r=t;if(z(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(z(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Se(n)}case 5:{const r=t;if(z(r,"i")&&X(r.i))return e.interpolate(e.list(r.i));if(z(r,"index")&&X(r.index))return e.interpolate(e.list(r.index));throw Se(n)}case 6:{const r=t,s=Ea(r),o=ga(r);return e.linked(ft(e,o),s?ft(e,s):void 0,e.type)}case 7:return Me(t,n);case 8:return Me(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const _a=["t","type"];function ht(e){return _e(e,_a)}const ma=["v","value"];function Me(e,t){const n=_e(e,ma);if(n)return n;throw Se(t)}const da=["m","modifier"];function Ea(e){return _e(e,da)}const Na=["k","key"];function ga(e){const t=_e(e,Na);if(t)return t;throw Se(6)}function _e(e,t,n){for(let r=0;r<t.length;r++){const s=t[r];if(z(e,s)&&e[s]!=null)return e[s]}return n}function Se(e){return new Error(`unhandled node type: ${e}`)}const La=e=>e;let ve=W();function Ie(e){return U(e)&&ht(e)===0&&(z(e,"b")||z(e,"body"))}function Ta(e,t={}){let n=!1;const r=t.onError||rr;return t.onError=s=>{n=!0,r(s)},{...Ar(e,t),detectError:n}}function Ia(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&C(e)){$(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||La)(e),s=ve[r];if(s)return s;const{ast:o,detectError:c}=Ta(e,{...t,location:!1,jit:!0}),u=it(o);return c?u:ve[r]=u}else{const n=e.cacheKey;if(n){const r=ve[n];return r||(ve[n]=it(e))}else return it(e)}}const Gt=()=>"",oe=e=>Y(e);function Xt(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:s,messageCompiler:o,fallbackLocale:c,messages:u}=e,[i,d]=_t(...t),O=$(d.missingWarn)?d.missingWarn:e.missingWarn,T=$(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn,L=$(d.escapeParameter)?d.escapeParameter:e.escapeParameter,h=!!d.resolvedMessage,R=C(d.default)||$(d.default)?$(d.default)?o?i:()=>i:d.default:n?o?i:()=>i:"",k=n||R!=="",A=Ot(e,d);L&&pa(d);let[y,v,_]=h?[i,A,u[A]||W()]:pn(e,i,A,c,T,O),E=y,I=i;if(!h&&!(C(E)||Ie(E)||oe(E))&&k&&(E=R,I=E),!h&&(!(C(E)||Ie(E)||oe(E))||!C(v)))return s?Ge:i;let N=!1;const p=()=>{N=!0},S=oe(E)?E:On(e,i,v,E,I,p);if(N)return E;const b=ha(e,v,_,d),K=Gr(b),te=Oa(e,S,K);return r?r(te,i):te}function pa(e){H(e.list)?e.list=e.list.map(t=>C(t)?yt(t):t):U(e.named)&&Object.keys(e.named).forEach(t=>{C(e.named[t])&&(e.named[t]=yt(e.named[t]))})}function pn(e,t,n,r,s,o){const{messages:c,onWarn:u,messageResolver:i,localeFallbacker:d}=e,O=d(e,r,n);let T=W(),L,h=null;const R="translate";for(let k=0;k<O.length&&(L=O[k],T=c[L]||W(),(h=i(T,t))===null&&(h=T[t]),!(C(h)||Ie(h)||oe(h)));k++)if(!na(L,O)){const A=bt(e,t,L,o,R);A!==t&&(h=A)}return[h,L,T]}function On(e,t,n,r,s,o){const{messageCompiler:c,warnHtmlMessage:u}=e;if(oe(r)){const d=r;return d.locale=d.locale||n,d.key=d.key||t,d}if(c==null){const d=()=>r;return d.locale=n,d.key=t,d}const i=c(r,ba(e,n,s,r,u,o));return i.locale=n,i.key=t,i.source=r,i}function Oa(e,t,n){return t(n)}function _t(...e){const[t,n,r]=e,s=W();if(!C(t)&&!X(t)&&!oe(t)&&!Ie(t))throw le(ne.INVALID_ARGUMENT);const o=X(t)?String(t):(oe(t),t);return X(n)?s.plural=n:C(n)?s.default=n:F(n)&&!$e(n)?s.named=n:H(n)&&(s.list=n),X(r)?s.plural=r:C(r)?s.default=r:F(r)&&B(s,r),[o,s]}function ba(e,t,n,r,s,o){return{locale:t,key:n,warnHtmlMessage:s,onError:c=>{throw o&&o(c),c},onCacheKey:c=>Yn(t,n,c)}}function ha(e,t,n,r){const{modifiers:s,pluralRules:o,messageResolver:c,fallbackLocale:u,fallbackWarn:i,missingWarn:d,fallbackContext:O}=e,L={locale:t,modifiers:s,pluralRules:o,messages:h=>{let R=c(n,h);if(R==null&&O){const[,,k]=pn(O,h,t,u,i,d);R=c(k,h)}if(C(R)||Ie(R)){let k=!1;const y=On(e,h,t,R,h,()=>{k=!0});return k?Gt:y}else return oe(R)?R:Gt}};return e.processor&&(L.processor=e.processor),r.list&&(L.list=r.list),r.named&&(L.named=r.named),X(r.plural)&&(L.pluralIndex=r.plural),L}function Ht(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:s,onWarn:o,localeFallbacker:c}=e,{__datetimeFormatters:u}=e,[i,d,O,T]=mt(...t),L=$(O.missingWarn)?O.missingWarn:e.missingWarn;$(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn;const h=!!O.part,R=Ot(e,O),k=c(e,s,R);if(!C(i)||i==="")return new Intl.DateTimeFormat(R,T).format(d);let A={},y,v=null;const _="datetime format";for(let N=0;N<k.length&&(y=k[N],A=n[y]||{},v=A[i],!F(v));N++)bt(e,i,y,L,_);if(!F(v)||!C(y))return r?Ge:i;let E=`${y}__${i}`;$e(T)||(E=`${E}__${JSON.stringify(T)}`);let I=u.get(E);return I||(I=new Intl.DateTimeFormat(y,B({},v,T)),u.set(E,I)),h?I.formatToParts(d):I.format(d)}const bn=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function mt(...e){const[t,n,r,s]=e,o=W();let c=W(),u;if(C(t)){const i=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!i)throw le(ne.INVALID_ISO_DATE_ARGUMENT);const d=i[3]?i[3].trim().startsWith("T")?`${i[1].trim()}${i[3].trim()}`:`${i[1].trim()}T${i[3].trim()}`:i[1].trim();u=new Date(d);try{u.toISOString()}catch{throw le(ne.INVALID_ISO_DATE_ARGUMENT)}}else if(Xn(t)){if(isNaN(t.getTime()))throw le(ne.INVALID_DATE_ARGUMENT);u=t}else if(X(t))u=t;else throw le(ne.INVALID_ARGUMENT);return C(n)?o.key=n:F(n)&&Object.keys(n).forEach(i=>{bn.includes(i)?c[i]=n[i]:o[i]=n[i]}),C(r)?o.locale=r:F(r)&&(c=r),F(s)&&(c=s),[o.key||"",u,o,c]}function Kt(e,t,n){const r=e;for(const s in n){const o=`${t}__${s}`;!r.__datetimeFormatters.has(o)||r.__datetimeFormatters.delete(o)}}function jt(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:s,onWarn:o,localeFallbacker:c}=e,{__numberFormatters:u}=e,[i,d,O,T]=dt(...t),L=$(O.missingWarn)?O.missingWarn:e.missingWarn;$(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn;const h=!!O.part,R=Ot(e,O),k=c(e,s,R);if(!C(i)||i==="")return new Intl.NumberFormat(R,T).format(d);let A={},y,v=null;const _="number format";for(let N=0;N<k.length&&(y=k[N],A=n[y]||{},v=A[i],!F(v));N++)bt(e,i,y,L,_);if(!F(v)||!C(y))return r?Ge:i;let E=`${y}__${i}`;$e(T)||(E=`${E}__${JSON.stringify(T)}`);let I=u.get(E);return I||(I=new Intl.NumberFormat(y,B({},v,T)),u.set(E,I)),h?I.formatToParts(d):I.format(d)}const hn=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function dt(...e){const[t,n,r,s]=e,o=W();let c=W();if(!X(t))throw le(ne.INVALID_ARGUMENT);const u=t;return C(n)?o.key=n:F(n)&&Object.keys(n).forEach(i=>{hn.includes(i)?c[i]=n[i]:o[i]=n[i]}),C(r)?o.locale=r:F(r)&&(c=r),F(s)&&(c=s),[o.key||"",u,o,c]}function Bt(e,t,n){const r=e;for(const s in n){const o=`${t}__${s}`;!r.__numberFormatters.has(o)||r.__numberFormatters.delete(o)}}Sr();/*!
  * vue-i18n v9.14.2
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const Ca="9.14.2";function Aa(){typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(We().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(We().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const Cn=Xr.__EXTEND_POINT__,se=Ye(Cn);se(),se(),se(),se(),se(),se(),se(),se(),se();const An=ne.__EXTEND_POINT__,J=Ye(An),Q={UNEXPECTED_RETURN_TYPE:An,INVALID_ARGUMENT:J(),MUST_BE_CALL_SETUP_TOP:J(),NOT_INSTALLED:J(),NOT_AVAILABLE_IN_LEGACY_MODE:J(),REQUIRED_VALUE:J(),INVALID_VALUE:J(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:J(),NOT_INSTALLED_WITH_PROVIDE:J(),UNEXPECTED_ERROR:J(),NOT_COMPATIBLE_LEGACY_VUE_I18N:J(),BRIDGE_SUPPORT_VUE_2_ONLY:J(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:J(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:J(),__EXTEND_POINT__:J()};function ee(e,...t){return pe(e,null,void 0)}const Et=ce("__translateVNode"),Nt=ce("__datetimeParts"),gt=ce("__numberParts"),Sa=ce("__setPluralRules");ce("__intlifyMeta");const Pa=ce("__injectWithOption"),Lt=ce("__dispose");function Pe(e){if(!U(e))return e;for(const t in e)if(!!z(e,t))if(!t.includes("."))U(e[t])&&Pe(e[t]);else{const n=t.split("."),r=n.length-1;let s=e,o=!1;for(let c=0;c<r;c++){if(n[c]in s||(s[n[c]]=W()),!U(s[n[c]])){o=!0;break}s=s[n[c]]}o||(s[n[r]]=e[t],delete e[t]),U(s[n[r]])&&Pe(s[n[r]])}return e}function Sn(e,t){const{messages:n,__i18n:r,messageResolver:s,flatJson:o}=t,c=F(n)?n:H(r)?W():{[e]:W()};if(H(r)&&r.forEach(u=>{if("locale"in u&&"resource"in u){const{locale:i,resource:d}=u;i?(c[i]=c[i]||W(),Fe(d,c[i])):Fe(d,c)}else C(u)&&Fe(JSON.parse(u),c)}),s==null&&o)for(const u in c)z(c,u)&&Pe(c[u]);return c}function ya(e){return e.type}function Ra(e,t,n){let r=U(t.messages)?t.messages:W();"__i18nGlobal"in n&&(r=Sn(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const s=Object.keys(r);s.length&&s.forEach(o=>{e.mergeLocaleMessage(o,r[o])});{if(U(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(c=>{e.mergeDateTimeFormat(c,t.datetimeFormats[c])})}if(U(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(c=>{e.mergeNumberFormat(c,t.numberFormats[c])})}}}function Jt(e){return Vn(xn,null,e,0)}const Qt=()=>[],Da=()=>!1;let qt=0;function Zt(e){return(t,n,r,s)=>e(n,r,on()||void 0,s)}function Pn(e={},t){const{__root:n,__injectWithOption:r}=e,s=n===void 0,o=e.flatJson,c=Ue?wn:Wn,u=!!e.translateExistCompatible;let i=$(e.inheritLocale)?e.inheritLocale:!0;const d=c(n&&i?n.locale.value:C(e.locale)?e.locale:xe),O=c(n&&i?n.fallbackLocale.value:C(e.fallbackLocale)||H(e.fallbackLocale)||F(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:d.value),T=c(Sn(d.value,e)),L=c(F(e.datetimeFormats)?e.datetimeFormats:{[d.value]:{}}),h=c(F(e.numberFormats)?e.numberFormats:{[d.value]:{}});let R=n?n.missingWarn:$(e.missingWarn)||we(e.missingWarn)?e.missingWarn:!0,k=n?n.fallbackWarn:$(e.fallbackWarn)||we(e.fallbackWarn)?e.fallbackWarn:!0,A=n?n.fallbackRoot:$(e.fallbackRoot)?e.fallbackRoot:!0,y=!!e.fallbackFormat,v=Y(e.missing)?e.missing:null,_=Y(e.missing)?Zt(e.missing):null,E=Y(e.postTranslation)?e.postTranslation:null,I=n?n.warnHtmlMessage:$(e.warnHtmlMessage)?e.warnHtmlMessage:!0,N=!!e.escapeParameter;const p=n?n.modifiers:F(e.modifiers)?e.modifiers:{};let S=e.pluralRules||n&&n.pluralRules,b;b=(()=>{s&&xt(null);const f={version:Ca,locale:d.value,fallbackLocale:O.value,messages:T.value,modifiers:p,pluralRules:S,missing:_===null?void 0:_,missingWarn:R,fallbackWarn:k,fallbackFormat:y,unresolving:!0,postTranslation:E===null?void 0:E,warnHtmlMessage:I,escapeParameter:N,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};f.datetimeFormats=L.value,f.numberFormats=h.value,f.__datetimeFormatters=F(b)?b.__datetimeFormatters:void 0,f.__numberFormatters=F(b)?b.__numberFormatters:void 0;const g=ea(f);return s&&xt(g),g})(),Ae(b,d.value,O.value);function te(){return[d.value,O.value,T.value,L.value,h.value]}const w=Ce({get:()=>d.value,set:f=>{d.value=f,b.locale=d.value}}),q=Ce({get:()=>O.value,set:f=>{O.value=f,b.fallbackLocale=O.value,Ae(b,d.value,f)}}),Xe=Ce(()=>T.value),He=Ce(()=>L.value),Ke=Ce(()=>h.value);function je(){return Y(E)?E:null}function Be(f){E=f,b.postTranslation=f}function Je(){return v}function Qe(f){f!==null&&(_=Zt(f)),v=f,b.missing=_}const re=(f,g,V,G,ie,De)=>{te();let ge;try{s||(b.fallbackContext=n?zr():void 0),ge=f(b)}finally{s||(b.fallbackContext=void 0)}if(V!=="translate exists"&&X(ge)&&ge===Ge||V==="translate exists"&&!ge){const[Dn,za]=g();return n&&A?G(n):ie(Dn)}else{if(De(ge))return ge;throw ee(Q.UNEXPECTED_RETURN_TYPE)}};function Oe(...f){return re(g=>Reflect.apply(Xt,null,[g,...f]),()=>_t(...f),"translate",g=>Reflect.apply(g.t,g,[...f]),g=>g,g=>C(g))}function qe(...f){const[g,V,G]=f;if(G&&!U(G))throw ee(Q.INVALID_ARGUMENT);return Oe(g,V,B({resolvedMessage:!0},G||{}))}function ye(...f){return re(g=>Reflect.apply(Ht,null,[g,...f]),()=>mt(...f),"datetime format",g=>Reflect.apply(g.d,g,[...f]),()=>Wt,g=>C(g))}function Ze(...f){return re(g=>Reflect.apply(jt,null,[g,...f]),()=>dt(...f),"number format",g=>Reflect.apply(g.n,g,[...f]),()=>Wt,g=>C(g))}function ze(f){return f.map(g=>C(g)||X(g)||$(g)?Jt(String(g)):g)}const et={normalize:ze,interpolate:f=>f,type:"vnode"};function tt(...f){return re(g=>{let V;const G=g;try{G.processor=et,V=Reflect.apply(Xt,null,[G,...f])}finally{G.processor=null}return V},()=>_t(...f),"translate",g=>g[Et](...f),g=>[Jt(g)],g=>H(g))}function Re(...f){return re(g=>Reflect.apply(jt,null,[g,...f]),()=>dt(...f),"number format",g=>g[gt](...f),Qt,g=>C(g)||H(g))}function nt(...f){return re(g=>Reflect.apply(Ht,null,[g,...f]),()=>mt(...f),"datetime format",g=>g[Nt](...f),Qt,g=>C(g)||H(g))}function rt(f){S=f,b.pluralRules=S}function at(f,g){return re(()=>{if(!f)return!1;const V=C(g)?g:d.value,G=Ee(V),ie=b.messageResolver(G,f);return u?ie!=null:Ie(ie)||oe(ie)||C(ie)},()=>[f],"translate exists",V=>Reflect.apply(V.te,V,[f,g]),Da,V=>$(V))}function st(f){let g=null;const V=Nn(b,O.value,d.value);for(let G=0;G<V.length;G++){const ie=T.value[V[G]]||{},De=b.messageResolver(ie,f);if(De!=null){g=De;break}}return g}function be(f){const g=st(f);return g!=null?g:n?n.tm(f)||{}:{}}function Ee(f){return T.value[f]||{}}function Ne(f,g){if(o){const V={[f]:g};for(const G in V)z(V,G)&&Pe(V[G]);g=V[f]}T.value[f]=g,b.messages=T.value}function he(f,g){T.value[f]=T.value[f]||{};const V={[f]:g};if(o)for(const G in V)z(V,G)&&Pe(V[G]);g=V[f],Fe(g,T.value[f]),b.messages=T.value}function lt(f){return L.value[f]||{}}function a(f,g){L.value[f]=g,b.datetimeFormats=L.value,Kt(b,f,g)}function l(f,g){L.value[f]=B(L.value[f]||{},g),b.datetimeFormats=L.value,Kt(b,f,g)}function m(f){return h.value[f]||{}}function P(f,g){h.value[f]=g,b.numberFormats=h.value,Bt(b,f,g)}function x(f,g){h.value[f]=B(h.value[f]||{},g),b.numberFormats=h.value,Bt(b,f,g)}qt++,n&&Ue&&(ut(n.locale,f=>{i&&(d.value=f,b.locale=f,Ae(b,d.value,O.value))}),ut(n.fallbackLocale,f=>{i&&(O.value=f,b.fallbackLocale=f,Ae(b,d.value,O.value))}));const M={id:qt,locale:w,fallbackLocale:q,get inheritLocale(){return i},set inheritLocale(f){i=f,f&&n&&(d.value=n.locale.value,O.value=n.fallbackLocale.value,Ae(b,d.value,O.value))},get availableLocales(){return Object.keys(T.value).sort()},messages:Xe,get modifiers(){return p},get pluralRules(){return S||{}},get isGlobal(){return s},get missingWarn(){return R},set missingWarn(f){R=f,b.missingWarn=R},get fallbackWarn(){return k},set fallbackWarn(f){k=f,b.fallbackWarn=k},get fallbackRoot(){return A},set fallbackRoot(f){A=f},get fallbackFormat(){return y},set fallbackFormat(f){y=f,b.fallbackFormat=y},get warnHtmlMessage(){return I},set warnHtmlMessage(f){I=f,b.warnHtmlMessage=f},get escapeParameter(){return N},set escapeParameter(f){N=f,b.escapeParameter=f},t:Oe,getLocaleMessage:Ee,setLocaleMessage:Ne,mergeLocaleMessage:he,getPostTranslationHandler:je,setPostTranslationHandler:Be,getMissingHandler:Je,setMissingHandler:Qe,[Sa]:rt};return M.datetimeFormats=He,M.numberFormats=Ke,M.rt=qe,M.te=at,M.tm=be,M.d=ye,M.n=Ze,M.getDateTimeFormat=lt,M.setDateTimeFormat=a,M.mergeDateTimeFormat=l,M.getNumberFormat=m,M.setNumberFormat=P,M.mergeNumberFormat=x,M[Pa]=r,M[Et]=tt,M[Nt]=nt,M[gt]=Re,M}const Ct={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function ka({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,s)=>[...r,...s.type===ln?s.children:[s]],[]):t.reduce((n,r)=>{const s=e[r];return s&&(n[r]=s()),n},W())}function yn(e){return ln}const Ma=Tt({name:"i18n-t",props:B({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>X(e)||!isNaN(e)}},Ct),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||At({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter(T=>T!=="_"),c=W();e.locale&&(c.locale=e.locale),e.plural!==void 0&&(c.plural=C(e.plural)?+e.plural:e.plural);const u=ka(t,o),i=s[Et](e.keypath,u,c),d=B(W(),r),O=C(e.tag)||U(e.tag)?e.tag:yn();return sn(O,d,i)}}}),zt=Ma;function va(e){return H(e)&&!C(e[0])}function Rn(e,t,n,r){const{slots:s,attrs:o}=t;return()=>{const c={part:!0};let u=W();e.locale&&(c.locale=e.locale),C(e.format)?c.key=e.format:U(e.format)&&(C(e.format.key)&&(c.key=e.format.key),u=Object.keys(e.format).reduce((L,h)=>n.includes(h)?B(W(),L,{[h]:e.format[h]}):L,W()));const i=r(e.value,c,u);let d=[c.key];H(i)?d=i.map((L,h)=>{const R=s[L.type],k=R?R({[L.type]:L.value,index:h,parts:i}):[L.value];return va(k)&&(k[0].key=`${L.type}-${h}`),k}):C(i)&&(d=[i]);const O=B(W(),o),T=C(e.tag)||U(e.tag)?e.tag:yn();return sn(T,O,d)}}const Fa=Tt({name:"i18n-n",props:B({value:{type:Number,required:!0},format:{type:[String,Object]}},Ct),setup(e,t){const n=e.i18n||At({useScope:e.scope,__useComponent:!0});return Rn(e,t,hn,(...r)=>n[gt](...r))}}),en=Fa,Ua=Tt({name:"i18n-d",props:B({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Ct),setup(e,t){const n=e.i18n||At({useScope:e.scope,__useComponent:!0});return Rn(e,t,bn,(...r)=>n[Nt](...r))}}),tn=Ua;function wa(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function Wa(e){const t=c=>{const{instance:u,modifiers:i,value:d}=c;if(!u||!u.$)throw ee(Q.UNEXPECTED_ERROR);const O=wa(e,u.$),T=nn(d);return[Reflect.apply(O.t,O,[...rn(T)]),O]};return{created:(c,u)=>{const[i,d]=t(u);Ue&&e.global===d&&(c.__i18nWatcher=ut(d.locale,()=>{u.instance&&u.instance.$forceUpdate()})),c.__composer=d,c.textContent=i},unmounted:c=>{Ue&&c.__i18nWatcher&&(c.__i18nWatcher(),c.__i18nWatcher=void 0,delete c.__i18nWatcher),c.__composer&&(c.__composer=void 0,delete c.__composer)},beforeUpdate:(c,{value:u})=>{if(c.__composer){const i=c.__composer,d=nn(u);c.textContent=Reflect.apply(i.t,i,[...rn(d)])}},getSSRProps:c=>{const[u]=t(c);return{textContent:u}}}}function nn(e){if(C(e))return{path:e};if(F(e)){if(!("path"in e))throw ee(Q.REQUIRED_VALUE,"path");return e}else throw ee(Q.INVALID_VALUE)}function rn(e){const{path:t,locale:n,args:r,choice:s,plural:o}=e,c={},u=r||{};return C(n)&&(c.locale=n),X(s)&&(c.plural=s),X(o)&&(c.plural=o),[t,u,c]}function Va(e,t,...n){const r=F(n[0])?n[0]:{},s=!!r.useI18nComponentName;($(r.globalInstall)?r.globalInstall:!0)&&([s?"i18n":zt.name,"I18nT"].forEach(c=>e.component(c,zt)),[en.name,"I18nN"].forEach(c=>e.component(c,en)),[tn.name,"I18nD"].forEach(c=>e.component(c,tn))),e.directive("t",Wa(t))}const xa=ce("global-vue-i18n");function $a(e={},t){const n=$(e.globalInjection)?e.globalInjection:!0,r=!0,s=new Map,[o,c]=Ya(e),u=ce("");function i(T){return s.get(T)||null}function d(T,L){s.set(T,L)}function O(T){s.delete(T)}{const T={get mode(){return"composition"},get allowComposition(){return r},async install(L,...h){if(L.__VUE_I18N_SYMBOL__=u,L.provide(L.__VUE_I18N_SYMBOL__,T),F(h[0])){const A=h[0];T.__composerExtend=A.__composerExtend,T.__vueI18nExtend=A.__vueI18nExtend}let R=null;n&&(R=Qa(L,T.global)),Va(L,T,...h);const k=L.unmount;L.unmount=()=>{R&&R(),T.dispose(),k()}},get global(){return c},dispose(){o.stop()},__instances:s,__getInstance:i,__setInstance:d,__deleteInstance:O};return T}}function At(e={}){const t=on();if(t==null)throw ee(Q.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw ee(Q.NOT_INSTALLED);const n=Ga(t),r=Ha(n),s=ya(t),o=Xa(e,s);if(o==="global")return Ra(r,e,s),r;if(o==="parent"){let i=Ka(n,t,e.__useComponent);return i==null&&(i=r),i}const c=n;let u=c.__getInstance(t);if(u==null){const i=B({},e);"__i18n"in s&&(i.__i18n=s.__i18n),r&&(i.__root=r),u=Pn(i),c.__composerExtend&&(u[Lt]=c.__composerExtend(u)),Ba(c,t,u),c.__setInstance(t,u)}return u}function Ya(e,t,n){const r=kn();{const s=r.run(()=>Pn(e));if(s==null)throw ee(Q.UNEXPECTED_ERROR);return[r,s]}}function Ga(e){{const t=Mn(e.isCE?xa:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw ee(e.isCE?Q.NOT_INSTALLED_WITH_PROVIDE:Q.UNEXPECTED_ERROR);return t}}function Xa(e,t){return $e(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Ha(e){return e.mode==="composition"?e.global:e.global.__composer}function Ka(e,t,n=!1){let r=null;const s=t.root;let o=ja(t,n);for(;o!=null;){const c=e;if(e.mode==="composition"&&(r=c.__getInstance(o)),r!=null||s===o)break;o=o.parent}return r}function ja(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function Ba(e,t,n){vn(()=>{},t),Fn(()=>{const r=n;e.__deleteInstance(t);const s=r[Lt];s&&(s(),delete r[Lt])},t)}const Ja=["locale","fallbackLocale","availableLocales"],an=["t","rt","d","n","tm","te"];function Qa(e,t){const n=Object.create(null);return Ja.forEach(s=>{const o=Object.getOwnPropertyDescriptor(t,s);if(!o)throw ee(Q.UNEXPECTED_ERROR);const c=Un(o.value)?{get(){return o.value.value},set(u){o.value.value=u}}:{get(){return o.get&&o.get()}};Object.defineProperty(n,s,c)}),e.config.globalProperties.$i18n=n,an.forEach(s=>{const o=Object.getOwnPropertyDescriptor(t,s);if(!o||!o.value)throw ee(Q.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${s}`,o)}),()=>{delete e.config.globalProperties.$i18n,an.forEach(s=>{delete e.config.globalProperties[`$${s}`]})}}Aa();__INTLIFY_JIT_COMPILATION__&&Qr(Ia);qr(Fr);Zr(Nn);var qa={failed:"Action failed",success:"Action was successful"},Za={"en-US":qa},ts=$n(({app:e})=>{const t=$a({locale:"en-US",globalInjection:!0,messages:Za});e.use(t)});export{ts as default};
