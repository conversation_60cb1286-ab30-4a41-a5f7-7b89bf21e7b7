const express = require("express");
const {
  getFormsPage,
  getFormsDetail,
  getFormsField,
  saveFormMapping,
  savePermissions,
} = require("../controllers/pageController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

router.get("/get_FormsPage", authMiddleware, getFormsPage);
router.get("/get_FormsDetail", authMiddleware, getFormsDetail);
router.get("/get_FormsField", authMiddleware, getFormsField);
router.post("/save_FormMapping", authMiddleware, saveFormMapping);
router.post("/save_Permissions", authMiddleware, savePermissions);
module.exports = router;
